@echo off
REM Azure Deployment Script for Windows
REM Usage: deploy-azure-windows.bat [app-name] [resource-group]

setlocal enabledelayedexpansion

REM Default values
set APP_NAME=%1
if "%APP_NAME%"=="" set APP_NAME=vwork-community-service

set RESOURCE_GROUP=%2
if "%RESOURCE_GROUP%"=="" set RESOURCE_GROUP=vwork-rg

echo 🚀 Azure Deployment for Community Service (Windows)
echo ================================================

REM Check Azure CLI
set AZURE_CLI_PATH=C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin\az.cmd
if not exist "%AZURE_CLI_PATH%" (
    echo ❌ Azure CLI is not installed. Please install it first.
    echo Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli
    pause
    exit /b 1
)

REM Check if logged in
"%AZURE_CLI_PATH%" account show >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Not logged in to Azure. Please login first.
    "%AZURE_CLI_PATH%" login
)

echo ✅ Azure CLI is ready

REM Create resource group
echo 📦 Creating resource group: %RESOURCE_GROUP%
"%AZURE_CLI_PATH%" group create --name %RESOURCE_GROUP% --location southeastasia --output none

REM Create App Service Plan
set PLAN_NAME=%APP_NAME%-plan
echo 📋 Creating App Service Plan: %PLAN_NAME%
"%AZURE_CLI_PATH%" appservice plan create --name %PLAN_NAME% --resource-group %RESOURCE_GROUP% --sku B1 --is-linux --output none

REM Create Web App
echo 🌐 Creating Web App: %APP_NAME%
"%AZURE_CLI_PATH%" webapp create --name %APP_NAME% --resource-group %RESOURCE_GROUP% --plan %PLAN_NAME% --runtime "NODE|18-lts" --output none

REM Configure basic settings
echo ⚙️  Configuring basic settings
"%AZURE_CLI_PATH%" webapp config set --name %APP_NAME% --resource-group %RESOURCE_GROUP% --linux-fx-version "NODE|18-lts" --startup-file "npm start" --output none

REM Enable logging
"%AZURE_CLI_PATH%" webapp log config --name %APP_NAME% --resource-group %RESOURCE_GROUP% --web-server-logging filesystem --output none

REM Set basic environment variables
echo 🔧 Setting basic environment variables
"%AZURE_CLI_PATH%" webapp config appsettings set --name %APP_NAME% --resource-group %RESOURCE_GROUP% --settings NODE_ENV=production PORT=8080 WEBSITES_PORT=8080 WEBSITES_CONTAINER_START_TIME_LIMIT=1800 WEBSITES_ENABLE_APP_SERVICE_STORAGE=true --output none

REM Create deployment package
echo 📦 Creating deployment package
if exist .azure-deploy rmdir /s /q .azure-deploy
mkdir .azure-deploy

REM Copy necessary files
echo 📋 Copying files...
xcopy /e /i src .azure-deploy\src
xcopy /e /i scripts .azure-deploy\scripts
xcopy /e /i migrations .azure-deploy\migrations
copy app.js .azure-deploy\
copy package.json .azure-deploy\
copy package-lock.json .azure-deploy\
copy web.config .azure-deploy\

REM Create zip file
echo 🗜️  Creating zip file...
cd .azure-deploy
powershell Compress-Archive -Path * -DestinationPath ..\azure-deploy.zip -Force
cd ..

REM Deploy to Azure
echo 🚀 Deploying to Azure...
"%AZURE_CLI_PATH%" webapp deployment source config-zip --resource-group %RESOURCE_GROUP% --name %APP_NAME% --src azure-deploy.zip

REM Clean up
echo 🧹 Cleaning up...
rmdir /s /q .azure-deploy
del azure-deploy.zip

REM Get the app URL
for /f "tokens=*" %%i in ('"%AZURE_CLI_PATH%" webapp show --name %APP_NAME% --resource-group %RESOURCE_GROUP% --query "defaultHostName" --output tsv') do set APP_URL=%%i

echo ✅ Deployment completed successfully!
echo 🌐 Your app is available at: https://%APP_URL%

REM Show important information
echo 📋 Important Information:
echo    App Name: %APP_NAME%
echo    Resource Group: %RESOURCE_GROUP%
echo    App URL: https://%APP_URL%

REM Show required environment variables
echo ⚠️  REQUIRED: Set these environment variables in Azure Portal:
echo    Configuration → Application settings → New application setting
echo.
echo    DATABASE_URL=****************************************/database?sslmode=require
echo    FIREBASE_PROJECT_ID=your-firebase-project-id
echo    FIREBASE_CLIENT_EMAIL=<EMAIL>
echo    FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key\n-----END PRIVATE KEY-----\n"
echo    ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

REM Show next steps
echo 📋 Next Steps:
echo 1. Set up PostgreSQL database (run setup-azure-db-windows.bat)
echo 2. Configure environment variables in Azure Portal
echo 3. Test the health endpoint: https://%APP_URL%/health
echo 4. Run database migrations via SSH

REM Show useful commands
echo 🔧 Useful Commands:
echo    View logs: "%AZURE_CLI_PATH%" webapp log tail --name %APP_NAME% --resource-group %RESOURCE_GROUP%
echo    SSH into app: "%AZURE_CLI_PATH%" webapp ssh --name %APP_NAME% --resource-group %RESOURCE_GROUP%
echo    Restart app: "%AZURE_CLI_PATH%" webapp restart --name %APP_NAME% --resource-group %RESOURCE_GROUP%
echo    View settings: "%AZURE_CLI_PATH%" webapp config appsettings list --name %APP_NAME% --resource-group %RESOURCE_GROUP%

REM Show current status
echo 📊 Current App Status:
"%AZURE_CLI_PATH%" webapp show --name %APP_NAME% --resource-group %RESOURCE_GROUP% --query "{name:name, state:state, defaultHostName:defaultHostName, resourceGroup:resourceGroup}" --output table

echo 🎉 Deployment script completed!
echo 📖 For detailed instructions, see: AZURE_DEPLOYMENT_GUIDE.md

pause 