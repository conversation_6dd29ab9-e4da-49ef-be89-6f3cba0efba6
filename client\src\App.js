import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

console.log('🔥 App.js is loading...');

// Import contexts
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';

import { OnboardingProvider } from './contexts/OnboardingContext';

// Import components
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import ProfileGuard from './components/auth/ProfileGuard';

// Import pages
import HomePage from './pages/HomePage';
import AppleAuthPage from './components/apple/pages/AppleAuthPage';
import AppleDashboard from './components/apple/pages/AppleDashboard';
import AppleProjectsPage from './components/apple/pages/AppleProjectsPage';
import AppleFreelancersPage from './components/apple/pages/AppleFreelancersPage';
import AppleJobsPage from './components/apple/pages/AppleJobsPage';
import AppleJobCreatePage from './components/apple/pages/AppleJobCreatePage';
import AppleJobDetailPage from './components/apple/pages/AppleJobDetailPage';
import AppleJobApplyPage from './components/apple/pages/AppleJobApplyPage';
import AppleContestsPage from './components/apple/pages/AppleContestsPage';
import AppleCommunityPage from './components/apple/pages/AppleCommunityPage';
import AppleTeamPage from './components/apple/pages/AppleTeamPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import EmailVerificationPage from './pages/EmailVerificationPage';
import PasswordResetPage from './pages/PasswordResetPage';
// ProfileSetupPage and ClientSetupPage removed - using OnboardingFlow instead
import MessagesPage from './pages/MessagesPage';
import SettingsPage from './pages/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';
import EmailVerificationModal from './components/common/EmailVerificationModal';
import LoginSuccessPage from './pages/LoginSuccessPage';
import SimpleLoginSuccess from './pages/SimpleLoginSuccess';
import EmailVerificationCheck from './pages/EmailVerificationCheck';
import FreelancerProfilePage from './pages/FreelancerProfilePage';
import ProjectDetailPage from './pages/ProjectDetailPage';
import SupportPage from './pages/SupportPage';
import ProfileDemoPage from './pages/ProfileDemoPage';
import OnboardingGuard from './components/onboarding/OnboardingGuard';
import OnboardingFlow from './components/onboarding/OnboardingFlow';

// Import utilities
import performanceMonitor from './utils/performanceMonitor';

// Debug utilities - safe imports for production
// AnimationPerformanceMonitor will be imported dynamically in development

// Safe dynamic import for development-only features
const DynamicAnimationMonitor = () => {
 // Always declare hooks first
 const [isExpanded, setIsExpanded] = React.useState(false);
 const [fps, setFps] = React.useState(60);

 React.useEffect(() => {
  // Only run in development
  if (process.env.NODE_ENV !== 'development') {
   return;
  }

  let frameCount = 0;
  let lastTime = performance.now();

  const measureFPS = () => {
   frameCount++;
   const now = performance.now();

   if (now - lastTime >= 1000) {
    setFps(Math.round((frameCount * 1000) / (now - lastTime)));
    frameCount = 0;
    lastTime = now;
   }

   requestAnimationFrame(measureFPS);
  };

  const animationFrame = requestAnimationFrame(measureFPS);
  return () => cancelAnimationFrame(animationFrame);
 }, []);

 // Only render in development
 if (process.env.NODE_ENV !== 'development') {
  return null;
 }

 return (
  <div className="fixed bottom-4 right-4 z-50 font-mono text-xs">
   <div className="bg-black bg-opacity-80 text-white rounded-lg shadow-lg">
    {!isExpanded ? (
     <button
      onClick={() => setIsExpanded(true)}
      className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
     >
      <div className="flex items-center space-x-2">
       <div className={`w-2 h-2 rounded-full ${fps >= 55 ? 'bg-green-500' : fps >= 30 ? 'bg-yellow-500' : 'bg-red-500'}`}></div>
       <span>{fps} FPS</span>
      </div>
     </button>
    ) : (
     <div className="p-3 min-w-48">
      <div className="flex justify-between items-center mb-2">
       <h3 className="font-semibold">Performance</h3>
       <button onClick={() => setIsExpanded(false)} className="text-gray-400 hover:text-white">×</button>
      </div>
      <div className="space-y-1">
       <div className="flex justify-between">
        <span>FPS:</span>
        <span className={fps >= 55 ? 'text-green-500' : fps >= 30 ? 'text-yellow-500' : 'text-red-500'}>{fps}</span>
       </div>
      </div>
     </div>
    )}
   </div>
  </div>
 );
};
const debugRouting = () => {
 if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Routing Debug Information:');
  console.log('📍 Current URL:', window.location.href);
  console.log('📍 Pathname:', window.location.pathname);
  console.log('📍 Search:', window.location.search);
  console.log('📍 Environment:', process.env.NODE_ENV);
 }
};

const checkCreateAccountButton = () => {
 if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Checking Create Account button functionality...');
  const createAccountButtons = document.querySelectorAll('a[href*="auth"], button[onclick*="auth"]');
  console.log('🔘 Found create account buttons:', createAccountButtons.length);
 }
};



// App component with email verification modal
const AppContent = () => {
 const { 
  showEmailVerification, 
  verificationEmail, 
  handleResendVerification, 
  handleContinueVerification 
 } = useAuth();

 return (
  <>
   <Router>
    <OnboardingGuard>
     <Layout>
      <ProfileGuard>
      <Routes>
       {/* Public Routes */}
       <Route path='/' element={<HomePage />} />
       <Route path='/projects' element={<AppleProjectsPage />} />
       <Route path='/projects/:id' element={<ProjectDetailPage />} />
       <Route path='/freelancers' element={<AppleFreelancersPage />} />
       <Route path='/freelancers/:id' element={<FreelancerProfilePage />} />
       <Route path='/jobs' element={<AppleJobsPage />} />
       <Route path='/contests' element={<AppleContestsPage />} />
       <Route path='/community' element={<AppleCommunityPage />} />
       <Route path='/team' element={<AppleTeamPage />} />
       <Route path='/support' element={<SupportPage />} />
       <Route path='/profile-demo' element={<ProfileDemoPage />} />

       {/* Auth Routes - redirect if already authenticated */}
       <Route
        path='/auth'
        element={
         <ProtectedRoute requireAuth={false}>
          <AppleAuthPage />
         </ProtectedRoute>
        }
       />
       <Route
        path='/login'
        element={
         <ProtectedRoute requireAuth={false}>
          <AppleAuthPage />
         </ProtectedRoute>
        }
       />
       <Route
        path='/register'
        element={
         <ProtectedRoute requireAuth={false}>
          <AppleAuthPage />
         </ProtectedRoute>
        }
       />
       
       {/* Login Success Handler */}
       <Route
        path='/login-success'
        element={
         <ProtectedRoute requireAuth={true}>
          <LoginSuccessPage />
         </ProtectedRoute>
        }
       />
       
       <Route
        path='/forgot-password'
        element={
         <ProtectedRoute requireAuth={false}>
          <ForgotPasswordPage />
         </ProtectedRoute>
        }
       />

       {/* Email Verification */}
       <Route
        path='/verify-email'
        element={
         <ProtectedRoute requireAuth={false}>
          <EmailVerificationPage />
         </ProtectedRoute>
        }
       />

       {/* Password Reset */}
       <Route
        path='/reset-password'
        element={
         <ProtectedRoute requireAuth={false}>
          <PasswordResetPage />
         </ProtectedRoute>
        }
       />

       {/* Profile Setup routes removed - using OnboardingFlow instead */}

       {/* Onboarding Flow - for first-time users */}
       <Route
        path='/onboarding'
        element={
         <ProtectedRoute requireAuth={true}>
          <OnboardingFlow />
         </ProtectedRoute>
        }
       />

       {/* Simple login success for testing */}
       <Route
        path='/simple-login-success'
        element={<SimpleLoginSuccess />}
       />

       {/* Email verification check */}
       <Route
        path='/check-email'
        element={<EmailVerificationCheck />}
       />

       {/* Job-related routes */}
       <Route path='/jobs/:id' element={<AppleJobDetailPage />} />
       <Route
        path='/jobs/create'
        element={
         <ProtectedRoute>
          <AppleJobCreatePage />
         </ProtectedRoute>
        }
       />
       <Route
        path='/jobs/:id/apply'
        element={
         <ProtectedRoute>
          <AppleJobApplyPage />
         </ProtectedRoute>
        }
       />

       {/* Protected Routes - require authentication */}
       <Route
        path='/dashboard'
        element={
         <ProtectedRoute>
          <AppleDashboard />
         </ProtectedRoute>
        }
       />
       <Route
        path='/messages'
        element={
         <ProtectedRoute>
          <MessagesPage />
         </ProtectedRoute>
        }
       />
       <Route
        path='/settings'
        element={
         <ProtectedRoute>
          <SettingsPage />
         </ProtectedRoute>
        }
       />

       {/* 404 Page */}
       <Route path='*' element={<NotFoundPage />} />
      </Routes>
     </ProfileGuard>
    </Layout>
    </OnboardingGuard>

    {/* Email Verification Modal */}
    <EmailVerificationModal
     isOpen={showEmailVerification}
     onClose={handleContinueVerification}
     email={verificationEmail}
     onResendVerification={handleResendVerification}
     onContinue={handleContinueVerification}
    />

    {/* Global Toast Notifications */}
    <Toaster
     position='top-right'
     containerStyle={{
      top: '80px', // Desktop header height (h-20 = 80px)
      '@media (max-width: 1024px)': {
       top: '64px', // Mobile header height (h-16 = 64px)
      }
     }}
     toastOptions={{
      duration: 4000,
      style: {
       background: 'var(--toast-bg)',
       color: 'var(--toast-color)',
       border: '1px solid var(--toast-border)',
       borderRadius: '12px',
       padding: '16px',
       fontSize: '14px',
       fontWeight: '500',
       boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
       marginTop: '8px', // Thêm khoảng cách nhỏ từ header
      },
      success: {
       iconTheme: {
        primary: '#10b981',
        secondary: '#ffffff',
       },
      },
      error: {
       iconTheme: {
        primary: '#ef4444',
        secondary: '#ffffff',
       },
      },
     }}
    />



    {/* Animation Performance Monitor for development */}
    {process.env.NODE_ENV === 'development' && <DynamicAnimationMonitor />}
   </Router>
  </>
 );
};

// Main App component
const App = () => {
 useEffect(() => {
  // Initialize performance monitoring
  performanceMonitor.startMonitoring();

  // Debug routing issues - only in development
  if (process.env.NODE_ENV === 'development') {
   console.log('🔍 VWork App Starting - Debugging routing...');
   debugRouting();

   // Check create account button functionality
   setTimeout(() => {
    checkCreateAccountButton();
   }, 2000); // Wait for components to render
  }
 }, []);

 return (
  <LanguageProvider>
   <AuthProvider>
    <OnboardingProvider>
     <AppContent />
    </OnboardingProvider>
   </AuthProvider>
  </LanguageProvider>
 );
};

export default App;
