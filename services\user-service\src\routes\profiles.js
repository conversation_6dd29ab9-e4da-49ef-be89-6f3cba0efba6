/**
 * Profile Routes for User Service
 * Handles user profile management
 */

const express = require('express');
const { verifyFirebaseToken, requireAuth, optionalAuth } = require('../middleware/auth');
const database = require('../config/database');

const router = express.Router();

/**
 * @route GET /api/v1/profiles/:userId
 * @desc Get user profile
 * @access Public (limited data without auth)
 */
router.get('/:userId', optionalAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    const isOwnProfile = req.user?.uid === userId;

    const query = `
      SELECT 
        p.*,
        u.display_name,
        u.first_name,
        u.last_name,
        u.avatar_url,
        u.user_type,
        u.created_at
      FROM user_profiles p
      LEFT JOIN users u ON p.user_id = u.id
      WHERE p.user_id = $1
    `;

    const result = await database.query(query, [userId]);

    if (result.rows.length === 0) {
      return res.error('Profile not found', 404);
    }

    const profile = result.rows[0];

    res.success({
      userId: profile.user_id,
      displayName: profile.display_name,
      firstName: profile.first_name,
      lastName: profile.last_name,
      avatarUrl: profile.avatar_url,
      userType: profile.user_type,
      bio: profile.bio,
      title: profile.title,
      company: profile.company,
      website: profile.website,
      location: {
        country: profile.country,
        city: profile.city,
        timezone: profile.timezone
      },
      hourlyRate: profile.hourly_rate,
      currency: profile.currency,
      availability: profile.availability,
      yearsExperience: profile.years_experience,
      
      // Social links
      linkedinUrl: profile.linkedin_url,
      githubUrl: profile.github_url,
      portfolioUrl: profile.portfolio_url,

      // Private settings (only own profile)
      ...(isOwnProfile && {
        languagePreference: profile.language_preference,
        emailNotifications: profile.email_notifications,
        marketingEmails: profile.marketing_emails
      })
    }, 'Profile retrieved successfully');

  } catch (error) {
    console.error('❌ Get profile error:', error);
    res.error('Failed to retrieve profile', 500, error.message);
  }
});

/**
 * @route PUT /api/v1/profiles/:userId
 * @desc Update user profile
 * @access Protected (own profile only)
 */
router.put('/:userId', verifyFirebaseToken, async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Check ownership
    if (req.user.uid !== userId) {
      return res.error('You can only update your own profile', 403);
    }

    const {
      bio,
      title,
      company,
      website,
      country,
      city,
      timezone,
      hourlyRate,
      currency,
      availability,
      yearsExperience,
      linkedinUrl,
      githubUrl,
      portfolioUrl,
      languagePreference,
      emailNotifications,
      marketingEmails
    } = req.body;

    const updateQuery = `
      UPDATE user_profiles SET
        bio = COALESCE($2, bio),
        title = COALESCE($3, title),
        company = COALESCE($4, company),
        website = COALESCE($5, website),
        country = COALESCE($6, country),
        city = COALESCE($7, city),
        timezone = COALESCE($8, timezone),
        hourly_rate = COALESCE($9, hourly_rate),
        currency = COALESCE($10, currency),
        availability = COALESCE($11, availability),
        years_experience = COALESCE($12, years_experience),
        linkedin_url = COALESCE($13, linkedin_url),
        github_url = COALESCE($14, github_url),
        portfolio_url = COALESCE($15, portfolio_url),
        language_preference = COALESCE($16, language_preference),
        email_notifications = COALESCE($17, email_notifications),
        marketing_emails = COALESCE($18, marketing_emails),
        updated_at = CURRENT_TIMESTAMP
      WHERE user_id = $1
      RETURNING *
    `;

    const result = await database.query(updateQuery, [
      userId, bio, title, company, website, country, city, timezone,
      hourlyRate, currency, availability, yearsExperience,
      linkedinUrl, githubUrl, portfolioUrl, languagePreference,
      emailNotifications, marketingEmails
    ]);

    if (result.rows.length === 0) {
      return res.error('Profile not found', 404);
    }

    // Check if profile is now complete
    const profile = result.rows[0];
    const isComplete = !!(profile.bio && profile.title && profile.country);

    if (isComplete) {
      await database.query(
        'UPDATE users SET profile_completed = true WHERE id = $1',
        [userId]
      );
    }

    res.success({
      message: 'Profile updated successfully',
      profileCompleted: isComplete
    });

  } catch (error) {
    console.error('❌ Update profile error:', error);
    res.error('Failed to update profile', 500, error.message);
  }
});

module.exports = router; 