{"name": "chat-service", "version": "1.0.0", "description": "Chat service for VWork platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "build": "npm ci --production", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"@google/generative-ai": "^0.24.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "morgan": "^1.10.0", "pg": "^8.16.3", "pg-pool": "^3.10.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}