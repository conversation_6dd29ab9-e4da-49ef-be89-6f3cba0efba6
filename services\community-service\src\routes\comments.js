/**
 * Community Comments API Routes
 * PostgreSQL implementation for comment management
 */

const express = require('express');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { verifyFirebaseToken } = require('../middleware/auth');

const router = express.Router();

// Get PostgreSQL database connection
let db = null;
try {
  const { postgresqlConfig } = require('../config/postgresql');
  db = postgresqlConfig;
} catch (error) {
  logger.error('Failed to initialize PostgreSQL in comments routes:', error);
}

/**
 * Get comments for a specific post
 * GET /api/comments/:postId
 */
router.get('/:postId', async (req, res) => {
  try {
    const { postId } = req.params;
    const { page = 1, limit = 20, sortBy = 'created_at', order = 'asc' } = req.query;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Get comments with author info from users table
    const query = `
      SELECT c.*,
             u.first_name, u.last_name, u.avatar_url, u.email,
             COUNT(*) OVER() as total_count
      FROM comments c
      LEFT JOIN users u ON c.author_id = u.id
      WHERE c.post_id = $1 AND c.is_deleted = FALSE
      ORDER BY ${sortBy} ${order.toUpperCase()}
      LIMIT $2 OFFSET $3
    `;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const result = await db.query(query, [postId, parseInt(limit), offset]);

    const comments = result.rows.map(comment => {
      // Parse author name to firstName and lastName
      let firstName = 'User';
      let lastName = '';
      
      if (comment.first_name || comment.last_name) {
        firstName = comment.first_name || '';
        lastName = comment.last_name || '';
      }
      
      return {
        id: comment.id,
        postId: comment.post_id,
        content: comment.content,
        likes: comment.like_count || 0,
        upvotes: comment.like_count || 0,
        downvotes: 0,
        depth: comment.depth,
        parentId: comment.parent_id,
        isEdited: comment.is_edited,
        editReason: comment.edit_reason,
        createdAt: comment.created_at,
        updatedAt: comment.updated_at,
        author: {
          id: comment.author_id,
          uid: comment.author_id,
          name: `${firstName} ${lastName}`.trim() || 'User',
          firstName: firstName,
          lastName: lastName,
          email: comment.email,
          picture: comment.avatar_url,
          avatarUrl: comment.avatar_url
        }
      };
    });

    const totalCount = result.rows.length > 0 ? result.rows[0].total_count : 0;

    res.json({
      comments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });

  } catch (error) {
    logger.error('Error fetching comments', { error: error.message, postId: req.params.postId });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create a new comment
 * POST /api/comments
 */
router.post('/', verifyFirebaseToken, async (req, res) => {
  try {
    const {
      postId,
      content,
      parentId = null
    } = req.body;
    
    // Get user info from Firebase auth middleware
    const authorId = req.user?.uid || req.user?.id;
    const authorName = req.user?.name || 'User';
    const authorEmail = req.user?.email;
    const authorPicture = req.user?.picture;

    if (!postId || !content) {
      return res.status(400).json({
        error: 'Missing required fields: postId, content'
      });
    }

    if (!authorId) {
      return res.status(401).json({
        error: 'User authentication required'
      });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Ensure user exists in users table
    try {
      const userCheckResult = await db.query('SELECT id FROM users WHERE id = $1', [authorId]);
      
      if (userCheckResult.rows.length === 0) {
        // Create user if not exists
        await db.query(`
          INSERT INTO users (id, email, first_name, last_name, avatar_url, email_verified)
          VALUES ($1, $2, $3, $4, $5, $6)
          ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            avatar_url = EXCLUDED.avatar_url,
            email_verified = EXCLUDED.email_verified,
            updated_at = CURRENT_TIMESTAMP
        `, [
          authorId,
          authorEmail,
          authorName ? authorName.split(' ')[0] : null,
          authorName ? authorName.split(' ').slice(1).join(' ') : null,
          authorPicture,
          true // email_verified from Firebase
        ]);
      }
    } catch (error) {
      logger.error('Error ensuring user exists', { error: error.message, authorId });
      // Continue anyway, user might already exist
    }

    const commentId = uuidv4();
    let depth = 0;

    // Calculate depth if this is a reply
    if (parentId) {
      const parentResult = await db.query('SELECT depth FROM comments WHERE id = $1', [parentId]);
      if (parentResult.rows.length > 0) {
        depth = parentResult.rows[0].depth + 1;
      }
    }

    // Insert new comment with author information from Firebase
    const query = `
      INSERT INTO comments (id, post_id, author_id, parent_id, content, depth)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const result = await db.query(query, [
      commentId, 
      postId, 
      authorId, 
      parentId, 
      content, 
      depth
    ]);

    // Update comment count on post
    await db.query('UPDATE posts SET comment_count = comment_count + 1 WHERE id = $1', [postId]);

    // Update reply count on parent comment if this is a reply
    if (parentId) {
      await db.query('UPDATE comments SET reply_count = reply_count + 1 WHERE id = $1', [parentId]);
    }

    // Get the created comment with author info from users table
    const commentWithAuthorResult = await db.query(`
      SELECT c.*, u.first_name, u.last_name, u.avatar_url, u.email
      FROM comments c
      LEFT JOIN users u ON c.author_id = u.id
      WHERE c.id = $1
    `, [commentId]);

    const comment = commentWithAuthorResult.rows[0];
    
    // Parse author name to firstName and lastName
    let firstName = 'User';
    let lastName = '';
    
    if (comment.first_name || comment.last_name) {
      firstName = comment.first_name || '';
      lastName = comment.last_name || '';
    } else if (authorName) {
      const nameParts = authorName.trim().split(' ');
      if (nameParts.length === 1) {
        firstName = nameParts[0];
      } else if (nameParts.length >= 2) {
        firstName = nameParts[0];
        lastName = nameParts.slice(1).join(' ');
      }
    }
    
    const responseComment = {
      id: comment.id,
      postId: comment.post_id,
      content: comment.content,
      likes: comment.like_count || 0,
      upvotes: comment.like_count || 0,
      downvotes: 0,
      depth: comment.depth,
      parentId: comment.parent_id,
      createdAt: comment.created_at,
      updatedAt: comment.updated_at,
      author: {
        id: comment.author_id,
        uid: comment.author_id,
        name: `${firstName} ${lastName}`.trim() || 'User',
        firstName: firstName,
        lastName: lastName,
        email: comment.email || authorEmail,
        picture: comment.avatar_url || authorPicture,
        avatarUrl: comment.avatar_url || authorPicture
      }
    };

    logger.info('Comment created successfully', { 
      commentId, 
      postId, 
      authorId, 
      authorName 
    });

    res.status(201).json({
      message: 'Comment created successfully',
      comment: responseComment
    });

  } catch (error) {
    logger.error('Error creating comment', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update a comment
 * PUT /api/comments/:id
 */
router.put('/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { content, editReason } = req.body;
    const userId = req.user?.uid || req.user?.id;

    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if user owns the comment
    const ownershipCheck = await db.query('SELECT author_id FROM comments WHERE id = $1', [id]);
    if (ownershipCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    if (ownershipCheck.rows[0].author_id !== userId) {
      return res.status(403).json({ error: 'You can only edit your own comments' });
    }

    // Update comment
    const query = `
      UPDATE comments
      SET content = $1, edit_reason = $2, is_edited = TRUE, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `;

    const result = await db.query(query, [content, editReason || null, id]);

    logger.info('Comment updated', { commentId: id, userId });

    res.json({ message: 'Comment updated successfully' });

  } catch (error) {
    logger.error('Error updating comment', { error: error.message, commentId: req.params.id });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Delete a comment
 * DELETE /api/comments/:id
 */
router.delete('/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.uid || req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User authentication required' });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if user owns the comment
    const ownershipCheck = await db.query('SELECT author_id, post_id FROM comments WHERE id = $1', [id]);
    if (ownershipCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    if (ownershipCheck.rows[0].author_id !== userId) {
      return res.status(403).json({ error: 'You can only delete your own comments' });
    }

    // Soft delete comment
    await db.query('UPDATE comments SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = $1', [id]);

    // Update comment count on post
    const postId = ownershipCheck.rows[0].post_id;
    await db.query('UPDATE posts SET comment_count = comment_count - 1 WHERE id = $1', [postId]);

    logger.info('Comment deleted', { commentId: id, userId });

    res.json({ message: 'Comment deleted successfully' });

  } catch (error) {
    logger.error('Error deleting comment', { error: error.message, commentId: req.params.id });
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
