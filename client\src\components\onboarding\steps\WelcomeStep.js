import React from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../contexts/AuthContext';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaRocket, FaUsers, FaShieldAlt, FaStar } from 'react-icons/fa';

const WelcomeStep = () => {
 const { user } = useAuth();
 const { goToNextStep, skipOnboarding, loading } = useOnboarding();
 const { t } = useLanguage();

 const features = [
  {
   icon: FaRocket,
   title: t('onboardingFeature1Title'),
   description: t('onboardingFeature1Desc')
  },
  {
   icon: FaUsers,
   title: t('onboardingFeature2Title'),
   description: t('onboardingFeature2Desc')
  },
  {
   icon: FaShieldAlt,
   title: t('onboardingFeature3Title'),
   description: t('onboardingFeature3Desc')
  },
  {
   icon: FaStar,
   title: t('onboardingFeature4Title'),
   description: t('onboardingFeature4Desc')
  }
 ];

 const handleGetStarted = () => {
  goToNextStep();
 };

 const handleSkip = async () => {
  const result = await skipOnboarding();
  if (result.success) {
   // Redirect will be handled by the auth context
   window.location.href = '/dashboard';
  }
 };

 return (
  <div className="p-8 md:p-12">
   {/* Header */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.2 }}
   >
    <div className="w-20 h-20 bg-gradient-to-br from-medieval-gold-400 to-medieval-gold-600 rounded-full flex items-center justify-center mx-auto mb-6">
     <FaRocket className="text-3xl text-white" />
    </div>
    
    <h1 className="text-3xl md:text-4xl font-medium font-bold text-gray-800 mb-4">
     {t('welcomeToVWork')}, {user?.name || user?.displayName || t('friend')}!
    </h1>
    
    <p className="text-lg text-gray-600 font-medium max-w-2xl mx-auto">
     {user?.userType === 'freelancer' 
      ? t('onboardingWelcomeFreelancer')
      : t('onboardingWelcomeClient')
     }
    </p>
   </motion.div>

   {/* Features Grid */}
   <motion.div
    className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.4 }}
   >
    {features.map((feature, index) => (
     <motion.div
      key={index}
      className="bg-medieval-brown-50 rounded-xl p-6 border border-gray-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5 + index * 0.1 }}
     >
      <div className="flex items-start space-x-4">
       <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
        <feature.icon className="text-xl text-medieval-gold-600" />
       </div>
       <div>
        <h3 className="font-medium font-semibold text-gray-800 mb-2">
         {feature.title}
        </h3>
        <p className="text-gray-600 text-sm">
         {feature.description}
        </p>
       </div>
      </div>
     </motion.div>
    ))}
   </motion.div>

   {/* User Type Badge */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.8 }}
   >
    <div className="inline-flex items-center bg-blue-100 text-medieval-gold-800 px-4 py-2 rounded-full font-medium font-medium">
     {user?.userType === 'freelancer' ? (
      <>
       <FaUsers className="mr-2" />
       {t('freelancerAccount')}
      </>
     ) : (
      <>
       <FaStar className="mr-2" />
       {t('clientAccount')}
      </>
     )}
    </div>
   </motion.div>

   {/* Action Buttons */}
   <motion.div
    className="flex flex-col sm:flex-row gap-4 justify-center"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 1.0 }}
   >
    <button
     onClick={handleGetStarted}
     disabled={loading}
     className="btn-primary btn-auto-scale px-8 py-3 text-lg font-medium font-semibold"
    >
     {t('getStarted')}
    </button>

    <button
     onClick={handleSkip}
     disabled={loading}
     className="btn-secondary btn-auto-scale px-8 py-3 text-lg font-medium font-semibold"
    >
     {t('skipForNow')}
    </button>
   </motion.div>

   {/* Additional Info */}
   <motion.div
    className="text-center mt-6"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 1.2 }}
   >
    <p className="text-sm text-gray-500 font-medium">
     {t('onboardingTimeEstimate')}
    </p>
   </motion.div>
  </div>
 );
};

export default WelcomeStep;
