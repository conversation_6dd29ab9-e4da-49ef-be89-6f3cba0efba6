/**
 * Teams Routes
 * CRUD operations for team management
 */

const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');

// Import middleware
const { 
  verifyFirebaseToken, 
  requireTeamOwnership, 
  requireTeamMembership 
} = require('../middleware/auth');
const { validateBody, validateQuery, schemas } = require('../middleware/validation');
const { responseMiddleware, createPagination } = require('../utils/response');
const { query, transaction } = require('../config/postgresql');
const logger = require('../utils/logger');
const config = require('../config/config');

// Apply response middleware
router.use(responseMiddleware);

// Get all teams with pagination and filters
router.get('/', validateQuery(schemas.pagination), async (req, res) => {
  try {
    const { page = 1, limit = 20, status, skills, location, min_rating, category } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (status) {
      paramCount++;
      whereClause += ` AND t.status = $${paramCount}`;
      params.push(status);
    }

    if (min_rating) {
      paramCount++;
      whereClause += ` AND t.rating >= $${paramCount}`;
      params.push(min_rating);
    }

    if (category) {
      paramCount++;
      whereClause += ` AND t.category = $${paramCount}`;
      params.push(category);
    }

    // Get teams with member count and basic info
    const teamsQuery = `
      SELECT 
        t.*,
        COUNT(tm.id) as member_count,
        AVG(tm.profit_share) as avg_profit_share,
        json_agg(
          DISTINCT jsonb_build_object(
            'id', tm.id,
            'user_id', tm.user_id,
            'role', tm.role,
            'position', tm.position,
            'joined_at', tm.joined_at
          )
        ) FILTER (WHERE tm.status = 'active') as members
      FROM teams t
      LEFT JOIN team_members tm ON t.id = tm.team_id AND tm.status = 'active'
      ${whereClause}
      GROUP BY t.id
      ORDER BY t.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);

    const teamsResult = await query(teamsQuery, params);
    const teams = teamsResult.rows;

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT t.id) as total
      FROM teams t
      ${whereClause}
    `;
    const countResult = await query(countQuery, params.slice(0, -2));
    const total = parseInt(countResult.rows[0].total);

    const pagination = createPagination(page, limit, total);

    logger.info(`Retrieved ${teams.length} teams`);
    res.apiSuccess(teams, 'Teams retrieved successfully', pagination);

  } catch (error) {
    logger.error('Get teams failed:', error.message);
    res.apiError('Failed to get teams', 'GET_TEAMS_ERROR', 500);
  }
});

// Get featured teams (for discovery)
router.get('/featured', async (req, res) => {
  try {
    const featuredQuery = `
      SELECT 
        t.*,
        COUNT(tm.id) as member_count,
        AVG(tm.profit_share) as avg_profit_share,
        json_agg(
          DISTINCT jsonb_build_object(
            'id', tm.id,
            'user_id', tm.user_id,
            'role', tm.role,
            'position', tm.position,
            'joined_at', tm.joined_at
          )
        ) FILTER (WHERE tm.status = 'active') as members
      FROM teams t
      LEFT JOIN team_members tm ON t.id = tm.team_id AND tm.status = 'active'
      WHERE t.status = 'active' AND t.rating >= 4.5
      GROUP BY t.id
      ORDER BY t.rating DESC, t.total_projects DESC, t.total_earnings DESC
      LIMIT 10
    `;

    const result = await query(featuredQuery);
    const teams = result.rows;

    logger.info(`Retrieved ${teams.length} featured teams`);
    res.apiSuccess(teams, 'Featured teams retrieved successfully');

  } catch (error) {
    logger.error('Get featured teams failed:', error.message);
    res.apiError('Failed to get featured teams', 'GET_FEATURED_TEAMS_ERROR', 500);
  }
});

// Get team by ID with full details
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Get team details with members and projects
    const teamQuery = `
      SELECT 
        t.*,
        json_agg(
          DISTINCT jsonb_build_object(
            'id', tm.id,
            'user_id', tm.user_id,
            'role', tm.role,
            'position', tm.position,
            'profit_share', tm.profit_share,
            'joined_at', tm.joined_at,
            'status', tm.status
          )
        ) FILTER (WHERE tm.id IS NOT NULL) as members,
        json_agg(
          DISTINCT jsonb_build_object(
            'id', tp.id,
            'project_id', tp.project_id,
            'status', tp.status,
            'started_at', tp.started_at,
            'completed_at', tp.completed_at,
            'earnings', tp.earnings
          )
        ) FILTER (WHERE tp.id IS NOT NULL) as projects
      FROM teams t
      LEFT JOIN team_members tm ON t.id = tm.team_id
      LEFT JOIN team_projects tp ON t.id = tp.team_id
      WHERE t.id = $1
      GROUP BY t.id
    `;

    const result = await query(teamQuery, [id]);

    if (result.rows.length === 0) {
      return res.apiNotFound('Team not found');
    }

    const team = result.rows[0];

    logger.info(`Retrieved team: ${team.id}`);
    res.apiSuccess(team, 'Team retrieved successfully');

  } catch (error) {
    logger.error('Get team failed:', error.message);
    res.apiError('Failed to get team', 'GET_TEAM_ERROR', 500);
  }
});

// Create new team (4-step workflow)
router.post('/', verifyFirebaseToken, validateBody(schemas.createTeam), async (req, res) => {
  try {
    const { 
      name, 
      slogan, 
      description, 
      logo_url,
      member_count, 
      members,
      category = 'development' // Default category
    } = req.body;
    const leaderId = req.user.uid;

    // Validate team creation conditions
    const validationResult = await validateTeamCreation(leaderId, members);
    if (!validationResult.isValid) {
      return res.apiBadRequest(validationResult.message);
    }

    // Create team in transaction
    const teamId = await transaction(async (client) => {
      // Insert team
      const teamResult = await client.query(
        `INSERT INTO teams (name, slogan, description, logo_url, leader_id, member_count, status, category)
         VALUES ($1, $2, $3, $4, $5, $6, 'pending', $7)
         RETURNING id`,
        [name, slogan, description, logo_url, leaderId, member_count, category]
      );

      const teamId = teamResult.rows[0].id;

      // Insert team leader
      await client.query(
        `INSERT INTO team_members (team_id, user_id, role, position, profit_share, status)
         VALUES ($1, $2, 'leader', $3, $4, 'active')`,
        [teamId, leaderId, members[0].position, members[0].profit_share]
      );

      // Insert other members
      for (let i = 1; i < members.length; i++) {
        const member = members[i];
        await client.query(
          `INSERT INTO team_members (team_id, user_id, role, position, profit_share, status)
           VALUES ($1, $2, 'member', $3, $4, 'pending')`,
          [teamId, member.user_id, member.position, member.profit_share]
        );

        // Create invitation for each member
        await client.query(
          `INSERT INTO team_invitations (team_id, inviter_id, invitee_id, message)
           VALUES ($1, $2, $3, $4)`,
          [teamId, leaderId, member.user_id, `Bạn được mời tham gia team ${name}`]
        );
      }

      return teamId;
    });

    logger.info(`Team created: ${teamId} by user: ${leaderId}`);
    res.apiSuccess({ 
      team_id: teamId,
      message: 'Team created successfully. Invitations sent to members.',
      next_steps: [
        'Wait for all members to accept invitations',
        'Team will be activated when all members accept',
        'You can start adding projects once team is active'
      ]
    });

  } catch (error) {
    logger.error('Create team failed:', error.message);
    res.apiError('Failed to create team', 'CREATE_TEAM_ERROR', 500);
  }
});

// Update team (leader only)
router.put('/:id', verifyFirebaseToken, requireTeamOwnership, validateBody(schemas.updateTeam), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, slogan, description, logo_url, category } = req.body;

    const updateQuery = `
      UPDATE teams 
      SET name = COALESCE($1, name),
          slogan = COALESCE($2, slogan),
          description = COALESCE($3, description),
          logo_url = COALESCE($4, logo_url),
          category = COALESCE($5, category),
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `;

    const result = await query(updateQuery, [name, slogan, description, logo_url, category, id]);

    if (result.rows.length === 0) {
      return res.apiNotFound('Team not found');
    }

    const updatedTeam = result.rows[0];

    logger.info(`Team updated: ${id} by user: ${req.user.uid}`);
    res.apiSuccess(updatedTeam, 'Team updated successfully');

  } catch (error) {
    logger.error('Update team failed:', error.message);
    res.apiError('Failed to update team', 'UPDATE_TEAM_ERROR', 500);
  }
});

// Update profit sharing (leader only)
router.put('/:id/profit-sharing', verifyFirebaseToken, requireTeamOwnership, validateBody(schemas.profitShare), async (req, res) => {
  try {
    const { id } = req.params;
    const { members } = req.body;

    // Validate total profit share equals 100%
    const totalShare = members.reduce((sum, member) => sum + member.profit_share, 0);
    if (Math.abs(totalShare - 100) > 0.01) {
      return res.apiBadRequest('Total profit share must equal 100%');
    }

    // Update profit sharing in transaction
    await transaction(async (client) => {
      for (const member of members) {
        await client.query(
          `UPDATE team_members 
           SET profit_share = $1, updated_at = CURRENT_TIMESTAMP
           WHERE team_id = $2 AND user_id = $3`,
          [member.profit_share, id, member.user_id]
        );
      }
    });

    logger.info(`Profit sharing updated for team: ${id} by user: ${req.user.uid}`);
    res.apiSuccess(null, 'Profit sharing updated successfully');

  } catch (error) {
    logger.error('Update profit sharing failed:', error.message);
    res.apiError('Failed to update profit sharing', 'UPDATE_PROFIT_SHARING_ERROR', 500);
  }
});

// Delete team (leader + 2/3 vote required)
router.delete('/:id', verifyFirebaseToken, requireTeamOwnership, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if team has active projects
    const activeProjectsQuery = `
      SELECT COUNT(*) as count FROM team_projects 
      WHERE team_id = $1 AND status = 'active'
    `;
    const activeProjectsResult = await query(activeProjectsQuery, [id]);
    
    if (parseInt(activeProjectsResult.rows[0].count) > 0) {
      return res.apiBadRequest('Cannot delete team with active projects');
    }

    // Get team members for voting
    const membersQuery = `
      SELECT user_id, role FROM team_members 
      WHERE team_id = $1 AND status = 'active'
    `;
    const membersResult = await query(membersQuery, [id]);
    const members = membersResult.rows;

    // For now, allow leader to delete (in future, implement voting system)
    await query('DELETE FROM teams WHERE id = $1', [id]);

    logger.info(`Team deleted: ${id} by user: ${req.user.uid}`);
    res.apiSuccess(null, 'Team deleted successfully');

  } catch (error) {
    logger.error('Delete team failed:', error.message);
    res.apiError('Failed to delete team', 'DELETE_TEAM_ERROR', 500);
  }
});

// Get team statistics
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params;

    const statsQuery = `
      SELECT 
        t.total_projects,
        t.total_earnings,
        t.rating,
        COUNT(tm.id) as active_members,
        COUNT(tp.id) as active_projects,
        AVG(tm.profit_share) as avg_profit_share,
        COUNT(DISTINCT tp.project_id) as unique_projects,
        SUM(CASE WHEN tp.status = 'completed' THEN 1 ELSE 0 END) as completed_projects,
        SUM(CASE WHEN tp.status = 'active' THEN 1 ELSE 0 END) as ongoing_projects
      FROM teams t
      LEFT JOIN team_members tm ON t.id = tm.team_id AND tm.status = 'active'
      LEFT JOIN team_projects tp ON t.id = tp.team_id
      WHERE t.id = $1
      GROUP BY t.id, t.total_projects, t.total_earnings, t.rating
    `;

    const result = await query(statsQuery, [id]);

    if (result.rows.length === 0) {
      return res.apiNotFound('Team not found');
    }

    const stats = result.rows[0];

    logger.info(`Retrieved stats for team: ${id}`);
    res.apiSuccess(stats, 'Team statistics retrieved successfully');

  } catch (error) {
    logger.error('Get team stats failed:', error.message);
    res.apiError('Failed to get team statistics', 'GET_TEAM_STATS_ERROR', 500);
  }
});

// Get user's teams
router.get('/user/my-teams', verifyFirebaseToken, async (req, res) => {
  try {
    const userId = req.user.uid;

    const userTeamsQuery = `
      SELECT 
        t.*,
        tm.role as user_role,
        tm.position as user_position,
        tm.profit_share as user_profit_share,
        tm.joined_at as user_joined_at,
        COUNT(tm2.id) as member_count
      FROM teams t
      JOIN team_members tm ON t.id = tm.team_id
      LEFT JOIN team_members tm2 ON t.id = tm2.team_id AND tm2.status = 'active'
      WHERE tm.user_id = $1
      GROUP BY t.id, tm.role, tm.position, tm.profit_share, tm.joined_at
      ORDER BY t.updated_at DESC
    `;

    const result = await query(userTeamsQuery, [userId]);
    const teams = result.rows;

    logger.info(`Retrieved ${teams.length} teams for user: ${userId}`);
    res.apiSuccess(teams, 'User teams retrieved successfully');

  } catch (error) {
    logger.error('Get user teams failed:', error.message);
    res.apiError('Failed to get user teams', 'GET_USER_TEAMS_ERROR', 500);
  }
});

// Get team categories (for frontend filters)
router.get('/categories/list', async (req, res) => {
  try {
    const categories = [
      { id: 'development', name: 'Phát triển phần mềm', icon: '💻' },
      { id: 'design', name: 'Thiết kế & UI/UX', icon: '🎨' },
      { id: 'marketing', name: 'Marketing & PR', icon: '📢' },
      { id: 'writing', name: 'Viết & Dịch thuật', icon: '✍️' },
      { id: 'video', name: 'Video & Animation', icon: '🎬' },
      { id: 'audio', name: 'Âm nhạc & Âm thanh', icon: '🎵' },
      { id: 'consulting', name: 'Tư vấn & HR', icon: '💼' },
      { id: 'admin', name: 'Hỗ trợ & Quản trị', icon: '🛠️' }
    ];

    res.apiSuccess(categories, 'Categories retrieved successfully');

  } catch (error) {
    logger.error('Get categories failed:', error.message);
    res.apiError('Failed to get categories', 'GET_CATEGORIES_ERROR', 500);
  }
});

// Get team creation requirements (for frontend validation)
router.get('/requirements', async (req, res) => {
  try {
    const requirements = {
      min_team_members: config.MIN_TEAM_MEMBERS,
      max_team_members: config.MAX_TEAM_MEMBERS,
      min_friendship_days: config.MIN_FRIENDSHIP_DAYS,
      min_completed_projects: config.MIN_COMPLETED_PROJECTS,
      min_rating: config.MIN_RATING,
      categories: [
        { id: 'development', name: 'Phát triển phần mềm', icon: '💻' },
        { id: 'design', name: 'Thiết kế & UI/UX', icon: '🎨' },
        { id: 'marketing', name: 'Marketing & PR', icon: '📢' },
        { id: 'writing', name: 'Viết & Dịch thuật', icon: '✍️' },
        { id: 'video', name: 'Video & Animation', icon: '🎬' },
        { id: 'audio', name: 'Âm nhạc & Âm thanh', icon: '🎵' },
        { id: 'consulting', name: 'Tư vấn & HR', icon: '💼' },
        { id: 'admin', name: 'Hỗ trợ & Quản trị', icon: '🛠️' }
      ]
    };

    res.apiSuccess(requirements, 'Team creation requirements retrieved successfully');

  } catch (error) {
    logger.error('Get requirements failed:', error.message);
    res.apiError('Failed to get requirements', 'GET_REQUIREMENTS_ERROR', 500);
  }
});

// Get user's friends for team creation (placeholder - needs friendship service integration)
router.get('/user/friends', verifyFirebaseToken, async (req, res) => {
  try {
    const userId = req.user.uid;

    // TODO: Integrate with friendship service
    // For now, return mock data
    const mockFriends = [
      {
        id: 'friend1',
        name: 'Nguyễn Văn B',
        email: '<EMAIL>',
        rating: 4.9,
        completed_projects: 15,
        skills: ['React', 'Node.js', 'MongoDB'],
        friendship_days: 10
      },
      {
        id: 'friend2', 
        name: 'Trần Thị C',
        email: '<EMAIL>',
        rating: 4.8,
        completed_projects: 12,
        skills: ['UI/UX Design', 'Figma', 'Adobe XD'],
        friendship_days: 7
      }
    ];

    res.apiSuccess(mockFriends, 'Friends retrieved successfully');

  } catch (error) {
    logger.error('Get friends failed:', error.message);
    res.apiError('Failed to get friends', 'GET_FRIENDS_ERROR', 500);
  }
});

// Search teams with advanced filters
router.get('/search', validateQuery(schemas.teamSearch), async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      skills, 
      location, 
      min_rating, 
      max_members,
      category,
      status = 'active'
    } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE t.status = $1';
    const params = [status];
    let paramCount = 1;

    if (skills) {
      paramCount++;
      whereClause += ` AND t.skills ILIKE $${paramCount}`;
      params.push(`%${skills}%`);
    }

    if (location) {
      paramCount++;
      whereClause += ` AND t.location ILIKE $${paramCount}`;
      params.push(`%${location}%`);
    }

    if (min_rating) {
      paramCount++;
      whereClause += ` AND t.rating >= $${paramCount}`;
      params.push(min_rating);
    }

    if (max_members) {
      paramCount++;
      whereClause += ` AND t.member_count <= $${paramCount}`;
      params.push(max_members);
    }

    if (category) {
      paramCount++;
      whereClause += ` AND t.category = $${paramCount}`;
      params.push(category);
    }

    const searchQuery = `
      SELECT 
        t.*,
        COUNT(tm.id) as member_count,
        AVG(tm.profit_share) as avg_profit_share,
        json_agg(
          DISTINCT jsonb_build_object(
            'id', tm.id,
            'user_id', tm.user_id,
            'role', tm.role,
            'position', tm.position,
            'joined_at', tm.joined_at
          )
        ) FILTER (WHERE tm.status = 'active') as members
      FROM teams t
      LEFT JOIN team_members tm ON t.id = tm.team_id AND tm.status = 'active'
      ${whereClause}
      GROUP BY t.id
      ORDER BY t.rating DESC, t.total_projects DESC, t.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);

    const result = await query(searchQuery, params);
    const teams = result.rows;

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT t.id) as total
      FROM teams t
      ${whereClause}
    `;
    const countResult = await query(countQuery, params.slice(0, -2));
    const total = parseInt(countResult.rows[0].total);

    const pagination = createPagination(page, limit, total);

    logger.info(`Search returned ${teams.length} teams for query:`, req.query);
    res.apiSuccess(teams, 'Teams search completed successfully', pagination);

  } catch (error) {
    logger.error('Search teams failed:', error.message);
    res.apiError('Failed to search teams', 'SEARCH_TEAMS_ERROR', 500);
  }
});

// Get team projects
router.get('/:id/projects', async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const projectsQuery = `
      SELECT 
        tp.*,
        p.title as project_title,
        p.description as project_description,
        p.budget as project_budget,
        p.category as project_category
      FROM team_projects tp
      LEFT JOIN projects p ON tp.project_id = p.id
      WHERE tp.team_id = $1
      ORDER BY tp.started_at DESC
      LIMIT $2 OFFSET $3
    `;

    const result = await query(projectsQuery, [id, limit, offset]);
    const projects = result.rows;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total FROM team_projects WHERE team_id = $1
    `;
    const countResult = await query(countQuery, [id]);
    const total = parseInt(countResult.rows[0].total);

    const pagination = createPagination(page, limit, total);

    logger.info(`Retrieved ${projects.length} projects for team: ${id}`);
    res.apiSuccess(projects, 'Team projects retrieved successfully', pagination);

  } catch (error) {
    logger.error('Get team projects failed:', error.message);
    res.apiError('Failed to get team projects', 'GET_TEAM_PROJECTS_ERROR', 500);
  }
});

// Get team earnings history
router.get('/:id/earnings', verifyFirebaseToken, requireTeamMembership, async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const earningsQuery = `
      SELECT 
        teh.*,
        tp.project_id,
        p.title as project_title
      FROM team_earnings_history teh
      LEFT JOIN team_projects tp ON teh.team_project_id = tp.id
      LEFT JOIN projects p ON tp.project_id = p.id
      WHERE teh.team_id = $1
      ORDER BY teh.distributed_at DESC
      LIMIT $2 OFFSET $3
    `;

    const result = await query(earningsQuery, [id, limit, offset]);
    const earnings = result.rows;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total FROM team_earnings_history WHERE team_id = $1
    `;
    const countResult = await query(countQuery, [id]);
    const total = parseInt(countResult.rows[0].total);

    const pagination = createPagination(page, limit, total);

    logger.info(`Retrieved ${earnings.length} earnings records for team: ${id}`);
    res.apiSuccess(earnings, 'Team earnings retrieved successfully', pagination);

  } catch (error) {
    logger.error('Get team earnings failed:', error.message);
    res.apiError('Failed to get team earnings', 'GET_TEAM_EARNINGS_ERROR', 500);
  }
});

// Remove team member (leader only)
router.delete('/:id/members/:userId', verifyFirebaseToken, requireTeamOwnership, validateBody(schemas.removeMember), async (req, res) => {
  try {
    const { id, userId } = req.params;
    const { reason } = req.body;

    // Check if user is trying to remove themselves
    if (userId === req.user.uid) {
      return res.apiBadRequest('Cannot remove yourself from team. Use leave team instead.');
    }

    // Check if user is a member
    const memberQuery = `
      SELECT role FROM team_members 
      WHERE team_id = $1 AND user_id = $2 AND status = 'active'
    `;
    const memberResult = await query(memberQuery, [id, userId]);

    if (memberResult.rows.length === 0) {
      return res.apiNotFound('Member not found in team');
    }

    // Remove member
    await query(
      'UPDATE team_members SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE team_id = $2 AND user_id = $3',
      ['removed', id, userId]
    );

    logger.info(`Member ${userId} removed from team ${id} by leader ${req.user.uid}`);
    res.apiSuccess(null, 'Member removed successfully');

  } catch (error) {
    logger.error('Remove member failed:', error.message);
    res.apiError('Failed to remove member', 'REMOVE_MEMBER_ERROR', 500);
  }
});

// Leave team (member only)
router.post('/:id/leave', verifyFirebaseToken, requireTeamMembership, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.uid;

    // Check if user is leader
    const memberQuery = `
      SELECT role FROM team_members 
      WHERE team_id = $1 AND user_id = $2 AND status = 'active'
    `;
    const memberResult = await query(memberQuery, [id, userId]);

    if (memberResult.rows.length === 0) {
      return res.apiNotFound('You are not a member of this team');
    }

    const member = memberResult.rows[0];

    if (member.role === 'leader') {
      return res.apiBadRequest('Leader cannot leave team. Transfer leadership or delete team instead.');
    }

    // Leave team
    await query(
      'UPDATE team_members SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE team_id = $2 AND user_id = $3',
      ['left', id, userId]
    );

    logger.info(`User ${userId} left team ${id}`);
    res.apiSuccess(null, 'Left team successfully');

  } catch (error) {
    logger.error('Leave team failed:', error.message);
    res.apiError('Failed to leave team', 'LEAVE_TEAM_ERROR', 500);
  }
});

// Transfer team leadership
router.put('/:id/transfer-leadership', verifyFirebaseToken, requireTeamOwnership, validateBody(schemas.teamMember), async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.body;

    // Check if new leader is a member
    const memberQuery = `
      SELECT role FROM team_members 
      WHERE team_id = $1 AND user_id = $2 AND status = 'active'
    `;
    const memberResult = await query(memberQuery, [id, user_id]);

    if (memberResult.rows.length === 0) {
      return res.apiNotFound('User is not a member of this team');
    }

    // Transfer leadership in transaction
    await transaction(async (client) => {
      // Remove current leader role
      await client.query(
        'UPDATE team_members SET role = $1, updated_at = CURRENT_TIMESTAMP WHERE team_id = $2 AND user_id = $3',
        ['member', id, req.user.uid]
      );

      // Assign new leader role
      await client.query(
        'UPDATE team_members SET role = $1, updated_at = CURRENT_TIMESTAMP WHERE team_id = $2 AND user_id = $3',
        ['leader', id, user_id]
      );

      // Update team leader
      await client.query(
        'UPDATE teams SET leader_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [user_id, id]
      );
    });

    logger.info(`Leadership transferred from ${req.user.uid} to ${user_id} in team ${id}`);
    res.apiSuccess(null, 'Leadership transferred successfully');

  } catch (error) {
    logger.error('Transfer leadership failed:', error.message);
    res.apiError('Failed to transfer leadership', 'TRANSFER_LEADERSHIP_ERROR', 500);
  }
});

// Get team settings
router.get('/:id/settings', verifyFirebaseToken, requireTeamMembership, async (req, res) => {
  try {
    const { id } = req.params;

    // For now, return default settings
    // TODO: Add settings table to database
    const settings = {
      allow_member_invites: true,
      require_approval_for_projects: false,
      auto_accept_invitations: false,
      notification_preferences: {
        email_notifications: true,
        push_notifications: true,
        chat_notifications: true
      }
    };

    res.apiSuccess(settings, 'Team settings retrieved successfully');

  } catch (error) {
    logger.error('Get team settings failed:', error.message);
    res.apiError('Failed to get team settings', 'GET_TEAM_SETTINGS_ERROR', 500);
  }
});

// Update team settings (leader only)
router.put('/:id/settings', verifyFirebaseToken, requireTeamOwnership, validateBody(schemas.teamSettings), async (req, res) => {
  try {
    const { id } = req.params;
    const settings = req.body;

    // TODO: Update settings in database
    // For now, just return success
    logger.info(`Team settings updated for team ${id} by user ${req.user.uid}`);
    res.apiSuccess(settings, 'Team settings updated successfully');

  } catch (error) {
    logger.error('Update team settings failed:', error.message);
    res.apiError('Failed to update team settings', 'UPDATE_TEAM_SETTINGS_ERROR', 500);
  }
});

// Helper function to validate team creation conditions
async function validateTeamCreation(leaderId, members) {
  try {
    // Check minimum members
    if (members.length < config.MIN_TEAM_MEMBERS) {
      return {
        isValid: false,
        message: `Team must have at least ${config.MIN_TEAM_MEMBERS} members`
      };
    }

    // Check maximum members
    if (members.length > config.MAX_TEAM_MEMBERS) {
      return {
        isValid: false,
        message: `Team cannot have more than ${config.MAX_TEAM_MEMBERS} members`
      };
    }

    // Check if leader is in members list
    const leaderInMembers = members.find(m => m.user_id === leaderId);
    if (!leaderInMembers) {
      return {
        isValid: false,
        message: 'Team leader must be included in members list'
      };
    }

    // Check profit share total
    const totalProfitShare = members.reduce((sum, member) => sum + member.profit_share, 0);
    if (Math.abs(totalProfitShare - 100) > 0.01) {
      return {
        isValid: false,
        message: 'Total profit share must equal 100%'
      };
    }

    // TODO: Add more validation checks:
    // - Check friendship duration (requires friendship service integration)
    // - Check completed projects (requires user service integration)
    // - Check ratings (requires user service integration)

    return { isValid: true };
  } catch (error) {
    logger.error('Team creation validation failed:', error.message);
    return {
      isValid: false,
      message: 'Validation failed'
    };
  }
}

module.exports = router; 