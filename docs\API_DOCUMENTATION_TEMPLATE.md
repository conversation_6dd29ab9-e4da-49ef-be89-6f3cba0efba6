# 📚 NERAFUS API Documentation Template

## Overview
This template provides a standardized format for documenting all NERAFUS platform APIs.

---

# 🔐 Authentication Service API

## Base URL
```
Development: http://localhost:3001/api/v1
Production: https://nerafus-auth-service.onrender.com/api/v1
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Error Responses
All endpoints return errors in the following format:
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ],
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

---

## Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "name": "<PERSON> Doe",
  "role": "freelancer"
}
```

**Validation Rules:**
- `email`: Required, valid email format
- `password`: Required, minimum 8 characters, must contain uppercase, lowercase, and number
- `name`: Required, 2-50 characters
- `role`: Required, one of: "freelancer", "client", "admin"

**Success Response (201):**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "freelancer",
      "isVerified": false,
      "createdAt": "2024-01-15T10:30:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Error Response (409):**
```json
{
  "success": false,
  "message": "User with this email already exists",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "name": "John Doe",
    "role": "freelancer"
  }'
```

---

### POST /auth/login
Authenticate user with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "freelancer",
      "isVerified": true
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "24h"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

---

### GET /auth/me
Get current authenticated user information.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "freelancer",
      "isVerified": true,
      "profile": {
        "avatar": "https://example.com/avatar.jpg",
        "bio": "Full-stack developer with 5 years experience",
        "skills": ["JavaScript", "React", "Node.js"]
      },
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

---

### POST /auth/logout
Logout current user and invalidate token.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Logout successful",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

---

### POST /auth/refresh
Refresh JWT token.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "24h"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

---

## Data Models

### User Model
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  role: 'freelancer' | 'client' | 'admin';
  isVerified: boolean;
  profile?: {
    avatar?: string;
    bio?: string;
    skills?: string[];
    location?: string;
    website?: string;
  };
  createdAt: string;
  updatedAt: string;
}
```

### Token Model
```typescript
interface AuthToken {
  token: string;
  expiresIn: string;
  type: 'Bearer';
}
```

---

## Rate Limiting
- **Authentication endpoints**: 5 requests per minute per IP
- **General endpoints**: 100 requests per minute per user

## Webhooks
The authentication service can send webhooks for the following events:
- `user.registered` - When a new user registers
- `user.verified` - When a user verifies their email
- `user.login` - When a user logs in (optional)

**Webhook Payload:**
```json
{
  "event": "user.registered",
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "freelancer"
    }
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

---

## SDK Examples

### JavaScript/Node.js
```javascript
const NerafusAuth = require('@nerafus/auth-sdk');

const auth = new NerafusAuth({
  baseURL: 'http://localhost:3001/api/v1',
  apiKey: 'your-api-key'
});

// Register user
const user = await auth.register({
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  name: 'John Doe',
  role: 'freelancer'
});

// Login
const session = await auth.login('<EMAIL>', 'SecurePassword123!');

// Get current user
const currentUser = await auth.getCurrentUser();
```

### Python
```python
from nerafus_auth import AuthClient

auth = AuthClient(base_url='http://localhost:3001/api/v1')

# Register user
user = auth.register(
    email='<EMAIL>',
    password='SecurePassword123!',
    name='John Doe',
    role='freelancer'
)

# Login
session = auth.login('<EMAIL>', 'SecurePassword123!')
```

---

## Testing
Use the following test accounts for development:

**Freelancer Account:**
- Email: `<EMAIL>`
- Password: `TestPassword123!`

**Client Account:**
- Email: `<EMAIL>`
- Password: `TestPassword123!`

**Admin Account:**
- Email: `<EMAIL>`
- Password: `AdminPassword123!`

---

## Changelog

### v1.2.0 (2024-01-15)
- Added token refresh endpoint
- Improved error messages
- Added rate limiting

### v1.1.0 (2024-01-10)
- Added user profile fields
- Implemented email verification
- Added webhook support

### v1.0.0 (2024-01-01)
- Initial release
- Basic authentication endpoints
- JWT token support
