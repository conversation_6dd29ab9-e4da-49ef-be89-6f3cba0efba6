const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3006;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Payment Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Payment routes
app.get('/payments', (req, res) => {
  console.log('💳 Get payments request');
  res.json({
    success: true,
    message: 'Payments retrieved successfully',
    data: []
  });
});

app.post('/payments', (req, res) => {
  console.log('💰 Create payment request:', req.body);
  res.json({
    success: true,
    message: 'Payment processed successfully',
    data: { id: Date.now(), ...req.body }
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Payment Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
