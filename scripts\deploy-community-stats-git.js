#!/usr/bin/env node

/**
 * Deploy Community Service with Stats Endpoint using Git
 * Adds missing /api/stats endpoint to Community Service
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 Deploying Community Service with Stats Endpoint via Git...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

// Check if we're in the right directory
const projectRoot = path.resolve(__dirname, '..');
if (!fs.existsSync(path.join(projectRoot, 'package.json'))) {
  logError('Script must be run from the project root directory');
  process.exit(1);
}

// Check if git is available
try {
  execSync('git --version', { stdio: 'ignore' });
} catch (error) {
  logError('Git not found. Please install Git first.');
  process.exit(1);
}

// Check if we're in a git repository
try {
  execSync('git status', { stdio: 'ignore' });
} catch (error) {
  logError('Not in a git repository. Please initialize git first.');
  process.exit(1);
}

async function deployViaGit() {
  try {
    logStep('1', 'Preparing git deployment...');
    
    // Check current branch
    const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    logInfo(`Current branch: ${currentBranch}`);
    
    if (currentBranch !== 'main') {
      logWarning(`You are on branch '${currentBranch}'. Consider switching to 'main' for deployment.`);
    }
    
    logStep('2', 'Adding new files to git...');
    
    // Add all changes
    execSync('git add .', { stdio: 'inherit' });
    logSuccess('Files added to git');
    
    logStep('3', 'Committing changes...');
    
    // Commit changes
    const commitMessage = 'feat: Add /api/stats endpoint to Community Service\n\n- Add community statistics endpoint\n- Add user statistics endpoint\n- Update API documentation\n- Fix CORS configuration';
    execSync(`git commit -m "${commitMessage}"`, { stdio: 'inherit' });
    logSuccess('Changes committed');
    
    logStep('4', 'Pushing to remote repository...');
    
    // Push to remote
    execSync('git push origin main', { stdio: 'inherit' });
    logSuccess('Changes pushed to remote repository');
    
    return true;
    
  } catch (error) {
    logError(`Git deployment failed: ${error.message}`);
    return false;
  }
}

async function main() {
  log('🚀 VWork Community Service Stats Deployment via Git', 'bright');
  log('This script will deploy the Community Service with the new /api/stats endpoint using git push', 'blue');
  
  // Deploy via git
  const deploymentSuccess = await deployViaGit();
  
  if (!deploymentSuccess) {
    logError('Git deployment failed.');
    process.exit(1);
  }
  
  log('\n🎉 Community Service Stats Deployment Complete!', 'bright');
  log('\nChanges deployed:', 'cyan');
  log('• Added /api/stats endpoint for community statistics', 'green');
  log('• Added /api/stats/user/:userId endpoint for user statistics', 'green');
  log('• Updated API documentation to include stats endpoints', 'green');
  log('• Fixed CORS configuration for nerafus.com', 'green');
  
  log('\n📋 New Endpoints Available:', 'cyan');
  log('• GET /api/v1/community/stats - Community overview statistics', 'yellow');
  log('• GET /api/v1/community/stats/user/:userId - User-specific statistics', 'yellow');
  
  log('\n📊 Statistics Include:', 'cyan');
  log('• Total posts, comments, users, and likes', 'yellow');
  log('• Engagement metrics (avg upvotes, views, comments)', 'yellow');
  log('• Recent activity from last 7 days', 'yellow');
  log('• Top categories and top users', 'yellow');
  log('• User-specific stats and recent activity', 'yellow');
  
  log('\n📋 Next steps:', 'cyan');
  log('1. Wait 3-5 minutes for Render to detect changes and deploy', 'yellow');
  log('2. Monitor deployment at: https://dashboard.render.com', 'yellow');
  log('3. Test the community page at https://nerafus.com/#/community', 'yellow');
  log('4. Check that stats are loading correctly', 'yellow');
  
  log('\n🔍 To monitor deployment:', 'cyan');
  log('• Check Render dashboard: https://dashboard.render.com', 'yellow');
  log('• Monitor service logs in Render dashboard', 'yellow');
  log('• Test endpoint: https://nerafus-community-service.onrender.com/api/stats', 'yellow');
  
  log('\n🧪 To test the new endpoint locally:', 'cyan');
  log('curl http://localhost:3006/api/stats', 'yellow');
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:');
  logError(`Promise: ${promise}`);
  logError(`Reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError('Uncaught Exception:');
  logError(error.message);
  logError(error.stack);
  process.exit(1);
});

// Run the deployment
main().catch(error => {
  logError('Deployment failed:');
  logError(error.message);
  process.exit(1);
}); 