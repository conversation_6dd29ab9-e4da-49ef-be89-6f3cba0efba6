import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  MusicalNoteIcon, 
  StarIcon, 
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  SpeakerWaveIcon,
  MicrophoneIcon
} from '@heroicons/react/24/outline';

// Mock data cho freelancers chuy<PERSON>n ng<PERSON>nh Music & Audio
const mockMusicians = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    title: 'Music Producer & Composer',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 167,
    hourlyRate: 45,
    location: '<PERSON><PERSON> Chí Minh',
    skills: ['Music Production', 'Composition', 'Mixing', 'Mastering'],
    description: 'Nhà sản xuất âm nhạc chuyên nghiệp với 8+ năm kinh nghiệm. Đ<PERSON> sản xuất 200+ bài hát.',
    completedJobs: 234,
    responseTime: '2 giờ',
    availability: 'Sẵn sàng',
    genres: ['Pop', 'Rock', 'Electronic', 'Hip-Hop'],
    instruments: ['Piano', 'Guitar', 'Synthesizer'],
    software: ['Pro Tools', 'Logic Pro', 'Ableton Live'],
    portfolio: [
      'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop'
    ],
    plays: 125000,
    followers: 3400
  },
  {
    id: 2,
    name: 'Trần Thị Linh',
    title: 'Vocalist & Singer',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 198,
    hourlyRate: 35,
    location: 'Hà Nội',
    skills: ['Vocal Performance', 'Songwriting', 'Harmony', 'Voice Acting'],
    description: 'Ca sĩ chuyên nghiệp với giọng hát đa dạng. Có thể hát nhiều thể loại từ ballad đến pop.',
    completedJobs: 312,
    responseTime: '1 giờ',
    availability: 'Sẵn sàng',
    genres: ['Pop', 'Ballad', 'Jazz', 'R&B'],
    instruments: ['Vocals', 'Piano'],
    software: ['Auto-Tune', 'Melodyne'],
    portfolio: [
      'https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=300&h=200&fit=crop'
    ],
    plays: 89000,
    followers: 2100
  },
  {
    id: 3,
    name: 'Lê Minh Đức',
    title: 'Audio Engineer & Sound Designer',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 4.7,
    reviewCount: 134,
    hourlyRate: 40,
    location: 'Đà Nẵng',
    skills: ['Audio Engineering', 'Sound Design', 'Post Production', 'Foley'],
    description: 'Kỹ sư âm thanh chuyên nghiệp. Chuyên thiết kế âm thanh cho phim và game.',
    completedJobs: 189,
    responseTime: '3 giờ',
    availability: 'Bận',
    genres: ['Cinematic', 'Ambient', 'Electronic'],
    instruments: ['Synthesizer', 'Field Recording'],
    software: ['Pro Tools', 'Reaper', 'Wwise'],
    portfolio: [
      'https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=300&h=200&fit=crop'
    ],
    plays: 45600,
    followers: 1567
  },
  {
    id: 4,
    name: 'Phạm Thị Mai',
    title: 'Guitarist & Music Teacher',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 145,
    hourlyRate: 25,
    location: 'Hồ Chí Minh',
    skills: ['Guitar Performance', 'Music Theory', 'Teaching', 'Arrangement'],
    description: 'Guitarist chuyên nghiệp và giáo viên âm nhạc. Có thể chơi nhiều thể loại guitar.',
    completedJobs: 267,
    responseTime: '1.5 giờ',
    availability: 'Sẵn sàng',
    genres: ['Classical', 'Flamenco', 'Jazz', 'Rock'],
    instruments: ['Classical Guitar', 'Electric Guitar', 'Bass'],
    software: ['Guitar Pro', 'Sibelius'],
    portfolio: [
      'https://images.unsplash.com/photo-1510915361894-db8b60106cb1?w=300&h=200&fit=crop'
    ],
    plays: 67800,
    followers: 1890
  },
  {
    id: 5,
    name: 'Võ Văn Tùng',
    title: 'DJ & Electronic Music Producer',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 4.6,
    reviewCount: 89,
    hourlyRate: 50,
    location: 'Hà Nội',
    skills: ['DJing', 'Electronic Production', 'Remixing', 'Live Performance'],
    description: 'DJ và producer chuyên nhạc điện tử. Đã biểu diễn tại nhiều club và festival lớn.',
    completedJobs: 156,
    responseTime: '4 giờ',
    availability: 'Bận',
    genres: ['House', 'Techno', 'Trance', 'Dubstep'],
    instruments: ['DJ Controller', 'Synthesizer'],
    software: ['Serato', 'Traktor', 'Ableton Live'],
    portfolio: [
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop'
    ],
    plays: 234000,
    followers: 5600
  },
  {
    id: 6,
    name: 'Hoàng Thị Lan',
    title: 'Podcast Producer & Voice Over',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 112,
    hourlyRate: 30,
    location: 'Hồ Chí Minh',
    skills: ['Podcast Production', 'Voice Over', 'Audio Editing', 'Scriptwriting'],
    description: 'Chuyên gia sản xuất podcast và lồng tiếng. Giọng nói chuyên nghiệp và cuốn hút.',
    completedJobs: 298,
    responseTime: '1 giờ',
    availability: 'Sẵn sàng',
    genres: ['Spoken Word', 'Commercial', 'Educational'],
    instruments: ['Professional Microphone'],
    software: ['Audacity', 'Adobe Audition', 'Hindenburg'],
    portfolio: [
      'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=300&h=200&fit=crop'
    ],
    plays: 156000,
    followers: 2890
  }
];

const MusicPage = () => {
  const { t } = useLanguage();
  const [filteredMusicians, setFilteredMusicians] = useState(mockMusicians);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSkill, setSelectedSkill] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [availability, setAvailability] = useState('');

  // Get all unique skills
  const allSkills = [...new Set(mockMusicians.flatMap(musician => musician.skills))];

  // Filter function
  useEffect(() => {
    let filtered = mockMusicians;

    if (searchQuery) {
      filtered = filtered.filter(musician => 
        musician.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        musician.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        musician.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedSkill) {
      filtered = filtered.filter(musician => musician.skills.includes(selectedSkill));
    }

    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filtered = filtered.filter(musician => {
        if (max) {
          return musician.hourlyRate >= min && musician.hourlyRate <= max;
        } else {
          return musician.hourlyRate >= min;
        }
      });
    }

    if (availability) {
      filtered = filtered.filter(musician => musician.availability === availability);
    }

    setFilteredMusicians(filtered);
  }, [searchQuery, selectedSkill, priceRange, availability]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <div className="p-3 bg-pink-100 rounded-lg">
              <MusicalNoteIcon className="h-8 w-8 text-pink-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Music & Audio</h1>
              <p className="text-lg text-gray-600">Tìm kiếm các nghệ sĩ và chuyên gia âm nhạc tài năng</p>
            </div>
          </div>
          
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input
              type="text"
              placeholder="Tìm kiếm musician..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            />

            <select
              value={selectedSkill}
              onChange={(e) => setSelectedSkill(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            >
              <option value="">Tất cả kỹ năng</option>
              {allSkills.map(skill => (
                <option key={skill} value={skill}>{skill}</option>
              ))}
            </select>

            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            >
              <option value="">Tất cả mức giá</option>
              <option value="0-30">$0 - $30/giờ</option>
              <option value="30-40">$30 - $40/giờ</option>
              <option value="40-50">$40 - $50/giờ</option>
              <option value="50">$50+/giờ</option>
            </select>

            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Sẵn sàng">Sẵn sàng</option>
              <option value="Bận">Bận</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Tìm thấy <span className="font-semibold">{filteredMusicians.length}</span> musician phù hợp
          </p>
        </div>

        {/* Musician Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMusicians.map(musician => (
            <div key={musician.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
              {/* Portfolio Preview */}
              {musician.portfolio.length > 0 && (
                <div className="relative h-48 bg-gradient-to-br from-pink-400 to-purple-600">
                  <img
                    src={musician.portfolio[0]}
                    alt="Portfolio preview"
                    className="w-full h-full object-cover mix-blend-overlay"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                    <div className="text-center text-white">
                      <SpeakerWaveIcon className="h-12 w-12 mx-auto mb-2" />
                      <p className="text-sm font-medium">{musician.plays.toLocaleString()} plays</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="p-6">
                {/* Musician Header */}
                <div className="flex items-start space-x-4 mb-4">
                  <img
                    src={musician.avatar}
                    alt={musician.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{musician.name}</h3>
                    <p className="text-pink-600 font-medium">{musician.title}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <div className="flex items-center">
                        <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">{musician.rating}</span>
                      </div>
                      <span className="text-gray-400">•</span>
                      <span className="text-sm text-gray-600">{musician.reviewCount} đánh giá</span>
                    </div>
                  </div>
                </div>

                {/* Music Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-pink-50 rounded-lg">
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1">
                      <SpeakerWaveIcon className="h-4 w-4 text-pink-600" />
                      <span className="text-sm font-semibold text-pink-800">{(musician.plays / 1000).toFixed(0)}K</span>
                    </div>
                    <p className="text-xs text-gray-600">Lượt nghe</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-1">
                      <MicrophoneIcon className="h-4 w-4 text-pink-600" />
                      <span className="text-sm font-semibold text-pink-800">{musician.followers}</span>
                    </div>
                    <p className="text-xs text-gray-600">Followers</p>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{musician.description}</p>

                {/* Skills */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {musician.skills.slice(0, 3).map(skill => (
                      <span key={skill} className="px-2 py-1 bg-pink-100 text-pink-800 text-xs rounded-full">
                        {skill}
                      </span>
                    ))}
                    {musician.skills.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{musician.skills.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* Genres & Instruments */}
                <div className="mb-4 text-xs">
                  <p className="text-gray-500 mb-1">Thể loại: {musician.genres.slice(0, 3).join(', ')}</p>
                  <p className="text-gray-500">Nhạc cụ: {musician.instruments.join(', ')}</p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">{musician.location}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">{musician.responseTime}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">${musician.hourlyRate}/giờ</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`w-2 h-2 rounded-full ${musician.availability === 'Sẵn sàng' ? 'bg-green-400' : 'bg-red-400'}`}></span>
                    <span className="text-gray-600">{musician.availability}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <button className="flex-1 bg-pink-600 text-white py-2 px-4 rounded-lg hover:bg-pink-700 transition-colors duration-200 text-sm font-medium">
                    Liên hệ
                  </button>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
                    Nghe demo
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {filteredMusicians.length > 0 && (
          <div className="text-center mt-8">
            <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              Xem thêm musician
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MusicPage;
