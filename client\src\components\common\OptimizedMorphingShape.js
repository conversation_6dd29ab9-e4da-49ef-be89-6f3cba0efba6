/**
 * Optimized Morphing Shape Component
 * Provides high-performance morphing animations with automatic scaling
 */

import React, { useRef, useEffect, useCallback } from 'react';
import { gsap } from 'gsap';
import responsiveAutoscaling from '../../services/responsiveAutoscaling';

const OptimizedMorphingShape = ({
 size = 100,
 color = '#3B82F6',
 opacity = 0.1,
 duration = 8,
 className = '',
 style = {},
 enabled = true,
 complexity = 'medium' // 'low', 'medium', 'high'
}) => {
 const shapeRef = useRef(null);
 const animationRef = useRef(null);
 const isActiveRef = useRef(false);

 // Define morphing complexity levels
 const morphingPatterns = {
  low: [
   "50%",
   "60% 40%",
   "40% 60%",
   "50%"
  ],
  medium: [
   "50%",
   "60% 40% 30% 70%",
   "40% 60% 70% 30%",
   "70% 30% 40% 60%",
   "50%"
  ],
  high: [
   "50%",
   "60% 40% 30% 70% / 60% 30% 70% 40%",
   "40% 60% 70% 30% / 40% 70% 30% 60%",
   "70% 30% 40% 60% / 30% 60% 40% 70%",
   "30% 70% 60% 40% / 70% 40% 60% 30%",
   "50%"
  ]
 };

 // Get optimized morphing pattern based on performance
 const getOptimizedPattern = useCallback(() => {
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  
  // Map performance levels to complexity
  const complexityMap = {
   minimal: 'low',
   low: 'low',
   medium: 'medium',
   high: complexity
  };
  
  const selectedComplexity = complexityMap[performanceLevel];
  return morphingPatterns[selectedComplexity];
 }, [complexity]);

 // Get optimized animation settings
 const getOptimizedSettings = useCallback(() => {
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  const baseSettings = responsiveAutoscaling.getOptimizedGSAPSettings();
  
  const settingsMap = {
   minimal: {
    duration: duration * 2, // Slower for minimal performance
    ease: 'none',
    scale: { min: 0.9, max: 1.1 },
    rotation: { min: -10, max: 10 }
   },
   low: {
    duration: duration * 1.5,
    ease: baseSettings.ease,
    scale: { min: 0.8, max: 1.2 },
    rotation: { min: -30, max: 30 }
   },
   medium: {
    duration: duration,
    ease: baseSettings.ease,
    scale: { min: 0.7, max: 1.3 },
    rotation: { min: -90, max: 90 }
   },
   high: {
    duration: duration,
    ease: 'power2.inOut',
    scale: { min: 0.6, max: 1.4 },
    rotation: { min: -180, max: 180 }
   }
  };
  
  return settingsMap[performanceLevel];
 }, [duration]);

 // Create morphing animation
 const createMorphingAnimation = useCallback(() => {
  if (!shapeRef.current || !enabled || isActiveRef.current) return;
  
  isActiveRef.current = true;
  const pattern = getOptimizedPattern();
  const settings = getOptimizedSettings();
  
  if (pattern.length < 2) return;
  
  const timeline = gsap.timeline({ 
   repeat: -1, 
   yoyo: true,
   onComplete: () => {
    isActiveRef.current = false;
   }
  });
  
  // Create morphing sequence
  pattern.forEach((borderRadius, index) => {
   if (index === 0) return; // Skip first as it's the initial state
   
   const animationVars = {
    borderRadius,
    duration: settings.duration / (pattern.length - 1),
    ease: settings.ease
   };
   
   // Add scale animation for medium+ performance
   if (responsiveAutoscaling.currentPerformanceLevel !== 'minimal') {
    animationVars.scale = gsap.utils.random(settings.scale.min, settings.scale.max);
   }
   
   // Add rotation for high performance
   if (responsiveAutoscaling.currentPerformanceLevel === 'high') {
    animationVars.rotation = gsap.utils.random(settings.rotation.min, settings.rotation.max);
   }
   
   // Add random movement for high performance
   if (responsiveAutoscaling.currentPerformanceLevel === 'high') {
    animationVars.x = gsap.utils.random(-20, 20);
    animationVars.y = gsap.utils.random(-20, 20);
   }
   
   timeline.to(shapeRef.current, animationVars);
  });
  
  animationRef.current = timeline;
 }, [enabled, getOptimizedPattern, getOptimizedSettings]);

 // Cleanup animation
 const cleanup = useCallback(() => {
  isActiveRef.current = false;
  
  if (animationRef.current) {
   animationRef.current.kill();
   animationRef.current = null;
  }
 }, []);

 // Handle performance level changes
 useEffect(() => {
  const handlePerformanceChange = () => {
   cleanup();
   setTimeout(createMorphingAnimation, 100);
  };

  window.addEventListener('performanceLevelChange', handlePerformanceChange);
  
  return () => {
   window.removeEventListener('performanceLevelChange', handlePerformanceChange);
  };
 }, [cleanup, createMorphingAnimation]);

 // Initialize animation
 useEffect(() => {
  if (enabled && responsiveAutoscaling.shouldUseFullAnimations()) {
   createMorphingAnimation();
  }
  
  return cleanup;
 }, [enabled, createMorphingAnimation, cleanup]);

 // Handle visibility changes
 useEffect(() => {
  const handleVisibilityChange = () => {
   if (animationRef.current) {
    if (document.hidden) {
     animationRef.current.pause();
    } else {
     animationRef.current.resume();
    }
   }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  return () => {
   document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
 }, []);

 if (!enabled || !responsiveAutoscaling.shouldUseFullAnimations()) {
  return null;
 }

 const shapeStyle = {
  width: size,
  height: size,
  backgroundColor: color,
  opacity,
  borderRadius: '50%',
  position: 'absolute',
  willChange: 'transform, border-radius',
  transform: 'translate3d(0, 0, 0)',
  ...style
 };

 return (
  <div
   ref={shapeRef}
   className={`pointer-events-none ${className}`}
   style={shapeStyle}
  />
 );
};

// Memoized component to prevent unnecessary re-renders
export default React.memo(OptimizedMorphingShape);

// Export a group component for multiple shapes
export const OptimizedMorphingShapeGroup = React.memo(({
 count = 3,
 colors = ['#3B82F6', '#60A5FA', '#93C5FD'],
 sizes = [80, 100, 120],
 className = '',
 enabled = true
}) => {
 const containerRef = useRef(null);

 if (!enabled || !responsiveAutoscaling.shouldUseFullAnimations()) {
  return null;
 }

 return (
  <div ref={containerRef} className={`absolute inset-0 overflow-hidden ${className}`}>
   {Array.from({ length: count }).map((_, index) => (
    <OptimizedMorphingShape
     key={index}
     size={sizes[index % sizes.length]}
     color={colors[index % colors.length]}
     opacity={0.05 + (index * 0.02)}
     duration={6 + (index * 2)}
     complexity={index === 0 ? 'high' : index === 1 ? 'medium' : 'low'}
     style={{
      left: `${20 + (index * 25)}%`,
      top: `${10 + (index * 20)}%`,
      zIndex: -index - 1
     }}
     enabled={enabled}
    />
   ))}
  </div>
 );
});
