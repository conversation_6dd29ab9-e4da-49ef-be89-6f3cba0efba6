# Simple Azure Deployment Script
$azureCliPath = "C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin\az.cmd"

Write-Host "Creating Resource Group..." -ForegroundColor Green
& $azureCliPath group create --name vwork-rg --location southeastasia

Write-Host "Creating App Service Plan..." -ForegroundColor Green
& $azureCliPath appservice plan create --name vwork-plan --resource-group vwork-rg --sku F1 --is-linux

Write-Host "Creating Web App..." -ForegroundColor Green
& $azureCliPath webapp create --name vwork-community-service --resource-group vwork-rg --plan vwork-plan --runtime "NODE:20-lts"

Write-Host "Configuration completed!" -ForegroundColor Green 