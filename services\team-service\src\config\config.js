/**
 * Team Service Configuration
 * Centralized configuration management
 */

require('dotenv').config();

const config = {
  // Service Configuration
  SERVICE_NAME: 'Team Service',
  SERVICE_VERSION: '1.0.0',
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT) || 3007,
  SERVICE_HOST: process.env.SERVICE_HOST || 'localhost',

  // Database Configuration
  DATABASE_URL: process.env.DATABASE_URL,
  DB_HOST: process.env.DB_HOST || 'localhost',
  DB_PORT: parseInt(process.env.DB_PORT) || 5432,
  DB_NAME: process.env.DB_NAME || 'vwork_team_service',
  DB_USER: process.env.DB_USER || 'vwork_admin',
  DB_PASSWORD: process.env.DB_PASSWORD || 'VWork2024!',

  // Database Pool Settings
  DB_POOL_MAX: parseInt(process.env.DB_POOL_MAX) || 20,
  DB_POOL_MIN: parseInt(process.env.DB_POOL_MIN) || 2,
  DB_CONNECTION_TIMEOUT: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 5000,
  DB_IDLE_TIMEOUT: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,

  // Firebase Configuration
  FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
  FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
  FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY,

  // CORS Configuration
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS ? 
    process.env.ALLOWED_ORIGINS.split(',') : 
    ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080'],
  CORS_CREDENTIALS: process.env.CORS_CREDENTIALS === 'true',

  // Service URLs (for inter-service communication)
  USER_SERVICE_URL: process.env.USER_SERVICE_URL || 'http://localhost:3001',
  PROJECT_SERVICE_URL: process.env.PROJECT_SERVICE_URL || 'http://localhost:3002',
  CHAT_SERVICE_URL: process.env.CHAT_SERVICE_URL || 'http://localhost:3004',

  // Logging Configuration
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  LOG_FORMAT: process.env.LOG_FORMAT || 'json',

  // Security Configuration
  ENABLE_COMPRESSION: process.env.ENABLE_COMPRESSION === 'true',
  ENABLE_HELMET: process.env.ENABLE_HELMET !== 'false',
  TRUST_PROXY: process.env.TRUST_PROXY === 'true',

  // Team Service Specific Configuration
  MIN_FRIENDSHIP_DAYS: parseInt(process.env.MIN_FRIENDSHIP_DAYS) || 5,
  MIN_COMPLETED_PROJECTS: parseInt(process.env.MIN_COMPLETED_PROJECTS) || 10,
  MIN_RATING: parseFloat(process.env.MIN_RATING) || 4.8,
  MAX_TEAM_MEMBERS: parseInt(process.env.MAX_TEAM_MEMBERS) || 10,
  MIN_TEAM_MEMBERS: parseInt(process.env.MIN_TEAM_MEMBERS) || 3
};

// Validation
const requiredEnvVars = [
  'FIREBASE_PROJECT_ID',
  'FIREBASE_CLIENT_EMAIL',
  'FIREBASE_PRIVATE_KEY'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.warn('⚠️ Missing required environment variables:', missingVars);
  console.warn('Service may not function properly without these variables');
}

// Auto-generate service URLs if not provided
if (!config.USER_SERVICE_URL) {
  config.USER_SERVICE_URL = `http://${config.SERVICE_HOST}:3001`;
}

if (!config.PROJECT_SERVICE_URL) {
  config.PROJECT_SERVICE_URL = `http://${config.SERVICE_HOST}:3002`;
}

if (!config.CHAT_SERVICE_URL) {
  config.CHAT_SERVICE_URL = `http://${config.SERVICE_HOST}:3004`;
}

module.exports = config; 