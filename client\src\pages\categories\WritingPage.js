import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  PencilIcon, 
  StarIcon, 
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  LanguageIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

// Mock data cho freelancers chuyên ng<PERSON>nh <PERSON>iế<PERSON> lách & <PERSON>ch thuật
const mockWriters = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    title: 'Content Writer & Copywriter',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 156,
    hourlyRate: 18,
    location: 'Hồ Chí Minh',
    skills: ['Content Writing', 'Copywriting', 'SEO Writing', 'Blog Writing'],
    description: 'Chuyên gia viết nội dung với 5+ năm kinh nghiệm. Đã viết 1000+ bài blog và content marketing.',
    completedJobs: 234,
    responseTime: '1 giờ',
    availability: 'Sẵn sàng',
    languages: ['Tiếng Việt', 'English'],
    specialties: ['Technology', 'Business', 'Lifestyle'],
    wordsPerHour: 800,
    portfolio: [
      'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=300&h=200&fit=crop'
    ]
  },
  {
    id: 2,
    name: 'Trần Văn Minh',
    title: 'Technical Writer',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 89,
    hourlyRate: 25,
    location: 'Hà Nội',
    skills: ['Technical Writing', 'Documentation', 'API Documentation', 'User Manuals'],
    description: 'Chuyên gia viết tài liệu kỹ thuật. Kinh nghiệm với software documentation và API guides.',
    completedJobs: 145,
    responseTime: '2 giờ',
    availability: 'Sẵn sàng',
    languages: ['Tiếng Việt', 'English'],
    specialties: ['Software', 'Engineering', 'IT'],
    wordsPerHour: 600,
    portfolio: []
  },
  {
    id: 3,
    name: 'Lê Thị Mai',
    title: 'Translator (EN-VI)',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 203,
    hourlyRate: 22,
    location: 'Đà Nẵng',
    skills: ['Translation', 'Localization', 'Proofreading', 'Interpretation'],
    description: 'Dịch giả chuyên nghiệp Anh-Việt với 8+ năm kinh nghiệm. Chuyên dịch tài liệu kinh doanh.',
    completedJobs: 456,
    responseTime: '30 phút',
    availability: 'Bận',
    languages: ['Tiếng Việt', 'English', 'Français'],
    specialties: ['Business', 'Legal', 'Medical'],
    wordsPerHour: 1200,
    portfolio: []
  },
  {
    id: 4,
    name: 'Phạm Văn Đức',
    title: 'Creative Writer',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 4.7,
    reviewCount: 67,
    hourlyRate: 20,
    location: 'Hồ Chí Minh',
    skills: ['Creative Writing', 'Storytelling', 'Script Writing', 'Fiction'],
    description: 'Nhà văn sáng tạo chuyên viết kịch bản, truyện ngắn và nội dung giải trí.',
    completedJobs: 89,
    responseTime: '3 giờ',
    availability: 'Sẵn sàng',
    languages: ['Tiếng Việt', 'English'],
    specialties: ['Entertainment', 'Media', 'Publishing'],
    wordsPerHour: 500,
    portfolio: [
      'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=200&fit=crop'
    ]
  },
  {
    id: 5,
    name: 'Võ Thị Lan',
    title: 'Academic Writer',
    avatar: 'https://images.unsplash.com/photo-**********-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 134,
    hourlyRate: 30,
    location: 'Hà Nội',
    skills: ['Academic Writing', 'Research', 'Thesis Writing', 'Citations'],
    description: 'Chuyên gia viết học thuật với PhD. Hỗ trợ nghiên cứu và viết luận văn, báo cáo khoa học.',
    completedJobs: 78,
    responseTime: '4 giờ',
    availability: 'Sẵn sàng',
    languages: ['Tiếng Việt', 'English'],
    specialties: ['Education', 'Research', 'Science'],
    wordsPerHour: 400,
    portfolio: []
  },
  {
    id: 6,
    name: 'Hoàng Văn Tài',
    title: 'Journalist & Editor',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 4.6,
    reviewCount: 112,
    hourlyRate: 28,
    location: 'Hồ Chí Minh',
    skills: ['Journalism', 'Editing', 'Proofreading', 'News Writing'],
    description: 'Nhà báo và biên tập viên với 10+ năm kinh nghiệm. Chuyên viết tin tức và chỉnh sửa nội dung.',
    completedJobs: 345,
    responseTime: '1.5 giờ',
    availability: 'Bận',
    languages: ['Tiếng Việt', 'English'],
    specialties: ['News', 'Media', 'Current Affairs'],
    wordsPerHour: 700,
    portfolio: [
      'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=300&h=200&fit=crop'
    ]
  }
];

const WritingPage = () => {
  const { t } = useLanguage();
  const [filteredWriters, setFilteredWriters] = useState(mockWriters);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSkill, setSelectedSkill] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [availability, setAvailability] = useState('');

  // Get all unique skills
  const allSkills = [...new Set(mockWriters.flatMap(writer => writer.skills))];

  // Filter function
  useEffect(() => {
    let filtered = mockWriters;

    if (searchQuery) {
      filtered = filtered.filter(writer => 
        writer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        writer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        writer.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedSkill) {
      filtered = filtered.filter(writer => writer.skills.includes(selectedSkill));
    }

    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filtered = filtered.filter(writer => {
        if (max) {
          return writer.hourlyRate >= min && writer.hourlyRate <= max;
        } else {
          return writer.hourlyRate >= min;
        }
      });
    }

    if (availability) {
      filtered = filtered.filter(writer => writer.availability === availability);
    }

    setFilteredWriters(filtered);
  }, [searchQuery, selectedSkill, priceRange, availability]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <div className="p-3 bg-orange-100 rounded-lg">
              <PencilIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Viết lách & Dịch thuật</h1>
              <p className="text-lg text-gray-600">Tìm kiếm các chuyên gia viết lách và dịch thuật hàng đầu</p>
            </div>
          </div>
          
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input
              type="text"
              placeholder="Tìm kiếm writer..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />

            <select
              value={selectedSkill}
              onChange={(e) => setSelectedSkill(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="">Tất cả kỹ năng</option>
              {allSkills.map(skill => (
                <option key={skill} value={skill}>{skill}</option>
              ))}
            </select>

            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="">Tất cả mức giá</option>
              <option value="0-20">$0 - $20/giờ</option>
              <option value="20-30">$20 - $30/giờ</option>
              <option value="30-40">$30 - $40/giờ</option>
              <option value="40">$40+/giờ</option>
            </select>

            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Sẵn sàng">Sẵn sàng</option>
              <option value="Bận">Bận</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Tìm thấy <span className="font-semibold">{filteredWriters.length}</span> writer phù hợp
          </p>
        </div>

        {/* Writer Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWriters.map(writer => (
            <div key={writer.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
              {/* Writer Header */}
              <div className="flex items-start space-x-4 mb-4">
                <img
                  src={writer.avatar}
                  alt={writer.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{writer.name}</h3>
                  <p className="text-orange-600 font-medium">{writer.title}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="flex items-center">
                      <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">{writer.rating}</span>
                    </div>
                    <span className="text-gray-400">•</span>
                    <span className="text-sm text-gray-600">{writer.reviewCount} đánh giá</span>
                  </div>
                </div>
              </div>

              {/* Writing Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-orange-50 rounded-lg">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    <DocumentTextIcon className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-semibold text-orange-800">{writer.wordsPerHour}</span>
                  </div>
                  <p className="text-xs text-gray-600">Từ/giờ</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    <LanguageIcon className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-semibold text-orange-800">{writer.languages.length}</span>
                  </div>
                  <p className="text-xs text-gray-600">Ngôn ngữ</p>
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{writer.description}</p>

              {/* Skills */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {writer.skills.slice(0, 3).map(skill => (
                    <span key={skill} className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                      {skill}
                    </span>
                  ))}
                  {writer.skills.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      +{writer.skills.length - 3}
                    </span>
                  )}
                </div>
              </div>

              {/* Languages & Specialties */}
              <div className="mb-4 text-xs">
                <p className="text-gray-500 mb-1">Ngôn ngữ: {writer.languages.join(', ')}</p>
                <p className="text-gray-500">Chuyên môn: {writer.specialties.join(', ')}</p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center space-x-2">
                  <MapPinIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{writer.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{writer.responseTime}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">${writer.hourlyRate}/giờ</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full ${writer.availability === 'Sẵn sàng' ? 'bg-green-400' : 'bg-red-400'}`}></span>
                  <span className="text-gray-600">{writer.availability}</span>
                </div>
              </div>

              {/* Portfolio Preview */}
              {writer.portfolio.length > 0 && (
                <div className="mb-4">
                  <div className="flex space-x-2">
                    {writer.portfolio.slice(0, 2).map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt={`Portfolio ${index + 1}`}
                        className="w-16 h-12 rounded object-cover"
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors duration-200 text-sm font-medium">
                  Liên hệ
                </button>
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
                  Xem mẫu
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {filteredWriters.length > 0 && (
          <div className="text-center mt-8">
            <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              Xem thêm writer
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WritingPage;
