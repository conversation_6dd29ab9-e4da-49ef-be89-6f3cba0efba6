# Firebase Configuration (VWork Project)
REACT_APP_FIREBASE_API_KEY=AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
REACT_APP_FIREBASE_AUTH_DOMAIN=vwork-786c3.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=vwork-786c3
REACT_APP_FIREBASE_STORAGE_BUCKET=vwork-786c3.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=1050922072615
REACT_APP_FIREBASE_APP_ID=1:1050922072615:web:dfeae89c9ba66c77aeec02

# Environment
NODE_ENV=development

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8080/api/v1
REACT_APP_COMMUNITY_SERVICE_URL=http://localhost:3001
REACT_APP_TEAM_SERVICE_URL=http://localhost:3002
REACT_APP_USER_SERVICE_URL=http://localhost:3003
REACT_APP_PROJECT_SERVICE_URL=http://localhost:3004
REACT_APP_JOB_SERVICE_URL=http://localhost:3005
REACT_APP_CHAT_SERVICE_URL=http://localhost:3006
REACT_APP_PAYMENT_SERVICE_URL=http://localhost:3007

# Development Settings
REACT_APP_DEBUG_MODE=true
REACT_APP_ENABLE_LOGGING=true 