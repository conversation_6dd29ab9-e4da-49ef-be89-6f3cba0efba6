#!/usr/bin/env node

/**
 * Wake up Render services by making continuous requests
 * This helps resolve timeout issues on Render free tier
 */

const https = require('https');

// Render service URLs
const SERVICES = [
  {
    name: 'Auth Service',
    url: 'https://vwork-auth-service.onrender.com/health',
    wakeUpUrl: 'https://vwork-auth-service.onrender.com'
  },
  {
    name: 'User Service', 
    url: 'https://vwork-user-service.onrender.com/health',
    wakeUpUrl: 'https://vwork-user-service.onrender.com'
  },
  {
    name: 'Project Service',
    url: 'https://vwork-project-service.onrender.com/health', 
    wakeUpUrl: 'https://vwork-project-service.onrender.com'
  },
  {
    name: 'Job Service',
    url: 'https://vwork-job-service.onrender.com/health',
    wakeUpUrl: 'https://vwork-job-service.onrender.com'
  },
  {
    name: 'Chat Service',
    url: 'https://vwork-chat-service.onrender.com/health',
    wakeUpUrl: 'https://vwork-chat-service.onrender.com'
  },
  {
    name: 'API Gateway',
    url: 'https://vwork-api-gateway.onrender.com/api/v1/status',
    wakeUpUrl: 'https://vwork-api-gateway.onrender.com'
  }
];

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Make HTTPS request with retry
 */
function makeRequest(url, options = {}, retries = 3) {
  return new Promise((resolve, reject) => {
    const attempt = () => {
      const req = https.request(url, options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        });
      });

      req.on('error', (error) => {
        if (retries > 0) {
          log.warn(`Request failed, retrying... (${retries} attempts left)`);
          setTimeout(() => attempt(), 2000);
          retries--;
        } else {
          reject(error);
        }
      });

      req.setTimeout(15000, () => {
        req.destroy();
        if (retries > 0) {
          log.warn(`Request timeout, retrying... (${retries} attempts left)`);
          setTimeout(() => attempt(), 2000);
          retries--;
        } else {
          reject(new Error('Request timeout'));
        }
      });

      if (options.body) {
        req.write(options.body);
      }
      req.end();
    };

    attempt();
  });
}

/**
 * Wake up service by making multiple requests
 */
async function wakeUpService(service, attempts = 5) {
  log.info(`Waking up ${service.name}...`);
  
  for (let i = 1; i <= attempts; i++) {
    try {
      log.info(`Attempt ${i}/${attempts} - Calling ${service.name}...`);
      
      // Try wake up URL first
      const wakeResponse = await makeRequest(service.wakeUpUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'VWork-WakeUp-Script/1.0',
          'Cache-Control': 'no-cache'
        }
      });
      
      log.info(`${service.name} wake up response: ${wakeResponse.statusCode}`);
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Try health check
      const healthResponse = await makeRequest(service.url, {
        method: 'GET',
        headers: {
          'User-Agent': 'VWork-WakeUp-Script/1.0',
          'Cache-Control': 'no-cache'
        }
      });
      
      if (healthResponse.statusCode === 200) {
        log.success(`${service.name} is now awake and healthy!`);
        return true;
      } else if (healthResponse.statusCode === 429) {
        log.warn(`${service.name} is rate limited, but awake`);
        return true;
      } else {
        log.warn(`${service.name} responded with ${healthResponse.statusCode}`);
      }
      
    } catch (error) {
      log.warn(`Attempt ${i} failed: ${error.message}`);
    }
    
    // Wait between attempts
    if (i < attempts) {
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  log.error(`${service.name} failed to wake up after ${attempts} attempts`);
  return false;
}

/**
 * Wake up all services
 */
async function wakeUpAllServices() {
  log.info('🌅 Starting service wake up process...');
  
  const results = [];
  
  for (const service of SERVICES) {
    const wokeUp = await wakeUpService(service);
    results.push({ service: service.name, wokeUp });
    
    // Add delay between services
    await new Promise(resolve => setTimeout(resolve, 10000));
  }
  
  // Summary
  log.info('\n📊 Wake Up Summary:');
  const successful = results.filter(r => r.wokeUp);
  const failed = results.filter(r => !r.wokeUp);
  
  log.success(`${successful.length} services woke up successfully`);
  if (failed.length > 0) {
    log.error(`${failed.length} services failed to wake up`);
    failed.forEach(f => log.error(`  - ${f.service}`));
  }
  
  return results;
}

/**
 * Check all services health after wake up
 */
async function checkAllServicesHealth() {
  log.info('\n🏥 Checking all services health after wake up...');
  
  const results = [];
  
  for (const service of SERVICES) {
    try {
      log.info(`Checking ${service.name}...`);
      
      const response = await makeRequest(service.url, {
        method: 'GET',
        headers: {
          'User-Agent': 'VWork-Health-Check/1.0',
          'Cache-Control': 'no-cache'
        }
      });
      
      if (response.statusCode === 200) {
        log.success(`${service.name}: Healthy (${response.statusCode})`);
        results.push({ service: service.name, healthy: true, statusCode: response.statusCode });
      } else if (response.statusCode === 429) {
        log.warn(`${service.name}: Rate limited (${response.statusCode}) - but awake`);
        results.push({ service: service.name, healthy: true, statusCode: response.statusCode });
      } else {
        log.error(`${service.name}: Unhealthy (${response.statusCode})`);
        results.push({ service: service.name, healthy: false, statusCode: response.statusCode });
      }
      
    } catch (error) {
      log.error(`${service.name}: Unreachable (${error.message})`);
      results.push({ service: service.name, healthy: false, error: error.message });
    }
    
    // Add delay between checks
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  const healthy = results.filter(r => r.healthy);
  const unhealthy = results.filter(r => !r.healthy);
  
  log.info(`\n📊 Health Summary:`);
  log.success(`${healthy.length} services are healthy`);
  if (unhealthy.length > 0) {
    log.error(`${unhealthy.length} services are unhealthy`);
    unhealthy.forEach(u => log.error(`  - ${u.service} (${u.statusCode || u.error})`));
  }
  
  return results;
}

// Main execution
async function main() {
  try {
    const command = process.argv[2];
    
    switch (command) {
      case 'wake':
        await wakeUpAllServices();
        break;
      case 'health':
        await checkAllServicesHealth();
        break;
      case 'full':
        log.info('🌅 Full wake up and health check process...');
        await wakeUpAllServices();
        log.info('Waiting 30 seconds for services to fully start...');
        await new Promise(resolve => setTimeout(resolve, 30000));
        await checkAllServicesHealth();
        break;
      default:
        log.info('Usage: node wake-up-render-services.js [wake|health|full]');
        log.info('  wake   - Wake up all services');
        log.info('  health - Check health of all services');
        log.info('  full   - Wake up and then check health');
    }
    
  } catch (error) {
    log.error(`Script failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { wakeUpAllServices, checkAllServicesHealth }; 