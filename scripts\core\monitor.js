#!/usr/bin/env node

/**
 * Monitoring dashboard for NERAFUS platform
 * Provides health checks, metrics collection, and service monitoring
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..', '..');

// Service configurations
const SERVICES = {
  'api-gateway': { port: 8080, url: 'http://localhost:8080' },
  'user-service': { port: 3001, url: 'http://localhost:3001' },
  'project-service': { port: 3002, url: 'http://localhost:3002' },
  'job-service': { port: 3003, url: 'http://localhost:3003' },
  'chat-service': { port: 3004, url: 'http://localhost:3004' },
  'community-service': { port: 3005, url: 'http://localhost:3005' },
  'payment-service': { port: 3006, url: 'http://localhost:3006' },
  'team-service': { port: 3007, url: 'http://localhost:3007' }
};

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Check health of a single service
 */
async function checkServiceHealth(serviceName, config) {
  try {
    const response = await axios.get(`${config.url}/health`, {
      timeout: 5000,
      validateStatus: () => true // Accept all status codes
    });

    return {
      service: serviceName,
      status: response.status === 200 ? 'healthy' : 'unhealthy',
      statusCode: response.status,
      responseTime: response.headers['x-response-time'] || 'unknown',
      details: response.data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      service: serviceName,
      status: 'unreachable',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get metrics from a single service
 */
async function getServiceMetrics(serviceName, config) {
  try {
    const response = await axios.get(`${config.url}/metrics`, {
      timeout: 5000
    });

    return {
      service: serviceName,
      metrics: response.data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      service: serviceName,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Check health of all services
 */
async function checkAllServices() {
  log.info('Checking health of all services...');

  const healthChecks = Object.entries(SERVICES).map(([name, config]) =>
    checkServiceHealth(name, config)
  );

  const results = await Promise.all(healthChecks);
  
  // Display results
  console.log('\n📊 Service Health Status:');
  console.log('─'.repeat(80));
  
  let healthyCount = 0;
  let unhealthyCount = 0;
  let unreachableCount = 0;

  results.forEach(result => {
    const statusIcon = result.status === 'healthy' ? '✅' : 
                      result.status === 'unhealthy' ? '⚠️' : '❌';
    
    console.log(`${statusIcon} ${result.service.padEnd(20)} ${result.status.toUpperCase()}`);
    
    if (result.details) {
      console.log(`   Response Time: ${result.responseTime}`);
      if (result.details.checks) {
        Object.entries(result.details.checks).forEach(([check, status]) => {
          const checkIcon = status.status === 'healthy' ? '✓' : '✗';
          console.log(`   ${checkIcon} ${check}: ${status.status}`);
        });
      }
    }
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    console.log('');
    
    // Count statuses
    if (result.status === 'healthy') healthyCount++;
    else if (result.status === 'unhealthy') unhealthyCount++;
    else unreachableCount++;
  });

  console.log('─'.repeat(80));
  console.log(`Summary: ${healthyCount} healthy, ${unhealthyCount} unhealthy, ${unreachableCount} unreachable`);
  
  return results;
}

/**
 * Collect metrics from all services
 */
async function collectAllMetrics() {
  log.info('Collecting metrics from all services...');

  const metricRequests = Object.entries(SERVICES).map(([name, config]) =>
    getServiceMetrics(name, config)
  );

  const results = await Promise.all(metricRequests);
  
  // Aggregate metrics
  const aggregatedMetrics = {
    timestamp: new Date().toISOString(),
    services: {},
    summary: {
      totalRequests: 0,
      totalErrors: 0,
      averageResponseTime: 0,
      uptime: {}
    }
  };

  results.forEach(result => {
    if (result.metrics) {
      aggregatedMetrics.services[result.service] = result.metrics;
      
      // Aggregate summary data
      const metrics = result.metrics.metrics || {};
      
      // Sum up HTTP requests
      Object.entries(metrics).forEach(([key, metric]) => {
        if (key.includes('http_requests_total')) {
          aggregatedMetrics.summary.totalRequests += metric.value || 0;
        }
        if (key.includes('errors_total')) {
          aggregatedMetrics.summary.totalErrors += metric.value || 0;
        }
      });
      
      aggregatedMetrics.summary.uptime[result.service] = result.metrics.uptime || 0;
    }
  });

  return aggregatedMetrics;
}

/**
 * Generate monitoring report
 */
async function generateReport() {
  log.info('Generating monitoring report...');

  const healthResults = await checkAllServices();
  const metricsResults = await collectAllMetrics();

  const report = {
    timestamp: new Date().toISOString(),
    platform: 'NERAFUS',
    health: healthResults,
    metrics: metricsResults,
    summary: {
      totalServices: Object.keys(SERVICES).length,
      healthyServices: healthResults.filter(r => r.status === 'healthy').length,
      unhealthyServices: healthResults.filter(r => r.status === 'unhealthy').length,
      unreachableServices: healthResults.filter(r => r.status === 'unreachable').length
    }
  };

  // Save report to file
  const reportPath = path.join(ROOT_DIR, 'monitoring-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log.success(`Monitoring report saved to: ${reportPath}`);
  
  return report;
}

/**
 * Watch services continuously
 */
async function watchServices(interval = 30000) {
  log.info(`Starting continuous monitoring (interval: ${interval}ms)...`);
  
  const watch = async () => {
    console.clear();
    console.log('🔍 NERAFUS Platform Monitoring Dashboard');
    console.log(`Last updated: ${new Date().toLocaleString()}`);
    console.log('');
    
    await checkAllServices();
    
    setTimeout(watch, interval);
  };
  
  await watch();
}

/**
 * Test service endpoints
 */
async function testEndpoints() {
  log.info('Testing service endpoints...');

  const testResults = [];

  for (const [serviceName, config] of Object.entries(SERVICES)) {
    log.info(`Testing ${serviceName}...`);
    
    const tests = [
      { endpoint: '/health', expected: 200 },
      { endpoint: '/metrics', expected: 200 },
      { endpoint: '/api/v1/nonexistent', expected: 404 }
    ];

    for (const test of tests) {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${config.url}${test.endpoint}`, {
          timeout: 5000,
          validateStatus: () => true
        });
        const duration = Date.now() - startTime;

        const result = {
          service: serviceName,
          endpoint: test.endpoint,
          expectedStatus: test.expected,
          actualStatus: response.status,
          duration,
          success: response.status === test.expected,
          timestamp: new Date().toISOString()
        };

        testResults.push(result);

        const icon = result.success ? '✅' : '❌';
        console.log(`  ${icon} ${test.endpoint} - ${response.status} (${duration}ms)`);

      } catch (error) {
        const result = {
          service: serviceName,
          endpoint: test.endpoint,
          error: error.message,
          success: false,
          timestamp: new Date().toISOString()
        };

        testResults.push(result);
        console.log(`  ❌ ${test.endpoint} - Error: ${error.message}`);
      }
    }
  }

  return testResults;
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'health';
  const options = {
    interval: parseInt(args.find(arg => arg.startsWith('--interval='))?.split('=')[1]) || 30000,
    service: args.find(arg => arg.startsWith('--service='))?.split('=')[1],
    output: args.find(arg => arg.startsWith('--output='))?.split('=')[1]
  };

  try {
    switch (command) {
      case 'health':
        await checkAllServices();
        break;

      case 'metrics':
        const metrics = await collectAllMetrics();
        console.log(JSON.stringify(metrics, null, 2));
        break;

      case 'report':
        await generateReport();
        break;

      case 'watch':
        await watchServices(options.interval);
        break;

      case 'test':
        await testEndpoints();
        break;

      default:
        console.log(`
Usage: node monitor.js <command> [options]

Commands:
  health              Check health of all services
  metrics             Collect metrics from all services
  report              Generate comprehensive monitoring report
  watch               Continuously monitor services
  test                Test service endpoints

Options:
  --interval=<ms>     Monitoring interval for watch mode (default: 30000)
  --service=<name>    Target specific service
  --output=<file>     Output file for reports

Examples:
  node monitor.js health
  node monitor.js watch --interval=10000
  node monitor.js report --output=report.json
  node monitor.js test --service=auth-service
        `);
        process.exit(1);
    }
  } catch (error) {
    log.error(`Monitoring failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  checkServiceHealth,
  getServiceMetrics,
  checkAllServices,
  collectAllMetrics,
  generateReport,
  testEndpoints
};
