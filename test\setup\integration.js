/**
 * Integration test setup
 * Configures testing environment for end-to-end service integration tests
 */

const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  services: {
    'api-gateway': { port: 8080, url: 'http://localhost:8080' },
    'user-service': { port: 3001, url: 'http://localhost:3001' },
    'project-service': { port: 3002, url: 'http://localhost:3002' },
    'job-service': { port: 3003, url: 'http://localhost:3003' },
    'chat-service': { port: 3004, url: 'http://localhost:3004' },
    'community-service': { port: 3005, url: 'http://localhost:3005' },
    'payment-service': { port: 3006, url: 'http://localhost:3006' },
    'team-service': { port: 3007, url: 'http://localhost:3007' },
  },
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
};

// Service management
class TestServiceManager {
  constructor() {
    this.processes = new Map();
    this.isSetup = false;
  }

  async startService(serviceName, options = {}) {
    const servicePath = path.join(__dirname, '..', '..', 'services', serviceName);
    const config = TEST_CONFIG.services[serviceName];
    
    if (!config) {
      throw new Error(`Unknown service: ${serviceName}`);
    }

    console.log(`Starting ${serviceName} for integration tests...`);

    const child = spawn('npm', ['start'], {
      cwd: servicePath,
      stdio: ['ignore', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'test',
        PORT: config.port,
        LOG_LEVEL: 'error',
        // Test-specific environment variables
        DB_TYPE: 'firebase',
        FIREBASE_PROJECT_ID: 'test-project',
        JWT_SECRET: 'test-jwt-secret',
      }
    });

    // Handle output
    child.stdout.on('data', (data) => {
      if (process.env.DEBUG_INTEGRATION) {
        console.log(`[${serviceName}] ${data.toString().trim()}`);
      }
    });

    child.stderr.on('data', (data) => {
      if (process.env.DEBUG_INTEGRATION) {
        console.error(`[${serviceName}] ${data.toString().trim()}`);
      }
    });

    child.on('close', (code) => {
      if (code !== 0) {
        console.error(`${serviceName} exited with code ${code}`);
      }
      this.processes.delete(serviceName);
    });

    this.processes.set(serviceName, child);

    // Wait for service to be ready
    await this.waitForService(serviceName);
    
    console.log(`✅ ${serviceName} ready for testing`);
    return child;
  }

  async waitForService(serviceName, maxAttempts = 30) {
    const config = TEST_CONFIG.services[serviceName];
    const healthUrl = `${config.url}/health`;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await axios.get(healthUrl, { timeout: 2000 });
        if (response.status === 200) {
          return true;
        }
      } catch (error) {
        // Service not ready yet
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error(`Service ${serviceName} failed to start within ${maxAttempts} seconds`);
  }

  async stopService(serviceName) {
    const child = this.processes.get(serviceName);
    if (child) {
      child.kill('SIGTERM');
      this.processes.delete(serviceName);
      
      // Wait a bit for graceful shutdown
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  async stopAllServices() {
    const stopPromises = Array.from(this.processes.keys()).map(serviceName => 
      this.stopService(serviceName)
    );
    
    await Promise.all(stopPromises);
  }

  isServiceRunning(serviceName) {
    return this.processes.has(serviceName);
  }
}

// Global service manager instance
const serviceManager = new TestServiceManager();

// Test utilities
global.integrationTestUtils = {
  serviceManager,
  
  // HTTP client with default configuration
  createHttpClient: (baseURL, options = {}) => {
    return axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
      ...options,
    });
  },
  
  // Authentication helpers
  async authenticateUser(credentials = { email: '<EMAIL>', password: 'testpass123' }) {
    const authClient = global.integrationTestUtils.createHttpClient(TEST_CONFIG.services['auth-service'].url);
    
    try {
      const response = await authClient.post('/api/v1/auth/login', credentials);
      return response.data.data.token;
    } catch (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
  },
  
  // Create authenticated HTTP client
  async createAuthenticatedClient(baseURL, credentials) {
    const token = await global.integrationTestUtils.authenticateUser(credentials);
    
    return global.integrationTestUtils.createHttpClient(baseURL, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
  },
  
  // Database helpers
  async cleanupTestData() {
    // Clean up test data from database
    // This would typically involve calling cleanup endpoints or direct database operations
    console.log('Cleaning up test data...');
  },
  
  async seedTestData() {
    // Seed database with test data
    console.log('Seeding test data...');
  },
  
  // Service health checks
  async checkServiceHealth(serviceName) {
    const config = TEST_CONFIG.services[serviceName];
    if (!config) {
      throw new Error(`Unknown service: ${serviceName}`);
    }
    
    try {
      const response = await axios.get(`${config.url}/health`, { timeout: 5000 });
      return response.data;
    } catch (error) {
      throw new Error(`Health check failed for ${serviceName}: ${error.message}`);
    }
  },
  
  async checkAllServicesHealth() {
    const healthChecks = Object.keys(TEST_CONFIG.services).map(async serviceName => {
      try {
        const health = await global.integrationTestUtils.checkServiceHealth(serviceName);
        return { service: serviceName, status: 'healthy', health };
      } catch (error) {
        return { service: serviceName, status: 'unhealthy', error: error.message };
      }
    });
    
    return Promise.all(healthChecks);
  },
  
  // Retry helper
  async retry(fn, maxAttempts = TEST_CONFIG.retries, delay = TEST_CONFIG.retryDelay) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        if (attempt === maxAttempts) {
          throw error;
        }
        
        console.log(`Attempt ${attempt} failed, retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  },
  
  // Wait for condition
  async waitForCondition(conditionFn, timeout = 10000, interval = 500) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await conditionFn()) {
        return true;
      }
      
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  },
};

// Global setup - start required services
beforeAll(async () => {
  console.log('🚀 Starting integration test setup...');
  
  // Start core services needed for integration tests
  const coreServices = ['auth-service', 'user-service'];
  
  for (const serviceName of coreServices) {
    await serviceManager.startService(serviceName);
  }
  
  // Seed test data
  await global.integrationTestUtils.seedTestData();
  
  console.log('✅ Integration test setup complete');
}, 60000); // 60 second timeout for setup

// Global teardown - stop all services
afterAll(async () => {
  console.log('🧹 Cleaning up integration test environment...');
  
  // Clean up test data
  await global.integrationTestUtils.cleanupTestData();
  
  // Stop all services
  await serviceManager.stopAllServices();
  
  console.log('✅ Integration test cleanup complete');
}, 30000); // 30 second timeout for cleanup

// Clean up between tests
afterEach(async () => {
  // Clean up any test-specific data
  await global.integrationTestUtils.cleanupTestData();
});

// Handle test timeouts
jest.setTimeout(TEST_CONFIG.timeout);

module.exports = {
  TEST_CONFIG,
  serviceManager,
};
