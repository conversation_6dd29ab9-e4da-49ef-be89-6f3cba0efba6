import { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from './AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import { toast } from 'react-hot-toast';
import PropTypes from 'prop-types';

const OnboardingContext = createContext();

export const useOnboarding = () => {
 const context = useContext(OnboardingContext);
 if (!context) {
  throw new Error('useOnboarding must be used within an OnboardingProvider');
 }
 return context;
};

// Onboarding steps configuration
const ONBOARDING_STEPS = {
 WELCOME: 'welcome',
 USER_TYPE: 'user_type',
 BASIC_INFO: 'basic_info',
 PROFILE_DETAILS: 'profile_details',
 SKILLS_SETUP: 'skills_setup',
 PREFERENCES: 'preferences',
 // Client specific steps
 CLIENT_PURPOSE: 'client_purpose',
 CLIENT_EXPERIENCE: 'client_experience',
 CLIENT_NEEDS: 'client_needs',
 COMPLETE: 'complete'
};

const FREELANCER_STEPS = [
 ONBOARDING_STEPS.WELCOME,
 ONBOARDING_STEPS.BASIC_INFO,
 ONBOARDING_STEPS.PROFILE_DETAILS,
 ONBOARDING_STEPS.SKILLS_SETUP,
 ONBOARDING_STEPS.PREFERENCES,
 ONBOARDING_STEPS.COMPLETE
];

const CLIENT_STEPS = [
 ONBOARDING_STEPS.WELCOME,
 ONBOARDING_STEPS.BASIC_INFO,
 ONBOARDING_STEPS.CLIENT_PURPOSE,
 ONBOARDING_STEPS.CLIENT_EXPERIENCE,
 ONBOARDING_STEPS.CLIENT_NEEDS,
 ONBOARDING_STEPS.PREFERENCES,
 ONBOARDING_STEPS.COMPLETE
];

export const OnboardingProvider = ({ children }) => {
 console.log('🔄 OnboardingProvider initializing...');
 const { user, updateUserProfile } = useAuth();
 const [currentStep, setCurrentStep] = useState(ONBOARDING_STEPS.WELCOME);
 const [onboardingData, setOnboardingData] = useState({});
 const [isOnboardingRequired, setIsOnboardingRequired] = useState(false);
 const [loading, setLoading] = useState(false);

 // Define checkOnboardingStatus first
 const checkOnboardingStatus = useCallback(async () => {
  console.log('🔄 checkOnboardingStatus called with user:', user?.email);
  try {
   // Check if profile is complete
   if (user?.profile?.isComplete) {
    console.log('🔄 Profile is complete, setting onboarding to false');
    setIsOnboardingRequired(false);
    return;
   }

   // Check if this is first login after email verification
   if (!user?.uid) {
    console.log('🔄 No user UID, skipping check');
    return;
   }

   const userDoc = await getDoc(doc(db, 'users', user.uid));
   if (userDoc.exists()) {
    const userData = userDoc.data();
    console.log('🔄 User document exists, profile complete:', userData.profile?.isComplete);
    
    // If profile is not complete, require onboarding
    if (!userData.profile?.isComplete) {
     console.log('🔄 Profile not complete, requiring onboarding');
     setIsOnboardingRequired(true);
     setCurrentStep(ONBOARDING_STEPS.WELCOME);
    }
   } else {
    // New user, definitely needs onboarding
    console.log('🔄 New user, requiring onboarding');
    setIsOnboardingRequired(true);
    setCurrentStep(ONBOARDING_STEPS.WELCOME);
   }
  } catch (error) {
   console.error('Error checking onboarding status:', error);
  }
 }, [user]);

 // Debug loading state changes
 useEffect(() => {
  console.log('🔄 Onboarding loading state changed:', loading);
 }, [loading]);

 // Check if user needs onboarding
 useEffect(() => {
  console.log('🔄 useEffect triggered with user:', user?.email, 'emailVerified:', user?.emailVerified);
  if (user && user.emailVerified && checkOnboardingStatus) {
   console.log('🔄 Calling checkOnboardingStatus...');
   checkOnboardingStatus();
  }
 }, [user, checkOnboardingStatus]);

 const getStepsForUserType = (userType) => {
  return userType === 'freelancer' ? FREELANCER_STEPS : CLIENT_STEPS;
 };

 const getCurrentStepIndex = useCallback(() => {
  const steps = getStepsForUserType(user?.userType || 'freelancer');
  return steps.indexOf(currentStep);
 }, [user?.userType, currentStep]);

 const getTotalSteps = useCallback(() => {
  const steps = getStepsForUserType(user?.userType || 'freelancer');
  return steps.length;
 }, [user?.userType]);

 const goToNextStep = useCallback(() => {
  console.log('🔄 Going to next step from:', currentStep);
  const steps = getStepsForUserType(user?.userType || 'freelancer');
  const currentIndex = steps.indexOf(currentStep);
  
  console.log('🔄 Current index:', currentIndex, 'Total steps:', steps.length);
  
  if (currentIndex < steps.length - 1) {
   const nextStep = steps[currentIndex + 1];
   console.log('🔄 Setting next step to:', nextStep);
   setCurrentStep(nextStep);
  } else {
   console.log('🔄 Already at last step');
  }
 }, [currentStep, user?.userType]);

 const goToPreviousStep = useCallback(() => {
  const steps = getStepsForUserType(user?.userType || 'freelancer');
  const currentIndex = steps.indexOf(currentStep);
  
  if (currentIndex > 0) {
   setCurrentStep(steps[currentIndex - 1]);
  }
 }, [currentStep, user?.userType]);

 const updateOnboardingData = useCallback((stepData) => {
  console.log('🔄 Updating onboarding data:', stepData);
  setOnboardingData(prev => {
   const newData = {
    ...prev,
    ...stepData
   };
   console.log('🔄 New onboarding data:', newData);
   return newData;
  });
 }, []);

 const completeOnboarding = useCallback(async () => {
  try {
   setLoading(true);

   console.log('🔄 Completing onboarding with data:', onboardingData);

   // Ensure we have all required fields from ProfileSetupPage
   const profileFields = {
    // Fields from ProfileSetupPage
    skillLevel: onboardingData.profile?.skillLevel || '',
    workExperience: onboardingData.profile?.workExperience || '',
    yearsOfExperience: onboardingData.profile?.yearsOfExperience || '',
    major: onboardingData.profile?.major || '',
    bio: onboardingData.profile?.bio || '',
    skills: onboardingData.profile?.skills || [],

    // Fields from OnboardingFlow
    hourlyRate: onboardingData.profile?.hourlyRate || 0,
    availability: onboardingData.profile?.availability || 'available',
    avatar: onboardingData.profile?.avatar || user?.photoURL || '',
    experience: onboardingData.profile?.experience || '',
    education: onboardingData.profile?.education || '',
    languages: onboardingData.profile?.languages || ['Vietnamese', 'English'],

    // Client specific fields
    clientPurpose: onboardingData.clientPurpose || '',
    clientExperience: onboardingData.clientExperience || '',
    clientNeeds: onboardingData.clientNeeds || [],

    // Mark as complete
    isComplete: true,
    completedAt: new Date().toISOString(),
   };

   // Merge onboarding data with user profile
   const profileData = {
    profile: {
     ...user.profile,
     ...profileFields
    },
    preferences: {
     ...user.preferences,
     ...onboardingData.preferences
    },
    updatedAt: new Date().toISOString(),
    onboardingCompletedAt: new Date().toISOString()
   };

   console.log('🚀 Submitting profile data to backend:', profileData);

   // Update user profile
   const result = await updateUserProfile(profileData);

   if (!result.success) {
    throw new Error(result.error || 'Failed to update profile');
   }

   // Mark onboarding as complete
   setIsOnboardingRequired(false);
   setCurrentStep(ONBOARDING_STEPS.COMPLETE);

   // Clear backup userType from localStorage since onboarding is complete
   localStorage.removeItem('pending_user_type');

   toast.success('Chào mừng bạn đến với VWork! Hồ sơ của bạn đã được thiết lập thành công.');

   return { success: true };
  } catch (error) {
   console.error('Error completing onboarding:', error);
   toast.error('Có lỗi xảy ra khi hoàn tất thiết lập. Vui lòng thử lại.');
   return { success: false, error: error.message };
  } finally {
   setLoading(false);
  }
 }, [onboardingData, user, updateUserProfile]);

 const skipOnboarding = useCallback(async () => {
  try {
   setLoading(true);

   // Mark profile as complete with minimal data
   const profileData = {
    profile: {
     ...user.profile,
     isComplete: true,
     completedAt: new Date().toISOString(),

     // Ensure minimal required fields are set
     skillLevel: user.profile?.skillLevel || 'intermediate',
     workExperience: user.profile?.workExperience || '1-3',
     bio: user.profile?.bio || 'Profile will be completed later.',
     skills: user.profile?.skills || [],
     hourlyRate: user.profile?.hourlyRate || (user.userType === 'freelancer' ? 25 : null),
     availability: user.profile?.availability || (user.userType === 'freelancer' ? 'available' : null),
    },
    updatedAt: new Date().toISOString(),
    onboardingSkippedAt: new Date().toISOString()
   };

   console.log('🔄 Skipping onboarding with minimal data:', profileData);

   const result = await updateUserProfile(profileData);

   if (!result.success) {
    throw new Error(result.error || 'Failed to update profile');
   }

   setIsOnboardingRequired(false);

   toast.success('Bạn có thể hoàn tất hồ sơ sau trong phần Cài đặt.');

   return { success: true };
  } catch (error) {
   console.error('Error skipping onboarding:', error);
   toast.error('Có lỗi xảy ra. Vui lòng thử lại.');
   return { success: false, error: error.message };
  } finally {
   setLoading(false);
  }
 }, [user, updateUserProfile]);

 const value = useMemo(() => ({
  // State
  currentStep,
  onboardingData,
  isOnboardingRequired,
  loading,
  
  // Step management
  getCurrentStepIndex,
  getTotalSteps,
  goToNextStep,
  goToPreviousStep,
  
  // Data management
  updateOnboardingData,
  completeOnboarding,
  skipOnboarding,
  
  // Constants
  ONBOARDING_STEPS,
  FREELANCER_STEPS,
  CLIENT_STEPS
 }), [
  currentStep,
  onboardingData,
  isOnboardingRequired,
  loading,
  getCurrentStepIndex,
  getTotalSteps,
  goToNextStep,
  goToPreviousStep,
  updateOnboardingData,
  completeOnboarding,
  skipOnboarding
 ]);

 return (
  <OnboardingContext.Provider value={value}>
   {children}
  </OnboardingContext.Provider>
 );
};

OnboardingProvider.propTypes = {
 children: PropTypes.node.isRequired,
};
