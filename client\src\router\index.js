/**
 * Optimized routing system with code splitting and preloading
 */

import { useState } from 'react';
import { createAsyncRoute } from '../utils/performance';

// Create async routes with automatic code splitting
export const routes = {
 // Public routes
 home: createAsyncRoute(() => import('../pages/HomePage')),
 login: createAsyncRoute(() => import('../components/apple/pages/AppleAuthPage')),
 forgotPassword: createAsyncRoute(() => import('../pages/ForgotPasswordPage')),
 emailVerification: createAsyncRoute(() => import('../pages/EmailVerificationPage')),
 passwordReset: createAsyncRoute(() => import('../pages/PasswordResetPage')),
 support: createAsyncRoute(() => import('../pages/SupportPage')),
 notFound: createAsyncRoute(() => import('../pages/NotFoundPage')),
 
 // Protected routes
 dashboard: createAsyncRoute(() => import('../components/apple/pages/AppleDashboard')),
 projects: createAsyncRoute(() => import('../components/apple/pages/AppleProjectsPage')),
 projectDetail: createAsyncRoute(() => import('../pages/ProjectDetailPage')),
 freelancers: createAsyncRoute(() => import('../components/apple/pages/AppleFreelancersPage')),
 freelancerProfile: createAsyncRoute(() => import('../pages/FreelancerProfilePage')),
 jobs: createAsyncRoute(() => import('../components/apple/pages/AppleJobsPage')),
 jobCreate: createAsyncRoute(() => import('../components/apple/pages/AppleJobCreatePage')),
 jobDetail: createAsyncRoute(() => import('../components/apple/pages/AppleJobDetailPage')),
 jobApply: createAsyncRoute(() => import('../components/apple/pages/AppleJobApplyPage')),
 contests: createAsyncRoute(() => import('../components/apple/pages/AppleContestsPage')),
 community: createAsyncRoute(() => import('../components/apple/pages/AppleCommunityPage')),
 team: createAsyncRoute(() => import('../components/apple/pages/AppleTeamPage')),
 messages: createAsyncRoute(() => import('../pages/MessagesPage')),
 settings: createAsyncRoute(() => import('../pages/SettingsPage')),
 
 // Onboarding
 onboarding: createAsyncRoute(() => import('../components/onboarding/OnboardingFlow')),
 
 // Success pages
 loginSuccess: createAsyncRoute(() => import('../pages/LoginSuccessPage')),
 simpleLoginSuccess: createAsyncRoute(() => import('../pages/SimpleLoginSuccess')),
 emailVerificationCheck: createAsyncRoute(() => import('../pages/EmailVerificationCheck')),
 
 // Test/Demo pages
 locationDropdownTest: createAsyncRoute(() => import('../components/demo/LocationDropdownTest'))
};

/**
 * Route configuration with metadata
 */
export const routeConfig = [
 // Public routes
 {
  path: '/',
  component: routes.home.Component,
  preload: routes.home.preload,
  public: true,
  title: 'NERAFUS - New Era For Us',
  description: 'Connect with top freelancers and find your next project'
 },
 {
  path: '/auth',
  component: routes.login.Component,
  preload: routes.login.preload,
  public: true,
  title: 'Login - NERAFUS',
  description: 'Sign in to your NERAFUS account'
 },
 {
  path: '/forgot-password',
  component: routes.forgotPassword.Component,
  preload: routes.forgotPassword.preload,
  public: true,
  title: 'Forgot Password - NERAFUS'
 },
 {
  path: '/email-verification',
  component: routes.emailVerification.Component,
  preload: routes.emailVerification.preload,
  public: true,
  title: 'Email Verification - NERAFUS'
 },
 {
  path: '/password-reset',
  component: routes.passwordReset.Component,
  preload: routes.passwordReset.preload,
  public: true,
  title: 'Password Reset - NERAFUS'
 },
 {
  path: '/support',
  component: routes.support.Component,
  preload: routes.support.preload,
  public: true,
  title: 'Support - NERAFUS'
 },
 
 // Protected routes
 {
  path: '/dashboard',
  component: routes.dashboard.Component,
  preload: routes.dashboard.preload,
  protected: true,
  title: 'Dashboard - NERAFUS',
  description: 'Your personalized dashboard'
 },
 {
  path: '/projects',
  component: routes.projects.Component,
  preload: routes.projects.preload,
  protected: true,
  title: 'Projects - NERAFUS'
 },
 {
  path: '/projects/:id',
  component: routes.projectDetail.Component,
  preload: routes.projectDetail.preload,
  protected: true,
  title: 'Project Details - NERAFUS'
 },
 {
  path: '/freelancers',
  component: routes.freelancers.Component,
  preload: routes.freelancers.preload,
  protected: true,
  title: 'Freelancers - NERAFUS'
 },
 {
  path: '/freelancers/:id',
  component: routes.freelancerProfile.Component,
  preload: routes.freelancerProfile.preload,
  protected: true,
  title: 'Freelancer Profile - NERAFUS'
 },
 {
  path: '/jobs',
  component: routes.jobs.Component,
  preload: routes.jobs.preload,
  protected: true,
  title: 'Jobs - NERAFUS'
 },
 {
  path: '/jobs/create',
  component: routes.jobCreate.Component,
  preload: routes.jobCreate.preload,
  protected: true,
  title: 'Create Job - NERAFUS'
 },
 {
  path: '/jobs/:id',
  component: routes.jobDetail.Component,
  preload: routes.jobDetail.preload,
  protected: true,
  title: 'Job Details - NERAFUS'
 },
 {
  path: '/jobs/:id/apply',
  component: routes.jobApply.Component,
  preload: routes.jobApply.preload,
  protected: true,
  title: 'Apply for Job - NERAFUS'
 },
 {
  path: '/contests',
  component: routes.contests.Component,
  preload: routes.contests.preload,
  protected: true,
  title: 'Contests - NERAFUS'
 },
 {
  path: '/community',
  component: routes.community.Component,
  preload: routes.community.preload,
  protected: true,
  title: 'Community - NERAFUS'
 },
 {
  path: '/team',
  component: routes.team.Component,
  preload: routes.team.preload,
  protected: true,
  title: 'Freelancer Team - NERAFUS',
  description: 'Kết nối và tạo team freelancer chuyên nghiệp'
 },
 {
  path: '/messages',
  component: routes.messages.Component,
  preload: routes.messages.preload,
  protected: true,
  title: 'Messages - NERAFUS'
 },
 {
  path: '/settings',
  component: routes.settings.Component,
  preload: routes.settings.preload,
  protected: true,
  title: 'Settings - NERAFUS'
 },
 
 // Onboarding
 {
  path: '/onboarding',
  component: routes.onboarding.Component,
  preload: routes.onboarding.preload,
  protected: true,
  title: 'Welcome to NERAFUS'
 },
 
 // Success pages
 {
  path: '/login-success',
  component: routes.loginSuccess.Component,
  preload: routes.loginSuccess.preload,
  protected: true,
  title: 'Welcome Back - NERAFUS'
 },
 {
  path: '/simple-login-success',
  component: routes.simpleLoginSuccess.Component,
  preload: routes.simpleLoginSuccess.preload,
  protected: true,
  title: 'Login Successful - NERAFUS'
 },
 {
  path: '/email-verification-check',
  component: routes.emailVerificationCheck.Component,
  preload: routes.emailVerificationCheck.preload,
  public: true,
  title: 'Email Verification - NERAFUS'
 },
 {
  path: '/test/location-dropdown',
  component: routes.locationDropdownTest.Component,
  preload: routes.locationDropdownTest.preload,
  protected: true,
  title: 'Location Dropdown Test - NERAFUS'
 },
 
 // 404 route (must be last)
 {
  path: '*',
  component: routes.notFound.Component,
  preload: routes.notFound.preload,
  public: true,
  title: 'Page Not Found - NERAFUS'
 }
];

/**
 * Route preloading utility
 */
export const preloadRoute = (routeName) => {
 if (routes[routeName]?.preload) {
  routes[routeName].preload();
 }
};

/**
 * Preload multiple routes
 */
export const preloadRoutes = (routeNames) => {
 routeNames.forEach(preloadRoute);
};

/**
 * Get route by path
 */
export const getRouteByPath = (path) => {
 return routeConfig.find(route => route.path === path);
};

/**
 * Update document title and meta tags
 */
export const updatePageMeta = (route, params = {}) => {
 if (route.title) {
  document.title = route.title;
 }
 
 if (route.description) {
  const metaDescription = document.querySelector('meta[name="description"]');
  if (metaDescription) {
   metaDescription.setAttribute('content', route.description);
  }
 }
 
 // Update Open Graph tags
 const ogTitle = document.querySelector('meta[property="og:title"]');
 const ogDescription = document.querySelector('meta[property="og:description"]');
 
 if (ogTitle && route.title) {
  ogTitle.setAttribute('content', route.title);
 }
 
 if (ogDescription && route.description) {
  ogDescription.setAttribute('content', route.description);
 }
};

/**
 * Route transition hook
 */
export const useRouteTransition = () => {
 const [isTransitioning, setIsTransitioning] = useState(false);
 
 const startTransition = () => setIsTransitioning(true);
 const endTransition = () => setIsTransitioning(false);
 
 return {
  isTransitioning,
  startTransition,
  endTransition
 };
};
