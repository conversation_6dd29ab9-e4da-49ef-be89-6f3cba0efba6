#!/usr/bin/env node

/**
 * Deploy Community Service with Configuration Check
 */

const { execSync } = require('child_process');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logSection = (title) => {
  console.log('\n' + '='.repeat(60));
  log(title, 'bright');
  console.log('='.repeat(60));
};

async function deployCommunityService() {
  try {
    logSection('🚀 Deploying Community Service');
    
    // Change to community service directory
    const serviceDir = path.join(__dirname, '..');
    process.chdir(serviceDir);
    
    log('📁 Working directory:', process.cwd());
    
    // Check if we're in the right directory
    if (!require('fs').existsSync('package.json')) {
      throw new Error('package.json not found. Are you in the community-service directory?');
    }
    
    // Check configuration first
    log('\n🔧 Checking configuration...');
    try {
      const configCheck = execSync('npm run config:check', { encoding: 'utf8' });
      log('✅ Configuration check passed', 'green');
      console.log(configCheck);
    } catch (error) {
      log('❌ Configuration check failed', 'red');
      console.log(error.stdout || error.message);
      return;
    }
    
    // Deploy to Render
    log('\n🚀 Deploying to Render...');
    log('This will trigger a new deployment on Render');
    log('Check Render dashboard for deployment status');
    
    // Note: Render auto-deploys on git push
    log('\n📋 Next steps:');
    log('1. Commit and push changes to trigger deployment');
    log('2. Check Render dashboard for deployment status');
    log('3. Monitor logs for any errors');
    log('4. Test endpoints after deployment');
    
    log('\n🔗 Service URLs:');
    log('- Production: https://vwork-community-service.onrender.com');
    log('- Health Check: https://vwork-community-service.onrender.com/health');
    log('- API Info: https://vwork-community-service.onrender.com/api');
    
    log('\n📊 Test Commands:');
    log('curl -s https://vwork-community-service.onrender.com/health');
    log('curl -s https://vwork-community-service.onrender.com/api');
    log('curl -s "https://vwork-community-service.onrender.com/api/posts?limit=1"');
    
  } catch (error) {
    log(`❌ Deployment failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  deployCommunityService();
}

module.exports = { deployCommunityService }; 