﻿Tính năng Freelancer Team của NERAFUS


1. <PERSON><PERSON> tả 
* Tên gọi: Nerafus team ( cân nhắc nghĩ ra cái tên nào cho hay hay)


* Tính năng: Kết nối những Freelancer hoạt động theo team với những dự án có quy mô lớn và đòi hỏi tính chuyên môn cao. Nâng cao tinh thần làm việc của Freelancer và giảm bớt gánh nặng, stress với những dự án lớn. Nơi khách hàng tin tưởng giao phó dự án lớn 


* Slogan: Hợp tác và phát triển ( cân nhắc nghĩ ra thêm cái nào hay hay để thiết kế cái giao diện cho wow)


* Tầm nhìn: Với những dự án lớn và đòi hỏi tính chuyên môn cao ví dụ như: <PERSON><PERSON><PERSON> triển ứng dụng điện tho<PERSON><PERSON> thì cần( frontend dev, backend dev, ui ux design) để tối ưu tốt ứng dụng. Thay vì tìm kiếm riêng lẻ từng Freelancer thì freelancer team của nerafus sẽ kết nối các freelancer tài năng lại với nhau theo hướng chủ động từ phía freelancer ( điều này giúp các thành viên trong team hiểu rõ năng lực và tính cách của nhau để cùng nhau làm việc nhóm một cách hiệu quả). Freelancer team không những là một cách giúp khách hàng giải quyết vấn đề mà còn là giá trị để giữ chân các freelancer ở lại với nền tảng. Việc xây dựng Freelancer team sẽ định hình lại cấu trúc nhân sự của doanh nghiệp, thay vì outsource riêng lẻ từng cá nhân làm dự án thì định hướng phát triển của Freelancer team sẽ là thay thế 1 phần bộ phận nhân sự của doanh nghiệp. Dẫn đến việc giúp doanh nghiệp cắt giảm chi phí nhân sự nhưng vẫn hoạt động hiểu quả qua mô hình Freelancer team này.


* Một Freelancer team gồm có tối thiểu 3 thành viên và tối đa 10 thành viên ( cân nhắc chỉnh sửa lại thành viên tối đa để tối ưu hệ thống giai đoạn đầu)


2. Điều kiện cần:
2.1 Tính năng kết bạn 
* Thêm vào ID freelancer cho từng hồ sơ của freelancer, nút kết bạn ở hồ sơ → Từ đó freelancer có thể kết bạn lẫn nhau thông qua cộng đồng, danh sách freelancer ở freelancer team.
* ID freelancer được đặt cạnh tên của freelancer, Nút kết bạn sẽ đặt phía dưới cùng của hồ sơ 
* Tính năng addfriend qua ID: Trong dấu tròn chỗ chứa con AI Chat bot thêm vào mục danh sách bạn bè, khi bấm vào đó thì sẽ hiện danh sách bạn bè là những freelancer và khi bấm vào từng cái tên thì sẽ hiện hồ sơ của họ. Tiếp đến là mục kết bạn. Tại đây sẽ thiết kế thanh nhập ID sau đó là nút ấn kết bạn, sau khi nhập ID và nhấn nút thì sẽ hiện gửi kết bạn thành công 
  

* Vậy là sẽ có 2 cách kết bạn với freelancer: Kết bạn thông qua hồ sơ trên community hoặc trong danh sách freelancer team của nền tảng và kết bạn thông qua tính năng add ID trong mục quản lý bạn bè 
* Rủi ro: Nếu xây dựng như này thì phải hạn chế bạn bè của từng freelancer là bao nhiêu để giảm thiểu việc quá tải dữ liệu trên hệ thống?


2.2 Điều kiện thành lập Freelancer team
* Các thành viên trong freelancer phải là bạn bè của nhau trong vòng 5 ngày trở lên (điều này sẽ hạn chế được việc cạnh tranh với những team không chất lượng, định hình được quy mô team theo tính chuyên nghiệp hơn chứ không phải đăng ký bậy bạ). 
* * Các thành viên trong team phải hoàn thành ít nhất 10 dự án trở lên 
* * Các thành viên trong team có điểm đánh giá ít nhất 4,8/5 sao 
* Phải có trưởng nhóm ( mặc định người ấn tạo team sẽ là leader)
2.3 Tính năng Freelancer team 
* Cân nhắc thêm vào đây:
  



* Khi ấn vào thì sẽ hiện giao diện mới gồm có các nội dung sau:
Đối với lần đầu đăng ký tạo team:
* Nội dung chữ để thu hút freelancer tạo team sao sao đó…. 
* Điều kiện đăng ký tạo team (ở trên đã đề cập)
* Nút tạo team 
* Khi ấn vào nút tạo team thì sẽ hiện lên giao diện tạo freelancer team 
Đối với đã có Freelancer team:
* Danh sách thành viên (hiện lên như danh sách freelancer ở mục tìm freelancer)
* Dự án đã làm
* Tỉ lệ % chia lợi nhuận và điều chỉnh 
* Chỉnh sửa hồ sơ freelancer team ( tên team, logo, slogan, mô tả, đính kèm dự án nổi bật đã làm) * tương tự như hồ sơ của freelancer riêng lẻ 


3. User Story 
3.1 Đối với Freelancer
Ấn vào mục Freelancer team trên thanh tác vụ 
3.1.1 Đối với Freelancer đăng ký tạo Freelancer team lần đầu: Người tạo team sẽ là leader và những thành viên trong team phải là bạn bè của nhau trên hệ thống
* Bấm vào nút tạo team → chuyển sang giao diện tạo team freelancer ( thiết kế theo dạng page) → gồm các nội dung như Tên team, Slogan, mô tả chung,số lượng thành viên → Bấm nút tiếp theo → Thông tin thành viên trong freelancer team → Nhóm trưởng: Họ và tên, vị trí đảm nhiệm → Các thành viên: Họ và tên, vị trí đảm nhiệm, ID ( lưu ý là ở trang trước điền bao nhiêu số thành viên thì trang sau hiện danh sách đăng ký thành viên trong team đúng số lượng như vậy) → Bấm nút tiếp theo → Tỉ lệ % ăn chia lợi nhuận → Hiện danh sách các thành viên và bên cạnh tên các thành viên là ô % chia lợi nhuận → Leader sẽ chia % → Bấm nút tiếp theo → Hiện lên notice: Việc đăng ký Freelancer team đồng nghĩa với bạn đã đồng ý điều khoản, quy định người dùng ( đính kèm đường link điều khoàn, quy định người dùng trong chữ điều khoản, quy định người dùng) → Bấm xác nhận  → Hiện thông báo yêu cầu tạo freelancer team của bạn thành công, lời mời sẽ được gửi đến các thành viên trong team, khi tất cả các thành viên xác định lời mời thì Freelancer team đã được đăng ký thành công. Đối với trường hợp các thành viên trong team chưa đạt đủ điều kiện đăng ký thì hệ thống sẽ báo lỗi 


* Đối với freelancer thành viên sẽ được nhận thông báo ở trên nút thông báo hoặc trong giao diện freelancer team. Hệ thống sẽ hiện thông báo: Bạn có một lời mời tham gia Freelancer team từ XXX → Khi ấn vào sẽ chuyển sang giao diện của freelancer team ( hoặc có thể bấm trực tiếp qua nút freelancer team trên thanh tác vụ cũng được) → Giao diện hiện thông tin lời mời gồm các nội dung theo trang như khi leader đăng ký → Freelancer thành viên bấm qua từng trang để xác nhận thông tin → Cuối cùng bấm đồng ý tham gia kèm theo notice: Việc bấm đồng ý tham gia đồng nghĩa với bạn đã đồng ý điều khoản, quy định người dùng ( đính kèm đường link điều khoàn, quy định người dùng trong chữ điều khoản, quy định người dùng)


Cân nhắc tính năng nền tảng can thiệp xác nhận đăng ký để tránh tình trạng đăng ký bậy bạ


3.1.2 Đối với freelancer đã có team freelancer
* Giao diện hồ sơ Freelancer team ( Việc thiết kế như nào tuỳ thuộc vào VInh)
* Gồm các thông tin: Tên nhóm, slogan, mô tả, hồ sơ từng thành viên, dự án nổi bật,...
* * Tính năng điều chỉnh thông tin: Điều chỉnh tất cả các thông tin như khi đăng ký. Và chỉ có leader mới có tính năng điều chỉnh. Sau khi điều chỉnh xong thì các thành viên sẽ nhận được thông báo điều chỉnh và ấn vào để xem thông tin mới điều chỉnh ( hiện giao diện theo dạng page như cũ) sau đó bấm xác nhận. Việc điều chỉnh thông tin hoàn tất sau khi tất cả các thành viên đều xác nhận 
* * Tính năng xoá nhóm: Bấm xoá nhóm ( trưởng nhóm ) và việc xác thực xoá nhóm thành công khi có trên ⅔ thành viên chấp nhận
* * Khung chat nhóm: Cái này tương tự như messenger trên facebook 


*Các tính năng khi apply jobs thì tương tự như freelancer riêng lẻ, khác ở chỗ là với freelancer team thì trưởng nhóm sẽ đứng ra thực hiện và khi apply jobs nào thì phải được đồng thuận bởi tất cả thành viên, khi trưởng nhóm apply thì thành viên sẽ được nhận thông báo và bấm xác nhận




3.2 Đối với Client 


* Ấn vào mục Freelancer team trên thanh tác vụ → Hiện danh sách các freelancer team ( theo t thì nên thiết kế giao diện tựa tựa như hồ sơ freelancer riêng lẻ hoặc thiết kế theo dạng page giống như khi đăng ký nhưng trừ trang chia lợi nhuận, t muốn khi bấm vào hồ sơ từng freelancer trong team thì sẽ hiện ra giao diện hồ sơ hoàn chỉnh giống như giao diện hồ sơ freelancer riêng lẻ) → Dưới cùng hồ sơ team sẽ có các tác vụ như thuê, trò chuyện ( qua trưởng nhóm hoặc group chat), lưu hồ sơ 
* * Về tính năng, quy trình làm việc thì tương tự như với freelancer riêng lẻ 




4. Những điều cần lưu ý 
* Vì chưa có hệ thống escorw nên quy trình apply job sẽ chưa được rõ ràng
* Chưa có mini jira 
* Chưa có tính năng messenger 
* Tất cả những nội dung trên mang tính sườn và tham khảo và hoàn thiện tốt ngày qua ngày