# 🚀 Quick Start - Team Service

## Vấn đề hiện tại
Team service đang gặp lỗi do thiếu cấu hình Firebase và database PostgreSQL.

## 🔧 Cách fix nhanh

### Option 1: Chạy với Mock Database (Khuyến nghị cho development)
```bash
cd services/team-service
npm run test:mock
```

### Option 2: Fix và chạy tự động
```bash
cd services/team-service
npm run fix
```

### Option 3: Cài đặt đầy đủ với PostgreSQL
```bash
cd services/team-service
npm run install:postgresql
npm run db:setup
npm start
```

## 📋 Các lệnh có sẵn

| Lệnh | Mô tả |
|------|-------|
| `npm run test:mock` | Chạy service với mock database (không cần PostgreSQL) |
| `npm run fix` | Tự động fix lỗi và khởi động service |
| `npm start` | Chạy service với database thật (cần PostgreSQL) |
| `npm run db:setup` | Setup database PostgreSQL |
| `npm run install:postgresql` | Cài đặt PostgreSQL |

## 🧪 Test API

Sau khi chạy service, test các endpoints:

```bash
# Health check
curl http://localhost:3008/health

# Lấy danh sách teams
curl http://localhost:3008/api/teams

# Lấy chi tiết team
curl http://localhost:3008/api/teams/team-1

# Lấy invitations
curl http://localhost:3008/api/invitations/my-invitations

# Lấy chat messages
curl http://localhost:3008/api/chat/team/team-1
```

## 🔍 Troubleshooting

### Lỗi Firebase
- Service sẽ tự động chuyển sang mock authentication
- Không cần cấu hình Firebase cho development

### Lỗi Database
- Chạy `npm run test:mock` để sử dụng mock database
- Hoặc cài đặt PostgreSQL và chạy `npm run db:setup`

### Lỗi Port
- Service mặc định chạy trên port 3008
- Có thể thay đổi trong file `.env`

## 📊 Trạng thái Service

- ✅ **Mock Mode**: Hoạt động tốt với dữ liệu mẫu
- ✅ **API Endpoints**: Đầy đủ các endpoints cần thiết
- ✅ **Authentication**: Mock authentication hoạt động
- ✅ **CORS**: Đã cấu hình cho development
- ⚠️ **Database**: Cần PostgreSQL cho production

## 🎯 Kết quả

Team service đã được fix và có thể chạy ngay lập tức với:
- Mock database cho development
- Đầy đủ API endpoints
- Authentication mock
- CORS configuration
- Error handling

**Khuyến nghị**: Sử dụng `npm run test:mock` để test nhanh và `npm run fix` để fix tự động. 