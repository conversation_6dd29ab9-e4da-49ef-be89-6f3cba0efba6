/**
 * Centralized Configuration for Community Service
 * Tất cả environment variables và URLs được quản lý tập trung tại đây
 */

const path = require('path');

// Load environment variables from local .env first, then root .env
require('dotenv').config({ path: path.join(__dirname, '../../.env') });
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });

class Config {
  constructor() {
    this.validateRequiredEnvVars();
  }

  // Environment
  get NODE_ENV() {
    return process.env.NODE_ENV || 'development';
  }

  get isDevelopment() {
    return this.NODE_ENV === 'development';
  }

  get isProduction() {
    return this.NODE_ENV === 'production';
  }

  get isTest() {
    return this.NODE_ENV === 'test';
  }

  // Service Configuration
  get PORT() {
    return parseInt(process.env.PORT) || parseInt(process.env.COMMUNITY_SERVICE_PORT) || 3005;
  }

  get SERVICE_NAME() {
    return 'community-service';
  }

  get SERVICE_VERSION() {
    return '1.0.0';
  }

  // Database Configuration
  get DATABASE_URL() {
    return process.env.DATABASE_URL;
  }

  get DB_HOST() {
    return process.env.DB_HOST || 'localhost';
  }

  get DB_PORT() {
    return parseInt(process.env.DB_PORT) || 5432;
  }

  get DB_NAME() {
    return process.env.DB_NAME || 'vwork_community_service';
  }

  get DB_USER() {
    return process.env.DB_USER || 'vwork_admin';
  }

  get DB_PASSWORD() {
    return process.env.DB_PASSWORD || 'VWork2024!';
  }

  get DB_POOL_MAX() {
    return parseInt(process.env.DB_POOL_MAX) || 20;
  }

  get DB_POOL_MIN() {
    return parseInt(process.env.DB_POOL_MIN) || 2;
  }

  get DB_CONNECTION_TIMEOUT() {
    return parseInt(process.env.DB_CONNECTION_TIMEOUT) || 5000;
  }

  get DB_IDLE_TIMEOUT() {
    return parseInt(process.env.DB_IDLE_TIMEOUT) || 30000;
  }

  // Firebase Configuration
  get FIREBASE_PROJECT_ID() {
    return process.env.FIREBASE_PROJECT_ID;
  }

  get FIREBASE_CLIENT_EMAIL() {
    return process.env.FIREBASE_CLIENT_EMAIL;
  }

  get FIREBASE_PRIVATE_KEY() {
    return process.env.FIREBASE_PRIVATE_KEY;
  }

  // CORS Configuration
  get ALLOWED_ORIGINS() {
    const origins = process.env.ALLOWED_ORIGINS;
    if (origins) {
      return origins.split(',').map(origin => origin.trim());
    }

    // Default origins based on environment
    if (this.isProduction) {
      return [
        'https://nerafus-client.onrender.com',
        'https://vwork-api-gateway.onrender.com',
        'https://nerafus.com',
        'https://www.nerafus.com',
        'https://factcheck-vn.netlify.app',
        'https://factcheck.vn',
        'https://frontend-ce4z.onrender.com'
      ];
    }

    // Development defaults
    return [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3007',
      'http://localhost:3008',
      'http://localhost:3009',
      'http://localhost:8000',
      'http://localhost:8080'
    ];
  }

  get CORS_CREDENTIALS() {
    return process.env.CORS_CREDENTIALS === 'true' || true;
  }

  // Service URLs (for inter-service communication)
  get API_GATEWAY_URL() {
    return process.env.API_GATEWAY_URL || this.getServiceUrl('api-gateway', 8080);
  }

  get AUTH_SERVICE_URL() {
    return process.env.AUTH_SERVICE_URL || this.getServiceUrl('auth-service', 3001);
  }

  get USER_SERVICE_URL() {
    return process.env.USER_SERVICE_URL || this.getServiceUrl('user-service', 3001);
  }

  get PROJECT_SERVICE_URL() {
    return process.env.PROJECT_SERVICE_URL || this.getServiceUrl('project-service', 3002);
  }

  get JOB_SERVICE_URL() {
    return process.env.JOB_SERVICE_URL || this.getServiceUrl('job-service', 3003);
  }

  get CHAT_SERVICE_URL() {
    return process.env.CHAT_SERVICE_URL || this.getServiceUrl('chat-service', 3004);
  }

  get PAYMENT_SERVICE_URL() {
    return process.env.PAYMENT_SERVICE_URL || this.getServiceUrl('payment-service', 3007);
  }

  get EVENT_BUS_SERVICE_URL() {
    return process.env.EVENT_BUS_SERVICE_URL || this.getServiceUrl('event-bus', 3007);
  }

  // Event Store Configuration
  get EVENT_STORE_ENABLED() {
    return process.env.EVENT_STORE_ENABLED === 'true' || process.env.KURRENTDB_ENABLED === 'true';
  }

  get KURRENTDB_URL() {
    return process.env.KURRENTDB_URL || this.getServiceUrl('eventstore', 2113, 'esdb');
  }

  get EVENT_STORE_BATCH_SIZE() {
    return parseInt(process.env.EVENT_STORE_BATCH_SIZE) || 100;
  }

  get EVENT_STORE_RETRY_ATTEMPTS() {
    return parseInt(process.env.EVENT_STORE_RETRY_ATTEMPTS) || 3;
  }

  get EVENT_STORE_TIMEOUT() {
    return parseInt(process.env.EVENT_STORE_TIMEOUT) || 5000;
  }

  // Content Limits
  get MAX_TITLE_LENGTH() {
    return parseInt(process.env.MAX_TITLE_LENGTH) || 200;
  }

  get MAX_POST_LENGTH() {
    return parseInt(process.env.MAX_POST_LENGTH) || 10000;
  }

  get MAX_COMMENT_LENGTH() {
    return parseInt(process.env.MAX_COMMENT_LENGTH) || 2000;
  }

  // Logging Configuration
  get LOG_LEVEL() {
    return process.env.LOG_LEVEL || (this.isProduction ? 'info' : 'debug');
  }

  get LOG_FORMAT() {
    return process.env.LOG_FORMAT || (this.isProduction ? 'json' : 'simple');
  }

  // Security Configuration
  get ENABLE_HELMET() {
    return process.env.ENABLE_HELMET !== 'false';
  }

  get ENABLE_COMPRESSION() {
    return process.env.ENABLE_COMPRESSION !== 'false';
  }

  get TRUST_PROXY() {
    return process.env.TRUST_PROXY === 'true';
  }

  // Health Check Configuration
  get HEALTH_CHECK_INTERVAL() {
    return parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000;
  }

  get HEALTH_CHECK_TIMEOUT() {
    return parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 10000;
  }

  // Helper method to generate service URLs
  getServiceUrl(serviceName, defaultPort, protocol = 'http') {
    const host = process.env[`${serviceName.toUpperCase().replace('-', '_')}_HOST`] || 'localhost';
    const port = process.env[`${serviceName.toUpperCase().replace('-', '_')}_PORT`] || defaultPort;
    return `${protocol}://${host}:${port}`;
  }

  // Get current service URL
  get SERVICE_URL() {
    const host = process.env.SERVICE_HOST || 'localhost';
    return `http://${host}:${this.PORT}`;
  }

  // Get health check URL
  get HEALTH_CHECK_URL() {
    return `${this.SERVICE_URL}/health`;
  }

  // Validate required environment variables
  validateRequiredEnvVars() {
    const required = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_CLIENT_EMAIL',
      'FIREBASE_PRIVATE_KEY'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      console.error('❌ Missing required environment variables:', missing);
      if (this.isProduction) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
      } else {
        console.warn('⚠️ Some required environment variables are missing. Service may not work correctly.');
      }
    }
  }

  // Get all configuration as object (for debugging)
  getAll() {
    return {
      environment: this.NODE_ENV,
      service: {
        name: this.SERVICE_NAME,
        version: this.SERVICE_VERSION,
        port: this.PORT,
        url: this.SERVICE_URL
      },
      database: {
        host: this.DB_HOST,
        port: this.DB_PORT,
        name: this.DB_NAME,
        user: this.DB_USER,
        hasPassword: !!this.DB_PASSWORD,
        pool: {
          max: this.DB_POOL_MAX,
          min: this.DB_POOL_MIN,
          connectionTimeout: this.DB_CONNECTION_TIMEOUT,
          idleTimeout: this.DB_IDLE_TIMEOUT
        }
      },
      cors: {
        allowedOrigins: this.ALLOWED_ORIGINS,
        credentials: this.CORS_CREDENTIALS
      },
      services: {
        apiGateway: this.API_GATEWAY_URL,
        auth: this.AUTH_SERVICE_URL,
        user: this.USER_SERVICE_URL,
        project: this.PROJECT_SERVICE_URL,
        job: this.JOB_SERVICE_URL,
        chat: this.CHAT_SERVICE_URL,
        payment: this.PAYMENT_SERVICE_URL,
        eventBus: this.EVENT_BUS_SERVICE_URL
      },
      eventStore: {
        enabled: this.EVENT_STORE_ENABLED,
        url: this.KURRENTDB_URL,
        batchSize: this.EVENT_STORE_BATCH_SIZE,
        retryAttempts: this.EVENT_STORE_RETRY_ATTEMPTS,
        timeout: this.EVENT_STORE_TIMEOUT
      },
      limits: {
        titleLength: this.MAX_TITLE_LENGTH,
        postLength: this.MAX_POST_LENGTH,
        commentLength: this.MAX_COMMENT_LENGTH
      },
      logging: {
        level: this.LOG_LEVEL,
        format: this.LOG_FORMAT
      },
      security: {
        helmet: this.ENABLE_HELMET,
        compression: this.ENABLE_COMPRESSION,
        trustProxy: this.TRUST_PROXY
      }
    };
  }
}

// Export singleton instance
module.exports = new Config(); 