import { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';

import {
 Bars3Icon,
 XMarkIcon,
 UserCircleIcon,
 MagnifyingGlassIcon,
 GlobeAltIcon,
 ChevronDownIcon,
 SunIcon,
 MoonIcon,
} from '@heroicons/react/24/outline';
// Use logo from public folder for better compatibility
const logo = '/logo.png';

const AppleHeader = () => {

 const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
 const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
 const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
 const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);
 const [isFindWorkOpen, setIsFindWorkOpen] = useState(false);
 const [isTalentOpen, setIsTalentOpen] = useState(false);
 const [isCompanyOpen, setIsCompanyOpen] = useState(false);
 const [searchQuery, setSearchQuery] = useState('');
 const [hideSignIn, setHideSignIn] = useState(false);
 const [hideLanguage, setHideLanguage] = useState(false);

 const [hideSearch, setHideSearch] = useState(false);
 const [visibleNavCount, setVisibleNavCount] = useState(5);
 const [forceMobileMenu, setForceMobileMenu] = useState(false);
 const [criticalOnly, setCriticalOnly] = useState(false);

 const { language, changeLanguage, t } = useLanguage();

 // Fallback function if t is not available
 const translate = t || ((key) => key);
 const { user, isAuthenticated, logout } = useAuth();

 const location = useLocation();
 const navigate = useNavigate();

 const headerRef = useRef(null);
 const logoRef = useRef(null);
 const userMenuRef = useRef(null);
 const languageMenuRef = useRef(null);
 const moreMenuRef = useRef(null);
 const findWorkRef = useRef(null);
 const talentRef = useRef(null);
 const companyRef = useRef(null);

 const navRef = useRef(null);
 const actionsRef = useRef(null);

 // Simplified responsive header logic - WITHOUT external service interference
 useEffect(() => {
  const handleResize = () => {
   if (!headerRef.current) return;

   const screenWidth = window.innerWidth;
   const userAgent = navigator.userAgent;
   const isVSCodeBrowser = userAgent.includes('VSCode') || userAgent.includes('Electron');
   
   // More aggressive breakpoints for VSCode browser
   const effectiveBreakpoint = isVSCodeBrowser ? 600 : 1100;
   const searchBreakpoint = isVSCodeBrowser ? 700 : 1100;
   
   // Optimized breakpoint logic to ensure all nav items are visible at proper zoom levels
   if (screenWidth >= effectiveBreakpoint) {
    // Desktop XL - show everything including search
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(screenWidth < searchBreakpoint);
    setHideLanguage(false);
    setHideSignIn(false);
    setVisibleNavCount(6); // Show all 6 navigation items
    console.log('✅ Full header mode - all items visible');
   } else if (screenWidth >= 900) {
    // Desktop LG - show all nav but hide search to save space
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(true);
    setHideLanguage(false);
    setHideSignIn(false);
    setVisibleNavCount(6); // Still show all 6 navigation items
    console.log('📱 Compact header mode - nav visible, search hidden');
   } else if (screenWidth >= 768) {
    // Tablet - show navigation but reduce some items
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(true);
    setHideLanguage(false);
    setHideSignIn(true);
    setVisibleNavCount(4); // Show 4 main nav items
    console.log('📱 Tablet mode - reduced nav items');
   } else if (screenWidth >= 640) {
    // Small tablet - show minimal navigation
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(true);
    setHideLanguage(true);
    setHideSignIn(true);
    setVisibleNavCount(3); // Show 3 main nav items
    console.log('📱 Small tablet mode - minimal nav');
   } else {
    // Mobile - use mobile menu
    setCriticalOnly(false);
    setForceMobileMenu(true);
    setHideSearch(true);
    setHideLanguage(true);
    setHideSignIn(true);
    setVisibleNavCount(0);
    console.log('📱 Mobile mode - hamburger menu');
   }
  };

  // Initial calculation
  handleResize();

  // Listen for resize events with debouncing
  let timeoutId;
  const debouncedResize = () => {
   clearTimeout(timeoutId);
   timeoutId = setTimeout(handleResize, 100);
  };

  window.addEventListener('resize', debouncedResize);

  return () => {
   window.removeEventListener('resize', debouncedResize);
   clearTimeout(timeoutId);
  };
 }, []);

 // Simplified animations
 useEffect(() => {
  if (headerRef.current) {
   gsap.fromTo(
    headerRef.current,
    { y: -20, opacity: 0 },
    { y: 0, opacity: 1, duration: 0.6, ease: 'power2.out' }
   );
  }

  if (logoRef.current) {
   gsap.fromTo(
    logoRef.current,
    { scale: 0.8, opacity: 0 },
    {
     scale: 1,
     opacity: 1,
     duration: 0.8,
     ease: 'back.out(1.2)',
     delay: 0.2,
    }
   );
  }
 }, []);

 // Upwork-style navigation with dropdown categories
 const findWorkDropdown = [
  { name: translate('projects'), href: '/projects', current: location.pathname === '/projects' },
  { name: translate('jobs'), href: '/jobs', current: location.pathname === '/jobs' },
  { name: translate('contests'), href: '/contests', current: location.pathname === '/contests' },
  { name: 'Phát triển IT', href: '/categories/development', current: location.pathname === '/categories/development' },
  { name: 'Thiết kế sáng tạo', href: '/categories/design', current: location.pathname === '/categories/design' },
  { name: 'Sales & Marketing', href: '/categories/marketing', current: location.pathname === '/categories/marketing' },
 ];

 const talentDropdown = [
  { name: translate('freelancers'), href: '/freelancers', current: location.pathname === '/freelancers' },
  { name: 'Tìm freelancer IT', href: '/freelancers?category=development', current: false },
  { name: 'Tìm designer', href: '/freelancers?category=design', current: false },
  { name: 'Tìm marketer', href: '/freelancers?category=marketing', current: false },
 ];

 const companyDropdown = [
  { name: translate('community'), href: '/community', current: location.pathname === '/community' },
  { name: 'Team', href: '/team', current: location.pathname === '/team' },
  { name: 'Về chúng tôi', href: '/about', current: location.pathname === '/about' },
 ];

 // Additional navigation items for "More" dropdown (only when space is extremely limited)
 const moreNavigation = [];

 const languageOptions = [
  { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
 ];

 // Handle click outside to close menus
 useEffect(() => {
  const handleClickOutside = event => {
   if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
    setIsUserMenuOpen(false);
   }
   if (
    languageMenuRef.current &&
    !languageMenuRef.current.contains(event.target)
   ) {
    setIsLanguageMenuOpen(false);
   }
   if (
    moreMenuRef.current &&
    !moreMenuRef.current.contains(event.target)
   ) {
    setIsMoreMenuOpen(false);
   }
   if (findWorkRef.current && !findWorkRef.current.contains(event.target)) {
    setIsFindWorkOpen(false);
   }
   if (talentRef.current && !talentRef.current.contains(event.target)) {
    setIsTalentOpen(false);
   }
   if (companyRef.current && !companyRef.current.contains(event.target)) {
    setIsCompanyOpen(false);
   }
  };

  document.addEventListener('mousedown', handleClickOutside);
  return () => {
   document.removeEventListener('mousedown', handleClickOutside);
  };
 }, []);

 const handleLogout = async () => {
  try {
   await logout();
   // Navigate to home page after successful logout
   navigate('/');
  } catch (error) {
   console.error('Logout error:', error);
   // Even if logout fails, redirect to home page
   navigate('/');
  }
 };



 return (
  <header
   ref={headerRef}
   className='fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200 shadow-sm transition-colors duration-300'
  >
   
   <div className='header-fixed-width mx-auto px-4 sm:px-6 lg:px-8'>
    <div className='header-container h-16 lg:h-20'>
     {/* Apple-style Logo */}
     <div className='header-logo-section'>
      <Link to='/' className='flex items-center justify-center w-full group'>
       <div
        ref={logoRef}
        className='w-36 lg:w-52 h-14 lg:h-18 rounded-lg flex items-center justify-center shadow-sm group-hover:shadow-md transition-all duration-200 bg-white'
       >
        <img
         src={logo}
         alt="NERAFUS Logo"
         className='w-full h-full object-contain'
         style={{
          objectPosition: 'center',
          filter: 'contrast(1.1) saturate(1.1)'
         }}
        />
       </div>
      </Link>
     </div>
     {/* Nếu criticalOnly, chỉ render nút Get started */}
     {criticalOnly ? (
      <div style={{ position: 'absolute', right: 0, top: '50%', transform: 'translateY(-50%)', zIndex: 100 }}>
       <Link
        to='/auth'
        className='px-3 lg:px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md header-critical-button'
        style={{ minWidth: 90, maxWidth: 140, whiteSpace: 'nowrap' }}
       >
        {translate('getStarted')}
       </Link>
      </div>
     ) : (
      // Upwork-style Navigation with Dropdowns */}
      <nav ref={navRef} className={`hidden ${forceMobileMenu ? '' : 'lg:block'} header-nav-section`}>
       <div className='flex items-center justify-center space-x-6'>

        {/* Find Work Dropdown */}
        <div className='relative' ref={findWorkRef}>
         <button
          onClick={() => setIsFindWorkOpen(!isFindWorkOpen)}
          className='flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-all duration-200'
         >
          <span>Tìm việc</span>
          <ChevronDownIcon className='h-4 w-4' />
         </button>
         <AnimatePresence>
          {isFindWorkOpen && (
           <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50'
           >
            {findWorkDropdown.map(item => (
             <Link
              key={item.name}
              to={item.href}
              className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200'
              onClick={() => setIsFindWorkOpen(false)}
             >
              {item.name}
             </Link>
            ))}
           </motion.div>
          )}
         </AnimatePresence>
        </div>

        {/* Talent Dropdown */}
        <div className='relative' ref={talentRef}>
         <button
          onClick={() => setIsTalentOpen(!isTalentOpen)}
          className='flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-all duration-200'
         >
          <span>Tài năng</span>
          <ChevronDownIcon className='h-4 w-4' />
         </button>
         <AnimatePresence>
          {isTalentOpen && (
           <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50'
           >
            {talentDropdown.map(item => (
             <Link
              key={item.name}
              to={item.href}
              className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200'
              onClick={() => setIsTalentOpen(false)}
             >
              {item.name}
             </Link>
            ))}
           </motion.div>
          )}
         </AnimatePresence>
        </div>

        {/* Company Dropdown */}
        <div className='relative' ref={companyRef}>
         <button
          onClick={() => setIsCompanyOpen(!isCompanyOpen)}
          className='flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-all duration-200'
         >
          <span>Công ty</span>
          <ChevronDownIcon className='h-4 w-4' />
         </button>
         <AnimatePresence>
          {isCompanyOpen && (
           <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50'
           >
            {companyDropdown.map(item => (
             <Link
              key={item.name}
              to={item.href}
              className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200'
              onClick={() => setIsCompanyOpen(false)}
             >
              {item.name}
             </Link>
            ))}
           </motion.div>
          )}
         </AnimatePresence>
        </div>
       </div>
      </nav>
     )}

     {/* Actions section */}
     {!criticalOnly && (
      <div ref={actionsRef} className='header-actions-section'>
       {/* Expanded Search Bar - Upwork Style */}
       {!hideSearch && (
        <div className='hidden lg:block relative flex-1 max-w-md mx-4'>
         <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
          <MagnifyingGlassIcon className='h-5 w-5 text-gray-400' />
         </div>
         <input
          type='text'
          placeholder='Tìm kiếm dự án, freelancer, kỹ năng...'
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          className='block w-full pl-12 pr-4 py-3 border border-gray-300 rounded-full text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 transition-all duration-200 shadow-sm hover:shadow-md'
         />
         {/* Search suggestions dropdown */}
         {searchQuery && (
          <div className='absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50'>
           <div className='px-4 py-2 text-xs text-gray-500 font-medium'>Gợi ý tìm kiếm</div>
           <div className='px-4 py-2 hover:bg-gray-50 cursor-pointer text-sm'>
            <MagnifyingGlassIcon className='h-4 w-4 inline mr-2 text-gray-400' />
            {searchQuery} trong dự án
           </div>
           <div className='px-4 py-2 hover:bg-gray-50 cursor-pointer text-sm'>
            <MagnifyingGlassIcon className='h-4 w-4 inline mr-2 text-gray-400' />
            {searchQuery} trong freelancer
           </div>
          </div>
         )}
        </div>
       )}

       {/* Language Selector */}
       {!hideLanguage && (
        <div className='relative flex-shrink-0' ref={languageMenuRef} style={{ minWidth: '70px' }}>
         <button
          onClick={() => setIsLanguageMenuOpen(!isLanguageMenuOpen)}
          className='flex items-center space-x-1 px-2 lg:px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-gray-200 hover:border-gray-300'
         >
          <GlobeAltIcon className='h-4 w-4' />
          <span className='hidden lg:block font-medium text-xs'>
           {language === 'vi' ? 'VI' : 'EN'}
          </span>
          <ChevronDownIcon className='h-3 w-3 hidden lg:block' />
         </button>

         <AnimatePresence>
          {isLanguageMenuOpen ? (
           <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1'
           >
            {languageOptions.map(option => (
             <button
              key={option.code}
              onClick={() => {
               changeLanguage(option.code);
               setIsLanguageMenuOpen(false);
              }}
              className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 flex items-center space-x-2 transition-colors duration-200 ${
               language === option.code
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700'
              }`}
             >
              <span>{option.flag}</span>
              <span>{option.name}</span>
             </button>
            ))}
           </motion.div>
          ) : null}
         </AnimatePresence>
        </div>
       )}

       {/* Theme Toggle */}


       {/* Post Job Button */}
       {isAuthenticated && (
        <Link
         to="/jobs/create"
         className="hidden lg:block px-3 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
        >
         + Đăng việc
        </Link>
       )}

       {/* User Menu */}
       {isAuthenticated ? (
        <div className='relative flex-shrink-0' ref={userMenuRef}>
         <button
          onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
          className='flex items-center space-x-1 px-1 lg:px-2 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-gray-200 hover:border-gray-300'
         >
          <UserCircleIcon className='h-5 w-5' />
          <span className='hidden lg:block font-medium text-xs'>
           {user?.displayName || translate('user')}
          </span>
          <ChevronDownIcon className='h-3 w-3 hidden lg:block' />
         </button>

         <AnimatePresence>
          {isUserMenuOpen ? (
           <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1'
           >
            <Link
             to='/dashboard'
             className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
             onClick={() => setIsUserMenuOpen(false)}
            >
             {translate('dashboard')}
            </Link>
            <Link
             to="/jobs/create"
             className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
             onClick={() => setIsUserMenuOpen(false)}
            >
             + Đăng việc
            </Link>
            <Link
             to='/settings'
             className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
             onClick={() => setIsUserMenuOpen(false)}
            >
             {translate('settings')}
            </Link>
            <button
             onClick={handleLogout}
             className='block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
            >
             {translate('signOut')}
            </button>
           </motion.div>
          ) : null}
         </AnimatePresence>
        </div>
       ) : (
        <div className='flex items-center gap-1 flex-shrink-0'>
         {!hideSignIn && (
          <Link
           to='/auth?mode=signin'
           className='hidden lg:block px-2 lg:px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200 rounded-lg hover:bg-gray-50 header-button-priority'
          >
           {translate('signIn')}
          </Link>
         )}

         <Link
          to='/auth?mode=signup'
          className='px-3 lg:px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md header-critical-button btn-auto-scale'
         >
          {translate('getStarted')}
         </Link>
        </div>
       )}

       {/* Mobile menu button */}
       <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className='lg:hidden p-1 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-all duration-200 border border-gray-200 hover:border-gray-300 flex-shrink-0'
       >
        {isMobileMenuOpen ? (
         <XMarkIcon className='h-4 w-4' />
        ) : (
         <Bars3Icon className='h-4 w-4' />
        )}
       </button>
      </div>
     )}
    </div>

    {/* Mobile Navigation */}
    <AnimatePresence>
     {isMobileMenuOpen ? (
      <motion.div
       initial={{ opacity: 0, height: 0 }}
       animate={{ opacity: 1, height: 'auto' }}
       exit={{ opacity: 0, height: 0 }}
       transition={{ duration: 0.3 }}
       className='lg:hidden border-t border-gray-200 pt-6 pb-6'
      >
       <div className='space-y-3'>
        {[...navigation, ...moreNavigation].map(item => (
         <Link
          key={item.name}
          to={item.href}
          className={`block px-4 py-3 text-base font-medium rounded-xl transition-all duration-200 ${
           item.current
            ? 'text-blue-600 bg-blue-50 shadow-sm'
            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
          }`}
          onClick={() => setIsMobileMenuOpen(false)}
         >
          {item.name}
         </Link>
        ))}

        {/* Mobile Search */}
        <div className='pt-4 border-t border-gray-200'>
         <div className='relative'>
          <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
           <MagnifyingGlassIcon className='h-5 w-5 text-gray-400' />
          </div>
          <input
           type='text'
           placeholder={translate('searchProjectsFreelancers')}
           value={searchQuery}
           onChange={e => setSearchQuery(e.target.value)}
           className='block w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-900'
          />
         </div>
        </div>

        {/* Mobile Auth Buttons */}
        <div className='pt-4 border-t border-gray-200'>
         <div className='space-y-3'>
          {isAuthenticated && (
           <Link
            to="/jobs/create"
            className="block w-full px-4 py-3 text-base font-medium text-white bg-green-600 hover:bg-green-700 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md"
            onClick={() => setIsMobileMenuOpen(false)}
           >
            + Đăng việc
           </Link>
          )}
          <Link
           to='/auth?mode=signin'
           className='block w-full px-4 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-xl transition-all duration-200 border border-gray-200'
           onClick={() => setIsMobileMenuOpen(false)}
          >
           {translate('signIn')}
          </Link>
          <Link
           to='/auth?mode=signup'
           className='block w-full px-4 py-3 text-base font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md'
           onClick={() => setIsMobileMenuOpen(false)}
          >
           {translate('getStarted')}
          </Link>
         </div>
        </div>


       </div>
      </motion.div>
     ) : null}
    </AnimatePresence>
   </div>
  </header>
 );
};

export default AppleHeader;
