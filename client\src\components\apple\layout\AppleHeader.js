import { useState, useRef, useEffect, useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';

import {
 Bars3Icon,
 XMarkIcon,
 UserCircleIcon,
 MagnifyingGlassIcon,
 GlobeAltIcon,
 ChevronDownIcon,
 SunIcon,
 MoonIcon,
} from '@heroicons/react/24/outline';
// Use logo from public folder for better compatibility
const logo = '/logo.png';

const AppleHeader = () => {
 // Helper functions for mega menu
 const showMegaMenu = useCallback((element) => {
  const megaMenu = element.querySelector('.mega-menu');
  if (megaMenu) {
   megaMenu.style.opacity = '1';
   megaMenu.style.visibility = 'visible';
   megaMenu.style.transform = 'translateY(0)';
  }
 }, []);

 const hideMegaMenu = useCallback((element) => {
  const megaMenu = element.querySelector('.mega-menu');
  if (megaMenu) {
   megaMenu.style.opacity = '0';
   megaMenu.style.visibility = 'hidden';
   megaMenu.style.transform = 'translateY(8px)';
  }
 }, []);

 const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
 const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
 const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
 const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);

 const [searchQuery, setSearchQuery] = useState('');
 const [hideSignIn, setHideSignIn] = useState(false);
 const [hideLanguage, setHideLanguage] = useState(false);

 const [hideSearch, setHideSearch] = useState(false);
 const [visibleNavCount, setVisibleNavCount] = useState(5);
 const [forceMobileMenu, setForceMobileMenu] = useState(false);
 const [criticalOnly, setCriticalOnly] = useState(false);

 const { language, changeLanguage, t } = useLanguage();

 // Fallback function if t is not available
 const translate = t || ((key) => key);
 const { user, isAuthenticated, logout } = useAuth();

 const location = useLocation();
 const navigate = useNavigate();

 const headerRef = useRef(null);
 const logoRef = useRef(null);
 const userMenuRef = useRef(null);
 const languageMenuRef = useRef(null);
 const moreMenuRef = useRef(null);


 const navRef = useRef(null);
 const actionsRef = useRef(null);

 // Simplified responsive header logic - WITHOUT external service interference
 useEffect(() => {
  const handleResize = () => {
   if (!headerRef.current) return;

   const screenWidth = window.innerWidth;
   const userAgent = navigator.userAgent;
   const isVSCodeBrowser = userAgent.includes('VSCode') || userAgent.includes('Electron');
   
   // More aggressive breakpoints for VSCode browser
   const effectiveBreakpoint = isVSCodeBrowser ? 600 : 1100;
   const searchBreakpoint = isVSCodeBrowser ? 700 : 1100;
   
   // Optimized breakpoint logic to ensure all nav items are visible at proper zoom levels
   if (screenWidth >= effectiveBreakpoint) {
    // Desktop XL - show everything including search
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(screenWidth < searchBreakpoint);
    setHideLanguage(false);
    setHideSignIn(false);
    setVisibleNavCount(6); // Show all 6 navigation items
    console.log('✅ Full header mode - all items visible');
   } else if (screenWidth >= 900) {
    // Desktop LG - show all nav but hide search to save space
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(true);
    setHideLanguage(false);
    setHideSignIn(false);
    setVisibleNavCount(6); // Still show all 6 navigation items
    console.log('📱 Compact header mode - nav visible, search hidden');
   } else if (screenWidth >= 768) {
    // Tablet - show navigation but reduce some items
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(true);
    setHideLanguage(false);
    setHideSignIn(true);
    setVisibleNavCount(4); // Show 4 main nav items
    console.log('📱 Tablet mode - reduced nav items');
   } else if (screenWidth >= 640) {
    // Small tablet - show minimal navigation
    setCriticalOnly(false);
    setForceMobileMenu(false);
    setHideSearch(true);
    setHideLanguage(true);
    setHideSignIn(true);
    setVisibleNavCount(3); // Show 3 main nav items
    console.log('📱 Small tablet mode - minimal nav');
   } else {
    // Mobile - use mobile menu
    setCriticalOnly(false);
    setForceMobileMenu(true);
    setHideSearch(true);
    setHideLanguage(true);
    setHideSignIn(true);
    setVisibleNavCount(0);
    console.log('📱 Mobile mode - hamburger menu');
   }
  };

  // Initial calculation
  handleResize();

  // Listen for resize events with debouncing
  let timeoutId;
  const debouncedResize = () => {
   clearTimeout(timeoutId);
   timeoutId = setTimeout(handleResize, 100);
  };

  window.addEventListener('resize', debouncedResize);

  return () => {
   window.removeEventListener('resize', debouncedResize);
   clearTimeout(timeoutId);
  };
 }, []);

 // Simplified animations
 useEffect(() => {
  if (headerRef.current) {
   gsap.fromTo(
    headerRef.current,
    { y: -20, opacity: 0 },
    { y: 0, opacity: 1, duration: 0.6, ease: 'power2.out' }
   );
  }

  if (logoRef.current) {
   gsap.fromTo(
    logoRef.current,
    { scale: 0.8, opacity: 0 },
    {
     scale: 1,
     opacity: 1,
     duration: 0.8,
     ease: 'back.out(1.2)',
     delay: 0.2,
    }
   );
  }
 }, []);

 // Upwork-style mega menu data
 const findWorkMegaMenu = {
  categories: [
    {
      title: 'Development & IT',
      icon: '💻',
      items: [
        { name: 'Web Development', href: '/categories/development', description: 'Frontend, Backend, Full-stack' },
        { name: 'Mobile Development', href: '/categories/mobile', description: 'iOS, Android, React Native' },
        { name: 'Desktop Development', href: '/categories/desktop', description: 'Windows, macOS, Linux apps' },
        { name: 'DevOps & Cloud', href: '/categories/devops', description: 'AWS, Docker, Kubernetes' },
        { name: 'Data Science', href: '/categories/data', description: 'ML, AI, Analytics' },
        { name: 'Cybersecurity', href: '/categories/security', description: 'Penetration testing, Security audit' }
      ]
    },
    {
      title: 'Design & Creative',
      icon: '🎨',
      items: [
        { name: 'UI/UX Design', href: '/categories/design', description: 'User interface, User experience' },
        { name: 'Graphic Design', href: '/categories/graphic', description: 'Logo, Branding, Print design' },
        { name: 'Web Design', href: '/categories/webdesign', description: 'Landing pages, Websites' },
        { name: '3D Design', href: '/categories/3d', description: '3D modeling, Animation' },
        { name: 'Illustration', href: '/categories/illustration', description: 'Digital art, Character design' },
        { name: 'Video & Animation', href: '/categories/video', description: 'Motion graphics, Video editing' }
      ]
    },
    {
      title: 'Writing & Translation',
      icon: '✍️',
      items: [
        { name: 'Content Writing', href: '/categories/writing', description: 'Blog posts, Articles, Copywriting' },
        { name: 'Technical Writing', href: '/categories/technical-writing', description: 'Documentation, Manuals' },
        { name: 'Translation', href: '/categories/translation', description: 'Document translation, Localization' },
        { name: 'Creative Writing', href: '/categories/creative-writing', description: 'Scripts, Stories, Poetry' },
        { name: 'Academic Writing', href: '/categories/academic', description: 'Research, Thesis, Papers' },
        { name: 'SEO Writing', href: '/categories/seo-writing', description: 'SEO-optimized content' }
      ]
    },
    {
      title: 'Sales & Marketing',
      icon: '📈',
      items: [
        { name: 'Digital Marketing', href: '/categories/marketing', description: 'SEM, Social media, Email marketing' },
        { name: 'Content Marketing', href: '/categories/content-marketing', description: 'Strategy, Content creation' },
        { name: 'SEO', href: '/categories/seo', description: 'Search optimization, Link building' },
        { name: 'Social Media', href: '/categories/social-media', description: 'Management, Advertising' },
        { name: 'Lead Generation', href: '/categories/lead-gen', description: 'B2B leads, Sales funnels' },
        { name: 'Market Research', href: '/categories/market-research', description: 'Analysis, Surveys, Reports' }
      ]
    }
  ],
  featured: [
    { name: 'Browse Projects', href: '/projects', description: 'Find your next project' },
    { name: 'Project Catalog', href: '/catalog', description: 'Ready-made solutions' },
    { name: 'Contest', href: '/contests', description: 'Compete for prizes' }
  ]
 };

 const talentMegaMenu = {
  categories: [
    {
      title: 'Top Skills',
      items: [
        { name: 'React Developers', href: '/freelancers?skill=react', count: '1,234', rate: '$25-50/hr' },
        { name: 'UI/UX Designers', href: '/freelancers?skill=uiux', count: '856', rate: '$20-45/hr' },
        { name: 'Content Writers', href: '/freelancers?skill=writing', count: '2,145', rate: '$15-35/hr' },
        { name: 'Digital Marketers', href: '/freelancers?skill=marketing', count: '967', rate: '$20-40/hr' },
        { name: 'Video Editors', href: '/freelancers?skill=video', count: '543', rate: '$25-55/hr' },
        { name: 'Data Scientists', href: '/freelancers?skill=data', count: '432', rate: '$35-70/hr' }
      ]
    },
    {
      title: 'By Location',
      items: [
        { name: 'Ho Chi Minh City', href: '/freelancers?location=hcm', count: '3,456' },
        { name: 'Hanoi', href: '/freelancers?location=hanoi', count: '2,789' },
        { name: 'Da Nang', href: '/freelancers?location=danang', count: '1,234' },
        { name: 'Can Tho', href: '/freelancers?location=cantho', count: '567' }
      ]
    }
  ],
  featured: [
    { name: 'Browse Talent', href: '/freelancers', description: 'Find skilled freelancers' },
    { name: 'Talent Scout', href: '/talent-scout', description: 'Let us find talent for you' },
    { name: 'Enterprise', href: '/enterprise', description: 'For large teams' }
  ]
 };

 const companyMegaMenu = {
  categories: [
    {
      title: 'About VWork',
      items: [
        { name: 'About Us', href: '/about', description: 'Our story and mission' },
        { name: 'Team', href: '/team', description: 'Meet our team' },
        { name: 'Community', href: '/community', description: 'Join our community' },
        { name: 'Careers', href: '/careers', description: 'Work with us' },
        { name: 'Press', href: '/press', description: 'News and media' }
      ]
    },
    {
      title: 'Resources',
      items: [
        { name: 'Help Center', href: '/help', description: 'Get support' },
        { name: 'Blog', href: '/blog', description: 'Tips and insights' },
        { name: 'API Documentation', href: '/api-docs', description: 'For developers' },
        { name: 'Terms of Service', href: '/terms', description: 'Legal information' }
      ]
    }
  ],
  featured: [
    { name: 'Contact Us', href: '/contact', description: 'Get in touch' },
    { name: 'Partner Program', href: '/partners', description: 'Become a partner' },
    { name: 'Investor Relations', href: '/investors', description: 'For investors' }
  ]
 };



 const languageOptions = [
  { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
 ];

 // Handle click outside to close menus
 useEffect(() => {
  const handleClickOutside = event => {
   if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
    setIsUserMenuOpen(false);
   }
   if (
    languageMenuRef.current &&
    !languageMenuRef.current.contains(event.target)
   ) {
    setIsLanguageMenuOpen(false);
   }
   if (
    moreMenuRef.current &&
    !moreMenuRef.current.contains(event.target)
   ) {
    setIsMoreMenuOpen(false);
   }

  };

  document.addEventListener('mousedown', handleClickOutside);
  return () => {
   document.removeEventListener('mousedown', handleClickOutside);
  };
 }, []);

 const handleLogout = async () => {
  try {
   await logout();
   // Navigate to home page after successful logout
   navigate('/');
  } catch (error) {
   console.error('Logout error:', error);
   // Even if logout fails, redirect to home page
   navigate('/');
  }
 };



 return (
  <header
   ref={headerRef}
   className='fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200 shadow-sm transition-colors duration-300'
  >
   
   <div className='header-fixed-width mx-auto px-4 sm:px-6 lg:px-8'>
    <div className='header-container h-16 lg:h-20'>
     {/* Apple-style Logo */}
     <div className='header-logo-section'>
      <Link to='/' className='flex items-center justify-center w-full group'>
       <div
        ref={logoRef}
        className='w-36 lg:w-52 h-14 lg:h-18 rounded-lg flex items-center justify-center shadow-sm group-hover:shadow-md transition-all duration-200 bg-white'
       >
        <img
         src={logo}
         alt="NERAFUS Logo"
         className='w-full h-full object-contain'
         style={{
          objectPosition: 'center',
          filter: 'contrast(1.1) saturate(1.1)'
         }}
        />
       </div>
      </Link>
     </div>
     {/* Nếu criticalOnly, chỉ render nút Get started */}
     {criticalOnly ? (
      <div style={{ position: 'absolute', right: 0, top: '50%', transform: 'translateY(-50%)', zIndex: 100 }}>
       <Link
        to='/auth'
        className='px-3 lg:px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md header-critical-button'
        style={{ minWidth: 90, maxWidth: 140, whiteSpace: 'nowrap' }}
       >
        {translate('getStarted')}
       </Link>
      </div>
     ) : (
      // Upwork-style Navigation with Dropdowns */}
      <nav ref={navRef} className={`hidden ${forceMobileMenu ? '' : 'lg:block'} header-nav-section`}>
       <div className='flex items-center justify-center space-x-6'>

        {/* Find Work Mega Menu */}
        <div
          className='relative group hover:bg-gray-50'
          style={{ position: 'relative' }}
          onMouseEnter={(e) => showMegaMenu(e.currentTarget)}
          onMouseLeave={(e) => hideMegaMenu(e.currentTarget)}
        >
         <button
          className='flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-all duration-200 cursor-pointer'
          style={{ pointerEvents: 'auto' }}
         >
          <span>Tìm việc</span>
          <ChevronDownIcon className='h-4 w-4 group-hover:rotate-180 transition-transform duration-200' />
         </button>

         {/* Mega Menu */}
         <div
          className='mega-menu absolute left-0 top-full mt-2 w-screen max-w-5xl bg-white rounded-lg shadow-2xl border border-red-500 transition-all duration-200'
          style={{
            position: 'absolute',
            pointerEvents: 'auto',
            left: '0',
            top: '100%',
            marginTop: '8px',
            opacity: '1',
            visibility: 'visible',
            transform: 'translateY(0)',
            zIndex: 999999,
            backgroundColor: 'white',
            border: '2px solid red'
          }}
         >
          <div className='p-8' style={{ backgroundColor: 'yellow', minHeight: '200px' }}>
           <h2 style={{ color: 'black', fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>MEGA MENU HIỂN THỊ!</h2>
           <div className='grid grid-cols-4 gap-8'>
            {findWorkMegaMenu.categories.map((category) => (
             <div key={category.title} className='space-y-4'>
              <h3 className='text-sm font-semibold text-gray-900 flex items-center border-b border-gray-100 pb-2'>
               <span className='mr-2 text-lg'>{category.icon}</span>
               {category.title}
              </h3>
              <ul className='space-y-2'>
               {category.items.map((item) => (
                <li key={item.name}>
                 <Link
                  to={item.href}
                  className='block text-sm hover:bg-blue-50 p-2 rounded-lg transition-colors duration-200'
                 >
                  <div className='font-medium text-gray-900 hover:text-blue-600'>{item.name}</div>
                  <div className='text-xs text-gray-500 mt-1'>{item.description}</div>
                 </Link>
                </li>
               ))}
              </ul>
             </div>
            ))}
           </div>

           {/* Featured Section */}
           <div className='mt-8 pt-6 border-t border-gray-200'>
            <div className='grid grid-cols-3 gap-4'>
             {findWorkMegaMenu.featured.map((item) => (
              <Link
               key={item.name}
               to={item.href}
               className='p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg hover:from-blue-100 hover:to-indigo-100 border border-blue-100 hover:border-blue-200 transition-all duration-200'
              >
               <div className='font-medium text-gray-900'>{item.name}</div>
               <div className='text-sm text-gray-600 mt-1'>{item.description}</div>
              </Link>
             ))}
            </div>
           </div>
          </div>
         </div>
        </div>

        {/* Talent Mega Menu */}
        <div
          className='relative group hover:bg-gray-50'
          style={{ position: 'relative' }}
          onMouseEnter={(e) => showMegaMenu(e.currentTarget)}
          onMouseLeave={(e) => hideMegaMenu(e.currentTarget)}
        >
         <button
          className='flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-all duration-200 cursor-pointer'
          style={{ pointerEvents: 'auto' }}
         >
          <span>Tài năng</span>
          <ChevronDownIcon className='h-4 w-4 group-hover:rotate-180 transition-transform duration-200' />
         </button>

         {/* Talent Mega Menu */}
         <div
          className='mega-menu absolute left-0 top-full mt-2 w-screen max-w-4xl bg-white rounded-lg shadow-2xl border border-gray-200 transition-all duration-200 z-[99999]'
          style={{
            position: 'absolute',
            pointerEvents: 'auto',
            left: '0',
            top: '100%',
            marginTop: '8px',
            opacity: '0',
            visibility: 'hidden',
            transform: 'translateY(8px)'
          }}
         >
          <div className='p-8'>
           <div className='grid grid-cols-2 gap-8'>
            {talentMegaMenu.categories.map((category) => (
             <div key={category.title} className='space-y-4'>
              <h3 className='text-sm font-semibold text-gray-900 border-b border-gray-100 pb-2'>
               {category.title}
              </h3>
              <ul className='space-y-2'>
               {category.items.map((item) => (
                <li key={item.name}>
                 <Link
                  to={item.href}
                  className='block text-sm hover:bg-blue-50 p-3 rounded-lg transition-colors duration-200'
                 >
                  <div className='flex justify-between items-start'>
                   <div>
                    <div className='font-medium text-gray-900 hover:text-blue-600'>{item.name}</div>
                    <div className='text-xs text-gray-500 mt-1'>{item.count} freelancers</div>
                   </div>
                   {item.rate && (
                    <div className='text-xs text-green-600 font-medium'>{item.rate}</div>
                   )}
                  </div>
                 </Link>
                </li>
               ))}
              </ul>
             </div>
            ))}
           </div>

           {/* Featured Section */}
           <div className='mt-8 pt-6 border-t border-gray-200'>
            <div className='grid grid-cols-3 gap-4'>
             {talentMegaMenu.featured.map((item) => (
              <Link
               key={item.name}
               to={item.href}
               className='p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg hover:from-green-100 hover:to-emerald-100 border border-green-100 hover:border-green-200 transition-all duration-200'
              >
               <div className='font-medium text-gray-900'>{item.name}</div>
               <div className='text-sm text-gray-600 mt-1'>{item.description}</div>
              </Link>
             ))}
            </div>
           </div>
          </div>
         </div>
        </div>

        {/* Company Mega Menu */}
        <div
          className='relative group hover:bg-gray-50'
          style={{ position: 'relative' }}
          onMouseEnter={(e) => showMegaMenu(e.currentTarget)}
          onMouseLeave={(e) => hideMegaMenu(e.currentTarget)}
        >
         <button
          className='flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-all duration-200 cursor-pointer'
          style={{ pointerEvents: 'auto' }}
         >
          <span>Công ty</span>
          <ChevronDownIcon className='h-4 w-4 group-hover:rotate-180 transition-transform duration-200' />
         </button>

         {/* Company Mega Menu */}
         <div
          className='mega-menu absolute left-0 top-full mt-2 w-screen max-w-3xl bg-white rounded-lg shadow-2xl border border-gray-200 transition-all duration-200 z-[99999]'
          style={{
            position: 'absolute',
            pointerEvents: 'auto',
            left: '0',
            top: '100%',
            marginTop: '8px',
            opacity: '0',
            visibility: 'hidden',
            transform: 'translateY(8px)'
          }}
         >
          <div className='p-8'>
           <div className='grid grid-cols-2 gap-8'>
            {companyMegaMenu.categories.map((category) => (
             <div key={category.title} className='space-y-4'>
              <h3 className='text-sm font-semibold text-gray-900 border-b border-gray-100 pb-2'>
               {category.title}
              </h3>
              <ul className='space-y-2'>
               {category.items.map((item) => (
                <li key={item.name}>
                 <Link
                  to={item.href}
                  className='block text-sm hover:bg-blue-50 p-3 rounded-lg transition-colors duration-200'
                 >
                  <div className='font-medium text-gray-900 hover:text-blue-600'>{item.name}</div>
                  <div className='text-xs text-gray-500 mt-1'>{item.description}</div>
                 </Link>
                </li>
               ))}
              </ul>
             </div>
            ))}
           </div>

           {/* Featured Section */}
           <div className='mt-8 pt-6 border-t border-gray-200'>
            <div className='grid grid-cols-3 gap-4'>
             {companyMegaMenu.featured.map((item) => (
              <Link
               key={item.name}
               to={item.href}
               className='p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg hover:from-purple-100 hover:to-pink-100 border border-purple-100 hover:border-purple-200 transition-all duration-200'
              >
               <div className='font-medium text-gray-900'>{item.name}</div>
               <div className='text-sm text-gray-600 mt-1'>{item.description}</div>
              </Link>
             ))}
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </nav>
     )}

     {/* Actions section */}
     {!criticalOnly && (
      <div ref={actionsRef} className='header-actions-section'>
       {/* Expanded Search Bar - Upwork Style */}
       {!hideSearch && (
        <div className='hidden lg:block relative flex-1 max-w-md mx-4'>
         <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
          <MagnifyingGlassIcon className='h-5 w-5 text-gray-400' />
         </div>
         <input
          type='text'
          placeholder='Tìm kiếm dự án, freelancer, kỹ năng...'
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          className='block w-full pl-12 pr-4 py-3 border border-gray-300 rounded-full text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 transition-all duration-200 shadow-sm hover:shadow-md'
         />
         {/* Search suggestions dropdown */}
         {searchQuery && (
          <div className='absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50'>
           <div className='px-4 py-2 text-xs text-gray-500 font-medium'>Gợi ý tìm kiếm</div>
           <div className='px-4 py-2 hover:bg-gray-50 cursor-pointer text-sm'>
            <MagnifyingGlassIcon className='h-4 w-4 inline mr-2 text-gray-400' />
            {searchQuery} trong dự án
           </div>
           <div className='px-4 py-2 hover:bg-gray-50 cursor-pointer text-sm'>
            <MagnifyingGlassIcon className='h-4 w-4 inline mr-2 text-gray-400' />
            {searchQuery} trong freelancer
           </div>
          </div>
         )}
        </div>
       )}

       {/* Language Selector */}
       {!hideLanguage && (
        <div className='relative flex-shrink-0' ref={languageMenuRef} style={{ minWidth: '70px' }}>
         <button
          onClick={() => setIsLanguageMenuOpen(!isLanguageMenuOpen)}
          className='flex items-center space-x-1 px-2 lg:px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-gray-200 hover:border-gray-300'
         >
          <GlobeAltIcon className='h-4 w-4' />
          <span className='hidden lg:block font-medium text-xs'>
           {language === 'vi' ? 'VI' : 'EN'}
          </span>
          <ChevronDownIcon className='h-3 w-3 hidden lg:block' />
         </button>

         <AnimatePresence>
          {isLanguageMenuOpen ? (
           <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1'
           >
            {languageOptions.map(option => (
             <button
              key={option.code}
              onClick={() => {
               changeLanguage(option.code);
               setIsLanguageMenuOpen(false);
              }}
              className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 flex items-center space-x-2 transition-colors duration-200 ${
               language === option.code
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700'
              }`}
             >
              <span>{option.flag}</span>
              <span>{option.name}</span>
             </button>
            ))}
           </motion.div>
          ) : null}
         </AnimatePresence>
        </div>
       )}

       {/* Theme Toggle */}


       {/* Post Job Button */}
       {isAuthenticated && (
        <Link
         to="/jobs/create"
         className="hidden lg:block px-3 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
        >
         + Đăng việc
        </Link>
       )}

       {/* User Menu */}
       {isAuthenticated ? (
        <div className='relative flex-shrink-0' ref={userMenuRef}>
         <button
          onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
          className='flex items-center space-x-1 px-1 lg:px-2 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 rounded-lg hover:bg-gray-50 transition-all duration-200 border border-gray-200 hover:border-gray-300'
         >
          <UserCircleIcon className='h-5 w-5' />
          <span className='hidden lg:block font-medium text-xs'>
           {user?.displayName || translate('user')}
          </span>
          <ChevronDownIcon className='h-3 w-3 hidden lg:block' />
         </button>

         <AnimatePresence>
          {isUserMenuOpen ? (
           <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1'
           >
            <Link
             to='/dashboard'
             className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
             onClick={() => setIsUserMenuOpen(false)}
            >
             {translate('dashboard')}
            </Link>
            <Link
             to="/jobs/create"
             className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
             onClick={() => setIsUserMenuOpen(false)}
            >
             + Đăng việc
            </Link>
            <Link
             to='/settings'
             className='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
             onClick={() => setIsUserMenuOpen(false)}
            >
             {translate('settings')}
            </Link>
            <button
             onClick={handleLogout}
             className='block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50'
            >
             {translate('signOut')}
            </button>
           </motion.div>
          ) : null}
         </AnimatePresence>
        </div>
       ) : (
        <div className='flex items-center gap-1 flex-shrink-0'>
         {!hideSignIn && (
          <Link
           to='/auth?mode=signin'
           className='hidden lg:block px-2 lg:px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200 rounded-lg hover:bg-gray-50 header-button-priority'
          >
           {translate('signIn')}
          </Link>
         )}

         <Link
          to='/auth?mode=signup'
          className='px-3 lg:px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md header-critical-button btn-auto-scale'
         >
          {translate('getStarted')}
         </Link>
        </div>
       )}

       {/* Mobile menu button */}
       <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className='lg:hidden p-1 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-all duration-200 border border-gray-200 hover:border-gray-300 flex-shrink-0'
       >
        {isMobileMenuOpen ? (
         <XMarkIcon className='h-4 w-4' />
        ) : (
         <Bars3Icon className='h-4 w-4' />
        )}
       </button>
      </div>
     )}
    </div>

    {/* Mobile Navigation */}
    <AnimatePresence>
     {isMobileMenuOpen ? (
      <motion.div
       initial={{ opacity: 0, height: 0 }}
       animate={{ opacity: 1, height: 'auto' }}
       exit={{ opacity: 0, height: 0 }}
       transition={{ duration: 0.3 }}
       className='lg:hidden border-t border-gray-200 pt-6 pb-6'
      >
       <div className='space-y-3'>
        {[
          ...findWorkMegaMenu.featured,
          ...talentMegaMenu.featured,
          ...companyMegaMenu.featured
        ].map(item => (
         <Link
          key={item.name}
          to={item.href}
          className={`block px-4 py-3 text-base font-medium rounded-xl transition-all duration-200 ${
           item.current
            ? 'text-blue-600 bg-blue-50 shadow-sm'
            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
          }`}
          onClick={() => setIsMobileMenuOpen(false)}
         >
          {item.name}
         </Link>
        ))}

        {/* Mobile Search */}
        <div className='pt-4 border-t border-gray-200'>
         <div className='relative'>
          <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
           <MagnifyingGlassIcon className='h-5 w-5 text-gray-400' />
          </div>
          <input
           type='text'
           placeholder={translate('searchProjectsFreelancers')}
           value={searchQuery}
           onChange={e => setSearchQuery(e.target.value)}
           className='block w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-900'
          />
         </div>
        </div>

        {/* Mobile Auth Buttons */}
        <div className='pt-4 border-t border-gray-200'>
         <div className='space-y-3'>
          {isAuthenticated && (
           <Link
            to="/jobs/create"
            className="block w-full px-4 py-3 text-base font-medium text-white bg-green-600 hover:bg-green-700 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md"
            onClick={() => setIsMobileMenuOpen(false)}
           >
            + Đăng việc
           </Link>
          )}
          <Link
           to='/auth?mode=signin'
           className='block w-full px-4 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-xl transition-all duration-200 border border-gray-200'
           onClick={() => setIsMobileMenuOpen(false)}
          >
           {translate('signIn')}
          </Link>
          <Link
           to='/auth?mode=signup'
           className='block w-full px-4 py-3 text-base font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md'
           onClick={() => setIsMobileMenuOpen(false)}
          >
           {translate('getStarted')}
          </Link>
         </div>
        </div>


       </div>
      </motion.div>
     ) : null}
    </AnimatePresence>
   </div>
  </header>
 );
};

export default AppleHeader;
