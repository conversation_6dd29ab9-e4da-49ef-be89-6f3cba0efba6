/**
 * Authentication Middleware for User Service
 * Handles Firebase token verification and user authentication
 */

const { verifyIdToken } = require('../config/firebase');

/**
 * Verify Firebase ID token middleware
 */
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    // Debug logs for development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Auth Debug:', {
        hasAuthHeader: !!authHeader,
        authHeader: authHeader ? authHeader.substring(0, 20) + '...' : 'none',
        headers: Object.keys(req.headers)
      });
    }
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'No token provided or invalid format. Expected: Bearer <token>',
        code: 'NO_TOKEN'
      });
    }

    const token = authHeader.split('Bearer ')[1];
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'No token provided',
        code: 'EMPTY_TOKEN'
      });
    }

    // Verify the Firebase token
    const verificationResult = await verifyIdToken(token);
    
    if (!verificationResult.success) {
      console.error('❌ Token verification failed:', verificationResult.error);
      
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'Invalid or expired token',
        code: verificationResult.code || 'INVALID_TOKEN',
        details: process.env.NODE_ENV === 'development' ? verificationResult.error : undefined
      });
    }

    // Add user info to request with consistent structure
    const firebaseUser = verificationResult.user;
    req.user = {
      // Primary identifiers
      id: firebaseUser.uid, // For backward compatibility
      uid: firebaseUser.uid,
      
      // Basic info
      email: firebaseUser.email,
      emailVerified: firebaseUser.emailVerified,
      displayName: firebaseUser.displayName || firebaseUser.name || 'User',
      photoURL: firebaseUser.photoURL,
      phoneNumber: firebaseUser.phoneNumber,
      
      // Firebase specific
      customClaims: firebaseUser.customClaims,
      firebase: firebaseUser.firebase,
      
      // Additional metadata
      isFirebaseUser: true,
      authProvider: 'firebase',
      tokenData: verificationResult.token
    };

    console.log('✅ Firebase token verified successfully:', {
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: req.user.displayName
    });
    
    next();

  } catch (error) {
    console.error('❌ Authentication middleware error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Authentication Error',
      message: 'Internal authentication error',
      code: 'AUTH_ERROR',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Optional authentication middleware
 * Allows requests to proceed even without valid token, but verifies token if present
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      req.user = null;
      return next();
    }

    const token = authHeader.split('Bearer ')[1];
    
    if (!token) {
      req.user = null;
      return next();
    }

    // Verify the Firebase token
    const verificationResult = await verifyIdToken(token);
    
    if (verificationResult.success) {
      // Add user info to request if token is valid
      const firebaseUser = verificationResult.user;
      req.user = {
        id: firebaseUser.uid,
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        emailVerified: firebaseUser.emailVerified,
        displayName: firebaseUser.displayName || firebaseUser.name || 'User',
        photoURL: firebaseUser.photoURL,
        phoneNumber: firebaseUser.phoneNumber,
        customClaims: firebaseUser.customClaims,
        firebase: firebaseUser.firebase,
        isFirebaseUser: true,
        authProvider: 'firebase',
        tokenData: verificationResult.token
      };

      console.log('✅ Optional auth: Firebase token verified successfully:', {
        uid: firebaseUser.uid,
        displayName: req.user.displayName
      });
    } else {
      console.log('⚠️ Optional auth: Token verification failed, proceeding without auth:', verificationResult.error);
      req.user = null;
    }
    
    next();

  } catch (error) {
    console.log('⚠️ Optional auth: Authentication error, proceeding without auth:', error.message);
    req.user = null;
    next();
  }
};

/**
 * Require authentication - returns 401 if not authenticated
 */
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }
  next();
};

/**
 * Require user ownership - checks if authenticated user owns the resource
 */
const requireOwnership = (paramName = 'id', userField = 'uid') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const resourceId = req.params[paramName];
    const userId = req.user[userField];

    if (resourceId !== userId) {
      console.warn('⚠️ Ownership check failed:', {
        resourceId,
        userId,
        userField,
        paramName
      });

      return res.status(403).json({
        success: false,
        error: 'Forbidden',
        message: 'You can only access your own resources',
        code: 'OWNERSHIP_REQUIRED'
      });
    }

    next();
  };
};

/**
 * Require admin role
 */
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  const customClaims = req.user.customClaims || {};
  const isAdmin = customClaims.admin === true || customClaims.role === 'admin';

  if (!isAdmin) {
    return res.status(403).json({
      success: false,
      error: 'Forbidden',
      message: 'Admin access required',
      code: 'ADMIN_REQUIRED'
    });
  }

  next();
};

/**
 * Check user type (freelancer, client, admin)
 */
const requireUserType = (allowedTypes) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const customClaims = req.user.customClaims || {};
    const userType = customClaims.userType || 'freelancer';

    if (!allowedTypes.includes(userType)) {
      return res.status(403).json({
        success: false,
        error: 'Forbidden',
        message: `Access restricted to: ${allowedTypes.join(', ')}`,
        code: 'USER_TYPE_REQUIRED',
        requiredTypes: allowedTypes,
        userType
      });
    }

    next();
  };
};

/**
 * Rate limiting middleware
 */
const rateLimit = (windowMs = 15 * 60 * 1000, maxRequests = 100) => {
  const requests = new Map();

  return (req, res, next) => {
    const identifier = req.user?.uid || req.ip;
    const now = Date.now();
    
    // Clean old entries
    const windowStart = now - windowMs;
    if (requests.has(identifier)) {
      const userRequests = requests.get(identifier).filter(time => time > windowStart);
      requests.set(identifier, userRequests);
    }

    // Check rate limit
    const userRequests = requests.get(identifier) || [];
    
    if (userRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        error: 'Too Many Requests',
        message: `Rate limit exceeded. Max ${maxRequests} requests per ${windowMs / 1000 / 60} minutes`,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((userRequests[0] + windowMs - now) / 1000)
      });
    }

    // Add current request
    userRequests.push(now);
    requests.set(identifier, userRequests);

    next();
  };
};

module.exports = {
  verifyFirebaseToken,
  optionalAuth,
  requireAuth,
  requireOwnership,
  requireAdmin,
  requireUserType,
  rateLimit
}; 