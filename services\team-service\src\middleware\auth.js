/**
 * Authentication Middleware for Team Service
 */

const { verifyToken } = require('../config/firebase');
const logger = require('../utils/logger');

// Verify Firebase token middleware
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.apiUnauthorized('No token provided');
    }

    const decodedToken = await verifyToken(token);
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.name || decodedToken.email,
      userType: decodedToken.userType || 'freelancer'
    };

    logger.info(`User authenticated: ${req.user.uid}`);
    next();
  } catch (error) {
    logger.error('Token verification failed:', error.message);
    res.apiUnauthorized('Invalid token');
  }
};

// Optional authentication middleware
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return next(); // Continue without user data
    }

    const decodedToken = await verifyToken(token);
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.name || decodedToken.email,
      userType: decodedToken.userType || 'freelancer'
    };

    logger.info(`Optional auth successful: ${req.user.uid}`);
    next();
  } catch (error) {
    logger.warn('Optional auth failed, continuing without user:', error.message);
    next(); // Continue without user data
  }
};

// Require team ownership middleware
const requireTeamOwnership = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.apiUnauthorized('Authentication required');
    }

    const { teamId } = req.params;
    const { query } = require('../config/postgresql');

    // Check if user is team leader
    const result = await query(
      'SELECT leader_id FROM teams WHERE id = $1',
      [teamId]
    );

    if (result.rows.length === 0) {
      return res.apiNotFound('Team not found');
    }

    if (result.rows[0].leader_id !== req.user.uid) {
      return res.apiForbidden('Only team leader can perform this action');
    }

    next();
  } catch (error) {
    logger.error('Team ownership check failed:', error.message);
    res.apiError('Failed to verify team ownership', 'OWNERSHIP_CHECK_ERROR', 500);
  }
};

// Require team membership middleware
const requireTeamMembership = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.apiUnauthorized('Authentication required');
    }

    const { teamId } = req.params;
    const { query } = require('../config/postgresql');

    // Check if user is team member
    const result = await query(
      'SELECT * FROM team_members WHERE team_id = $1 AND user_id = $2 AND status = $3',
      [teamId, req.user.uid, 'active']
    );

    if (result.rows.length === 0) {
      return res.apiForbidden('You must be a team member to perform this action');
    }

    req.teamMember = result.rows[0];
    next();
  } catch (error) {
    logger.error('Team membership check failed:', error.message);
    res.apiError('Failed to verify team membership', 'MEMBERSHIP_CHECK_ERROR', 500);
  }
};

module.exports = {
  verifyFirebaseToken,
  optionalAuth,
  requireTeamOwnership,
  requireTeamMembership
}; 