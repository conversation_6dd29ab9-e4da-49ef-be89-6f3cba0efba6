import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaCog, FaBell, FaEnvelope, FaMobile, FaPalette, FaGlobe } from 'react-icons/fa';

const PreferencesStep = () => {
 const { 
  onboardingData, 
  updateOnboardingData, 
  goToPreviousStep,
  completeOnboarding,
  loading 
 } = useOnboarding();
 const { t, language, changeLanguage } = useLanguage();

 const [preferences, setPreferences] = useState({
  emailNotifications: true,
  pushNotifications: true,
  marketingEmails: false,
  projectUpdates: true,
  messageNotifications: true,
  theme: 'medieval',
  language: language,
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  currency: 'USD'
 });

 useEffect(() => {
  if (onboardingData.preferences) {
   setPreferences(prev => ({
    ...prev,
    ...onboardingData.preferences
   }));
  }
 }, [onboardingData]);

 const handleToggle = (field) => {
  setPreferences(prev => ({
   ...prev,
   [field]: !prev[field]
  }));
 };

 const handleSelect = (field, value) => {
  setPreferences(prev => ({
   ...prev,
   [field]: value
  }));

  // Update language immediately if changed
  if (field === 'language') {
   changeLanguage(value);
  }
 };

 const handleComplete = async () => {
  updateOnboardingData({
   preferences
  });
  
  const result = await completeOnboarding();
  if (result.success) {
   // Redirect will be handled by the auth context
   window.location.href = '/dashboard';
  }
 };

 const notificationOptions = [
  {
   key: 'emailNotifications',
   icon: FaEnvelope,
   title: t('emailNotifications'),
   description: t('emailNotificationsDesc')
  },
  {
   key: 'pushNotifications',
   icon: FaMobile,
   title: t('pushNotifications'),
   description: t('pushNotificationsDesc')
  },
  {
   key: 'projectUpdates',
   icon: FaBell,
   title: t('projectUpdates'),
   description: t('projectUpdatesDesc')
  },
  {
   key: 'messageNotifications',
   icon: FaBell,
   title: t('messageNotifications'),
   description: t('messageNotificationsDesc')
  },
  {
   key: 'marketingEmails',
   icon: FaEnvelope,
   title: t('marketingEmails'),
   description: t('marketingEmailsDesc')
  }
 ];

 const themeOptions = [
  { value: 'medieval', label: t('medievalTheme'), preview: 'bg-gradient-to-r from-medieval-brown-500 to-medieval-gold-500' },
  { value: 'modern', label: t('modernTheme'), preview: 'bg-gradient-to-r from-blue-500 to-purple-500' },
  { value: 'dark', label: t('darkTheme'), preview: 'bg-gradient-to-r from-gray-800 to-gray-900' }
 ];

 const languageOptions = [
  { value: 'vi', label: 'Tiếng Việt', flag: '🇻🇳' },
  { value: 'en', label: 'English', flag: '🇺🇸' }
 ];

 const currencyOptions = [
  { value: 'USD', label: 'USD ($)', symbol: '$' },
  { value: 'VND', label: 'VND (₫)', symbol: '₫' },
  { value: 'EUR', label: 'EUR (€)', symbol: '€' }
 ];

 return (
  <div className="p-8 md:p-12">
   {/* Header */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
   >
    <div className="w-16 h-16 bg-gradient-to-br from-medieval-gold-400 to-medieval-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
     <FaCog className="text-2xl text-white" />
    </div>
    
    <h2 className="text-2xl md:text-3xl font-medium font-bold text-gray-800 mb-2">
     {t('preferences')}
    </h2>
    
    <p className="text-gray-600 font-medium">
     {t('preferencesDescription')}
    </p>
   </motion.div>

   <div className="max-w-3xl mx-auto space-y-8">
    {/* Notifications */}
    <motion.div
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     transition={{ delay: 0.2 }}
    >
     <h3 className="font-medium font-semibold text-gray-800 mb-4 flex items-center">
      <FaBell className="mr-2" />
      {t('notificationSettings')}
     </h3>
     
     <div className="space-y-4">
      {notificationOptions.map((option, index) => (
       <motion.div
        key={option.key}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.3 + index * 0.1 }}
        className="flex items-center justify-between p-4 bg-medieval-brown-50 rounded-lg border border-gray-200"
       >
        <div className="flex items-center">
         <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
          <option.icon className="text-medieval-gold-600" />
         </div>
         <div>
          <h4 className="font-medium font-medium text-gray-800">
           {option.title}
          </h4>
          <p className="text-sm text-gray-600">
           {option.description}
          </p>
         </div>
        </div>
        
        <button
         onClick={() => handleToggle(option.key)}
         className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          preferences[option.key] ? 'bg-blue-500' : 'bg-medieval-brown-300'
         }`}
        >
         <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
           preferences[option.key] ? 'translate-x-6' : 'translate-x-1'
          }`}
         />
        </button>
       </motion.div>
      ))}
     </div>
    </motion.div>

    {/* Appearance */}
    <motion.div
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     transition={{ delay: 0.4 }}
    >
     <h3 className="font-medium font-semibold text-gray-800 mb-4 flex items-center">
      <FaPalette className="mr-2" />
      {t('appearance')}
     </h3>
     
     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {themeOptions.map((theme) => (
       <button
        key={theme.value}
        onClick={() => handleSelect('theme', theme.value)}
        className={`p-4 rounded-lg border-2 transition-all ${
         preferences.theme === theme.value
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-gray-300'
        }`}
       >
        <div className={`w-full h-8 rounded mb-2 ${theme.preview}`}></div>
        <p className="font-medium text-sm">{theme.label}</p>
       </button>
      ))}
     </div>
    </motion.div>

    {/* Language & Region */}
    <motion.div
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     transition={{ delay: 0.6 }}
    >
     <h3 className="font-medium font-semibold text-gray-800 mb-4 flex items-center">
      <FaGlobe className="mr-2" />
      {t('languageRegion')}
     </h3>
     
     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Language */}
      <div>
       <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
        {t('language')}
       </label>
       <div className="space-y-2">
        {languageOptions.map((lang) => (
         <button
          key={lang.value}
          onClick={() => handleSelect('language', lang.value)}
          className={`w-full p-3 rounded-lg border-2 transition-all flex items-center ${
           preferences.language === lang.value
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-200 hover:border-gray-300'
          }`}
         >
          <span className="text-xl mr-3">{lang.flag}</span>
          <span className="font-medium">{lang.label}</span>
         </button>
        ))}
       </div>
      </div>

      {/* Currency */}
      <div>
       <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
        {t('currency')}
       </label>
       <select
        value={preferences.currency}
        onChange={(e) => handleSelect('currency', e.target.value)}
        className="form-input w-full"
       >
        {currencyOptions.map((currency) => (
         <option key={currency.value} value={currency.value}>
          {currency.label}
         </option>
        ))}
       </select>
      </div>
     </div>
    </motion.div>
   </div>

   {/* Navigation */}
   <motion.div
    className="flex justify-between mt-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.8 }}
   >
    <button
     onClick={goToPreviousStep}
     disabled={loading}
     className="btn-secondary px-6 py-2 font-medium"
    >
     {t('previous')}
    </button>

    <button
     onClick={handleComplete}
     disabled={loading}
     className="btn-primary px-8 py-3 text-lg font-medium font-semibold"
    >
     {loading ? t('completing') : t('completeSetup')}
    </button>
   </motion.div>
  </div>
 );
};

export default PreferencesStep;
