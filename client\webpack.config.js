/**
 * Webpack configuration for bundle optimization
 * This extends Create React App's webpack config
 */

const path = require('path');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const CompressionPlugin = require('compression-webpack-plugin');

module.exports = function override(config, env) {
  // Only apply optimizations in production
  if (env === 'production') {
    // Bundle splitting optimization
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          // Vendor chunk for third-party libraries
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
            reuseExistingChunk: true,
          },
          // Common chunk for shared code
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true,
          },
          // React chunk
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 20,
          },
          // UI libraries chunk
          ui: {
            test: /[\\/]node_modules[\\/](@headlessui|@heroicons|lucide-react|framer-motion)[\\/]/,
            name: 'ui-libs',
            chunks: 'all',
            priority: 15,
          },
          // Animation libraries chunk
          animations: {
            test: /[\\/]node_modules[\\/](gsap|three)[\\/]/,
            name: 'animations',
            chunks: 'all',
            priority: 15,
          },
          // Firebase chunk
          firebase: {
            test: /[\\/]node_modules[\\/](firebase)[\\/]/,
            name: 'firebase',
            chunks: 'all',
            priority: 15,
          },
        },
      },
    };

    // Add compression plugin
    config.plugins.push(
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|html|svg)$/,
        threshold: 8192,
        minRatio: 0.8,
      })
    );

    // Add bundle analyzer in analyze mode
    if (process.env.ANALYZE) {
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: 'bundle-report.html',
        })
      );
    }
  }

  // Resolve aliases for cleaner imports
  config.resolve.alias = {
    ...config.resolve.alias,
    '@': path.resolve(__dirname, 'src'),
    '@components': path.resolve(__dirname, 'src/components'),
    '@pages': path.resolve(__dirname, 'src/pages'),
    '@utils': path.resolve(__dirname, 'src/utils'),
    '@hooks': path.resolve(__dirname, 'src/hooks'),
    '@contexts': path.resolve(__dirname, 'src/contexts'),
    '@services': path.resolve(__dirname, 'src/services'),
    '@styles': path.resolve(__dirname, 'src/styles'),
    '@assets': path.resolve(__dirname, 'src/assets'),
  };

  // Optimize module resolution
  config.resolve.modules = [
    path.resolve(__dirname, 'src'),
    'node_modules'
  ];

  // Add performance hints
  config.performance = {
    ...config.performance,
    maxAssetSize: 250000,
    maxEntrypointSize: 250000,
    hints: env === 'production' ? 'warning' : false,
  };

  // Tree shaking optimization
  config.optimization.usedExports = true;
  config.optimization.sideEffects = false;

  // Add source map optimization for production
  if (env === 'production') {
    config.devtool = 'source-map';
  }

  return config;
};

// Package.json scripts to add:
/*
{
  "scripts": {
    "analyze": "ANALYZE=true npm run build",
    "build:stats": "npm run build -- --stats",
    "serve": "serve -s build -l 3000"
  },
  "devDependencies": {
    "webpack-bundle-analyzer": "^4.9.0",
    "compression-webpack-plugin": "^10.0.0",
    "serve": "^14.2.0"
  }
}
*/
