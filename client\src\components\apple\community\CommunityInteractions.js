import React, { useState, useEffect } from 'react';
import {
 HeartI<PERSON>,
 ChatBubbleLeftRightIcon,
 ShareIcon,
 BookmarkIcon,
 EyeIcon,
 HandThumbUpIcon,
 HandThumbDownIcon,
 FaceSmileIcon,
 GiftIcon,
 BoltIcon,
} from '@heroicons/react/24/outline';
import {
 HeartIcon as HeartSolidIcon,
 BookmarkIcon as BookmarkSolidIcon,
 HandThumbUpIcon as HandThumbUpSolidIcon,
 HandThumbDownIcon as HandThumbDownSolidIcon,
} from '@heroicons/react/24/solid';
import { likesApi, communityUtils } from '../../../services/communityApiService';
import { toast } from 'react-hot-toast';

const CommunityInteractions = ({
 post,
 onLike,
 onBookmark,
 onShare,
 onReaction,
 userInteractions = {},
 authState = { isAuthenticated: false, canInteract: false }
}) => {
 // Debug logs for like state
 console.log(`🎯 CommunityInteractions render for post ${post.id}:`, {
  liked: userInteractions?.liked,
  likeCount: post.likes || post.like_count,
  userInteractions,
  authState: { isAuthenticated: authState.isAuthenticated, canInteract: authState.canInteract }
 });
 const [showReactions, setShowReactions] = useState(false);
 const [animatingReaction, setAnimatingReaction] = useState(null);

 const reactions = [
  { emoji: '👍', name: 'like', color: 'text-blue-500' },
  { emoji: '❤️', name: 'love', color: 'text-red-500' },
  { emoji: '😂', name: 'laugh', color: 'text-yellow-500' },
  { emoji: '😮', name: 'wow', color: 'text-purple-500' },
  { emoji: '😢', name: 'sad', color: 'text-gray-500' },
  { emoji: '😡', name: 'angry', color: 'text-red-600' },
  { emoji: '🔥', name: 'fire', color: 'text-orange-500' },
  { emoji: '💯', name: 'hundred', color: 'text-green-500' },
 ];

 const formatNumber = (num) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}k`;
  return num.toString();
 };

 const handleReaction = (reaction) => {
  setAnimatingReaction(reaction.emoji);
  onReaction(post.id, reaction);
  setShowReactions(false);
  
  // Reset animation after delay
  setTimeout(() => setAnimatingReaction(null), 1000);
 };

 const handleShare = async () => {
  if (navigator.share) {
   try {
    await navigator.share({
     title: post.title,
     text: post.content.substring(0, 100) + '...',
     url: window.location.href + `/post/${post.id}`,
    });
   } catch (error) {
    console.log('Error sharing:', error);
   }
  } else {
   // Fallback: copy to clipboard
   navigator.clipboard.writeText(window.location.href + `/post/${post.id}`);
   // Show toast notification
   alert('Link copied to clipboard!');
  }
  onShare(post.id);
 };

 return (
  <div className="flex items-center justify-between p-4 bg-gray-50 border-t border-gray-100">
   {/* Main interaction buttons */}
   <div className="flex items-center space-x-4">
    {/* Like Button with Animation */}
    <div className="relative">
     <button
      onClick={() => onLike(post.id)}
      disabled={!authState.canInteract}
      className={`group flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 ${
       !authState.canInteract
        ? 'text-gray-400 cursor-not-allowed opacity-60'
        : userInteractions.liked
        ? 'text-red-500 bg-red-50 scale-105'
        : 'text-gray-600 hover:text-red-500 hover:bg-red-50 hover:scale-105'
      }`}
      title={!authState.canInteract ? 'Đăng nhập để thích bài viết' : ''}
     >
      {userInteractions.liked ? (
       <HeartSolidIcon className="h-5 w-5 animate-pulse" />
      ) : (
       <HeartIcon className="h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
      )}
      <span className="font-medium">{formatNumber(post.likes || post.like_count || 0)}</span>
     </button>
     {!authState.isAuthenticated && (
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse" title="Cần đăng nhập" />
     )}
    </div>





    {/* Reactions Dropdown */}
    <div className="relative">
     <button
      onClick={() => setShowReactions(!showReactions)}
      disabled={!authState.canInteract}
      className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 ${
       !authState.canInteract
        ? 'text-gray-400 cursor-not-allowed opacity-60'
        : 'text-gray-600 hover:text-yellow-600 hover:bg-yellow-50'
      }`}
      title={!authState.canInteract ? 'Đăng nhập để thả cảm xúc' : ''}
     >
      <FaceSmileIcon className="h-5 w-5" />
      {animatingReaction && (
       <span className="text-lg animate-bounce">{animatingReaction}</span>
      )}
     </button>

     {/* Reactions Popup */}
     {showReactions && (
      <div className="absolute bottom-full left-0 mb-2 bg-white rounded-xl shadow-lg border border-gray-200 p-2 flex space-x-1 z-10">
       {reactions.map((reaction) => (
        <button
         key={reaction.name}
         onClick={() => handleReaction(reaction)}
         className="w-10 h-10 rounded-lg hover:bg-gray-100 flex items-center justify-center text-xl hover:scale-125 transition-all duration-200"
         title={reaction.name}
        >
         {reaction.emoji}
        </button>
       ))}
      </div>
     )}
    </div>

    {/* Share */}
    <button
     onClick={handleShare}
     disabled={!authState.canInteract}
     className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 ${
      !authState.canInteract
       ? 'text-gray-400 cursor-not-allowed opacity-60'
       : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
     }`}
     title={!authState.canInteract ? 'Đăng nhập để chia sẻ bài viết' : ''}
    >
     <ShareIcon className="h-5 w-5" />
     <span className="font-medium">{formatNumber(post.shares || 0)}</span>
    </button>
   </div>

   {/* Right side actions */}
   <div className="flex items-center space-x-4">
    {/* Views */}
    <div className="flex items-center space-x-1 text-gray-500">
     <EyeIcon className="h-4 w-4" />
     <span className="text-sm">{formatNumber(post.views)}</span>
    </div>

    {/* Bookmark */}
    <button
     onClick={() => onBookmark(post.id)}
     disabled={!authState.canInteract}
     className={`p-2 rounded-lg transition-all duration-300 ${
      !authState.canInteract
       ? 'text-gray-400 cursor-not-allowed opacity-60'
       : userInteractions.bookmarked
       ? 'text-blue-600 bg-blue-50 scale-110'
       : 'text-gray-400 hover:text-blue-600 hover:bg-blue-50 hover:scale-110'
     }`}
     title={!authState.canInteract ? 'Đăng nhập để lưu bài viết' : ''}
    >
     {userInteractions.bookmarked ? (
      <BookmarkSolidIcon className="h-5 w-5" />
     ) : (
      <BookmarkIcon className="h-5 w-5" />
     )}
    </button>

    {/* Award/Tip Button for quality posts */}
    {post.featured && (
     <button className="flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-lg text-sm font-medium hover:from-yellow-500 hover:to-orange-600 transition-all duration-300 hover:scale-105 shadow-md">
      <GiftIcon className="h-4 w-4" />
      <span>Tip</span>
     </button>
    )}
   </div>

   {/* Reaction Display */}
   {post.reactions && Object.keys(post.reactions).length > 0 && (
    <div className="absolute -top-3 left-4 flex items-center space-x-1 bg-white rounded-full px-3 py-1 shadow-md border border-gray-200">
     {Object.entries(post.reactions).slice(0, 3).map(([emoji, count]) => (
      <div key={emoji} className="flex items-center space-x-1">
       <span className="text-sm">{emoji}</span>
       <span className="text-xs text-gray-600">{count}</span>
      </div>
     ))}
     {Object.keys(post.reactions).length > 3 && (
      <span className="text-xs text-gray-500">
       +{Object.keys(post.reactions).length - 3}
      </span>
     )}
    </div>
   )}

  </div>
 );
};

export default CommunityInteractions;
