import { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { animationController } from '../../../utils/animations';
import { BriefcaseIcon } from '@heroicons/react/24/outline';
import { AppleProjectCard } from '../cards';
import { AppleAnimatedFilter } from '../interactive';
import { useLanguage } from '../../../contexts/LanguageContext';

// Move static data outside component to prevent re-renders
const MOCK_PROJECTS = [
 {
  id: 1,
  title: 'Landing Page - Coffee Shop',
  description:
   'Landing page hiện đại cho quán cà phê, tối ưu mobile, tích hợp đặt bàn online và hiệu ứng động bắt mắt.',
  price: 120,
  images: [
   'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
  ],
  techStack: ['React', '<PERSON><PERSON><PERSON>CSS', 'Framer Motion'],
  freelancer: {
   name: '<PERSON><PERSON><PERSON><PERSON>',
   avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
   rating: 4.9,
   reviewCount: 21,
  },
  soldCount: 8,
  category: 'web',
  postedAt: '2024-06-01T10:00:00Z',
 },
 {
  id: 2,
  title: 'Ứng dụng Quản lý Chi tiêu Cá nhân',
  description:
   'App mobile giúp quản lý chi tiêu, thống kê thu chi, nhắc nhở hóa đơn, giao diện thân thiện, dễ dùng.',
  price: 200,
  images: [
   'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80',
  ],
  techStack: ['React Native', 'Redux', 'Expo'],
  freelancer: {
   name: 'Trần Thị B',
   avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
   rating: 5.0,
   reviewCount: 35,
  },
  soldCount: 15,
  category: 'mobile',
  postedAt: '2024-05-28T15:30:00Z',
 },
 {
  id: 3,
  title: 'Bộ icon vector đa dạng chủ đề',
  description:
   'Gói 120+ icon vector chất lượng cao, phù hợp cho web/app, dễ dàng tuỳ biến màu sắc, kích thước.',
  price: 35,
  images: [
   'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',
  ],
  techStack: ['Illustrator', 'SVG'],
  freelancer: {
   name: 'Lê Quốc C',
   avatar: 'https://randomuser.me/api/portraits/men/65.jpg',
   rating: 4.7,
   reviewCount: 12,
  },
  soldCount: 42,
  category: 'design',
  postedAt: '2024-05-20T09:15:00Z',
 },
 {
  id: 4,
  title: 'Website Portfolio cá nhân',
  description:
   'Website giới thiệu bản thân, CV online, tích hợp hiệu ứng động, tối ưu SEO, responsive mọi thiết bị.',
  price: 90,
  images: [
   'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=400&q=80',
  ],
  techStack: ['Next.js', 'Styled Components'],
  freelancer: {
   name: 'Phạm Minh D',
   avatar: 'https://randomuser.me/api/portraits/men/77.jpg',
   rating: 4.8,
   reviewCount: 18,
  },
  soldCount: 5,
  category: 'web',
  postedAt: '2024-05-18T13:00:00Z',
 },
 {
  id: 5,
  title: 'Template PowerPoint thuyết trình chuyên nghiệp',
  description:
   '20+ slide template đẹp, dễ chỉnh sửa, phù hợp cho startup, giáo dục, kinh doanh.',
  price: 25,
  images: [
   'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=400&q=80',
  ],
  techStack: ['PowerPoint'],
  freelancer: {
   name: 'Vũ Thị E',
   avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
   rating: 4.6,
   reviewCount: 9,
  },
  soldCount: 27,
  category: 'design',
  postedAt: '2024-05-10T08:00:00Z',
 },
];

const FILTER_CONFIG = [
 {
  id: 'category',
  label: 'Category',
  options: [
   { value: 'web-development', label: 'Web Development', count: 45 },
   { value: 'mobile-development', label: 'Mobile Development', count: 32 },
   { value: 'design', label: 'Design', count: 28 },
   { value: 'writing', label: 'Writing', count: 19 },
   { value: 'marketing', label: 'Marketing', count: 23 },
   { value: 'data-science', label: 'Data Science', count: 15 },
  ],
 },
 {
  id: 'budget',
  label: 'Budget Range',
  options: [
   { value: 'under-500', label: 'Under $500', count: 67 },
   { value: '500-1000', label: '$500 - $1,000', count: 45 },
   { value: '1000-5000', label: '$1,000 - $5,000', count: 38 },
   { value: 'over-5000', label: 'Over $5,000', count: 12 },
  ],
 },
 {
  id: 'duration',
  label: 'Project Duration',
  options: [
   { value: 'less-than-week', label: 'Less than a week', count: 23 },
   { value: '1-4-weeks', label: '1-4 weeks', count: 56 },
   { value: '1-3-months', label: '1-3 months', count: 34 },
   { value: 'more-than-3-months', label: 'More than 3 months', count: 19 },
  ],
 },
 {
  id: 'experience',
  label: 'Experience Level',
  options: [
   { value: 'entry-level', label: 'Entry Level', count: 42 },
   { value: 'intermediate', label: 'Intermediate', count: 58 },
   { value: 'expert', label: 'Expert', count: 32 },
  ],
 },
];

const AppleProjectsPage = () => {
 const { t } = useLanguage();
 const [searchQuery, setSearchQuery] = useState('');
 const [activeFilters, setActiveFilters] = useState([]);
 const [filteredProjects, setFilteredProjects] = useState(MOCK_PROJECTS);

 const headerRef = useRef(null);
 const projectsGridRef = useRef(null);
 const previousFilteredLength = useRef(MOCK_PROJECTS.length);

 // Create translated filter config
 const translatedFilterConfig = useMemo(() => [
  {
   id: 'category',
   label: t('category'),
   options: [
    { value: 'web-development', label: t('webDevelopmentCategory'), count: 45 },
    { value: 'mobile-development', label: t('mobileDevelopmentCategory'), count: 32 },
    { value: 'design', label: t('designCategory'), count: 28 },
    { value: 'writing', label: t('writingCategory'), count: 19 },
    { value: 'marketing', label: t('marketingCategory'), count: 23 },
    { value: 'data-science', label: t('dataScience'), count: 15 },
   ],
  },
  {
   id: 'budget',
   label: t('budgetRange'),
   options: [
    { value: 'under-500', label: t('under500'), count: 67 },
    { value: '500-1000', label: t('budget500to1000'), count: 45 },
    { value: '1000-5000', label: t('budget1000to5000'), count: 38 },
    { value: 'over-5000', label: t('over5000'), count: 12 },
   ],
  },
  {
   id: 'duration',
   label: t('projectDuration'),
   options: [
    { value: 'less-than-week', label: t('lessThanWeek'), count: 23 },
    { value: '1-4-weeks', label: t('oneToFourWeeks'), count: 56 },
    { value: '1-3-months', label: t('oneToThreeMonths'), count: 34 },
    { value: 'more-than-3-months', label: t('moreThanThreeMonths'), count: 19 },
   ],
  },
  {
   id: 'experience',
   label: t('experienceLevel'),
   options: [
    { value: 'entry-level', label: t('entryLevel'), count: 42 },
    { value: 'intermediate', label: t('intermediate'), count: 58 },
    { value: 'expert', label: t('expert'), count: 32 },
   ],
  },
 ], [t]);

 // Enhanced animations with ScrollTrigger
 useEffect(() => {
  let scrollTriggerInstance = null;

  if (headerRef.current) {
   animationController.fadeIn(headerRef.current, {
    duration: 0.8,
    y: 30,
   });
  }

  if (projectsGridRef.current) {
   scrollTriggerInstance = ScrollTrigger.create({
    trigger: projectsGridRef.current,
    start: 'top 80%',
    onEnter: () => {
     if (projectsGridRef.current?.children) {
      animationController.staggerGrid(projectsGridRef.current.children, {
       duration: 0.6,
       stagger: {
        amount: 0.8,
        grid: 'auto',
        from: 'start',
       },
      });
     }
    },
   });
  }

  return () => {
   if (scrollTriggerInstance) {
    scrollTriggerInstance.kill();
   }
  };
 }, []);

 // Filter and search logic with useMemo to prevent infinite re-renders
 const filteredProjectsMemo = useMemo(() => {
  let filtered = [...MOCK_PROJECTS];

  // Apply search filter
  if (searchQuery) {
   filtered = filtered.filter(
    project =>
     project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
     project.description
      .toLowerCase()
      .includes(searchQuery.toLowerCase()) ||
     project.techStack.some(skill =>
      skill.toLowerCase().includes(searchQuery.toLowerCase())
     )
   );
  }

  // Apply category filters
  const categoryFilters = activeFilters.filter(f =>
   FILTER_CONFIG[0].options.some(opt => opt.value === f)
  );
  if (categoryFilters.length > 0) {
   filtered = filtered.filter(project =>
    categoryFilters.includes(project.category)
   );
  }

  // Apply budget filters
  const budgetFilters = activeFilters.filter(f =>
   FILTER_CONFIG[1].options.some(opt => opt.value === f)
  );
  if (budgetFilters.length > 0) {
   filtered = filtered.filter(project => {
    const price = project.price;
    return budgetFilters.some(filter => {
     switch (filter) {
      case 'under-500':
       return price < 500;
      case '500-1000':
       return price >= 500 && price <= 1000;
      case '1000-5000':
       return price > 1000 && price <= 5000;
      case 'over-5000':
       return price > 5000;
      default:
       return true;
     }
    });
   });
  }

  return filtered;
 }, [searchQuery, activeFilters]);

 // Initialize filtered projects only once
 useEffect(() => {
  setFilteredProjects(MOCK_PROJECTS);
 }, []);

 // Animate projects when filter changes
 useEffect(() => {
  const hasChanged =
   previousFilteredLength.current !== filteredProjectsMemo.length;

  if (hasChanged && projectsGridRef.current?.children) {
   previousFilteredLength.current = filteredProjectsMemo.length;

   gsap.to(projectsGridRef.current.children, {
    opacity: 0,
    y: 20,
    duration: 0.3,
    stagger: 0.05,
    onComplete: () => {
     setFilteredProjects(filteredProjectsMemo);
     requestAnimationFrame(() => {
      if (projectsGridRef.current?.children) {
       gsap.fromTo(
        projectsGridRef.current.children,
        { opacity: 0, y: 20 },
        {
         opacity: 1,
         y: 0,
         duration: 0.4,
         stagger: 0.05,
         ease: 'power2.out',
        }
       );
      }
     });
    },
   });
  } else if (!hasChanged) {
   setFilteredProjects(filteredProjectsMemo);
  }
 }, [filteredProjectsMemo]);

 const handleFilterChange = useCallback((filterId, clearAll = false) => {
  if (clearAll) {
   setActiveFilters([]);
   return;
  }

  setActiveFilters(prev => {
   if (prev.includes(filterId)) {
    return prev.filter(id => id !== filterId);
   } else {
    return [...prev, filterId];
   }
  });
 }, []);

 const handleSearch = useCallback(query => {
  setSearchQuery(query);
 }, []);

 const handleClearFilters = useCallback(() => {
  handleFilterChange('', true);
 }, [handleFilterChange]);

 return (
  <div className='min-h-screen bg-gray-50 transition-colors duration-300'>
   <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8'>
    {/* Header */}
    <div ref={headerRef} className='text-center mb-12'>
     <h1 className='text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 transition-colors duration-300'>
      Khám phá
      <span className='bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent'> sản phẩm/dự án </span>
      nổi bật từ freelancer
     </h1>
     <p className='text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed transition-colors duration-300'>
      Mua các sản phẩm số, template, ứng dụng, thiết kế... chất lượng, giá tốt, trực tiếp từ freelancer Việt Nam.
     </p>
    </div>

    {/* Filters */}
    <div className='mb-8'>
     <AppleAnimatedFilter
      filterConfig={[
       {
        id: 'category',
        label: 'Lĩnh vực',
        options: [
         { value: 'web', label: 'Web', count: 12 },
         { value: 'mobile', label: 'Mobile', count: 8 },
         { value: 'design', label: 'Thiết kế', count: 15 },
         { value: 'template', label: 'Template', count: 7 },
        ],
       },
       {
        id: 'price',
        label: 'Giá',
        options: [
         { value: 'under-50', label: 'Dưới 50$', count: 20 },
         { value: '50-100', label: '50$ - 100$', count: 10 },
         { value: '100-200', label: '100$ - 200$', count: 7 },
         { value: 'over-200', label: 'Trên 200$', count: 5 },
        ],
       },
       {
        id: 'techStack',
        label: 'Công nghệ',
        options: [
         { value: 'React', label: 'React', count: 8 },
         { value: 'Next.js', label: 'Next.js', count: 4 },
         { value: 'React Native', label: 'React Native', count: 3 },
         { value: 'Illustrator', label: 'Illustrator', count: 2 },
         { value: 'PowerPoint', label: 'PowerPoint', count: 2 },
        ],
       },
      ]}
      activeFilters={activeFilters}
      onFilterChange={handleFilterChange}
      onSearch={handleSearch}
      searchQuery={searchQuery}
     />
    </div>

    {/* Projects Grid */}
    <div
     ref={projectsGridRef}
     className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
    >
     {filteredProjects.map(project => (
      <AppleProjectCard key={project.id} project={project} />
     ))}
    </div>

    {/* Empty State */}
    {filteredProjects.length === 0 && (
     <div className='text-center py-16'>
      <BriefcaseIcon className='h-16 w-16 text-gray-300 mx-auto mb-4 animate-bounce' />
      <h3 className='text-xl font-semibold text-gray-900 mb-2'>
       Chưa có sản phẩm phù hợp
      </h3>
      <p className='text-gray-600 mb-6'>
       Hãy thử bộ lọc khác hoặc quay lại sau!
      </p>
      <button
       onClick={handleClearFilters}
       className='px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-200'
      >
       Xóa tất cả bộ lọc
      </button>
     </div>
    )}
   </div>
  </div>
 );
};

export default AppleProjectsPage;
