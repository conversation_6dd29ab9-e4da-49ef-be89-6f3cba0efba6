/**
 * Deploy script để fix vấn đề date trong community service
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Deploying community service date fix...');

try {
  // 1. Build community service
  console.log('\n📦 Building community service...');
  execSync('cd services/community-service && npm run build', { stdio: 'inherit' });
  
  // 2. Deploy to production
  console.log('\n🚀 Deploying to production...');
  execSync('cd services/community-service && npm run deploy', { stdio: 'inherit' });
  
  // 3. Deploy client changes
  console.log('\n🌐 Deploying client changes...');
  execSync('cd client && npm run build', { stdio: 'inherit' });
  execSync('cd client && npm run deploy', { stdio: 'inherit' });
  
  console.log('\n✅ Date fix deployed successfully!');
  console.log('\n🔧 Changes made:');
  console.log('- Fixed timestamp mapping in client (createdAt -> timestamp)');
  console.log('- Improved date formatting in API responses');
  console.log('- Enhanced error handling for invalid dates');
  console.log('- Better author name handling');
  
} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  process.exit(1);
} 