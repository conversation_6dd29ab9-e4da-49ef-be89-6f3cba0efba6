@echo off
echo Installing PostgreSQL for VWork Platform...

REM Check if PostgreSQL is already installed
where psql >nul 2>nul
if %errorlevel% == 0 (
    echo PostgreSQL is already installed.
    psql --version
    goto :setup_database
)

echo Downloading PostgreSQL installer...
REM Download PostgreSQL installer (using winget if available)
winget install PostgreSQL.PostgreSQL --accept-source-agreements --accept-package-agreements

if %errorlevel% neq 0 (
    echo Winget not available, please install PostgreSQL manually from:
    echo https://www.postgresql.org/download/windows/
    pause
    exit /b 1
)

echo PostgreSQL installed successfully!
echo Please restart your terminal and run this script again to setup the database.

:setup_database
echo Setting up VWork database...

REM Create database and user
psql -U postgres -c "CREATE DATABASE vwork_db;" 2>nul
if %errorlevel% neq 0 (
    echo Failed to create database. Please run as administrator or check PostgreSQL installation.
    pause
    exit /b 1
)

psql -U postgres -c "CREATE USER vwork_user WITH PASSWORD 'vwork_password';" 2>nul
psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE vwork_db TO vwork_user;" 2>nul

echo Database setup completed!
echo Database: vwork_db
echo User: vwork_user
echo Password: vwork_password

pause 