/**
 * Response Utilities for Team Service
 */

// Response middleware
const responseMiddleware = (req, res, next) => {
  res.apiSuccess = (data = null, message = 'Success', pagination = null) => {
    const response = {
      success: true,
      data,
      message
    };

    if (pagination) {
      response.pagination = pagination;
    }

    res.status(200).json(response);
  };

  res.apiError = (message = 'An error occurred', code = 'INTERNAL_ERROR', statusCode = 500) => {
    const response = {
      success: false,
      error: message,
      message,
      code,
      statusCode
    };
    res.status(statusCode).json(response);
  };

  res.apiValidationError = (errors, message = 'Validation failed') => {
    const response = {
      success: false,
      error: message,
      message,
      code: 'VALIDATION_ERROR',
      statusCode: 422,
      errors
    };
    res.status(422).json(response);
  };

  res.apiUnauthorized = (message = 'Unauthorized access') => {
    const response = {
      success: false,
      error: message,
      message,
      code: 'UNAUTHORIZED',
      statusCode: 401
    };
    res.status(401).json(response);
  };

  res.apiForbidden = (message = 'Access forbidden') => {
    const response = {
      success: false,
      error: message,
      message,
      code: 'FORBIDDEN',
      statusCode: 403
    };
    res.status(403).json(response);
  };

  res.apiNotFound = (message = 'Resource not found') => {
    const response = {
      success: false,
      error: message,
      message,
      code: 'NOT_FOUND',
      statusCode: 404
    };
    res.status(404).json(response);
  };

  res.apiBadRequest = (message = 'Bad request') => {
    const response = {
      success: false,
      error: message,
      message,
      code: 'BAD_REQUEST',
      statusCode: 400
    };
    res.status(400).json(response);
  };

  next();
};

// Pagination utility
const createPagination = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

module.exports = {
  responseMiddleware,
  createPagination
}; 