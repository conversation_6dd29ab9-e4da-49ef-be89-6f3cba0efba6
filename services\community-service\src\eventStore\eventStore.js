/**
 * Community Service Event Store
 * Handles event persistence using EventStore DB
 */

const { EventStoreDBClient, jsonEvent } = require('@eventstore/db-client');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const config = require('../config/config');

class CommunityEventStore {
  constructor() {
    this.config = {
      connectionString: config.KURRENTDB_URL,
      enabled: config.EVENT_STORE_ENABLED,
      batchSize: config.EVENT_STORE_BATCH_SIZE,
      retryAttempts: config.EVENT_STORE_RETRY_ATTEMPTS,
      timeout: config.EVENT_STORE_TIMEOUT
    };

    this.client = null;
    this.isConnected = false;
    this.stats = {
      eventsAppended: 0,
      eventsRead: 0,
      errors: 0
    };

    // Initialize if enabled
    if (this.config.enabled) {
      this.connect().catch(error => {
        logger.error('Failed to initialize EventStore', { error: error.message });
        throw error; // Fail fast if EventStore cannot be initialized
      });
    } else {
      logger.warn('EventStore is disabled - events will not be persisted');
    }
  }

  /**
   * Connect to EventStore
   */
  async connect() {
    if (!this.config.enabled) {
      throw new Error('EventStore is disabled');
    }

    try {
      logger.info('Connecting to EventStore...', { url: this.config.connectionString });
      
      this.client = EventStoreDBClient.connectionString(this.config.connectionString);
      
      // Test connection
      await this.client.readAll({ 
        direction: 'forwards', 
        fromPosition: 'start', 
        maxCount: 1 
      });
      
      this.isConnected = true;
      logger.info('✅ EventStore connected successfully');
      
    } catch (error) {
      this.isConnected = false;
      this.stats.errors++;
      logger.error('❌ EventStore connection failed', { 
        error: error.message 
      });
      throw error; // Fail fast if connection fails
    }
  }

  /**
   * Append event to stream
   */
  async appendEvent(streamName, eventType, eventData, metadata = {}) {
    if (!this.isConnected || !this.client) {
      throw new Error('EventStore not connected');
    }

    const event = {
      id: uuidv4(),
      type: eventType,
      data: eventData,
      metadata: {
        timestamp: new Date().toISOString(),
        source: 'community-service',
        correlationId: metadata.correlationId || uuidv4(),
        causationId: metadata.causationId || null,
        version: '1.0.0',
        ...metadata
      }
    };

    try {
      const eventObject = jsonEvent(event);
      const result = await this.client.appendToStream(streamName, eventObject);
      
      this.stats.eventsAppended++;
      logger.debug('Event appended successfully', {
        streamName,
        eventType,
        eventNumber: result.nextExpectedRevision
      });
      
      return {
        success: true,
        eventNumber: result.nextExpectedRevision,
        eventId: event.id
      };
      
    } catch (error) {
      this.stats.errors++;
      logger.error('Failed to append event to EventStore', {
        streamName,
        eventType,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Read events from stream
   */
  async readEvents(streamName, options = {}) {
    if (!this.isConnected || !this.client) {
      throw new Error('EventStore not connected');
    }

    const {
      fromRevision = 0,
      direction = 'forwards',
      maxCount = this.config.batchSize,
      includeMetadata = true
    } = options;

    try {
      const events = [];
      const result = await this.client.readStream(streamName, {
        fromRevision,
        direction,
        maxCount
      });

      for await (const resolvedEvent of result) {
        const event = {
          id: resolvedEvent.event.id,
          type: resolvedEvent.event.type,
          data: resolvedEvent.event.data,
          metadata: resolvedEvent.event.userMetadata,
          eventNumber: resolvedEvent.event.revision,
          streamName: resolvedEvent.event.streamId,
          timestamp: resolvedEvent.event.created
        };
        
        events.push(event);
      }

      this.stats.eventsRead += events.length;
      logger.debug('Events read successfully', {
        streamName,
        count: events.length,
        fromRevision
      });

      return {
        success: true,
        events,
        lastEventNumber: events.length > 0 ? events[events.length - 1].eventNumber : fromRevision
      };

    } catch (error) {
      this.stats.errors++;
      logger.error('Failed to read events from EventStore', {
        streamName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Subscribe to stream events
   */
  async subscribeToStream(streamName, eventHandler, options = {}) {
    if (!this.isConnected || !this.client) {
      throw new Error('EventStore not connected');
    }

    try {
      const subscription = await this.client.subscribeToStream(streamName, {
        fromRevision: options.fromRevision || 0
      });

      logger.info('Subscribed to stream', { streamName });

      for await (const resolvedEvent of subscription) {
        const event = {
          id: resolvedEvent.event.id,
          type: resolvedEvent.event.type,
          data: resolvedEvent.event.data,
          metadata: resolvedEvent.event.userMetadata,
          eventNumber: resolvedEvent.event.revision,
          streamName: resolvedEvent.event.streamId,
          timestamp: resolvedEvent.event.created
        };

        await eventHandler(event);
      }

    } catch (error) {
      this.stats.errors++;
      logger.error('Stream subscription failed', {
        streamName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Create snapshot
   */
  async createSnapshot(streamName, aggregateId, snapshot, version) {
    if (!this.isConnected || !this.client) {
      throw new Error('EventStore not connected');
    }

    try {
      const snapshotEvent = {
        id: uuidv4(),
        type: 'snapshot',
        data: {
          aggregateId,
          snapshot,
          version
        },
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'community-service',
          snapshotType: 'aggregate'
        }
      };

      const eventObject = jsonEvent(snapshotEvent);
      await this.client.appendToStream(`${streamName}-snapshots`, eventObject);
      
      logger.debug('Snapshot created', {
        streamName,
        aggregateId,
        version
      });

      return { success: true };

    } catch (error) {
      this.stats.errors++;
      logger.error('Failed to create snapshot', {
        streamName,
        aggregateId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get latest snapshot
   */
  async getLatestSnapshot(streamName, aggregateId) {
    if (!this.isConnected || !this.client) {
      throw new Error('EventStore not connected');
    }

    try {
      const result = await this.client.readStream(`${streamName}-snapshots`, {
        direction: 'backwards',
        maxCount: 1
      });

      for await (const resolvedEvent of result) {
        if (resolvedEvent.event.data.aggregateId === aggregateId) {
          return {
            success: true,
            snapshot: resolvedEvent.event.data.snapshot,
            version: resolvedEvent.event.data.version
          };
        }
      }

      return { success: true, snapshot: null, version: 0 };

    } catch (error) {
      this.stats.errors++;
      logger.error('Failed to get latest snapshot', {
        streamName,
        aggregateId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      ...this.stats,
      isConnected: this.isConnected,
      enabled: this.config.enabled,
      connectionString: this.config.connectionString
    };
  }

  /**
   * Health check
   */
  async healthCheck() {
    if (!this.config.enabled) {
      return {
        status: 'disabled',
        message: 'EventStore is disabled'
      };
    }

    if (!this.isConnected || !this.client) {
      return {
        status: 'unhealthy',
        message: 'EventStore not connected'
      };
    }

    try {
      // Test connection with a simple read
      await this.client.readAll({ 
        direction: 'forwards', 
        fromPosition: 'start', 
        maxCount: 1 
      });

      return {
        status: 'healthy',
        message: 'EventStore connection is working',
        stats: this.getStats()
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `EventStore health check failed: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * Disconnect from EventStore
   */
  async disconnect() {
    if (this.client) {
      try {
        await this.client.dispose();
        this.isConnected = false;
        logger.info('EventStore disconnected');
      } catch (error) {
        logger.error('Error disconnecting from EventStore', { error: error.message });
      }
    }
  }
}

module.exports = CommunityEventStore;
