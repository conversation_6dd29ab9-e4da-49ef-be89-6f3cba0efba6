/**
 * Firebase Configuration for User Service
 * Handles Firebase Admin SDK initialization and authentication
 */

const admin = require('firebase-admin');
const config = require('./config');

class FirebaseConfig {
  constructor() {
    this.app = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Firebase Admin SDK
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        console.log('🔥 Firebase already initialized');
        return this.app;
      }

      console.log('🔄 Initializing Firebase Admin SDK for User Service...');

      // Validate required environment variables
      if (!config.FIREBASE_PROJECT_ID) {
        throw new Error('FIREBASE_PROJECT_ID is required');
      }

      let serviceAccount;

      // Check if we have all required Firebase credentials
      if (config.FIREBASE_CLIENT_EMAIL && config.FIREBASE_PRIVATE_KEY) {
        // Use individual environment variables
        serviceAccount = {
          projectId: config.FIREBASE_PROJECT_ID,
          clientEmail: config.FIREBASE_CLIENT_EMAIL,
          privateKey: config.FIREBASE_PRIVATE_KEY
        };

        console.log('🔑 Using Firebase credentials from environment variables');
      } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        // Use service account JSON string
        try {
          serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
          console.log('🔑 Using Firebase service account key');
        } catch (error) {
          throw new Error('Invalid FIREBASE_SERVICE_ACCOUNT_KEY JSON format');
        }
      } else if (config.isDevelopment) {
        // Development mode - try to use default credentials or emulator
        console.log('⚠️ Development mode: Using default Firebase credentials');
        serviceAccount = {
          projectId: config.FIREBASE_PROJECT_ID
        };
      } else {
        throw new Error('Firebase credentials not found. Please set FIREBASE_CLIENT_EMAIL and FIREBASE_PRIVATE_KEY or FIREBASE_SERVICE_ACCOUNT_KEY');
      }

      // Initialize Firebase Admin
      this.app = admin.initializeApp({
        credential: config.isDevelopment && !serviceAccount.privateKey 
          ? admin.credential.applicationDefault()
          : admin.credential.cert(serviceAccount),
        projectId: config.FIREBASE_PROJECT_ID
      });

      // Test the connection
      await this.testConnection();

      this.isInitialized = true;
      console.log('🎉 Firebase Admin SDK initialized successfully');
      
      return this.app;

    } catch (error) {
      console.error('❌ Failed to initialize Firebase:', error);
      throw error;
    }
  }

  /**
   * Test Firebase connection
   */
  async testConnection() {
    try {
      // Test by getting auth instance
      const auth = admin.auth();
      
      // Try to list users (limited to 1 for testing)
      await auth.listUsers(1);
      
      console.log('✅ Firebase connection test passed');
    } catch (error) {
      console.error('❌ Firebase connection test failed:', error.message);
      throw error;
    }
  }

  /**
   * Verify Firebase ID token
   */
  async verifyIdToken(idToken) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const decodedToken = await admin.auth().verifyIdToken(idToken);
      
      return {
        success: true,
        user: {
          uid: decodedToken.uid,
          email: decodedToken.email,
          emailVerified: decodedToken.email_verified,
          displayName: decodedToken.name || decodedToken.display_name,
          photoURL: decodedToken.picture,
          phoneNumber: decodedToken.phone_number,
          customClaims: decodedToken.custom_claims || {},
          // Additional Firebase fields
          firebase: {
            authTime: new Date(decodedToken.auth_time * 1000),
            issuedAt: new Date(decodedToken.iat * 1000),
            expiresAt: new Date(decodedToken.exp * 1000),
            issuer: decodedToken.iss,
            audience: decodedToken.aud
          }
        },
        token: decodedToken
      };

    } catch (error) {
      console.error('❌ Token verification failed:', error.message);
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Get user by UID
   */
  async getUserByUid(uid) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const userRecord = await admin.auth().getUser(uid);
      
      return {
        success: true,
        user: {
          uid: userRecord.uid,
          email: userRecord.email,
          emailVerified: userRecord.emailVerified,
          displayName: userRecord.displayName,
          photoURL: userRecord.photoURL,
          phoneNumber: userRecord.phoneNumber,
          disabled: userRecord.disabled,
          metadata: {
            creationTime: userRecord.metadata.creationTime,
            lastSignInTime: userRecord.metadata.lastSignInTime,
            lastRefreshTime: userRecord.metadata.lastRefreshTime
          },
          customClaims: userRecord.customClaims || {},
          providerData: userRecord.providerData
        }
      };

    } catch (error) {
      console.error('❌ Get user by UID failed:', error.message);
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const userRecord = await admin.auth().getUserByEmail(email);
      
      return {
        success: true,
        user: {
          uid: userRecord.uid,
          email: userRecord.email,
          emailVerified: userRecord.emailVerified,
          displayName: userRecord.displayName,
          photoURL: userRecord.photoURL,
          phoneNumber: userRecord.phoneNumber,
          disabled: userRecord.disabled,
          metadata: userRecord.metadata,
          customClaims: userRecord.customClaims || {}
        }
      };

    } catch (error) {
      console.error('❌ Get user by email failed:', error.message);
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Update user custom claims
   */
  async setCustomClaims(uid, claims) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      await admin.auth().setCustomUserClaims(uid, claims);
      
      console.log(`✅ Custom claims updated for user: ${uid}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Set custom claims failed:', error.message);
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Create custom token
   */
  async createCustomToken(uid, additionalClaims = {}) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const customToken = await admin.auth().createCustomToken(uid, additionalClaims);
      
      return {
        success: true,
        token: customToken
      };

    } catch (error) {
      console.error('❌ Create custom token failed:', error.message);
      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Health check for Firebase
   */
  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return {
          status: 'error',
          message: 'Firebase not initialized',
          timestamp: new Date().toISOString()
        };
      }

      // Test connection by attempting to list users
      const start = Date.now();
      await admin.auth().listUsers(1);
      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        service: 'Firebase Admin SDK',
        projectId: config.FIREBASE_PROJECT_ID,
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get Firebase app instance
   */
  getApp() {
    return this.app;
  }

  /**
   * Get Auth instance
   */
  getAuth() {
    if (!this.isInitialized) {
      throw new Error('Firebase not initialized');
    }
    return admin.auth();
  }

  /**
   * Get Firestore instance (if needed)
   */
  getFirestore() {
    if (!this.isInitialized) {
      throw new Error('Firebase not initialized');
    }
    return admin.firestore();
  }
}

// Create singleton instance
const firebaseConfig = new FirebaseConfig();

// Export convenient functions
module.exports = {
  firebaseConfig,
  
  // Direct access to methods
  async verifyIdToken(idToken) {
    return await firebaseConfig.verifyIdToken(idToken);
  },

  async getUserByUid(uid) {
    return await firebaseConfig.getUserByUid(uid);
  },

  async getUserByEmail(email) {
    return await firebaseConfig.getUserByEmail(email);
  },

  async initialize() {
    return await firebaseConfig.initialize();
  },

  async healthCheck() {
    return await firebaseConfig.healthCheck();
  },

  getAuth() {
    return firebaseConfig.getAuth();
  },

  getFirestore() {
    return firebaseConfig.getFirestore();
  }
}; 