/**
 * Community API Service
 * Handles all API calls for community features (likes, comments, etc.)
 */

import axios from 'axios';
import { auth } from '../config/firebase';

// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development' || 
           window.location.hostname === 'localhost' || 
           window.location.hostname === '127.0.0.1';

// Get API base URL - Always use API Gateway for consistency
const getAPIBaseURL = () => {
 // Development: Use localhost API Gateway
 if (isDevelopment) {
  return process.env.REACT_APP_COMMUNITY_API_URL || 'http://localhost:8080/api/v1/community';
 }
 
 // Production: Use production API Gateway
 return process.env.REACT_APP_COMMUNITY_API_URL || 'https://vwork-api-gateway.onrender.com/api/v1/community';
};

const API_BASE_URL = getAPIBaseURL();

console.log('🌐 Community API Configuration:', {
 environment: process.env.NODE_ENV,
 isDevelopment,
 baseUrl: API_BASE_URL,
 reactAppCommunityApiUrl: process.env.REACT_APP_COMMUNITY_API_URL
});

// Helper function to get Firebase token
const getFirebaseToken = async () => {
 try {
  const user = auth.currentUser;
  if (user) {
   const token = await user.getIdToken();
   console.log('🔥 Firebase token obtained:', token.substring(0, 20) + '...');
   return token;
  }
  console.warn('⚠️ No authenticated user found - using development mode');
  return null;
 } catch (error) {
  console.error('❌ Error getting Firebase token:', error);
  // In development, continue without token
  if (isDevelopment) {
   console.warn('🔧 Development mode: proceeding without auth token');
   return null;
  }
  return null;
 }
};

// Create axios instance with default config
const apiClient = axios.create({
 baseURL: API_BASE_URL,
 timeout: 10000,
 headers: {
  'Content-Type': 'application/json',
 },
});

// Add request interceptor for Firebase auth token
apiClient.interceptors.request.use(
 async (config) => {
  const token = await getFirebaseToken();
  if (token) {
   config.headers.Authorization = `Bearer ${token}`;
   console.log('🔐 Added Firebase token to request:', config.url);
  } else {
   console.warn('⚠️ No Firebase token available for request:', config.url);
  }
  return config;
 },
 (error) => {
  return Promise.reject(error);
 }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
 (response) => response,
 (error) => {
  console.error('API Error:', error.response?.data || error.message);
  return Promise.reject(error);
 }
);

/**
 * LIKES API
 */
export const likesApi = {
 // Like/Unlike a post or comment
 toggleLike: async (targetId, targetType) => {
  try {
   // Check if user is authenticated
   const user = auth.currentUser;
   if (!user) {
    throw new Error('User must be logged in to like/unlike');
   }

   console.log('🔥 Toggling like for:', { targetId, targetType, userId: user.uid });

   const response = await apiClient.post('/likes', {
    targetId,
    targetType
    // userId is now obtained from Firebase token in auth middleware
   });
   return response.data;
  } catch (error) {
   console.error('❌ Toggle like error:', error);
   throw new Error(error.response?.data?.error || 'Failed to toggle like');
  }
 },

 // Get likes for a post
 getPostLikes: async (postId) => {
  try {
   const response = await apiClient.get(`/likes/post/${postId}`);
   return response.data;
  } catch (error) {
   throw new Error(error.response?.data?.error || 'Failed to get post likes');
  }
 },

 // Get likes for a comment
 getCommentLikes: async (commentId) => {
  try {
   const response = await apiClient.get(`/likes/comment/${commentId}`);
   return response.data;
  } catch (error) {
   throw new Error(error.response?.data?.error || 'Failed to get comment likes');
  }
 },

 // Check if user liked a target
 checkLikeStatus: async (targetId, targetType, userId) => {
  try {
   // Use provided userId or get from current user
   let userIdToCheck = userId;
   if (!userIdToCheck) {
    const user = auth.currentUser;
    if (user) {
     userIdToCheck = user.uid;
    } else {
     // Return not liked immediately if no user (no API call)
     console.log('⏭️ Skipping like status check - no authenticated user');
     return { liked: false };
    }
   }

   // Skip API call if no valid userId
   if (!userIdToCheck || userIdToCheck === 'null' || userIdToCheck === 'undefined') {
    console.log('⏭️ Skipping like status check - invalid userId:', userIdToCheck);
    return { liked: false };
   }

   const response = await apiClient.get(`/likes/check/${targetType}/${targetId}`);
   return response.data;
  } catch (error) {
   console.error('❌ Check like status error:', error);
   // Return not liked on error to avoid breaking UI
   return { liked: false };
  }
 }
};

/**
 * COMMENTS API
 */
export const commentsApi = {
 // Get comments for a post
 getComments: async (postId, options = {}) => {
  try {
   const { page = 1, limit = 20, sortBy = 'created_at', order = 'asc' } = options;
   const response = await apiClient.get(`/comments/${postId}`, {
    params: { page, limit, sortBy, order }
   });
   return response.data;
  } catch (error) {
   throw new Error(error.response?.data?.error || 'Failed to get comments');
  }
 },

 // Create a new comment
 createComment: async (postId, content, parentId = null) => {
  try {
   // Check if user is authenticated
   const user = auth.currentUser;
   if (!user) {
    throw new Error('User must be logged in to comment');
   }

   console.log('🔥 Creating comment for:', { postId, content, parentId, userId: user.uid });

   const response = await apiClient.post('/comments', {
    postId,
    content,
    parentId
    // authorId is now obtained from Firebase token in auth middleware
   });
   return response.data;
  } catch (error) {
   console.error('❌ Create comment error:', error);
   throw new Error(error.response?.data?.error || 'Failed to create comment');
  }
 },

 // Update a comment
 updateComment: async (commentId, content, editReason = null) => {
  try {
   const response = await apiClient.put(`/comments/${commentId}`, {
    content,
    editReason
   });
   return response.data;
  } catch (error) {
   throw new Error(error.response?.data?.error || 'Failed to update comment');
  }
 },

 // Delete a comment
 deleteComment: async (commentId) => {
  try {
   const response = await apiClient.delete(`/comments/${commentId}`);
   return response.data;
  } catch (error) {
   throw new Error(error.response?.data?.error || 'Failed to delete comment');
  }
 }
};

/**
 * POSTS API (for updating counts)
 */
export const postsApi = {
 // Get post with updated counts
 getPost: async (postId) => {
  try {
   const response = await apiClient.get(`/posts/${postId}`);
   return response.data;
  } catch (error) {
   throw new Error(error.response?.data?.error || 'Failed to get post');
  }
 },

 // Update post view count
 incrementViewCount: async (postId) => {
  try {
   const response = await apiClient.post(`/posts/${postId}/view`);
   return response.data;
  } catch (error) {
   throw new Error(error.response?.data?.error || 'Failed to increment view count');
  }
 }
};

/**
 * UTILITY FUNCTIONS
 */
export const communityUtils = {
 // Get user ID - this should be called with user from AuthContext
 getCurrentUserId: (user = null) => {
  try {
   // If user is passed from AuthContext, use it directly
   if (user && user.uid) {
    console.log('✅ Using AuthContext user UID:', user.uid);
    return user.uid;
   }

   // Debug: Log all possible sources
   const backendToken = localStorage.getItem('backend_token');
   const authToken = localStorage.getItem('authToken');
   const userId = localStorage.getItem('userId');

   console.log('🔍 getCurrentUserId Debug:', {
    backendToken: backendToken ? 'exists' : 'null',
    authToken: authToken ? 'exists' : 'null',
    userId: userId || 'null',
    localStorage_keys: Object.keys(localStorage)
   });

   // Try to get from Firebase Auth directly (if available)
   if (typeof window !== 'undefined' && window.firebase?.auth?.currentUser) {
    const currentUser = window.firebase.auth.currentUser;
    if (currentUser && currentUser.uid) {
     console.log('✅ Using Firebase currentUser UID:', currentUser.uid);
     return currentUser.uid;
    }
   }

   // Try backend token (JWT from our backend)
   if (backendToken && backendToken !== 'null' && backendToken !== 'undefined') {
    try {
     const payload = JSON.parse(atob(backendToken.split('.')[1]));
     const tokenUserId = payload.userId || payload.sub || payload.uid;
     if (tokenUserId) {
      console.log('✅ Using backend token UID:', tokenUserId);
      return tokenUserId;
     }
    } catch (tokenError) {
     console.log('⚠️ Failed to decode backend token:', tokenError);
    }
   }

   // Try auth token
   if (authToken && authToken !== 'null' && authToken !== 'undefined') {
    try {
     const payload = JSON.parse(atob(authToken.split('.')[1]));
     const tokenUserId = payload.userId || payload.sub || payload.uid;
     if (tokenUserId) {
      console.log('✅ Using auth token UID:', tokenUserId);
      return tokenUserId;
     }
    } catch (tokenError) {
     console.log('⚠️ Failed to decode auth token:', tokenError);
    }
   }

   // Finally try direct userId from localStorage
   if (userId && userId !== 'null' && userId !== 'undefined') {
    console.log('✅ Using localStorage userId:', userId);
    return userId;
   }

   // If no valid user found, return null (don't use 'anonymous')
   console.log('❌ No valid user ID found');
   return null;
  } catch (error) {
   console.error('Error getting user ID:', error);
   return null;
  }
 },

 // Format API error for display
 formatApiError: (error) => {
  if (error.response?.status === 401) {
   return 'Bạn cần đăng nhập để thực hiện hành động này';
  }
  if (error.response?.status === 403) {
   return 'Bạn không có quyền thực hiện hành động này';
  }
  if (error.response?.status === 404) {
   return 'Không tìm thấy nội dung';
  }
  if (error.response?.status >= 500) {
   return 'Lỗi server, vui lòng thử lại sau';
  }
  return error.message || 'Có lỗi xảy ra';
 },

 // Check if API is available
 checkApiHealth: async () => {
  try {
   const response = await apiClient.get('/health');
   return response.data;
  } catch (error) {
   return { status: 'error', message: 'API not available' };
  }
 }
};

/**
 * INTEGRATION HELPERS
 */
export const integrationHelpers = {
 // Sync local state with backend
 syncLikeState: async (postId, userId) => {
  try {
   const likeStatus = await likesApi.checkLikeStatus(postId, 'post', userId);
   const postLikes = await likesApi.getPostLikes(postId);
   return {
    liked: likeStatus.liked,
    count: postLikes.count
   };
  } catch (error) {
   console.error('Error syncing like state:', error);
   return null;
  }
 },

 // Sync comment state with backend
 syncCommentState: async (postId) => {
  try {
   const comments = await commentsApi.getComments(postId);
   return comments;
  } catch (error) {
   console.error('Error syncing comment state:', error);
   return null;
  }
 },

 // Batch sync multiple posts
 syncMultiplePosts: async (postIds, userId) => {
  try {
   const promises = postIds.map(postId => 
    integrationHelpers.syncLikeState(postId, userId)
   );
   const results = await Promise.allSettled(promises);
   
   const syncedData = {};
   results.forEach((result, index) => {
    if (result.status === 'fulfilled' && result.value) {
     syncedData[postIds[index]] = result.value;
    }
   });
   
   return syncedData;
  } catch (error) {
   console.error('Error syncing multiple posts:', error);
   return {};
  }
 }
};

export default {
 likesApi,
 commentsApi,
 postsApi,
 communityUtils,
 integrationHelpers
};
