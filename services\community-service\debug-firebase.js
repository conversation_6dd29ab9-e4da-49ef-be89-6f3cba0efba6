const admin = require('firebase-admin');
const path = require('path');

console.log('=== Firebase Debug Script ===');

// Try different paths
const paths = [
  path.resolve(__dirname, '../../../../.env'),
  path.resolve(__dirname, '../../../.env'),
  path.resolve(__dirname, '../../.env'),
  path.resolve(__dirname, '../.env'),
  path.resolve(__dirname, '.env'),
];

console.log('Testing different paths:');
paths.forEach((envPath, index) => {
  const exists = require('fs').existsSync(envPath);
  console.log(`${index + 1}. ${envPath} - ${exists ? '✅ EXISTS' : '❌ NOT FOUND'}`);
});

// Use the first existing path
const envPath = paths.find(p => require('fs').existsSync(p));
if (envPath) {
  console.log(`\nUsing path: ${envPath}`);
  require('dotenv').config({ path: envPath });
  
  console.log('\nEnvironment variables:');
  console.log('FIREBASE_PROJECT_ID:', process.env.FIREBASE_PROJECT_ID);
  console.log('FIREBASE_CLIENT_EMAIL:', process.env.FIREBASE_CLIENT_EMAIL?.substring(0, 30) + '...');
  console.log('FIREBASE_PRIVATE_KEY exists:', !!process.env.FIREBASE_PRIVATE_KEY);
  
  if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PRIVATE_KEY) {
    console.log('\n✅ All Firebase variables found!');
    
    try {
      const serviceAccount = {
        type: 'service_account',
        project_id: process.env.FIREBASE_PROJECT_ID,
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
      };

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      });

      console.log('🔥 Firebase Admin SDK initialized successfully!');
      
      // Test token verification
      const testToken = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwczovL3NlY3VyZXRva2VuLmdvb2dsZS5jb20vdndvcmstNzg2YzMiLCJhdWQiOiJ2d29yay03ODZjMyIsImF1dGhfdGltZSI6MTczMjQ5NzY5MCwidXNlcl9pZCI6IjBvRTc3N2VWZ1ZWWDBydllRNkkzODZ1Y2VZQTMiLCJpYXQiOjE3MzI0OTc2OTAsImV4cCI6MTczMjUwMTI5MCwiZW1haWwiOiJuZHZpbmgyN0BnbWFpbC5jb20iLCJlbWFpbF92ZXJpZmllZCI6dHJ1ZSwiZmlyZWJhc2UiOnsiaWRlbnRpdHkiOnsicHJvdmlkZXJzIjpbImdvb2dsZS5jb20iXSwic2lnbl9pbl9wcm92aWRlciI6Imdvb2dsZS5jb20ifSwibmFtZSI6IsSQw6luaCBWaW5oIE5ndXnhu4VuIiwicGljdHVyZSI6Imh0dHBzOi8vbGgzLmdvb2dsZXVzZXJjb250ZW50LmNvbS9hL0FDZzhvY0s2TlNuLU1EWjdIaTM3SFZEeDhPTkh4YzRkZ19nall6LUZOdXR6TUtxUGgyQVJoUT1zOTYtYyIsImxvY2FsZSI6InZpIn0.test';
      
      console.log('\nTesting token verification...');
      admin.auth().verifyIdToken(testToken)
        .then(decodedToken => {
          console.log('✅ Token verified successfully!');
          console.log('User ID:', decodedToken.uid);
          console.log('Email:', decodedToken.email);
          console.log('Name:', decodedToken.name);
        })
        .catch(error => {
          console.log('❌ Token verification failed:', error.message);
        });
        
    } catch (error) {
      console.log('❌ Firebase initialization failed:', error.message);
    }
  } else {
    console.log('\n❌ Missing Firebase environment variables');
  }
} else {
  console.log('\n❌ No .env file found in any of the tested paths');
} 