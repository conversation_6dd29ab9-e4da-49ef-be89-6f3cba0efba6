#!/usr/bin/env node

/**
 * Unified testing script for NERAFUS platform
 * Handles unit, integration, and end-to-end testing
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..', '..');

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Execute command with proper error handling
 */
function execCommand(command, cwd = ROOT_DIR, options = {}) {
  try {
    log.info(`Executing: ${command}`);
    const result = execSync(command, { 
      cwd, 
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8'
    });
    return result;
  } catch (error) {
    log.error(`Command failed: ${command}`);
    if (options.exitOnError !== false) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if Jest is available
 */
function checkJestAvailability() {
  try {
    execSync('npx jest --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    log.error('Jest is not available. Please install Jest first.');
    log.info('Run: npm install --save-dev jest @testing-library/react @testing-library/jest-dom');
    return false;
  }
}

/**
 * Run unit tests
 */
function runUnitTests(options = {}) {
  log.info('Running unit tests...');
  
  const jestArgs = [
    'jest',
    '--selectProjects client server',
    options.watch ? '--watch' : '',
    options.coverage ? '--coverage' : '',
    options.verbose ? '--verbose' : '',
    options.pattern ? `--testNamePattern="${options.pattern}"` : '',
    options.file ? options.file : ''
  ].filter(Boolean).join(' ');

  return execCommand(`npx ${jestArgs}`, ROOT_DIR, { exitOnError: false });
}

/**
 * Run integration tests
 */
function runIntegrationTests(options = {}) {
  log.info('Running integration tests...');
  
  const jestArgs = [
    'jest',
    '--selectProjects integration',
    '--runInBand', // Run tests serially for integration tests
    options.verbose ? '--verbose' : '',
    options.pattern ? `--testNamePattern="${options.pattern}"` : '',
    options.file ? options.file : ''
  ].filter(Boolean).join(' ');

  return execCommand(`npx ${jestArgs}`, ROOT_DIR, { exitOnError: false });
}

/**
 * Run end-to-end tests
 */
function runE2ETests(options = {}) {
  log.info('Running end-to-end tests...');
  
  // Check if Playwright or Cypress is available
  const hasPlaywright = fs.existsSync(path.join(ROOT_DIR, 'playwright.config.js'));
  const hasCypress = fs.existsSync(path.join(ROOT_DIR, 'cypress.config.js'));
  
  if (hasPlaywright) {
    const playwrightArgs = [
      'playwright test',
      options.headed ? '--headed' : '',
      options.browser ? `--project=${options.browser}` : '',
      options.pattern ? `--grep="${options.pattern}"` : ''
    ].filter(Boolean).join(' ');
    
    return execCommand(`npx ${playwrightArgs}`, ROOT_DIR, { exitOnError: false });
  } else if (hasCypress) {
    const cypressArgs = [
      'cypress',
      options.headed ? 'open' : 'run',
      options.browser ? `--browser ${options.browser}` : '',
      options.pattern ? `--spec "**/e2e/**/*${options.pattern}*"` : ''
    ].filter(Boolean).join(' ');
    
    return execCommand(`npx ${cypressArgs}`, ROOT_DIR, { exitOnError: false });
  } else {
    log.warn('No E2E testing framework found. Please install Playwright or Cypress.');
    log.info('For Playwright: npm install --save-dev @playwright/test');
    log.info('For Cypress: npm install --save-dev cypress');
    return false;
  }
}

/**
 * Run linting
 */
function runLinting(options = {}) {
  log.info('Running linting...');
  
  const eslintArgs = [
    'eslint',
    options.fix ? '--fix' : '',
    options.quiet ? '--quiet' : '',
    'client/src services/*/src scripts'
  ].filter(Boolean).join(' ');

  return execCommand(`npx ${eslintArgs}`, ROOT_DIR, { exitOnError: false });
}

/**
 * Run type checking
 */
function runTypeChecking(options = {}) {
  log.info('Running type checking...');
  
  // Check if TypeScript is used
  const hasTSConfig = fs.existsSync(path.join(ROOT_DIR, 'tsconfig.json'));
  
  if (hasTSConfig) {
    return execCommand('npx tsc --noEmit', ROOT_DIR, { exitOnError: false });
  } else {
    log.info('No TypeScript configuration found, skipping type checking.');
    return true;
  }
}

/**
 * Generate test coverage report
 */
function generateCoverageReport(options = {}) {
  log.info('Generating coverage report...');
  
  const jestArgs = [
    'jest',
    '--coverage',
    '--coverageReporters=text',
    '--coverageReporters=lcov',
    '--coverageReporters=html',
    options.threshold ? `--coverageThreshold='${JSON.stringify(options.threshold)}'` : ''
  ].filter(Boolean).join(' ');

  const result = execCommand(`npx ${jestArgs}`, ROOT_DIR, { exitOnError: false });
  
  if (result !== null) {
    log.success('Coverage report generated in ./coverage directory');
    
    // Open coverage report if requested
    if (options.open) {
      const coverageFile = path.join(ROOT_DIR, 'coverage', 'lcov-report', 'index.html');
      if (fs.existsSync(coverageFile)) {
        execCommand(`open ${coverageFile}`, ROOT_DIR, { exitOnError: false });
      }
    }
  }
  
  return result;
}

/**
 * Run performance tests
 */
function runPerformanceTests(options = {}) {
  log.info('Running performance tests...');
  
  // Check if Lighthouse CI is available
  const hasLighthouseCI = fs.existsSync(path.join(ROOT_DIR, 'lighthouserc.js'));
  
  if (hasLighthouseCI) {
    return execCommand('npx lhci autorun', ROOT_DIR, { exitOnError: false });
  } else {
    log.warn('Lighthouse CI not configured. Skipping performance tests.');
    log.info('To set up performance testing: npm install --save-dev @lhci/cli');
    return true;
  }
}

/**
 * Run security tests
 */
function runSecurityTests(options = {}) {
  log.info('Running security tests...');
  
  // Run npm audit
  const auditResult = execCommand('npm audit --audit-level moderate', ROOT_DIR, { exitOnError: false });
  
  // Check if Snyk is available
  try {
    execSync('npx snyk --version', { stdio: 'pipe' });
    execCommand('npx snyk test', ROOT_DIR, { exitOnError: false });
  } catch (error) {
    log.info('Snyk not available. Install with: npm install --save-dev snyk');
  }
  
  return auditResult;
}

/**
 * Run all tests
 */
function runAllTests(options = {}) {
  log.info('Running complete test suite...');
  
  const results = {
    lint: false,
    typeCheck: false,
    unit: false,
    integration: false,
    e2e: false,
    coverage: false,
    performance: false,
    security: false
  };
  
  // Run tests in order
  if (!options.skipLint) {
    results.lint = runLinting(options) !== null;
  }
  
  if (!options.skipTypeCheck) {
    results.typeCheck = runTypeChecking(options) !== null;
  }
  
  if (!options.skipUnit) {
    results.unit = runUnitTests(options) !== null;
  }
  
  if (!options.skipIntegration) {
    results.integration = runIntegrationTests(options) !== null;
  }
  
  if (!options.skipE2E) {
    results.e2e = runE2ETests(options) !== null;
  }
  
  if (options.coverage) {
    results.coverage = generateCoverageReport(options) !== null;
  }
  
  if (options.performance) {
    results.performance = runPerformanceTests(options) !== null;
  }
  
  if (options.security) {
    results.security = runSecurityTests(options) !== null;
  }
  
  // Print summary
  console.log('\n📊 Test Results Summary:');
  console.log('─'.repeat(40));
  
  Object.entries(results).forEach(([test, passed]) => {
    if (passed !== false) {
      const icon = passed ? '✅' : '❌';
      const status = passed ? 'PASSED' : 'FAILED';
      console.log(`${icon} ${test.padEnd(15)} ${status}`);
    }
  });
  
  const failedTests = Object.entries(results).filter(([, passed]) => passed === false);
  
  if (failedTests.length > 0) {
    console.log(`\n❌ ${failedTests.length} test suite(s) failed`);
    process.exit(1);
  } else {
    console.log('\n🎉 All tests passed!');
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';
  
  const options = {
    watch: args.includes('--watch'),
    coverage: args.includes('--coverage'),
    verbose: args.includes('--verbose'),
    fix: args.includes('--fix'),
    quiet: args.includes('--quiet'),
    headed: args.includes('--headed'),
    open: args.includes('--open'),
    skipLint: args.includes('--skip-lint'),
    skipTypeCheck: args.includes('--skip-type-check'),
    skipUnit: args.includes('--skip-unit'),
    skipIntegration: args.includes('--skip-integration'),
    skipE2E: args.includes('--skip-e2e'),
    performance: args.includes('--performance'),
    security: args.includes('--security'),
    pattern: args.find(arg => arg.startsWith('--pattern='))?.split('=')[1],
    file: args.find(arg => arg.startsWith('--file='))?.split('=')[1],
    browser: args.find(arg => arg.startsWith('--browser='))?.split('=')[1]
  };

  if (!checkJestAvailability()) {
    process.exit(1);
  }

  switch (command) {
    case 'unit':
      runUnitTests(options);
      break;

    case 'integration':
      runIntegrationTests(options);
      break;

    case 'e2e':
      runE2ETests(options);
      break;

    case 'lint':
      runLinting(options);
      break;

    case 'type-check':
      runTypeChecking(options);
      break;

    case 'coverage':
      generateCoverageReport(options);
      break;

    case 'performance':
      runPerformanceTests(options);
      break;

    case 'security':
      runSecurityTests(options);
      break;

    case 'all':
      runAllTests(options);
      break;

    default:
      console.log(`
Usage: node test.js <command> [options]

Commands:
  unit            Run unit tests
  integration     Run integration tests
  e2e             Run end-to-end tests
  lint            Run linting
  type-check      Run type checking
  coverage        Generate coverage report
  performance     Run performance tests
  security        Run security tests
  all             Run all tests (default)

Options:
  --watch         Watch mode for unit tests
  --coverage      Generate coverage report
  --verbose       Verbose output
  --fix           Auto-fix linting issues
  --quiet         Quiet mode
  --headed        Run E2E tests in headed mode
  --open          Open coverage report
  --skip-lint     Skip linting
  --skip-unit     Skip unit tests
  --skip-integration  Skip integration tests
  --skip-e2e      Skip E2E tests
  --performance   Include performance tests
  --security      Include security tests
  --pattern=<pattern>  Run tests matching pattern
  --file=<file>   Run specific test file
  --browser=<browser>  Browser for E2E tests

Examples:
  node test.js unit --watch
  node test.js all --coverage --skip-e2e
  node test.js integration --verbose
  node test.js e2e --headed --browser=chrome
  node test.js --pattern="Button" --file="Button.test.js"
      `);
      process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  runUnitTests,
  runIntegrationTests,
  runE2ETests,
  runLinting,
  runTypeChecking,
  generateCoverageReport,
  runPerformanceTests,
  runSecurityTests,
  runAllTests
};
