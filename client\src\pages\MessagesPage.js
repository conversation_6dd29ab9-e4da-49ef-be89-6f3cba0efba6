import React from 'react';
import {
 ChatBubbleLeftRightIcon,
 EnvelopeIcon,
} from '@heroicons/react/24/outline';
import { ApplePageWrapper } from '../components/apple';

const MessagesPage = () => {
 return (
  <ApplePageWrapper variant='gray'>
   <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
    {/* Apple-style Header */}
    <div className='text-center mb-12'>
     <div className='w-16 h-16 bg-blue-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg'>
      <ChatBubbleLeftRightIcon className='h-8 w-8 text-white' />
     </div>
     <h1 className='text-4xl md:text-5xl font-bold text-gray-900 mb-4'>
      Messages
     </h1>
     <p className='text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed'>
      Communicate with clients and freelancers
     </p>
    </div>

    {/* Apple-style Content */}
    <div className='bg-white rounded-2xl p-8 max-w-4xl mx-auto shadow-sm border border-gray-100'>
     <div className='text-center'>
      <div className='w-12 h-12 bg-blue-100 rounded-xl mx-auto mb-4 flex items-center justify-center'>
       <EnvelopeIcon className='h-6 w-6 text-blue-600' />
      </div>
      <h3 className='text-2xl font-semibold text-gray-900 mb-4'>
       Messaging System Coming Soon
      </h3>
      <p className='text-lg text-gray-600 mb-8'>
       Soon you'll be able to exchange messages with all members of the
       platform.
      </p>
      <div className='flex justify-center space-x-4'>
       <button className='btn btn-primary px-6 py-3'>
        Return to Dashboard
       </button>
       <button className='btn btn-secondary px-6 py-3'>
        Browse Projects
       </button>
      </div>
     </div>
    </div>
   </div>
  </ApplePageWrapper>
 );
};

export default MessagesPage;
