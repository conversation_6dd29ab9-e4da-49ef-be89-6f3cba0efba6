import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useOnboarding } from '../../contexts/OnboardingContext';

const ProfileGuard = ({ children }) => {
 const { user, isAuthenticated, loading } = useAuth();
 const { isOnboardingRequired } = useOnboarding();
 const navigate = useNavigate();
 const location = useLocation();

 useEffect(() => {
  // Don't check if still loading or not authenticated
  if (loading || !isAuthenticated || !user) return;

  const currentPath = location.pathname;
  console.log('🔍 ProfileGuard: Checking path:', currentPath);

  // First priority: Check email verification
  if (!user.emailVerified) {
   console.log('📧 ProfileGuard: Email not verified, redirecting...');
   navigate('/verify-email', { replace: true });
   return;
  }

  // If OnboardingGuard is handling this user, don't do anything
  if (isOnboardingRequired) {
   console.log('🔄 ProfileGuard: Onboarding is required, letting OnboardingGuard handle it');
   return;
  }

  // Profile setup pages have been removed - OnboardingFlow handles this now

  // Don't check other auth/verification pages
  const skipCheckPages = [
   '/verify-email',
   '/login-success',
   '/simple-login-success',
   '/user-debug',
   '/test-status',
   '/check-email',
   '/login-flow',
   '/onboarding'
  ];

  if (skipCheckPages.includes(currentPath)) return;

  // For all other pages: Check profile completion and redirect if needed
  // Profile is incomplete if isComplete is false or undefined
  const isProfileIncomplete = user.profile?.isComplete !== true;

  console.log('🔍 ProfileGuard: Profile completion check', {
   userType: user.userType,
   isComplete: user.profile?.isComplete,
   profileExists: !!user.profile,
   currentPath: currentPath,
   isProfileIncomplete,
   fullProfile: user.profile,
   userKeys: Object.keys(user)
  });

  // Only redirect if OnboardingGuard is not handling this
  if (isProfileIncomplete && !isOnboardingRequired) {
   console.log('👤 ProfileGuard: Profile incomplete, redirecting to onboarding...');
   navigate('/onboarding', { replace: true });
   return;
  } else {
   console.log('✅ ProfileGuard: Profile complete, allowing access to', currentPath);
  }
 }, [user, isAuthenticated, loading, navigate, location.pathname, isOnboardingRequired]);

 // Show loading or return children
 if (loading) {
  return (
   <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
    <div className='text-center'>
     <div className='w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto' />
     <p className='mt-4 text-gray-600'>
      Loading...
     </p>
    </div>
   </div>
  );
 }

 return children;
};

export default ProfileGuard;
