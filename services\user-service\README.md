# VWork User Service

## Tổng quan

VWork User Service là một microservice độc lập chuyên quản lý người dùng và xác thực cho nền tảng VWork. Service này xử lý tất cả các hoạt động liên quan đến người dùng bao gồm đăng ký, đ<PERSON><PERSON> nh<PERSON>, h<PERSON> s<PERSON>, k<PERSON> năng, danh tiếng và tích hợp với Firebase Authentication.

## ✨ Tính năng chính

- 🔐 **Firebase Authentication Integration** - Tích hợp hoàn toàn với Firebase Auth
- 👤 **User Registration & Login** - Đăng ký và đăng nhập người dùng
- 📋 **User Management** - CRUD operations cho người dùng
- 📋 **Profile Management** - Quản lý hồ sơ chi tiết của người dùng
- 🎯 **Skills Management** - Quản lý kỹ năng và chứng chỉ
- ⭐ **Reputation System** - Hệ thống đánh giá và danh tiếng
- 🔍 **Advanced Search** - Tìm kiếm người dùng với bộ lọc phức tạp
- 🏗️ **Microservice Architecture** - Độc lập hoàn toàn, không share code
- 📊 **PostgreSQL Database** - Database riêng biệt cho User Service
- 🚀 **High Performance** - Connection pooling và caching
- 🔒 **Security** - Rate limiting, input validation, JWT tokens

## 🏗️ Kiến trúc

### Database Schema

```sql
- users              # Main user table (Firebase UID as PK)
- user_profiles      # Extended user profile information
- user_skills        # User skills and expertise levels
- user_reputation    # Reputation scores and metrics
- user_certifications # Certifications and achievements
- user_work_history  # Work experience history
- user_sessions      # Session tracking
```

### API Endpoints

#### Authentication & User Management
- `POST /api/v1/users` - Tạo/sync người dùng từ Firebase
- `POST /api/v1/users/login` - Đăng nhập người dùng
- `GET /api/v1/users/me` - Lấy thông tin user hiện tại
- `GET /api/v1/users` - Danh sách người dùng với filters
- `GET /api/v1/users/:id` - Chi tiết người dùng
- `PUT /api/v1/users/:id` - Cập nhật thông tin cơ bản
- `DELETE /api/v1/users/:id` - Vô hiệu hóa tài khoản
- `GET /api/v1/users/search` - Tìm kiếm nâng cao

#### Profiles API
- `GET /api/v1/profiles/:userId` - Lấy hồ sơ người dùng
- `PUT /api/v1/profiles/:userId` - Cập nhật hồ sơ

#### Skills API
- `GET /api/v1/skills/:userId` - Lấy kỹ năng của người dùng
- `POST /api/v1/skills/:userId` - Thêm kỹ năng mới

#### Reputation API
- `GET /api/v1/reputation/:userId` - Lấy thông tin danh tiếng
- `PUT /api/v1/reputation/:userId` - Cập nhật danh tiếng

## 🔐 Authentication Flow

### 1. User Registration
```javascript
// Frontend: Firebase Auth
const userCredential = await createUserWithEmailAndPassword(auth, email, password);

// Frontend: Call User Service
const response = await fetch('/api/v1/users', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${await getIdToken(userCredential.user)}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userType: 'freelancer',
    firstName: 'John',
    lastName: 'Doe'
  })
});
```

### 2. User Login
```javascript
// Frontend: Firebase Auth
const userCredential = await signInWithEmailAndPassword(auth, email, password);

// Frontend: Sync with User Service
const response = await fetch('/api/v1/users/login', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${await getIdToken(userCredential.user)}`,
    'Content-Type': 'application/json'
  }
});
```

### 3. Protected API Calls
```javascript
// Frontend: Any protected endpoint
const response = await fetch('/api/v1/users/me', {
  headers: {
    'Authorization': `Bearer ${await getIdToken(auth.currentUser)}`
  }
});
```

## 🚀 Installation & Setup

### Prerequisites
- Node.js 16+
- PostgreSQL 12+
- Firebase project

### Quick Start
```bash
cd services/user-service
npm install
npm run db:setup  # Run migrations
npm run dev       # Start development server
```

### Environment Variables
```bash
# Server
PORT=3002
NODE_ENV=development

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/vwork_user_service

# Firebase
FIREBASE_PROJECT_ID=vwork-786c3
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3006
CORS_CREDENTIALS=true

# Security
LOG_LEVEL=info
LOG_FORMAT=combined
```

## 📊 API Usage Examples

### Register New User
```bash
curl -X POST http://localhost:3002/api/v1/users \
  -H "Authorization: Bearer <firebase-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "userType": "freelancer",
    "firstName": "John",
    "lastName": "Doe"
  }'
```

### Login User
```bash
curl -X POST http://localhost:3002/api/v1/users/login \
  -H "Authorization: Bearer <firebase-token>" \
  -H "Content-Type: application/json"
```

### Get User Profile
```bash
curl -X GET http://localhost:3002/api/v1/users/me \
  -H "Authorization: Bearer <firebase-token>"
```

### Search Freelancers
```bash
curl -X GET "http://localhost:3002/api/v1/users/search?userType=freelancer&availability=available&minRating=4.5" \
  -H "Authorization: Bearer <firebase-token>"
```

## 🔄 Migration from Auth Service

### What Changed
- **Auth Service removed** - Firebase handles authentication directly
- **User Service enhanced** - Now handles user registration and login
- **Simplified architecture** - Less services, cleaner flow

### Migration Steps
1. **Update frontend** - Use Firebase Auth directly
2. **Update API calls** - Use User Service endpoints
3. **Remove Auth Service** - No longer needed

### Before (with Auth Service)
```javascript
// Frontend
const response = await fetch('/api/v1/auth/login', {
  method: 'POST',
  body: JSON.stringify({ firebaseToken })
});

// API Gateway
app.use('/api/v1/auth', proxyToAuthService);
```

### After (without Auth Service)
```javascript
// Frontend
const response = await fetch('/api/v1/users/login', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${firebaseToken}` }
});

// API Gateway
app.use('/api/v1/users', proxyToUserService);
```

## 🔒 Security Features

### Firebase Token Verification
- **Automatic verification** of Firebase ID tokens
- **User context** added to all requests
- **Token expiration** handling
- **Invalid token** rejection

### Rate Limiting
- **100 requests per 15 minutes** per IP
- **Configurable limits** for different endpoints
- **Automatic blocking** of abusive requests

### Input Validation
- **Joi schemas** for all inputs
- **SQL injection protection** with parameterized queries
- **XSS protection** with input sanitization

## 📈 Performance Optimizations

### Database
- **Connection pooling** with min/max connections
- **Indexed queries** for fast lookups
- **Efficient pagination** for large datasets
- **Query optimization** for complex filters

### Caching
- **Response compression** with gzip
- **Efficient JSON serialization**
- **Minimal database queries** per request

## 🚀 Deployment

### Render.com
```yaml
services:
  - type: web
    name: vwork-user-service
    env: node
    plan: free
    buildCommand: npm ci --production
    startCommand: npm start
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: vwork-user-service-db
          property: connectionString
      - key: FIREBASE_PROJECT_ID
        value: vwork-786c3
```

### Docker
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --production
COPY . .
EXPOSE 3002
CMD ["npm", "start"]
```

## 🔍 Monitoring & Health Checks

### Health Check Endpoint
```bash
curl http://localhost:3002/health
```

Response:
```json
{
  "status": "healthy",
  "service": "VWork User Service",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "database": "connected",
  "firebase": "connected"
}
```

### Logging
- **Structured logging** with Morgan
- **Error tracking** with detailed stack traces
- **Performance metrics** for slow queries
- **Security events** for failed authentications

## 🤝 Integration with Other Services

### Community Service
```javascript
// Community Service calls User Service
const userResponse = await fetch(`${USER_SERVICE_URL}/api/v1/users/${userId}`);
const userData = await userResponse.json();
```

### Team Service
```javascript
// Team Service calls User Service
const userResponse = await fetch(`${USER_SERVICE_URL}/api/v1/users/${userId}`);
const userData = await userResponse.json();
```

### Frontend
```javascript
// Frontend calls via API Gateway
const response = await fetch('/api/v1/users/me', {
  headers: { 'Authorization': `Bearer ${firebaseToken}` }
});
```

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
npm run test:integration
```

### API Tests
```bash
npm run test:api
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Firebase Connection Failed
```bash
# Check Firebase credentials
echo $FIREBASE_PROJECT_ID
echo $FIREBASE_CLIENT_EMAIL
```

#### 2. Database Connection Failed
```bash
# Check database URL
echo $DATABASE_URL
# Test connection
psql $DATABASE_URL -c "SELECT 1;"
```

#### 3. CORS Issues
```bash
# Check allowed origins
echo $ALLOWED_ORIGINS
```

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug npm run dev
```

## 📝 Changelog

### v2.0.0 - Auth Service Integration
- ✅ **Removed Auth Service** - Firebase handles authentication directly
- ✅ **Enhanced User Service** - Added registration and login endpoints
- ✅ **Simplified architecture** - Cleaner service communication
- ✅ **Backward compatibility** - Existing endpoints still work

### v1.0.0 - Initial Release
- ✅ **User management** - CRUD operations
- ✅ **Profile management** - Extended user profiles
- ✅ **Skills management** - User skills and expertise
- ✅ **Reputation system** - User ratings and metrics
- ✅ **Firebase integration** - Authentication and user sync
- ✅ **PostgreSQL database** - Reliable data storage
- ✅ **API Gateway integration** - Seamless routing

## 📞 Support

For issues and questions:
- **GitHub Issues**: Create an issue in the repository
- **Documentation**: Check this README and API docs
- **Logs**: Check service logs for detailed error information

---

**VWork User Service** - Centralized user management and authentication for the VWork platform. 