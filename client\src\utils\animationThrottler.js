/**
 * Animation Throttler
 * Manages animation queue and prevents performance issues
 */

// import { gsap } from 'gsap';
import responsiveAutoscaling from '../services/responsiveAutoscaling';

class AnimationThrottler {
 constructor() {
  this.activeAnimations = new Map();
  this.animationQueue = [];
  this.maxConcurrentAnimations = {
   high: 6,  // Reduced from 8
   medium: 3, // Reduced from 4
   low: 2,   // Same
   minimal: 1 // Same
  };
  
  this.animationPriorities = {
   critical: 1, // Essential UI animations
   high: 2,   // Important user interactions
   medium: 3,  // Visual enhancements
   low: 4    // Decorative animations
  };

  this.isProcessing = false;
  this.frameRateThreshold = 45; // Kill animations if FPS drops below this
  this.lastFrameTime = performance.now();
  this.frameCount = 0;
  this.currentFPS = 60;

  this.startFPSMonitoring();
 }

 /**
  * Monitor FPS and auto-throttle animations
  */
 startFPSMonitoring() {
  const measureFPS = () => {
   this.frameCount++;
   const now = performance.now();
   
   if (now >= this.lastFrameTime + 1000) {
    this.currentFPS = Math.round((this.frameCount * 1000) / (now - this.lastFrameTime));
    this.frameCount = 0;
    this.lastFrameTime = now;
    
    // Auto-throttle if FPS drops
    if (this.currentFPS < this.frameRateThreshold) {
     this.emergencyThrottle();
    }
   }
   
   requestAnimationFrame(measureFPS);
  };
  
  requestAnimationFrame(measureFPS);
 }

 /**
  * Emergency throttling when FPS drops
  */
 emergencyThrottle() {
  console.warn(`🚨 Emergency throttle activated - FPS: ${this.currentFPS}`);
  
  // Kill low priority animations first
  const sortedAnimations = Array.from(this.activeAnimations.entries())
   .sort((a, b) => (b[1].priority || 4) - (a[1].priority || 4));
  
  const toKill = Math.ceil(sortedAnimations.length * 0.4); // Kill 40% of animations
  
  for (let i = 0; i < toKill && i < sortedAnimations.length; i++) {
   const [animation, config] = sortedAnimations[i];
   this.killAnimation(animation, `Emergency throttle - FPS: ${this.currentFPS}`);
  }
 }

 /**
  * Add animation to queue or start immediately
  */
 addAnimation(animationConfig) {
  const {
   name,
   animation,
   priority = 'medium',
   element,
   onComplete,
   onKill
  } = animationConfig;

  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  const maxConcurrent = this.maxConcurrentAnimations[performanceLevel];

  // Check if we can start immediately
  if (this.activeAnimations.size < maxConcurrent) {
   this.startAnimation(animationConfig);
  } else {
   // Add to queue with priority
   this.animationQueue.push({
    ...animationConfig,
    priorityValue: this.animationPriorities[priority] || 3,
    timestamp: Date.now()
   });
   
   // Sort queue by priority (lower number = higher priority)
   this.animationQueue.sort((a, b) => {
    if (a.priorityValue !== b.priorityValue) {
     return a.priorityValue - b.priorityValue;
    }
    return a.timestamp - b.timestamp; // FIFO for same priority
   });

   console.log(`⏳ Animation "${name}" queued (priority: ${priority})`);
  }
 }

 /**
  * Start animation and track it
  */
 startAnimation(config) {
  const { name, animation } = config;

  try {
   // Wrap the animation to track completion
   this.wrapAnimation(animation, {
    onComplete: () => {
     this.removeAnimation(animation);
     this.processQueue();
    },
    onKill: () => {
     this.removeAnimation(animation);
     this.processQueue();
    }
   });

   this.activeAnimations.set(animation, {
    ...config,
    startTime: Date.now()
   });

   console.log(`🎬 Started animation "${name}" (${this.activeAnimations.size}/${this.maxConcurrentAnimations[responsiveAutoscaling.currentPerformanceLevel]} active)`);

  } catch (error) {
   console.error(`❌ Failed to start animation "${name}":`, error);
   this.processQueue();
  }
 }

 /**
  * Wrap animation with tracking callbacks
  */
 wrapAnimation(animation, callbacks) {
  if (animation && typeof animation.eventCallback === 'function') {
   // GSAP timeline or tween
   animation.eventCallback('onComplete', callbacks.onComplete);
   animation.eventCallback('onKill', callbacks.onKill);
  }
  
  return animation;
 }

 /**
  * Remove animation from tracking
  */
 removeAnimation(animation) {
  if (this.activeAnimations.has(animation)) {
   const config = this.activeAnimations.get(animation);
   this.activeAnimations.delete(animation);
   
   const duration = Date.now() - config.startTime;
   console.log(`✅ Completed animation "${config.name}" (${duration}ms)`);
  }
 }

 /**
  * Kill specific animation
  */
 killAnimation(animation, reason = 'Manual kill') {
  if (this.activeAnimations.has(animation)) {
   const config = this.activeAnimations.get(animation);
   
   if (animation && typeof animation.kill === 'function') {
    animation.kill();
   }
   
   this.removeAnimation(animation);
   console.log(`🔥 Killed animation "${config.name}" - ${reason}`);
  }
 }

 /**
  * Process animation queue
  */
 processQueue() {
  if (this.isProcessing || this.animationQueue.length === 0) return;
  
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  const maxConcurrent = this.maxConcurrentAnimations[performanceLevel];
  
  while (this.activeAnimations.size < maxConcurrent && this.animationQueue.length > 0) {
   const nextAnimation = this.animationQueue.shift();
   this.startAnimation(nextAnimation);
  }
 }

 /**
  * Get current status
  */
 getStatus() {
  return {
   active: this.activeAnimations.size,
   queued: this.animationQueue.length,
   maxConcurrent: this.maxConcurrentAnimations[responsiveAutoscaling.currentPerformanceLevel],
   currentFPS: this.currentFPS,
   performanceLevel: responsiveAutoscaling.currentPerformanceLevel
  };
 }

 /**
  * Clear all animations
  */
 clearAll() {
  // Kill all active animations
  this.activeAnimations.forEach((config, animation) => {
   this.killAnimation(animation, 'Clear all');
  });
  
  // Clear queue
  this.animationQueue = [];
  
  console.log('🧹 Cleared all animations');
 }
}

// Export singleton instance
export const animationThrottler = new AnimationThrottler();

// Convenience functions
export const addAnimation = (config) => animationThrottler.addAnimation(config);
export const killAnimation = (animation, reason) => animationThrottler.killAnimation(animation, reason);
export const getAnimationStatus = () => animationThrottler.getStatus();
export const clearAllAnimations = () => animationThrottler.clearAll();

export default animationThrottler;
