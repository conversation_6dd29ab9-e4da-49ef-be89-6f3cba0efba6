# 🔐 Phân tích Logic và Flow Đăng ký Người dùng Mới - VWork

## 📋 Tổng quan

Hệ thống VWork sử dụng một flow đăng ký phức tạp với nhiều bước xác thực và onboarding để đảm bảo trải nghiệm người dùng tốt nhất.

## 🔄 Flow Đăng ký Hoàn chỉnh

### 1. **Bước Đ<PERSON>ng k<PERSON> (Registration)**

#### 1.1 Form Registration
- **Location**: `/register` hoặc `AppleAuthPage`
- **Fields**: `email`, `password`, `firstName`, `lastName`, `userType` (freelancer/client)
- **Validation**:
  - Email format validation
  - Password strength (min 6 chars, max 128 chars)
  - Weak password detection
  - User type validation

#### 1.2 Firebase Authentication
```javascript
// Từ AuthContext.js
const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password);
await updateProfile(firebaseUser, { displayName: userData.name });
```

#### 1.3 Email Verification Setup
```javascript
// Gửi email verification với custom URL
await sendEmailVerification(firebaseUser, {
  url: `${window.location.origin}/verify-email?continueUrl=${encodeURIComponent(window.location.origin + '/dashboard')}`,
  handleCodeInApp: false,
});
```

#### 1.4 Temporary User Data Storage
```javascript
// Lưu vào tempUsers collection (chưa verify email)
const tempUserDocData = {
  name: userData.name,
  email: email,
  userType: userData.userType,
  guildRank: userData.userType === 'client' ? 'lord' : 'apprentice',
  guildTitle: userData.userType === 'client' ? 'Noble Client' : 'New Member',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  isActive: true,
  isVerified: false,
  emailVerificationSent: true,
  profile: {
    bio: '',
    avatar: firebaseUser.photoURL || null,
    location: { country: '', city: '', timezone: '' },
    website: '',
    phoneNumber: '',
    skills: [],
    hourlyRate: userData.userType === 'freelancer' ? 0 : null,
    availability: userData.userType === 'freelancer' ? 'available' : null,
    isComplete: false, // Trigger onboarding
  },
  stats: {
    totalProjects: 0,
    completedProjects: 0,
    totalEarnings: 0,
    averageRating: 0,
    totalReviews: 0,
    responseTime: 24,
    completionRate: 100,
  },
  preferences: {
    emailNotifications: true,
    pushNotifications: true,
    theme: 'medieval',
    language: 'vi',
  },
};

await setDoc(doc(db, 'tempUsers', firebaseUser.uid), tempUserDocData);
```

### 2. **Logic Temp User vs Main User**

#### 2.1 TempUsers Collection
- **Mục đích**: Chứa data của users chưa verify email
- **Trạng thái**: `isVerified: false`
- **Storage**: Temporary storage cho đến khi email được verify
- **Access**: Limited access, không thể truy cập features chính

#### 2.2 Users Collection
- **Mục đích**: Chứa data của users đã verify email
- **Trạng thái**: `isVerified: true`
- **Storage**: Main storage cho authenticated users
- **Access**: Full access to all features

#### 2.3 Migration Logic
```javascript
// Khi user verify email, data được chuyển từ tempUsers → users
if (firebaseUser.emailVerified && !userData.isVerified) {
  const verifiedUserData = {
    ...userData,
    isVerified: true,
    emailVerifiedAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    profile: {
      ...userData.profile,
      isComplete: false // Trigger onboarding
    }
  };
  
  // Save to main users collection
  await setDoc(doc(db, 'users', firebaseUser.uid), verifiedUserData);
  
  // Delete from temporary collection
  await deleteDoc(doc(db, 'tempUsers', firebaseUser.uid));
}
```

### 3. **Email Verification Flow**

#### 3.1 Email Verification Process
1. **User click link** trong email verification
2. **Firebase applyActionCode**: `applyActionCode(auth, actionCode)`
3. **Reload user**: `reload(auth.currentUser)` để update emailVerified status
4. **Move data**: Từ tempUsers → users collection
5. **Update status**: Set `isVerified: true` và `emailVerifiedAt`

#### 3.2 EmailVerificationPage.js
```javascript
// Auto redirect khi verified
if (user?.emailVerified) {
  navigate('/login-success');
}

// Resend verification với cooldown 60s
const handleResendVerification = async () => {
  if (!user || countdown > 0) return;
  
  setLoading(true);
  try {
    await sendEmailVerification(auth.currentUser);
    toast.success('Email xác thực đã được gửi!');
    setCountdown(60); // 60 second cooldown
  } catch (error) {
    toast.error('Không thể gửi email xác thực. Vui lòng thử lại.');
  } finally {
    setLoading(false);
  }
};
```

### 4. **Onboarding Flow Logic**

#### 4.1 OnboardingContext.js Workflow
```javascript
const ONBOARDING_STEPS = {
  WELCOME: 'welcome',
  BASIC_INFO: 'basic_info', 
  PROFILE_DETAILS: 'profile_details',
  SKILLS_SETUP: 'skills_setup', // Chỉ cho freelancer
  PREFERENCES: 'preferences',
  COMPLETE: 'complete'
};

// Check onboarding status
const checkOnboardingStatus = async () => {
  if (user?.profile?.isComplete) {
    setIsOnboardingRequired(false);
    return;
  }
  
  // If profile not complete, require onboarding
  if (!userData.profile?.isComplete) {
    setIsOnboardingRequired(true);
    setCurrentStep(ONBOARDING_STEPS.WELCOME);
  }
};
```

#### 4.2 Onboarding Steps Configuration
- **Freelancer Flow**: 
  ```
  Welcome → Basic Info → Profile Details → Skills Setup → Preferences → Complete
  ```
- **Client Flow**: 
  ```
  Welcome → Basic Info → Profile Details → Preferences → Complete
  ```

#### 4.3 Step Components
- **WelcomeStep**: Giới thiệu và features
- **BasicInfoStep**: Tên, bio, location, contact
- **ProfileDetailsStep**: Hourly rate, availability, experience
- **SkillsSetupStep**: Chọn skills (freelancer only)
- **PreferencesStep**: Notifications, theme, language
- **CompleteStep**: Hoàn thành và redirect

### 5. **Guard Components Logic**

#### 5.1 ProfileGuard.js
```javascript
// Check authentication status và redirect logic
useEffect(() => {
  if (loading || !isAuthenticated || !user) return;

  // First priority: Check email verification
  if (!user.emailVerified) {
    navigate('/verify-email', { replace: true });
    return;
  }

  // If OnboardingGuard is handling this user, don't do anything
  if (isOnboardingRequired) {
    return;
  }

  // Check profile completion
  const isProfileIncomplete = user.profile?.isComplete !== true;
  
  if (isProfileIncomplete && !isOnboardingRequired) {
    navigate('/onboarding', { replace: true });
    return;
  }
}, [user, isAuthenticated, loading, navigate, location.pathname, isOnboardingRequired]);
```

#### 5.2 OnboardingGuard.js
```javascript
// If user is authenticated and needs onboarding, show onboarding flow
if (user && user.emailVerified && isOnboardingRequired) {
  return <OnboardingFlow />;
}

// Otherwise, show the normal app content
return children;
```

#### 5.3 ProtectedRoute.js
```javascript
// Protect routes dựa trên auth status
if (requireAuth && !isAuthenticated) {
  return <Navigate to={redirectTo} state={{ from: location }} replace />;
}

// Redirect authenticated users away from auth pages
if (!requireAuth && isAuthenticated) {
  const from = location.state?.from?.pathname || '/dashboard';
  return <Navigate to={from} replace />;
}
```

### 6. **Backend Integration (User Service)**

#### 6.1 PostgreSQL Schema
```sql
-- users table: Main user data
CREATE TABLE users (
  id VARCHAR(255) PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  email_verified BOOLEAN DEFAULT FALSE,
  display_name VARCHAR(255),
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  phone_number VARCHAR(20),
  avatar_url TEXT,
  user_type VARCHAR(50) NOT NULL,
  last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- user_profiles table: Extended profile info
CREATE TABLE user_profiles (
  user_id VARCHAR(255) PRIMARY KEY REFERENCES users(id),
  bio TEXT,
  language_preference VARCHAR(10) DEFAULT 'vi',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- user_reputation table: Reputation tracking
CREATE TABLE user_reputation (
  user_id VARCHAR(255) PRIMARY KEY REFERENCES users(id),
  total_projects INTEGER DEFAULT 0,
  completed_projects INTEGER DEFAULT 0,
  total_earnings DECIMAL(10,2) DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0,
  total_reviews INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.2 Sync Process
```javascript
// Firebase token verification
const firebaseUser = await verifyFirebaseToken(req);

// Create/update user records
if (existingUser) {
  // Update existing user
  const updateResult = await database.query(`
    UPDATE users SET 
      email_verified = $1,
      display_name = $2,
      last_login = CURRENT_TIMESTAMP
    WHERE id = $3
    RETURNING *
  `, [firebaseUser.emailVerified, firebaseUser.displayName, firebaseUser.uid]);
} else {
  // Create new user
  await database.transaction(async (client) => {
    // Insert user
    const userResult = await client.query(`
      INSERT INTO users (id, email, email_verified, display_name, user_type)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [firebaseUser.uid, firebaseUser.email, firebaseUser.emailVerified, 
        firebaseUser.displayName, userType]);

    // Create user profile
    await client.query(`
      INSERT INTO user_profiles (user_id, bio, language_preference)
      VALUES ($1, $2, $3)
    `, [user.id, profile.bio || '', profile.languagePreference || 'vi']);

    // Create user reputation
    await client.query(`
      INSERT INTO user_reputation (user_id)
      VALUES ($1)
    `, [user.id]);
  });
}
```

## 🔧 Các Điểm Quan trọng

### 1. **Security Considerations**
- Email verification bắt buộc trước khi access features
- Temporary user data được lưu riêng biệt
- Firebase token verification cho backend calls
- Password strength validation

### 2. **User Experience**
- Progressive disclosure: từ temp user → verified user → complete profile
- Onboarding flow tùy chỉnh theo user type
- Clear error messages và guidance
- Resend email verification với cooldown

### 3. **Data Consistency**
- Migration logic đảm bảo data integrity
- Profile completion tracking
- Backend sync với Firebase
- Error handling cho network issues

### 4. **Performance**
- Lazy loading cho onboarding steps
- Optimized database queries
- Caching cho user data
- Background sync processes

## 🚀 Testing Scenarios

### 1. **New User Registration**
1. Navigate to `/register`
2. Fill registration form
3. Verify email verification page appears
4. Check email for verification link
5. Click verification link
6. Verify onboarding flow starts

### 2. **Email Verification**
1. Test resend functionality
2. Verify cooldown period
3. Test invalid verification links
4. Check redirect after verification

### 3. **Onboarding Flow**
1. Test all steps for freelancer
2. Test all steps for client
3. Verify data persistence
4. Test skip functionality
5. Check completion redirect

### 4. **Guard Components**
1. Test unauthenticated access
2. Test unverified email access
3. Test incomplete profile access
4. Verify proper redirects

## 📝 Kết luận

Hệ thống VWork có một flow đăng ký phức tạp nhưng được thiết kế tốt với:
- **Security**: Email verification bắt buộc
- **UX**: Progressive onboarding
- **Scalability**: Backend integration
- **Reliability**: Error handling và data consistency

Flow này đảm bảo users có trải nghiệm mượt mà từ registration đến fully functional account. 