import React from 'react';
import { ExclamationTriangleIcon, InformationCircleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

const GoogleLoginHelper = ({ errorCode, attempted, onRetry, isRetrying = false }) => {
 if (!attempted || (!errorCode || (errorCode !== 'auth/popup-closed-by-user' && errorCode !== 'auth/popup-blocked' && errorCode !== 'auth/timeout'))) {
  return null;
 }

 const getHelperContent = () => {
  switch (errorCode) {
   case 'auth/popup-closed-by-user':
    return {
     icon: InformationCircleIcon,
     iconColor: 'text-blue-600',
     bgColor: 'bg-blue-50',
     borderColor: 'border-blue-200',
     textColor: 'text-blue-800',
     title: '<PERSON><PERSON><PERSON> nhập Google bị gián đoạn',
     message: 'Không sao cả! Bạn có thể thử lại bất cứ lúc nào.',
     showRetryButton: true,
     tips: [
      'Đảm bảo hoàn thành quá trình trong cửa sổ popup',
      'Không đóng cửa sổ popup cho đến khi thấy thông báo thành công',
      'Nếu vẫn gặp vấn đề, hãy thử tắt trình chặn popup'
     ]
    };
   
   case 'auth/popup-blocked':
    return {
     icon: ExclamationTriangleIcon,
     iconColor: 'text-orange-600',
     bgColor: 'bg-orange-50',
     borderColor: 'border-orange-200',
     textColor: 'text-orange-800',
     title: 'Popup bị chặn bởi trình duyệt',
     message: 'Trình duyệt đã chặn cửa sổ đăng nhập. Hãy làm theo hướng dẫn bên dưới.',
     showRetryButton: true,
     showRedirectOption: true,
     tips: [
      'Tìm biểu tượng popup bị chặn trên thanh địa chỉ',
      'Nhấn vào biểu tượng và chọn "Luôn cho phép popup"',
      'Tắt trình chặn popup tạm thời',
      'Hoặc sử dụng phương thức chuyển hướng bên dưới'
     ]
    };
   
   case 'auth/timeout':
    return {
     icon: ExclamationTriangleIcon,
     iconColor: 'text-red-600',
     bgColor: 'bg-red-50',
     borderColor: 'border-red-200',
     textColor: 'text-red-800',
     title: 'Đăng nhập Google bị timeout',
     message: 'Quá trình đăng nhập mất quá nhiều thời gian. Hãy thử lại.',
     showRetryButton: true,
     tips: [
      'Kiểm tra kết nối internet của bạn',
      'Đảm bảo không có phần mềm chặn kết nối',
      'Nếu vẫn lỗi, hãy thử đăng nhập bằng email/mật khẩu'
     ]
    };
   
   default:
    return null;
  }
 };

 const content = getHelperContent();
 if (!content) return null;

 const { icon: Icon, iconColor, bgColor, borderColor, textColor, title, message, tips, showRetryButton, showRedirectOption } = content;

 return (
  <div className={`${bgColor} border ${borderColor} rounded-lg p-4 mt-4 animate-in fade-in duration-300`}>
   <div className="flex items-start space-x-3">
    <div className="flex-shrink-0">
     <Icon className={`w-5 h-5 ${iconColor}`} />
    </div>
    <div className={`flex-1 text-sm ${textColor}`}>
     <p className="font-medium mb-1">{title}</p>
     {message && (
      <p className="mb-3 text-sm opacity-90">{message}</p>
     )}

     {/* Action Buttons */}
     {(showRetryButton || showRedirectOption) && (
      <div className="flex flex-col sm:flex-row gap-2 mb-3">
       {showRetryButton && onRetry && (
        <button
         onClick={onRetry}
         disabled={isRetrying}
         className="flex items-center justify-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
         {isRetrying ? (
          <>
           <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
           <span>Đang thử lại...</span>
          </>
         ) : (
          <>
           <ArrowPathIcon className="w-4 h-4" />
           <span>Thử lại</span>
          </>
         )}
        </button>
       )}

       {showRedirectOption && (
        <button
         onClick={() => onRetry && onRetry('redirect')}
         className="flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
         <span>Dùng chuyển hướng</span>
        </button>
       )}
      </div>
     )}

     {/* Tips */}
     <ul className="list-disc list-inside space-y-1 text-xs opacity-80">
      {tips.map((tip, index) => (
       <li key={index}>{tip}</li>
      ))}
     </ul>
    </div>
   </div>
  </div>
 );
};

export default GoogleLoginHelper;
