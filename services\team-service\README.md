# Team Service - NERAFUS Platform

Team Service là microservice quản lý tính năng Freelancer Team cho nền tảng NERAFUS, cho phép freelancer tạ<PERSON> và quản lý teams để thực hiện các dự án lớn.

## 🚀 Tính năng chính

### ✅ Team Management
- <PERSON><PERSON><PERSON>, c<PERSON><PERSON>, xóa teams
- Quản lý thành viên team
- Chia sẻ lợi nhuận
- Đi<PERSON>u kiện thành lập team (3-10 thành viên, 10+ dự án, 4.8+ rating)

### ✅ Invitation System
- Gửi lời mời tham gia team
- Chấp nhận/từ chối lời mời
- Quản lý trạng thái lời mời
- Tự động kích hoạt team khi tất cả thành viên chấp nhận

### ✅ Team Chat
- Chat real-time trong team
- Lị<PERSON> sử tin nhắn
- <PERSON><PERSON><PERSON> tin nhắn (chỉ người gửi)
- <PERSON><PERSON><PERSON> tin nhắn chưa đọc

### ✅ Discovery & Search
- Khám phá teams nổi bật
- T<PERSON>m kiếm teams theo tiêu chí
- Phân trang và lọc

## 🏗️ Architecture

```
Team Service
├── src/
│   ├── routes/
│   │   ├── teams.js          # Team CRUD operations
│   │   ├── invitations.js    # Invitation management
│   │   └── chat.js          # Team chat
│   ├── config/
│   │   ├── config.js        # Centralized configuration
│   │   ├── firebase.js      # Firebase Auth
│   │   └── postgresql.js    # PostgreSQL connection
│   ├── middleware/
│   │   ├── auth.js          # Authentication
│   │   └── validation.js    # Request validation
│   └── utils/
│       ├── logger.js        # Winston logging
│       └── response.js      # API response helpers
├── migrations/
│   └── team_schema.sql      # Database schema
└── app.js                   # Express app setup
```

## 🔧 API Endpoints

### Teams
```
GET    /api/teams              # Get teams with pagination & filters
GET    /api/teams/featured     # Get featured teams
GET    /api/teams/:id          # Get team details
POST   /api/teams              # Create new team
PUT    /api/teams/:id          # Update team (leader only)
DELETE /api/teams/:id          # Delete team (leader only)
GET    /api/teams/:id/stats    # Get team statistics
```

### Invitations
```
GET    /api/invitations/my-invitations     # Get user's pending invitations
GET    /api/invitations/team/:teamId       # Get team invitations (leader only)
POST   /api/invitations/team/:teamId       # Send invitation
PUT    /api/invitations/:id/accept         # Accept invitation
PUT    /api/invitations/:id/reject         # Reject invitation
DELETE /api/invitations/:id                # Cancel invitation
POST   /api/invitations/:id/resend         # Resend invitation
```

### Chat
```
GET    /api/chat/team/:teamId              # Get chat messages
POST   /api/chat/team/:teamId              # Send message
DELETE /api/chat/message/:messageId        # Delete message
GET    /api/chat/team/:teamId/unread       # Get unread count
PUT    /api/chat/team/:teamId/read         # Mark as read
GET    /api/chat/my-teams/recent           # Get recent messages
```

## 🗄️ Database Schema

### Teams Table
```sql
teams (
  id UUID PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slogan TEXT,
  description TEXT,
  logo_url TEXT,
  leader_id VARCHAR(255) NOT NULL,
  status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
  member_count INTEGER DEFAULT 0,
  total_projects INTEGER DEFAULT 0,
  total_earnings DECIMAL(15,2) DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
```

### Team Members Table
```sql
team_members (
  id UUID PRIMARY KEY,
  team_id UUID REFERENCES teams(id),
  user_id VARCHAR(255) NOT NULL,
  role ENUM('leader', 'member') DEFAULT 'member',
  position VARCHAR(100),
  profit_share DECIMAL(5,2) DEFAULT 0,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  status ENUM('active', 'pending', 'left') DEFAULT 'pending',
  UNIQUE(team_id, user_id)
)
```

### Team Invitations Table
```sql
team_invitations (
  id UUID PRIMARY KEY,
  team_id UUID REFERENCES teams(id),
  inviter_id VARCHAR(255) NOT NULL,
  invitee_id VARCHAR(255) NOT NULL,
  message TEXT,
  status ENUM('pending', 'accepted', 'rejected') DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days'),
  UNIQUE(team_id, invitee_id)
)
```

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- PostgreSQL 12+
- Firebase project

### Installation

1. **Clone và cài đặt dependencies:**
```bash
cd services/team-service
npm install
```

2. **Cấu hình environment:**
```bash
cp env.example .env
# Cập nhật các biến môi trường trong .env
```

3. **Khởi tạo database:**
```bash
npm run db:setup
```

4. **Khởi động service:**
```bash
# Development
npm run dev

# Production
npm start
```

### Environment Variables

```bash
# Service Configuration
NODE_ENV=development
PORT=3008

# Database
DATABASE_URL=postgresql://username:password@host:port/database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_team_service
DB_USER=vwork_admin
DB_PASSWORD=VWork2024!

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-service-account-email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Team Service Specific
MIN_FRIENDSHIP_DAYS=5
MIN_COMPLETED_PROJECTS=10
MIN_RATING=4.8
MAX_TEAM_MEMBERS=10
MIN_TEAM_MEMBERS=3
```

## 🔐 Authentication

Service sử dụng Firebase Authentication:
- Tất cả endpoints (trừ health check) yêu cầu Firebase ID token
- Token được gửi trong header: `Authorization: Bearer <token>`
- User info được lưu trong `req.user`

## 📊 Response Format

Tất cả responses tuân theo format chuẩn:

```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

## 🔗 Integration

### API Gateway
Service được tích hợp với API Gateway qua proxy:
```
/api/v1/teams -> http://localhost:3008/api/teams
/api/v1/team-invitations -> http://localhost:3008/api/invitations
/api/v1/team-chat -> http://localhost:3008/api/chat
```

### External Services
- **User Service**: Lấy thông tin user profile
- **Project Service**: Quản lý dự án của team
- **Chat Service**: Real-time messaging (future)

## 🧪 Testing

```bash
# Run tests
npm test

# Test service manually
npm run test:service
```

## 📝 Development

### Adding New Features
1. Tạo route mới trong `src/routes/`
2. Thêm validation schema trong `src/middleware/validation.js`
3. Cập nhật database schema nếu cần
4. Thêm tests
5. Cập nhật documentation

### Database Migrations
```bash
# Create new migration
npm run db:migrate

# Check database schema
npm run db:check
```

## 🚀 Deployment

### Production
```bash
# Build và deploy
npm run build:production
npm start
```

### Docker
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3008
CMD ["npm", "start"]
```

## 📚 Documentation

- [API Documentation](docs/API.md)
- [Database Schema](docs/DATABASE.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details. 