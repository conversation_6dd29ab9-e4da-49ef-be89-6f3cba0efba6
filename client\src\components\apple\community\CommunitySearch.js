import React, { useState, useEffect, useRef } from 'react';
import {
 MagnifyingGlassIcon,
 FunnelIcon,
 XMarkIcon,
 ClockIcon,
 FireIcon,
 TrophyIcon,
 UserIcon,
 TagIcon,
 AdjustmentsHorizontalIcon,
 ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { gsap } from 'gsap';

const CommunitySearch = ({ 
 searchQuery, 
 onSearchChange, 
 filters, 
 onFiltersChange,
 categories,
 searchSuggestions = [],
 recentSearches = []
}) => {
 const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
 const [showSuggestions, setShowSuggestions] = useState(false);
 const [activeFilters, setActiveFilters] = useState(filters || {});
 const [searchFocus, setSearchFocus] = useState(false);

 const searchRef = useRef(null);
 const suggestionsRef = useRef(null);
 const filtersRef = useRef(null);

 // Animation for advanced filters
 useEffect(() => {
  if (showAdvancedFilters && filtersRef.current) {
   gsap.fromTo(
    filtersRef.current,
    { height: 0, opacity: 0 },
    { height: 'auto', opacity: 1, duration: 0.3, ease: 'power2.out' }
   );
  }
 }, [showAdvancedFilters]);

 // Handle search input changes
 const handleSearchChange = (value) => {
  onSearchChange(value);
  setShowSuggestions(value.length > 0);
 };

 // Handle filter changes
 const handleFilterChange = (filterType, value) => {
  const newFilters = { ...activeFilters, [filterType]: value };
  setActiveFilters(newFilters);
  onFiltersChange(newFilters);
 };

 // Clear all filters
 const clearFilters = () => {
  const clearedFilters = {};
  setActiveFilters(clearedFilters);
  onFiltersChange(clearedFilters);
 };

 // Get active filter count
 const getActiveFilterCount = () => {
  return Object.values(activeFilters).filter(value => 
   value && value !== 'all' && value !== ''
  ).length;
 };

 const timeRanges = [
  { value: 'all', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'year', label: 'This Year' }
 ];

 const sortOptions = [
  { value: 'recent', label: 'Most Recent', icon: ClockIcon },
  { value: 'popular', label: 'Most Popular', icon: TrophyIcon },
  { value: 'trending', label: 'Trending', icon: FireIcon },
  { value: 'discussed', label: 'Most Discussed', icon: UserIcon }
 ];

 const postTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'text', label: 'Text Posts' },
  { value: 'discussion', label: 'Discussions' },
  { value: 'help', label: 'Help Requests' },
  { value: 'showcase', label: 'Showcases' },
  { value: 'news', label: 'News' },
  { value: 'job', label: 'Job Posts' }
 ];

 return (
  <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
   {/* Main Search Bar */}
   <div className="p-6 pb-4">
    <div className="relative">
     <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
      <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
     </div>
     <input
      ref={searchRef}
      type="text"
      placeholder="Search posts, topics, users, or tags..."
      value={searchQuery}
      onChange={(e) => handleSearchChange(e.target.value)}
      onFocus={() => {
       setSearchFocus(true);
       setShowSuggestions(searchQuery.length > 0);
      }}
      onBlur={() => {
       setTimeout(() => {
        setSearchFocus(false);
        setShowSuggestions(false);
       }, 200);
      }}
      className={`block w-full pl-12 pr-12 py-4 border rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-900 transition-all duration-300 ${
       searchFocus ? 'border-blue-300 shadow-lg' : 'border-gray-200'
      }`}
     />
     {searchQuery && (
      <button
       onClick={() => handleSearchChange('')}
       className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600"
      >
       <XMarkIcon className="h-5 w-5" />
      </button>
     )}

     {/* Search Suggestions Dropdown */}
     {showSuggestions && (searchSuggestions.length > 0 || recentSearches.length > 0) && (
      <div
       ref={suggestionsRef}
       className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-200 z-20 max-h-80 overflow-y-auto"
      >
       {/* Recent Searches */}
       {recentSearches.length > 0 && (
        <div className="p-3 border-b border-gray-100">
         <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
          Recent Searches
         </h4>
         {recentSearches.slice(0, 3).map((search, index) => (
          <button
           key={index}
           onClick={() => handleSearchChange(search)}
           className="flex items-center space-x-2 w-full text-left px-2 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
           <ClockIcon className="h-4 w-4 text-gray-400" />
           <span className="text-sm text-gray-700">{search}</span>
          </button>
         ))}
        </div>
       )}

       {/* Search Suggestions */}
       {searchSuggestions.length > 0 && (
        <div className="p-3">
         <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
          Suggestions
         </h4>
         {searchSuggestions.slice(0, 5).map((suggestion, index) => (
          <button
           key={index}
           onClick={() => handleSearchChange(suggestion.text)}
           className="flex items-center space-x-2 w-full text-left px-2 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
           {suggestion.type === 'user' && <UserIcon className="h-4 w-4 text-blue-500" />}
           {suggestion.type === 'tag' && <TagIcon className="h-4 w-4 text-green-500" />}
           {suggestion.type === 'topic' && <MagnifyingGlassIcon className="h-4 w-4 text-purple-500" />}
           <span className="text-sm text-gray-700">{suggestion.text}</span>
           {suggestion.count && (
            <span className="text-xs text-gray-500 ml-auto">
             {suggestion.count} posts
            </span>
           )}
          </button>
         ))}
        </div>
       )}
      </div>
     )}
    </div>
   </div>

   {/* Quick Filters */}
   <div className="px-6 pb-4">
    <div className="flex flex-wrap items-center gap-3">
     {/* Sort Options */}
     {sortOptions.map((option) => (
      <button
       key={option.value}
       onClick={() => handleFilterChange('sort', option.value)}
       className={`inline-flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105 ${
        activeFilters.sort === option.value
         ? 'bg-blue-600 text-white shadow-lg'
         : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
       }`}
      >
       <option.icon className="h-4 w-4" />
       <span>{option.label}</span>
      </button>
     ))}

     {/* Advanced Filters Toggle */}
     <button
      onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
      className={`inline-flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105 ${
       showAdvancedFilters || getActiveFilterCount() > 0
        ? 'bg-purple-600 text-white shadow-lg'
        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
     >
      <AdjustmentsHorizontalIcon className="h-4 w-4" />
      <span>Filters</span>
      {getActiveFilterCount() > 0 && (
       <span className="bg-white text-purple-600 text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
        {getActiveFilterCount()}
       </span>
      )}
      <ChevronDownIcon className={`h-4 w-4 transition-transform duration-300 ${showAdvancedFilters ? 'rotate-180' : ''}`} />
     </button>

     {/* Clear Filters */}
     {getActiveFilterCount() > 0 && (
      <button
       onClick={clearFilters}
       className="inline-flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium bg-red-100 text-red-600 hover:bg-red-200 transition-all duration-300 hover:scale-105"
      >
       <XMarkIcon className="h-4 w-4" />
       <span>Clear All</span>
      </button>
     )}
    </div>
   </div>

   {/* Advanced Filters Panel */}
   {showAdvancedFilters && (
    <div ref={filtersRef} className="border-t border-gray-200 bg-gray-50">
     <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
       {/* Time Range */}
       <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
         Time Range
        </label>
        <select
         value={activeFilters.timeRange || 'all'}
         onChange={(e) => handleFilterChange('timeRange', e.target.value)}
         className="w-full px-4 py-3 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
        >
         {timeRanges.map((range) => (
          <option key={range.value} value={range.value}>
           {range.label}
          </option>
         ))}
        </select>
       </div>

       {/* Post Type */}
       <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
         Post Type
        </label>
        <select
         value={activeFilters.postType || 'all'}
         onChange={(e) => handleFilterChange('postType', e.target.value)}
         className="w-full px-4 py-3 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
        >
         {postTypes.map((type) => (
          <option key={type.value} value={type.value}>
           {type.label}
          </option>
         ))}
        </select>
       </div>

       {/* Engagement Level */}
       <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
         Engagement Level
        </label>
        <select
         value={activeFilters.engagement || 'all'}
         onChange={(e) => handleFilterChange('engagement', e.target.value)}
         className="w-full px-4 py-3 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
        >
         <option value="all">All Posts</option>
         <option value="trending">Trending</option>
         <option value="featured">Featured</option>
         <option value="solved">Solved</option>
         <option value="high">High Engagement</option>
         <option value="low">Low Engagement</option>
        </select>
       </div>
      </div>

      {/* Tags Input */}
      <div className="mt-6">
       <label className="block text-sm font-medium text-gray-700 mb-3">
        Tags (comma-separated)
       </label>
       <input
        type="text"
        placeholder="e.g. react, javascript, freelancing"
        value={activeFilters.tags || ''}
        onChange={(e) => handleFilterChange('tags', e.target.value)}
        className="w-full px-4 py-3 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
       />
      </div>
     </div>
    </div>
   )}
  </div>
 );
};

export default CommunitySearch;
