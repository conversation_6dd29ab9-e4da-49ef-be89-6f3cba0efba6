#!/usr/bin/env node

/**
 * Deploy All Community Service Fixes
 * Fixes CORS, adds missing endpoints, and resolves database issues
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 Deploying All Community Service Fixes...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

// Check if we're in the right directory
const projectRoot = path.resolve(__dirname, '..');
if (!fs.existsSync(path.join(projectRoot, 'package.json'))) {
  logError('Script must be run from the project root directory');
  process.exit(1);
}

// Check if git is available
try {
  execSync('git --version', { stdio: 'ignore' });
} catch (error) {
  logError('Git not found. Please install Git first.');
  process.exit(1);
}

// Check if we're in a git repository
try {
  execSync('git status', { stdio: 'ignore' });
} catch (error) {
  logError('Not in a git repository. Please initialize git first.');
  process.exit(1);
}

async function deployAllFixes() {
  try {
    logStep('1', 'Preparing comprehensive deployment...');
    
    // Check current branch
    const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    logInfo(`Current branch: ${currentBranch}`);
    
    if (currentBranch !== 'main') {
      logWarning(`You are on branch '${currentBranch}'. Consider switching to 'main' for deployment.`);
    }
    
    logStep('2', 'Adding all fixes to git...');
    
    // Add all changes
    execSync('git add .', { stdio: 'inherit' });
    logSuccess('All files added to git');
    
    logStep('3', 'Committing comprehensive fixes...');
    
    // Commit changes
    const commitMessage = `fix: Comprehensive Community Service fixes

- Fix CORS configuration for nerafus.com
- Add missing /api/categories endpoint
- Add missing /api/stats endpoint  
- Add missing /api/stats/user/:userId endpoint
- Update API documentation
- Fix database connection issues
- Add proper error handling
- Update environment variables

This fixes all 404 and 500 errors for community endpoints.`;
    
    execSync(`git commit -m "${commitMessage}"`, { stdio: 'inherit' });
    logSuccess('All fixes committed');
    
    logStep('4', 'Pushing to remote repository...');
    
    // Push to remote
    execSync('git push origin main', { stdio: 'inherit' });
    logSuccess('All fixes pushed to remote repository');
    
    return true;
    
  } catch (error) {
    logError(`Deployment failed: ${error.message}`);
    return false;
  }
}

async function main() {
  log('🚀 VWork Community Service - All Fixes Deployment', 'bright');
  log('This script will deploy all fixes for CORS, missing endpoints, and database issues', 'blue');
  
  // Deploy all fixes
  const deploymentSuccess = await deployAllFixes();
  
  if (!deploymentSuccess) {
    logError('Deployment failed.');
    process.exit(1);
  }
  
  log('\n🎉 All Community Service Fixes Deployed Successfully!', 'bright');
  log('\n🔧 Fixes Applied:', 'cyan');
  log('• ✅ Fixed CORS configuration for nerafus.com', 'green');
  log('• ✅ Added /api/categories endpoint', 'green');
  log('• ✅ Added /api/stats endpoint', 'green');
  log('• ✅ Added /api/stats/user/:userId endpoint', 'green');
  log('• ✅ Updated API documentation', 'green');
  log('• ✅ Fixed database connection issues', 'green');
  log('• ✅ Added proper error handling', 'green');
  log('• ✅ Updated environment variables', 'green');
  
  log('\n📋 New Endpoints Available:', 'cyan');
  log('• GET /api/v1/community/categories - Get all categories', 'yellow');
  log('• GET /api/v1/community/categories/:category/posts - Get posts by category', 'yellow');
  log('• GET /api/v1/community/categories/:category/stats - Get category stats', 'yellow');
  log('• GET /api/v1/community/stats - Get community statistics', 'yellow');
  log('• GET /api/v1/community/stats/user/:userId - Get user statistics', 'yellow');
  
  log('\n🔧 Issues Fixed:', 'cyan');
  log('• ❌ 404 Error: /api/v1/community/categories → ✅ Fixed', 'green');
  log('• ❌ 404 Error: /api/v1/community/stats → ✅ Fixed', 'green');
  log('• ❌ 500 Error: /api/v1/community/posts → ✅ Fixed', 'green');
  log('• ❌ CORS Error: nerafus.com → ✅ Fixed', 'green');
  
  log('\n📋 Next Steps:', 'cyan');
  log('1. ⏳ Wait 3-5 minutes for Render to detect changes and deploy', 'yellow');
  log('2. 🔍 Monitor deployment at: https://dashboard.render.com', 'yellow');
  log('3. 🧪 Test the community page at https://nerafus.com/#/community', 'yellow');
  log('4. ✅ Check that all endpoints are working correctly', 'yellow');
  
  log('\n🔍 To monitor deployment:', 'cyan');
  log('• Check Render dashboard: https://dashboard.render.com', 'yellow');
  log('• Monitor service logs in Render dashboard', 'yellow');
  log('• Test endpoints after deployment completes', 'yellow');
  
  log('\n🧪 To test endpoints after deployment:', 'cyan');
  log('• Categories: https://nerafus-community-service.onrender.com/api/categories', 'yellow');
  log('• Stats: https://nerafus-community-service.onrender.com/api/stats', 'yellow');
  log('• API Gateway: https://vwork-api-gateway.onrender.com/api/v1/community/categories', 'yellow');
  log('• Frontend: https://nerafus.com/#/community', 'yellow');
  
  log('\n📊 Expected Results:', 'cyan');
  log('• ✅ No more 404 errors for categories and stats endpoints', 'green');
  log('• ✅ No more 500 errors for posts endpoint', 'green');
  log('• ✅ No more CORS errors from nerafus.com', 'green');
  log('• ✅ Community page loads successfully', 'green');
  log('• ✅ All statistics and categories display correctly', 'green');
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:');
  logError(`Promise: ${promise}`);
  logError(`Reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError('Uncaught Exception:');
  logError(error.message);
  logError(error.stack);
  process.exit(1);
});

// Run the deployment
main().catch(error => {
  logError('Deployment failed:');
  logError(error.message);
  process.exit(1);
}); 