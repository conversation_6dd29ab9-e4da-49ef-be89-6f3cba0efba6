/**
 * Data Service
 * Direct integration with Community API Service - Real API Only
 */

import {
 likesApi,
 commentsApi,
 postsApi,
 communityUtils
} from './communityApiService';

/**
 * Unified Data Service
 * Uses only real Community API Service from PostgreSQL
 */
class DataService {
 constructor() {
  this.useRealApi = true; // Always use real API
  console.log('🚀 DataService initialized with Community API (Real API Only)');
 }

 /**
  * Set API mode (always real API)
  */
 setUseRealApi(useReal = true) {
  this.useRealApi = true; // Force to always use real API
  console.log('📡 DataService: Always using real API');
 }

 getApiStatus() {
  return {
   isRealApi: true,
   useRealApi: true,
   source: 'Community API Service (PostgreSQL)',
   apiType: 'Real API Only'
  };
 }

 /**
  * Community Posts - Use real API from PostgreSQL
  */
 async getCommunityPosts(filters = {}) {
  try {
   // Filter out undefined values to avoid "undefined" in URL
   const cleanFilters = Object.entries(filters)
    .filter(([key, value]) => value !== undefined && value !== null && value !== 'undefined')
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
   
   console.log('🔍 Fetching community posts with filters:', cleanFilters);

   // Call real community API through API Gateway with correct URL
   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/community/posts?${new URLSearchParams(cleanFilters)}`, {
    method: 'GET',
    headers: {
     'Content-Type': 'application/json',
    },
   });

   console.log('📡 Community Posts API Response:', {
    status: response.status,
    statusText: response.statusText,
    url: response.url
   });

   if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
   }

   const data = await response.json();
   console.log('📊 Community Posts Data:', data);
   return data.posts || [];
  } catch (error) {
   console.error('❌ Error fetching community posts:', error);
   throw error;
  }
 }

 /**
  * Create new post - Real API
  */
 async createPost(postData) {
  try {
   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/community/posts`, {
    method: 'POST',
    headers: {
     'Content-Type': 'application/json',
    },
    body: JSON.stringify(postData)
   });
   
   if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
   }
   
   return await response.json();
  } catch (error) {
   console.error('Error creating post:', error);
   throw error;
  }
 }

 /**
  * Get post comments - Real API
  */
 async getPostComments(postId) {
  try {
   // Call PostgreSQL API for comments
   try {
    console.log('💬 Fetching comments for post:', postId);
    
    const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/community/comments/${postId}`);
    
    if (!response.ok) {
     throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('💬 Comments data:', data);
    return data.comments || [];
   } catch (error) {
    console.error('❌ Error fetching comments:', error);
    throw error;
   }
  } catch (error) {
   console.error('Error fetching post comments:', error);
   throw error;
  }
 }

 /**
  * Create new comment - Real API 
  */
 async createComment(commentData) {
  try {
   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/community/comments`, {
    method: 'POST',
    headers: {
     'Content-Type': 'application/json',
    },
    body: JSON.stringify(commentData)
   });
   
   if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
   }
   
   return await response.json();
  } catch (error) {
   console.error('Error creating comment:', error);
   throw error;
  }
 }

 /**
  * Like/Unlike post or comment - Real API
  */
 async toggleLike(targetId, targetType = 'post') {
  try {
   const response = await fetch('http://localhost:8080/api/v1/community/likes', {
    method: 'POST',
    headers: {
     'Content-Type': 'application/json',
    },
    body: JSON.stringify({
     targetId,
     targetType
    })
   });
   
   if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
   }
   
   return await response.json();
  } catch (error) {
   console.error('Error toggling like:', error);
   throw error;
  }
 }

 /**
  * Community Users - Use real API from PostgreSQL
  */
 async getCommunityUsers(filters = {}) {
  try {
   // Filter out undefined values
   const cleanFilters = Object.entries(filters)
    .filter(([key, value]) => value !== undefined && value !== null && value !== 'undefined')
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

   console.log('🔍 Fetching community users with filters:', cleanFilters);

   // Call real community API through API Gateway with correct URL
   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/community/users?${new URLSearchParams(cleanFilters)}`, {
    method: 'GET',
    headers: {
     'Content-Type': 'application/json',
    },
   });

   console.log('📡 Community Users API Response:', {
    status: response.status,
    statusText: response.statusText,
    url: response.url
   });

   if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
   }

   const data = await response.json();
   console.log('👥 Community Users Data:', data);
   return data.users || [];
  } catch (error) {
   console.error('❌ Error fetching community users:', error);
   throw error;
  }
 }

 /**
  * Community Categories - Real API (TODO: implement in backend)
  */
 async getCommunityCategories() {
  // Return default categories until backend implements this endpoint
  return [
   { id: 'general', name: 'General', count: 0 },
   { id: 'jobs', name: 'Jobs', count: 0 },
   { id: 'tech', name: 'Technology', count: 0 },
   { id: 'business', name: 'Business', count: 0 }
  ];
 }

 /**
  * Community Stats - Optimized for performance
  */
 async getCommunityStats() {
  try {
   // Return placeholder stats immediately for better performance
   // This avoids unnecessary API calls when user is not authenticated
   return {
    totalPosts: 100, // Placeholder
    totalUsers: 50, // Placeholder
    totalComments: 200, // Placeholder
    activeUsers: 25   // Placeholder
   };
  } catch (error) {
   console.error('Error fetching community stats:', error);
   return {
    totalPosts: 0,
    totalUsers: 0,
    totalComments: 0,
    activeUsers: 0
   };
  }
 }

 /**
  * Utility Methods
  */
 isUsingRealApi() {
  return true; // Always true
 }
}

// Create singleton instance
const dataService = new DataService();

// Export both the instance and the class
export { DataService };
export default dataService;

/**
 * Legacy exports for backward compatibility
 * All use real API only
 */
export const getCommunityPosts = (filters) => dataService.getCommunityPosts(filters);
export const createPost = (postData) => dataService.createPost(postData);
export const getPostComments = (postId) => dataService.getPostComments(postId);
export const createComment = (commentData) => dataService.createComment(commentData);
export const toggleLike = (targetId, targetType) => dataService.toggleLike(targetId, targetType);
export const getCommunityUsers = (filters) => dataService.getCommunityUsers(filters);
export const getCommunityCategories = () => dataService.getCommunityCategories();
export const getCommunityStats = () => dataService.getCommunityStats();

/**
 * API Control Functions - Real API Only
 */
export const switchToRealApi = () => {
 dataService.setUseRealApi(true);
 console.log('✅ Switched to Real API (always active)');
};

export const switchToMockData = () => {
 console.warn('⚠️ Mock data is no longer supported. Using Real API only.');
 dataService.setUseRealApi(true);
};

export const getApiStatus = () => dataService.getApiStatus();
