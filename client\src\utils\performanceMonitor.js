/**
 * Performance Monitor and Memory Leak Detection
 * VWork Frontend Performance Optimization
 */

import { gsap } from 'gsap';

class PerformanceMonitor {
 constructor() {
  this.isMonitoring = false;
  this.metrics = {
   memoryUsage: [],
   frameRate: 60,
   eventListeners: 0,
   gsapTweens: 0,
   domNodes: 0
  };
  this.intervals = new Set();
  this.observers = new Set();
  this.startTime = performance.now();
 }

 // Start monitoring
 startMonitoring() {
  if (this.isMonitoring) return;
  this.isMonitoring = true;
  
  console.log('🔍 Performance Monitor: Starting...');
  
  // Monitor memory usage
  const memoryInterval = setInterval(() => {
   this.checkMemoryUsage();
  }, 10000); // Every 10 seconds
  this.intervals.add(memoryInterval);
  
  // Monitor DOM nodes
  const domInterval = setInterval(() => {
   this.checkDOMNodes();
  }, 15000); // Every 15 seconds
  this.intervals.add(domInterval);
  
  // Monitor GSAP tweens
  const gsapInterval = setInterval(() => {
   this.checkGSAPTweens();
  }, 5000); // Every 5 seconds
  this.intervals.add(gsapInterval);
  
  // Monitor event listeners (approximate)
  const eventInterval = setInterval(() => {
   this.checkEventListeners();
  }, 20000); // Every 20 seconds
  this.intervals.add(eventInterval);
 }

 // Stop monitoring
 stopMonitoring() {
  if (!this.isMonitoring) return;
  this.isMonitoring = false;
  
  console.log('🛑 Performance Monitor: Stopping...');
  
  // Clear all intervals
  this.intervals.forEach(interval => clearInterval(interval));
  this.intervals.clear();
  
  // Disconnect all observers
  this.observers.forEach(observer => observer.disconnect());
  this.observers.clear();
 }

 // Check memory usage
 checkMemoryUsage() {
  if (!performance.memory) return;
  
  const memory = {
   used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
   total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
   limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024),
   timestamp: Date.now()
  };
  
  this.metrics.memoryUsage.push(memory);
  
  // Keep only last 20 measurements
  if (this.metrics.memoryUsage.length > 20) {
   this.metrics.memoryUsage.shift();
  }
  
  // Check for memory leaks
  if (this.metrics.memoryUsage.length >= 5) {
   this.detectMemoryLeak();
  }
  
  // Log if memory usage is high
  if (memory.used > memory.limit * 0.8) {
   console.warn('⚠️ High memory usage detected:', memory);
  }
 }

 // Detect memory leaks
 detectMemoryLeak() {
  const recent = this.metrics.memoryUsage.slice(-5);
  const increasing = recent.every((current, index) => {
   if (index === 0) return true;
   return current.used >= recent[index - 1].used;
  });
  
  if (increasing) {
   const increase = recent[recent.length - 1].used - recent[0].used;
   if (increase > 10) { // More than 10MB increase
    console.warn('🚨 Potential memory leak detected!', {
     increase: `${increase}MB`,
     recent: recent
    });
    
    // Trigger cleanup
    this.triggerCleanup();
   }
  }
 }

 // Check DOM nodes count
 checkDOMNodes() {
  const nodeCount = document.querySelectorAll('*').length;
  this.metrics.domNodes = nodeCount;
  
  if (nodeCount > 5000) {
   console.warn('⚠️ High DOM node count:', nodeCount);
  }
 }

 // Check GSAP tweens
 checkGSAPTweens() {
  if (typeof gsap !== 'undefined') {
   const activeTweens = gsap.globalTimeline.getChildren().length;
   this.metrics.gsapTweens = activeTweens;
   
   if (activeTweens > 50) {
    console.warn('⚠️ High number of active GSAP tweens:', activeTweens);
   }
  }
 }

 // Check event listeners (approximate)
 checkEventListeners() {
  // This is an approximation - exact count is not easily available
  let estimatedListeners = 0;

  // Count elements that commonly have event listeners
  estimatedListeners += document.querySelectorAll('button').length;
  estimatedListeners += document.querySelectorAll('input').length;
  estimatedListeners += document.querySelectorAll('a').length;
  estimatedListeners += document.querySelectorAll('[onclick]').length;

  this.metrics.eventListeners = estimatedListeners;

  if (estimatedListeners > 200) {
   console.warn('⚠️ High estimated event listener count:', estimatedListeners);
  }
 }

 // Trigger cleanup
 triggerCleanup() {
  console.log('🧹 Triggering performance cleanup...');
  
  // Dispatch cleanup event
  window.dispatchEvent(new CustomEvent('performanceCleanup', {
   detail: { metrics: this.metrics }
  }));
  
  // Force garbage collection if available
  if (window.gc) {
   window.gc();
  }
 }

 // Get current metrics
 getMetrics() {
  return {
   ...this.metrics,
   uptime: Math.round((performance.now() - this.startTime) / 1000),
   isMonitoring: this.isMonitoring
  };
 }

 // Get memory trend
 getMemoryTrend() {
  if (this.metrics.memoryUsage.length < 2) return 'stable';
  
  const recent = this.metrics.memoryUsage.slice(-3);
  const trend = recent[recent.length - 1].used - recent[0].used;
  
  if (trend > 5) return 'increasing';
  if (trend < -5) return 'decreasing';
  return 'stable';
 }

 // Log performance report
 logReport() {
  const metrics = this.getMetrics();
  const trend = this.getMemoryTrend();
  
  console.group('📊 Performance Report');
  console.log('Memory Trend:', trend);
  console.log('Current Memory:', metrics.memoryUsage[metrics.memoryUsage.length - 1]);
  console.log('DOM Nodes:', metrics.domNodes);
  console.log('Active GSAP Tweens:', metrics.gsapTweens);
  console.log('Estimated Event Listeners:', metrics.eventListeners);
  console.log('Uptime:', `${metrics.uptime}s`);
  console.groupEnd();
 }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Auto-start in development
if (process.env.NODE_ENV === 'development') {
 performanceMonitor.startMonitoring();
 
 // Log report every 60 seconds
 setInterval(() => {
  performanceMonitor.logReport();
 }, 60000);
}

export default performanceMonitor;
