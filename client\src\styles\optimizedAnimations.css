/**
 * Optimized Animations CSS
 * Performance-focused animation styles for VWork platform
 */

/* Hardware acceleration for all animated elements */
.animate-optimized {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-optimized {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .gsap-animate {
    transform: none !important;
    opacity: 1 !important;
  }
}

/* Performance-optimized keyframes */
@keyframes optimizedFadeIn {
  0% {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes optimizedSlideUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes optimizedScale {
  0% {
    opacity: 0;
    transform: translate3d(0, 0, 0) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes optimizedFloat {
  0%, 100% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -5px, 0);
  }
}

/* Optimized utility classes */
.fade-in-optimized {
  animation: optimizedFadeIn 0.3s ease-out forwards;
}

.slide-up-optimized {
  animation: optimizedSlideUp 0.4s ease-out forwards;
}

.scale-in-optimized {
  animation: optimizedScale 0.3s ease-out forwards;
}

.float-optimized {
  animation: optimizedFloat 3s ease-in-out infinite;
}

/* Performance levels */
.performance-high .complex-animation {
  animation-duration: 1s;
  animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.performance-medium .complex-animation {
  animation-duration: 0.6s;
  animation-timing-function: ease-out;
}

.performance-low .complex-animation {
  animation-duration: 0.3s;
  animation-timing-function: ease;
}

.performance-minimal .complex-animation {
  animation: none !important;
  transition: none !important;
}

/* Optimized hover effects */
.hover-optimized {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-optimized:hover {
  transform: translate3d(0, -2px, 0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Magnetic effect optimization */
.magnetic-optimized {
  transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

/* Particle system optimizations */
.particle-container {
  contain: layout style paint;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  will-change: transform, opacity;
  pointer-events: none;
}

/* Morphing shape optimizations */
.morphing-shape {
  will-change: border-radius, transform;
  contain: layout style paint;
}

/* Text animation optimizations */
.text-reveal {
  overflow: hidden;
}

.text-reveal .char {
  display: inline-block;
  will-change: transform, opacity;
}

/* Scroll trigger optimizations */
.scroll-trigger {
  will-change: transform, opacity;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dark mode optimizations */


/* Container queries for responsive animations */
@container (max-width: 768px) {
  .complex-animation {
    animation-duration: 0.3s !important;
    animation-timing-function: ease !important;
  }
}

/* GPU layer promotion for critical elements */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Intersection observer optimizations */
.intersection-animate {
  opacity: 0;
  transform: translate3d(0, 20px, 0);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.intersection-animate.visible {
  opacity: 1;
  transform: translate3d(0, 0, 0);
}

/* Memory-efficient animations */
.memory-efficient {
  contain: layout style paint;
  content-visibility: auto;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .animate-optimized {
    animation: none !important;
    transition: none !important;
  }
}

/* Print styles - disable animations */
@media print {
  .animate-optimized,
  .gsap-animate,
  .complex-animation {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    opacity: 1 !important;
  }
}

/* Focus management for animations */
.animate-optimized:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Cleanup utilities */
.animation-cleanup {
  animation: none;
  transition: none;
  transform: none;
  will-change: auto;
}

/* Performance debugging */
.debug-performance .animate-optimized {
  border: 1px dashed red;
}

.debug-performance .gpu-accelerated {
  border: 1px dashed green;
}

.debug-performance .memory-efficient {
  border: 1px dashed blue;
}
