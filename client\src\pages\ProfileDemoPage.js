import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import UserCard from '../components/common/UserCard';

const ProfileDemoPage = () => {
  const navigate = useNavigate();
  const [selectedUserId, setSelectedUserId] = useState('');

  // Mock users for demo
  const mockUsers = [
    {
      id: 'user1',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      username: '@nguyenvana',
      title: 'Senior Frontend Developer',
      location: 'Hà Nội, Việt Nam',
      avatar: 'https://via.placeholder.com/40x40/6366f1/ffffff?text=NA',
      verified: true
    },
    {
      id: 'user2', 
      name: '<PERSON><PERSON><PERSON><PERSON>',
      username: '@tranthib',
      title: 'UI/UX Designer',
      location: 'TP.HCM, Việt Nam',
      avatar: 'https://via.placeholder.com/40x40/10b981/ffffff?text=TB',
      verified: false
    },
    {
      id: 'user3',
      name: '<PERSON><PERSON>',
      username: '@levanc',
      title: 'Backend Developer',
      location: 'Đà Nẵng, Việt Nam',
      avatar: 'https://via.placeholder.com/40x40/f59e0b/ffffff?text=LC',
      verified: true
    }
  ];

  const handleUserClick = (user) => {
    setSelectedUserId(user.id);
    navigate(`/freelancers/${user.id}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            🧪 Demo: Tính năng xem Profile User
          </h1>
          
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Cách sử dụng:
            </h2>
            <div className="bg-blue-50 rounded-lg p-4 space-y-2">
              <p className="text-blue-800">
                • <strong>Click vào tên user</strong> trong community posts/comments để xem profile
              </p>
              <p className="text-blue-800">
                • <strong>Click vào UserCard</strong> bên dưới để test navigation
              </p>
              <p className="text-blue-800">
                • <strong>URL:</strong> /freelancers/[user-id] sẽ hiển thị profile chi tiết
              </p>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Test User Cards (Click để xem profile):
            </h2>
            <div className="space-y-4">
              {mockUsers.map((user) => (
                <div key={user.id} className="border border-gray-200 rounded-lg p-4">
                  <UserCard 
                    user={user} 
                    size="large"
                    onClick={handleUserClick}
                  />
                  <div className="mt-2 text-sm text-gray-500">
                    User ID: {user.id} | Verified: {user.verified ? '✅' : '❌'}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Test Navigation:
            </h2>
            <div className="flex flex-wrap gap-4">
              {mockUsers.map((user) => (
                <button
                  key={user.id}
                  onClick={() => navigate(`/freelancers/${user.id}`)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Xem profile {user.name}
                </button>
              ))}
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Test với User ID tùy chỉnh:
            </h2>
            <div className="flex gap-4">
              <input
                type="text"
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                placeholder="Nhập User ID..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={() => {
                  if (selectedUserId.trim()) {
                    navigate(`/freelancers/${selectedUserId.trim()}`);
                  }
                }}
                disabled={!selectedUserId.trim()}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Xem Profile
              </button>
            </div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              📝 Lưu ý:
            </h3>
            <ul className="text-yellow-700 space-y-1">
              <li>• Tính năng này yêu cầu API backend đang chạy</li>
              <li>• User ID phải tồn tại trong database</li>
              <li>• Nếu user không tồn tại, sẽ hiển thị trang "Không tìm thấy profile"</li>
              <li>• Profile page sẽ hiển thị thông tin public của user</li>
              <li>• Chỉ chủ profile mới thấy được thông tin private (email, phone)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileDemoPage; 