#!/usr/bin/env node

/**
 * Restart Render services by triggering deployments
 * This script helps resolve 429 rate limit issues
 */

const https = require('https');

// Render service URLs
const SERVICES = [
  {
    name: 'Auth Service',
    url: 'https://vwork-auth-service.onrender.com/health',
    restartUrl: 'https://vwork-auth-service.onrender.com/restart'
  },
  {
    name: 'User Service', 
    url: 'https://vwork-user-service.onrender.com/health',
    restartUrl: 'https://vwork-user-service.onrender.com/restart'
  },
  {
    name: 'Project Service',
    url: 'https://vwork-project-service.onrender.com/health', 
    restartUrl: 'https://vwork-project-service.onrender.com/restart'
  },
  {
    name: 'Job Service',
    url: 'https://vwork-job-service.onrender.com/health',
    restartUrl: 'https://vwork-job-service.onrender.com/restart'
  },
  {
    name: 'Chat Service',
    url: 'https://vwork-chat-service.onrender.com/health',
    restartUrl: 'https://vwork-chat-service.onrender.com/restart'
  },
  {
    name: 'Community Service',
    url: 'https://vwork-community-service.onrender.com/health',
    restartUrl: 'https://vwork-community-service.onrender.com/restart'
  },
  {
    name: 'API Gateway',
    url: 'https://vwork-api-gateway.onrender.com/api/v1/status',
    restartUrl: 'https://vwork-api-gateway.onrender.com/restart'
  }
];

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Make HTTPS request
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

/**
 * Check service health
 */
async function checkServiceHealth(service) {
  try {
    log.info(`Checking ${service.name}...`);
    
    const response = await makeRequest(service.url);
    
    if (response.statusCode === 200) {
      log.success(`${service.name}: Healthy (${response.statusCode})`);
      return true;
    } else if (response.statusCode === 429) {
      log.warn(`${service.name}: Rate limited (${response.statusCode})`);
      return false;
    } else {
      log.error(`${service.name}: Unhealthy (${response.statusCode})`);
      return false;
    }
    
  } catch (error) {
    log.error(`${service.name}: Unreachable (${error.message})`);
    return false;
  }
}

/**
 * Restart service by triggering a request
 */
async function restartService(service) {
  try {
    log.info(`Attempting to restart ${service.name}...`);
    
    // Make a request to trigger service restart
    const response = await makeRequest(service.url, {
      method: 'GET',
      headers: {
        'User-Agent': 'VWork-Restart-Script/1.0',
        'Cache-Control': 'no-cache'
      }
    });
    
    log.success(`${service.name}: Restart triggered`);
    return true;
    
  } catch (error) {
    log.error(`${service.name}: Failed to restart (${error.message})`);
    return false;
  }
}

/**
 * Wait for service to become healthy
 */
async function waitForServiceHealth(service, maxAttempts = 10) {
  log.info(`Waiting for ${service.name} to become healthy...`);
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    const isHealthy = await checkServiceHealth(service);
    
    if (isHealthy) {
      log.success(`${service.name} is now healthy!`);
      return true;
    }
    
    if (attempt < maxAttempts) {
      log.info(`Attempt ${attempt}/${maxAttempts} - Waiting 30 seconds...`);
      await new Promise(resolve => setTimeout(resolve, 30000));
    }
  }
  
  log.error(`${service.name} failed to become healthy after ${maxAttempts} attempts`);
  return false;
}

/**
 * Main restart process
 */
async function restartAllServices() {
  log.info('🔄 Starting service restart process...');
  
  const results = [];
  
  for (const service of SERVICES) {
    // Check current health
    const wasHealthy = await checkServiceHealth(service);
    
    if (!wasHealthy) {
      // Try to restart
      const restartSuccess = await restartService(service);
      
      if (restartSuccess) {
        // Wait for service to become healthy
        const becameHealthy = await waitForServiceHealth(service);
        results.push({
          service: service.name,
          wasHealthy,
          restartSuccess,
          becameHealthy
        });
      } else {
        results.push({
          service: service.name,
          wasHealthy,
          restartSuccess: false,
          becameHealthy: false
        });
      }
    } else {
      log.info(`${service.name} is already healthy, skipping restart`);
      results.push({
        service: service.name,
        wasHealthy: true,
        restartSuccess: true,
        becameHealthy: true
      });
    }
    
    // Add delay between services
    await new Promise(resolve => setTimeout(resolve, 10000));
  }
  
  // Summary
  log.info('\n📊 Restart Summary:');
  const successful = results.filter(r => r.becameHealthy);
  const failed = results.filter(r => !r.becameHealthy);
  
  log.success(`${successful.length} services are healthy`);
  if (failed.length > 0) {
    log.error(`${failed.length} services are still unhealthy`);
    failed.forEach(f => log.error(`  - ${f.service}`));
  }
  
  return results;
}

/**
 * Check all services health
 */
async function checkAllServicesHealth() {
  log.info('\n🏥 Checking all services health...');
  
  const results = [];
  
  for (const service of SERVICES) {
    const isHealthy = await checkServiceHealth(service);
    results.push({ service: service.name, healthy: isHealthy });
  }
  
  const healthy = results.filter(r => r.healthy);
  const unhealthy = results.filter(r => !r.healthy);
  
  log.info(`\n📊 Health Summary:`);
  log.success(`${healthy.length} services are healthy`);
  if (unhealthy.length > 0) {
    log.error(`${unhealthy.length} services are unhealthy`);
    unhealthy.forEach(u => log.error(`  - ${u.service}`));
  }
  
  return results;
}

// Main execution
async function main() {
  try {
    const command = process.argv[2];
    
    switch (command) {
      case 'restart':
        await restartAllServices();
        break;
      case 'health':
        await checkAllServicesHealth();
        break;
      default:
        log.info('Usage: node restart-render-services.js [restart|health]');
        log.info('  restart - Restart all unhealthy services');
        log.info('  health  - Check health of all services');
    }
    
  } catch (error) {
    log.error(`Script failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { restartAllServices, checkAllServicesHealth }; 