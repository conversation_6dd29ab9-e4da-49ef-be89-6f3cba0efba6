# Create All VWork Services on Azure
$azureCliPath = "C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin\az.cmd"
$resourceGroup = "appsvc_linux_southeastasia_basic"
$planName = "appsvc_linux_southeastasia_basic"

# List of services to create
$services = @(
    "vwork-user-service",
    "vwork-auth-service", 
    "vwork-job-service",
    "vwork-project-service",
    "vwork-chat-service",
    "vwork-api-gateway"
)

Write-Host "🚀 Creating all VWork services..." -ForegroundColor Blue

foreach ($service in $services) {
    Write-Host "Creating $service..." -ForegroundColor Green
    
    # Create Web App
    & $azureCliPath webapp create --name $service --resource-group $resourceGroup --plan $planName --runtime "NODE:20-lts"
    
    if ($LASTEXITCODE -eq 0) {
        # Get URL
        $url = & $azureCliPath webapp show --name $service --resource-group $resourceGroup --query "defaultHostName" --output tsv
        Write-Host "✅ $service created successfully!" -ForegroundColor Green
        Write-Host "   URL: https://$url" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Failed to create $service" -ForegroundColor Red
    }
    
    Write-Host "---" -ForegroundColor Gray
}

Write-Host "🎉 All services creation completed!" -ForegroundColor Blue 