import React, { useState } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';

const TestForm = () => {
 // const { t } = useLanguage(); // Unused variable
 const [loading, setLoading] = useState(false);
 const [formData, setFormData] = useState({
  hourlyRate: 25,
  availability: 'available',
  experience: 'Test experience'
 });

 const handleNext = () => {
  console.log('🔘 Test button clicked!');
  setLoading(true);
  
  setTimeout(() => {
   console.log('✅ Test completed');
   setLoading(false);
  }, 2000);
 };

 return (
  <div className="p-8">
   <h2>Test Form</h2>
   
   <div className="space-y-4">
    <div>
     <label htmlFor="hourly-rate">Hourly Rate</label>
     <input
      id="hourly-rate"
      type="number"
      value={formData.hourlyRate}
      onChange={(e) => setFormData(prev => ({ ...prev, hourlyRate: parseInt(e.target.value) }))}
      className="form-input w-full"
     />
    </div>
    
    <div>
     <label htmlFor="experience">Experience</label>
     <textarea
      id="experience"
      value={formData.experience}
      onChange={(e) => setFormData(prev => ({ ...prev, experience: e.target.value }))}
      className="form-input w-full"
      rows={4}
     />
    </div>
   </div>

   <div className="flex justify-between mt-8">
    <button
     onClick={() => console.log('Previous clicked')}
     className="btn-secondary px-6 py-2"
    >
     Previous
    </button>

    <button
     onClick={handleNext}
     disabled={loading}
     className="btn-primary px-6 py-2"
     style={{ 
      pointerEvents: loading ? 'none' : 'auto',
      position: 'relative',
      zIndex: 1000
     }}
    >
     {loading ? 'Processing...' : 'Next'}
    </button>
   </div>
  </div>
 );
};

export default TestForm; 
