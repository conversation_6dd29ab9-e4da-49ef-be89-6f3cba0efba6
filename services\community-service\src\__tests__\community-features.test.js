const request = require('supertest');
const { v4: uuidv4 } = require('uuid');

// Mock database and dependencies
const mockDb = {
  query: jest.fn(),
  rows: []
};

// Mock the PostgreSQL config
jest.mock('../config/postgresql.js', () => ({
  postgresqlConfig: mockDb,
  db: mockDb,
  testConnection: jest.fn().mockResolvedValue({ success: true })
}));

// Mock logger
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

// Mock auth middleware
jest.mock('../middleware/auth-new', () => (req, res, next) => {
  req.user = { id: 'test-user-id' };
  next();
});

describe('Community Features Integration Tests', () => {
  let app;
  
  beforeAll(() => {
    // Set environment variables
    process.env.NODE_ENV = 'test';
    process.env.MAX_TITLE_LENGTH = '200';
    process.env.MAX_POST_LENGTH = '10000';
    process.env.MAX_COMMENT_LENGTH = '2000';
    
    // Require app after setting env vars
    app = require('../app');
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockDb.query.mockClear();
  });

  describe('POST Creation Tests', () => {
    const validPost = {
      title: 'Test Post Title',
      content: 'This is test content for the post.',
      authorId: uuidv4(),
      postType: 'discussion',
      category: 'general',
      tags: ['test', 'javascript']
    };

    test('should create a post successfully', async () => {
      const postId = uuidv4();
      const authorData = {
        first_name: 'John',
        last_name: 'Doe',
        avatar_url: 'https://example.com/avatar.jpg'
      };

      // Mock database responses
      mockDb.query
        .mockResolvedValueOnce({ rows: [{ id: postId, ...validPost, post_type: validPost.postType }] }) // Insert post
        .mockResolvedValueOnce({ rows: [{ id: postId, ...validPost, post_type: validPost.postType }] }) // Get created post
        .mockResolvedValueOnce({ rows: [authorData] }); // Get author info

      const response = await request(app)
        .post('/api/posts')
        .send(validPost)
        .expect(201);

      expect(response.body.message).toBe('Post created successfully');
      expect(response.body.post).toHaveProperty('id');
      expect(response.body.post.title).toBe(validPost.title);
      expect(response.body.post.content).toBe(validPost.content);
      expect(mockDb.query).toHaveBeenCalledTimes(3);
    });

    test('should reject post with missing required fields', async () => {
      const invalidPost = {
        title: 'Test Title'
        // Missing content and authorId
      };

      const response = await request(app)
        .post('/api/posts')
        .send(invalidPost)
        .expect(400);

      expect(response.body.error).toContain('Missing required fields');
    });

    test('should reject post with title too long', async () => {
      const longTitlePost = {
        ...validPost,
        title: 'a'.repeat(201) // Exceeds MAX_TITLE_LENGTH
      };

      const response = await request(app)
        .post('/api/posts')
        .send(longTitlePost)
        .expect(400);

      expect(response.body.error).toContain('Title must be 200 characters or less');
    });

    test('should reject post with invalid post type', async () => {
      const invalidTypePost = {
        ...validPost,
        postType: 'invalid-type'
      };

      const response = await request(app)
        .post('/api/posts')
        .send(invalidTypePost)
        .expect(400);

      expect(response.body.error).toContain('Invalid post type');
    });
  });

  describe('Like System Tests', () => {
    const userId = uuidv4();
    const postId = uuidv4();

    test('should like a post successfully', async () => {
      // Mock no existing like
      mockDb.query
        .mockResolvedValueOnce({ rows: [] }) // Check existing like
        .mockResolvedValueOnce({ rows: [] }) // Insert like
        .mockResolvedValueOnce({ rows: [] }) // Update like count
        .mockResolvedValueOnce({ rows: [{ like_count: 1 }] }); // Get updated count

      const response = await request(app)
        .post('/api/likes')
        .send({
          targetId: postId,
          targetType: 'post'
        })
        .expect(200);

      expect(response.body.message).toBe('Like added');
      expect(response.body.liked).toBe(true);
      expect(response.body.likeCount).toBe(1);
    });

    test('should unlike a post successfully', async () => {
      // Mock existing like
      mockDb.query
        .mockResolvedValueOnce({ rows: [{ id: uuidv4() }] }) // Check existing like
        .mockResolvedValueOnce({ rows: [] }) // Delete like
        .mockResolvedValueOnce({ rows: [] }) // Update like count
        .mockResolvedValueOnce({ rows: [{ like_count: 0 }] }); // Get updated count

      const response = await request(app)
        .post('/api/likes')
        .send({
          targetId: postId,
          targetType: 'post'
        })
        .expect(200);

      expect(response.body.message).toBe('Like removed');
      expect(response.body.liked).toBe(false);
      expect(response.body.likeCount).toBe(0);
    });

    test('should reject like with invalid target type', async () => {
      const response = await request(app)
        .post('/api/likes')
        .send({
          targetId: postId,
          targetType: 'invalid'
        })
        .expect(400);

      expect(response.body.error).toContain('targetType must be either "post" or "comment"');
    });
  });

  describe('Comment System Tests', () => {
    const postId = uuidv4();
    const authorId = uuidv4();
    const commentId = uuidv4();

    test('should create a comment successfully', async () => {
      const commentData = {
        postId,
        content: 'This is a test comment'
      };

      const mockComment = {
        id: commentId,
        post_id: postId,
        author_id: authorId,
        content: commentData.content,
        like_count: 0,
        upvotes: 0,
        downvotes: 0,
        depth: 0,
        parent_id: null,
        created_at: new Date(),
        updated_at: new Date(),
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe',
        avatar_url: 'https://example.com/avatar.jpg'
      };

      // Mock database responses
      mockDb.query
        .mockResolvedValueOnce({ rows: [mockComment] }) // Insert comment
        .mockResolvedValueOnce({ rows: [] }) // Update post comment count
        .mockResolvedValueOnce({ rows: [mockComment] }); // Get comment with author

      const response = await request(app)
        .post('/api/comments')
        .send(commentData)
        .expect(201);

      expect(response.body.message).toBe('Comment created successfully');
      expect(response.body.comment.content).toBe(commentData.content);
      expect(response.body.comment.depth).toBe(0);
    });

    test('should create a reply comment with correct depth', async () => {
      const parentCommentId = uuidv4();
      const replyData = {
        postId,
        content: 'This is a reply comment',
        parentId: parentCommentId
      };

      const mockReply = {
        id: commentId,
        post_id: postId,
        author_id: authorId,
        parent_id: parentCommentId,
        content: replyData.content,
        like_count: 0,
        upvotes: 0,
        downvotes: 0,
        depth: 1,
        created_at: new Date(),
        updated_at: new Date(),
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe',
        avatar_url: 'https://example.com/avatar.jpg'
      };

      // Mock database responses
      mockDb.query
        .mockResolvedValueOnce({ rows: [{ depth: 0 }] }) // Get parent depth
        .mockResolvedValueOnce({ rows: [mockReply] }) // Insert reply
        .mockResolvedValueOnce({ rows: [] }) // Update post comment count
        .mockResolvedValueOnce({ rows: [] }) // Update parent reply count
        .mockResolvedValueOnce({ rows: [mockReply] }); // Get reply with author

      const response = await request(app)
        .post('/api/comments')
        .send(replyData)
        .expect(201);

      expect(response.body.comment.depth).toBe(1);
      expect(response.body.comment.parentId).toBe(parentCommentId);
    });
  });

  describe('Share System Tests', () => {
    const postId = uuidv4();
    const userId = uuidv4();

    test('should share a post successfully', async () => {
      const shareData = {
        userId,
        userName: 'John Doe'
      };

      // Mock database responses
      mockDb.query
        .mockResolvedValueOnce({ rows: [{ id: postId, status: 'published' }] }) // Check post exists
        .mockResolvedValueOnce({ rows: [] }) // Check no existing share
        .mockResolvedValueOnce({ rows: [] }) // Insert share
        .mockResolvedValueOnce({ rows: [] }) // Update share count
        .mockResolvedValueOnce({ rows: [{ share_count: 1 }] }); // Get updated count

      const response = await request(app)
        .post(`/api/posts/${postId}/share`)
        .send(shareData)
        .expect(200);

      expect(response.body.message).toBe('Post shared successfully');
      expect(response.body.shareCount).toBe(1);
      expect(response.body.share.userId).toBe(userId);
    });

    test('should prevent duplicate shares', async () => {
      const shareData = {
        userId,
        userName: 'John Doe'
      };

      // Mock database responses
      mockDb.query
        .mockResolvedValueOnce({ rows: [{ id: postId, status: 'published' }] }) // Check post exists
        .mockResolvedValueOnce({ rows: [{ id: uuidv4() }] }); // Existing share found

      const response = await request(app)
        .post(`/api/posts/${postId}/share`)
        .send(shareData)
        .expect(400);

      expect(response.body.error).toBe('Post already shared by this user');
    });
  });
});
