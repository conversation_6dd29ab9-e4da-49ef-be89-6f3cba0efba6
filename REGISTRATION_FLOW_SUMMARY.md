# 🔐 Tóm tắt Logic và Flow Đăng ký Người dùng Mới - VWork

## 📋 Tổng quan

Hệ thống VWork có một **flow đăng ký phức tạp và được thiết kế tốt** với nhiều lớp bảo mật và trải nghiệm người dùng tối ưu.

## 🔄 Flow Hoàn chỉnh

### 1. **Đăng ký (Registration)**
```
User Form → Firebase Auth → Email Verification → TempUser Storage
```

**Chi tiết:**
- **Form**: Email, password, firstName, lastName, userType
- **Validation**: Email format, password strength, user type
- **Firebase**: `createUserWithEmailAndPassword()`
- **Email**: `sendEmailVerification()` với custom URL
- **Storage**: Lưu vào `tempUsers` collection với `isVerified: false`

### 2. **<PERSON><PERSON><PERSON> thực <PERSON> (Email Verification)**
```
Email Link → Firebase applyActionCode → Data Migration → User Activation
```

**Chi tiết:**
- User click link trong email
- Firebase `applyActionCode()` xác thực
- `reload()` user để update `emailVerified` status
- **Migration**: Move data từ `tempUsers` → `users` collection
- Set `isVerified: true` và `emailVerifiedAt`

### 3. **Onboarding Flow**
```
ProfileGuard Check → OnboardingGuard → OnboardingFlow → Complete Profile
```

**Chi tiết:**
- **ProfileGuard**: Check `user.profile.isComplete`
- **OnboardingGuard**: Show `OnboardingFlow` nếu cần
- **Steps**: Welcome → Basic Info → Profile Details → Skills (freelancer) → Preferences → Complete
- **Completion**: Set `profile.isComplete = true`

## 🗄️ Logic Temp User vs Main User

### **TempUsers Collection**
- **Mục đích**: Temporary storage cho unverified users
- **Trạng thái**: `isVerified: false`
- **Access**: Limited, không thể truy cập features chính
- **Lifetime**: Tạm thời cho đến khi email verified

### **Users Collection**
- **Mục đích**: Main storage cho verified users
- **Trạng thái**: `isVerified: true`
- **Access**: Full access to all features
- **Lifetime**: Permanent storage

### **Migration Logic**
```javascript
// Khi email verified
if (firebaseUser.emailVerified && !userData.isVerified) {
  const verifiedUserData = {
    ...userData,
    isVerified: true,
    emailVerifiedAt: new Date().toISOString(),
    profile: { ...userData.profile, isComplete: false }
  };
  
  await setDoc(doc(db, 'users', firebaseUser.uid), verifiedUserData);
  await deleteDoc(doc(db, 'tempUsers', firebaseUser.uid));
}
```

## 🛡️ Guard Components

### **ProfileGuard.js**
- Check authentication status
- Redirect unverified users to `/verify-email`
- Redirect incomplete profiles to `/onboarding`
- Handle loading states

### **OnboardingGuard.js**
- Show `OnboardingFlow` cho users cần onboarding
- Check `user.emailVerified && isOnboardingRequired`
- Return normal app content nếu không cần

### **ProtectedRoute.js**
- Protect routes dựa trên auth status
- Redirect unauthenticated users
- Handle auth page redirects

## 🔧 Backend Integration

### **User Service (PostgreSQL)**
- **Tables**: `users`, `user_profiles`, `user_reputation`
- **Sync**: Firebase token verification
- **Operations**: Create/update user records
- **Profile**: Track completion status

### **Sync Process**
```javascript
// Firebase token verification
const firebaseUser = await verifyFirebaseToken(req);

// Create/update user records
if (existingUser) {
  // Update existing user
} else {
  // Create new user with profile and reputation
}
```

## 📊 Data Structure

### **TempUser Data**
```javascript
{
  name: string,
  email: string,
  userType: 'freelancer' | 'client',
  guildRank: 'apprentice' | 'lord',
  isVerified: false,
  emailVerificationSent: true,
  profile: {
    isComplete: false, // Trigger onboarding
    bio: '',
    skills: [],
    hourlyRate: number | null,
    availability: string | null
  },
  stats: { /* default stats */ },
  preferences: { /* default preferences */ }
}
```

### **Verified User Data**
```javascript
{
  // ... tempUser data
  isVerified: true,
  emailVerifiedAt: ISOString,
  profile: {
    isComplete: false // Will be true after onboarding
  }
}
```

## 🚀 Testing

### **Test Script**
- `npm run test:registration`
- Tests complete flow: Registration → Email Verification → Migration → Onboarding
- Creates test users và cleanup
- Verifies data integrity

### **Manual Testing**
1. Navigate to `/register`
2. Fill form và submit
3. Check email verification
4. Click verification link
5. Verify onboarding starts
6. Complete onboarding steps
7. Verify redirect to dashboard

## 🔍 Key Features

### **Security**
- ✅ Email verification bắt buộc
- ✅ Temporary user isolation
- ✅ Firebase token verification
- ✅ Password strength validation

### **User Experience**
- ✅ Progressive disclosure
- ✅ Custom onboarding per user type
- ✅ Clear error messages
- ✅ Resend email với cooldown

### **Data Integrity**
- ✅ Migration logic đảm bảo consistency
- ✅ Profile completion tracking
- ✅ Backend sync với Firebase
- ✅ Error handling

### **Performance**
- ✅ Lazy loading cho onboarding
- ✅ Optimized database queries
- ✅ User data caching
- ✅ Background sync

## 📝 Kết luận

Hệ thống VWork có một **flow đăng ký hoàn chỉnh và robust** với:

- **Security**: Email verification bắt buộc, temporary user isolation
- **UX**: Progressive onboarding, custom flows per user type
- **Scalability**: Backend integration, optimized data structures
- **Reliability**: Comprehensive error handling, data consistency

Flow này đảm bảo users có trải nghiệm mượt mà và an toàn từ registration đến fully functional account.

## 🔗 Files Quan trọng

- `client/src/contexts/AuthContext.js` - Main authentication logic
- `client/src/contexts/OnboardingContext.js` - Onboarding flow management
- `client/src/components/auth/ProfileGuard.js` - Route protection
- `client/src/components/onboarding/OnboardingGuard.js` - Onboarding guard
- `client/src/pages/EmailVerificationPage.js` - Email verification UI
- `services/user-service/src/routes/users.js` - Backend user management
- `docs/REGISTRATION_FLOW_ANALYSIS.md` - Detailed analysis
- `scripts/test-registration-flow.js` - Test script 