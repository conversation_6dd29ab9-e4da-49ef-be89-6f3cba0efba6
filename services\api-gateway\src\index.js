const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 8080;

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = process.env.CORS_ORIGINS ?
      process.env.CORS_ORIGINS.split(',') :
      ['http://localhost:3000', 'http://localhost:3001', 'https://frontend-ce4z.onrender.com', 'https://nerafus.com'];
    
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('🚫 CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'x-auth-token', 
    'Cache-Control', 
    'Pragma', 
    'Expires',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposedHeaders: ['Content-Length', 'X-Requested-With'],
  preflightContinue: false,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// Handle preflight requests explicitly
app.options('*', cors(corsOptions));

// Rate limiting - More permissive in development
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'development' ? 1000 : 500, // 1000 requests in dev, 500 in production
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health' || req.path === '/api/v1/community/health';
  }
});
app.use(limiter);

console.log(`🚦 Rate limiting: ${process.env.NODE_ENV === 'development' ? '1000' : '100'} requests per 15 minutes`);

// Logging
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'VWork API Gateway',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Get service URLs from environment variables
const USER_SERVICE_URL = process.env.USER_SERVICE_URL || 'http://localhost:3001';
const PROJECT_SERVICE_URL = process.env.PROJECT_SERVICE_URL || 'http://localhost:3002';
const JOB_SERVICE_URL = process.env.JOB_SERVICE_URL || 'http://localhost:3003';
const CHAT_SERVICE_URL = process.env.CHAT_SERVICE_URL || 'http://localhost:3004';
const COMMUNITY_SERVICE_URL = process.env.COMMUNITY_SERVICE_URL || 'http://localhost:3005';
const PAYMENT_SERVICE_URL = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3006';
const TEAM_SERVICE_URL = process.env.TEAM_SERVICE_URL || 'http://localhost:3007';
const SEARCH_SERVICE_URL = process.env.SEARCH_SERVICE_URL || 'http://localhost:3009';

// Services status endpoint
app.get('/api/v1/status', async (req, res) => {
  const services = [
    { name: 'user', url: USER_SERVICE_URL },
    { name: 'project', url: PROJECT_SERVICE_URL },
    { name: 'job', url: JOB_SERVICE_URL },
    { name: 'chat', url: CHAT_SERVICE_URL },
    { name: 'community', url: COMMUNITY_SERVICE_URL },
    { name: 'payment', url: PAYMENT_SERVICE_URL },
    { name: 'team', url: TEAM_SERVICE_URL },
    { name: 'search', url: SEARCH_SERVICE_URL }
  ];

  const status = {};
  
  for (const service of services) {
    try {
      const response = await fetch(`${service.url}/health`, { 
        method: 'GET',
        timeout: 5000 
      });
      status[service.name] = {
        status: response.ok ? 'healthy' : 'unhealthy',
        statusCode: response.status,
        url: service.url
      };
    } catch (error) {
      status[service.name] = {
        status: 'unavailable',
        error: error.message,
        url: service.url
      };
    }
  }

  res.json({
    gateway: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    },
    services: status
  });
});

// API v1 routes
app.use('/api/v1', (req, res, next) => {
  console.log(`🌐 API Gateway: ${req.method} ${req.path}`);
  next();
});

// User service proxy (handles authentication via Firebase)
app.use('/api/v1/users', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/users': '/api/v1/users'
  },
  onError: (err, req, res) => {
    console.log('❌ User service error:', err.message);
    res.status(503).json({
      error: 'User service unavailable',
      message: 'User service is currently unavailable. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`🔀 Proxying to user service: ${req.method} ${req.path}`);
  }
}));

// User profiles proxy
app.use('/api/v1/profiles', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/profiles': '/api/v1/profiles'
  },
  onError: (err, req, res) => {
    console.log('❌ User service (profiles) error:', err.message);
    res.status(503).json({
      error: 'User service unavailable',
      message: 'User profile service is currently unavailable. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
}));

// User skills proxy
app.use('/api/v1/skills', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/skills': '/api/v1/skills'
  },
  onError: (err, req, res) => {
    console.log('❌ User service (skills) error:', err.message);
    res.status(503).json({
      error: 'User service unavailable',
      message: 'User skills service is currently unavailable. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
}));

// User reputation proxy
app.use('/api/v1/reputation', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/reputation': '/api/v1/reputation'
  },
  onError: (err, req, res) => {
    console.log('❌ User service (reputation) error:', err.message);
    res.status(503).json({
      error: 'User service unavailable',
      message: 'User reputation service is currently unavailable. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
}));

// Freelancers endpoint (redirect to users with userType filter)
app.use('/api/v1/freelancers', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/freelancers': '/api/v1/users?userType=freelancer'
  },
  onError: (err, req, res) => {
    console.log('❌ User service (freelancers) error:', err.message);
    res.status(503).json({
      error: 'User service unavailable',
      message: 'Freelancers service is currently unavailable. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
}));

// Project service proxy
app.use('/api/v1/projects', createProxyMiddleware({
  target: PROJECT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/projects': '/projects'
  },
  onError: (err, req, res) => {
    console.log('❌ Project service error:', err.message);
    res.status(503).json({
      error: 'Project service unavailable',
      message: 'Project service is currently unavailable. Please try again later.'
    });
  }
}));

// Community service proxy
app.use('/api/v1/community', createProxyMiddleware({
  target: COMMUNITY_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/community': '/api'
  },
  timeout: 60000, // 60 seconds timeout for community service
  proxyTimeout: 60000,
  onError: (err, req, res) => {
    console.log('❌ Community service error:', err.message);
    res.status(503).json({
      error: 'Community service unavailable',
      message: 'Community service is currently unavailable. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`🔀 Proxying to community service: ${req.method} ${req.path}`);
    // Add timeout headers
    proxyReq.setHeader('Connection', 'keep-alive');
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`✅ Community service response: ${proxyRes.statusCode} for ${req.method} ${req.path}`);
  }
}));

// Payment service proxy
app.use('/api/v1/payments', createProxyMiddleware({
  target: PAYMENT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/payments': '/payments'
  },
  onError: (err, req, res) => {
    console.log('❌ Payment service error:', err.message);
    res.status(503).json({
      error: 'Payment service unavailable',
      message: 'Payment service is currently unavailable. Please try again later.'
    });
  }
}));

// Job service proxy
app.use('/api/v1/jobs', createProxyMiddleware({
  target: JOB_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/jobs': '/jobs'
  },
  onError: (err, req, res) => {
    console.log('❌ Job service error:', err.message);
    res.status(503).json({
      error: 'Job service unavailable',
      message: 'Job service is currently unavailable. Please try again later.'
    });
  }
}));

// Search service proxy
app.use('/api/v1/search', createProxyMiddleware({
  target: SEARCH_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/search': '/search'
  },
  onError: (err, req, res) => {
    console.log('❌ Search service error:', err.message);
    res.status(503).json({
      error: 'Search service unavailable',
      message: 'Search service is currently unavailable. Please try again later.'
    });
  }
}));

// Chat service proxy
app.use('/api/v1/chat', createProxyMiddleware({
  target: CHAT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/chat': '/chat'
  },
  onError: (err, req, res) => {
    console.log('❌ Chat service error:', err.message);
    res.status(503).json({
      error: 'Chat service unavailable',
      message: 'Chat service is currently unavailable. Please try again later.'
    });
  }
}));

// Team service proxy
app.use('/api/v1/teams', createProxyMiddleware({
  target: TEAM_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/teams': '/api/teams'
  },
  onError: (err, req, res) => {
    console.log('❌ Team service error:', err.message);
    res.status(503).json({
      error: 'Team service unavailable',
      message: 'Team service is currently unavailable. Please try again later.'
    });
  }
}));

// Team invitations proxy
app.use('/api/v1/team-invitations', createProxyMiddleware({
  target: TEAM_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/team-invitations': '/api/invitations'
  },
  onError: (err, req, res) => {
    console.log('❌ Team invitations error:', err.message);
    res.status(503).json({
      error: 'Team invitations unavailable',
      message: 'Team invitations service is currently unavailable. Please try again later.'
    });
  }
}));

// Team chat proxy
app.use('/api/v1/team-chat', createProxyMiddleware({
  target: TEAM_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/team-chat': '/api/chat'
  },
  onError: (err, req, res) => {
    console.log('❌ Team chat error:', err.message);
    res.status(503).json({
      error: 'Team chat unavailable',
      message: 'Team chat service is currently unavailable. Please try again later.'
    });
  }
}));

// Chatbot service proxy
app.use('/api/v1/chatbot', createProxyMiddleware({
  target: CHAT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/chatbot': '/chatbot'
  },
  onError: (err, req, res) => {
    console.log('❌ Chatbot service error:', err.message);
    res.status(503).json({
      error: 'Chatbot service unavailable',
      message: 'Chatbot service is currently unavailable. Please try again later.'
    });
  }
}));

// Mock endpoints for development when services are not running
app.post('/api/v1/auth/login', (req, res) => {
  console.log('🔐 Mock login endpoint called - Auth Service removed, use Firebase directly');
  res.json({
    success: false,
    message: 'Auth Service has been removed. Please use Firebase Authentication directly.',
    error: 'AUTH_SERVICE_DEPRECATED'
  });
});

app.get('/api/v1/auth/me', (req, res) => {
  console.log('👤 Mock get current user endpoint called');
  res.json({
    success: true,
    user: {
      id: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      role: 'freelancer'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('❌ API Gateway Error:', err);
  
  // Handle specific error types
  if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    return res.status(503).json({
      error: 'Service Unavailable',
      message: 'The requested service is currently unavailable. Please try again later.',
      retryAfter: 30
    });
  }
  
  if (err.code === 'ETIMEDOUT') {
    return res.status(504).json({
      error: 'Gateway Timeout',
      message: 'The request timed out. Please try again later.',
      retryAfter: 30
    });
  }
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong on the server',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

app.listen(PORT, () => {
  console.log(`🚀 VWork API Gateway running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api/v1`);
}); 