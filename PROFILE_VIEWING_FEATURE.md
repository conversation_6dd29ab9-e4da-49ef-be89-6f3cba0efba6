# Tính năng xem Profile User - VWork

## 🎯 Tổng quan

Tính năng này cho phép người dùng xem profile của các user khác trong hệ thống VWork, tươ<PERSON> tự như Facebook khi click vào tên user.

## ✅ Tính năng đã hoàn thiện

### 1. **Backend API**
- ✅ `GET /api/v1/profiles/:userId` - Lấy thông tin profile user
- ✅ `GET /api/v1/users/:id` - Lấy thông tin user chi tiết
- ✅ Phân quyền: Public data cho tất cả, Private data chỉ cho chủ profile
- ✅ Hỗ trợ authentication với Firebase token

### 2. **Frontend Components**
- ✅ `FreelancerProfilePage` - Trang hiển thị profile chi tiết
- ✅ `UserCard` - Component hiển thị user có thể click
- ✅ Click navigation trong community posts/comments
- ✅ Responsive design với Tailwind CSS

### 3. **Tính năng UI/UX**
- ✅ Loading states và error handling
- ✅ Hover effects và cursor pointer
- ✅ Avatar với fallback image
- ✅ Verified badge cho user đã xác thực
- ✅ Social links (LinkedIn, GitHub, Portfolio)
- ✅ Reputation và rating system
- ✅ Responsive layout

## 🚀 Cách sử dụng

### 1. **Trong Community**
```javascript
// Click vào tên user trong posts
<h3 
  className="font-semibold text-gray-900 hover:text-blue-600 cursor-pointer"
  onClick={() => navigate(`/freelancers/${post.author.id}`)}
>
  {post.author.name}
</h3>

// Click vào tên user trong comments
<span 
  className="font-semibold text-sm text-gray-900 hover:text-blue-600 cursor-pointer"
  onClick={() => navigate(`/freelancers/${comment.author.id}`)}
>
  {comment.author.name}
</span>
```

### 2. **Sử dụng UserCard Component**
```javascript
import UserCard from '../components/common/UserCard';

<UserCard 
  user={userData}
  size="medium" // small, medium, large
  showLocation={true}
  showTitle={true}
  onClick={(user) => console.log('User clicked:', user)}
/>
```

### 3. **Direct Navigation**
```javascript
// Navigate trực tiếp đến profile
navigate(`/freelancers/${userId}`);

// URL: /freelancers/user123
```

## 📱 Demo Page

Truy cập `/profile-demo` để test tính năng:
- Test UserCard components
- Test navigation với mock users
- Test với User ID tùy chỉnh
- Hướng dẫn sử dụng

## 🔧 Cấu trúc Data

### User Profile Data
```javascript
{
  userId: "user123",
  displayName: "Nguyễn Văn A",
  firstName: "Nguyễn Văn",
  lastName: "A",
  avatarUrl: "https://example.com/avatar.jpg",
  userType: "freelancer",
  bio: "Frontend developer với 5 năm kinh nghiệm...",
  title: "Senior Frontend Developer",
  company: "Tech Company",
  website: "https://example.com",
  location: {
    country: "Việt Nam",
    city: "Hà Nội",
    timezone: "Asia/Ho_Chi_Minh"
  },
  hourlyRate: 25,
  currency: "USD",
  availability: "available",
  yearsExperience: 5,
  linkedinUrl: "https://linkedin.com/in/user",
  githubUrl: "https://github.com/user",
  portfolioUrl: "https://portfolio.com",
  reputation: {
    score: 95,
    averageRating: 4.8,
    totalReviews: 12
  }
}
```

## 🛡️ Bảo mật

### Public Data (Hiển thị cho tất cả)
- Tên hiển thị
- Avatar
- Title/Position
- Company
- Location
- Hourly rate
- Experience
- Social links
- Reputation score
- Bio

### Private Data (Chỉ chủ profile)
- Email
- Phone number
- Language preference
- Email notifications settings
- Marketing emails settings

## 🎨 UI Components

### FreelancerProfilePage
- **Header**: Back button + title
- **Profile Card**: Avatar, name, title, verified badge
- **Info Section**: Location, hourly rate, experience, availability
- **About Section**: Bio
- **Skills Section**: Skills và languages
- **Reputation Section**: Rating, reviews, score
- **Recent Work**: Placeholder cho dự án gần đây

### UserCard
- **Props**:
  - `user`: User data object
  - `size`: "small" | "medium" | "large"
  - `showLocation`: boolean
  - `showTitle`: boolean
  - `onClick`: callback function
  - `disabled`: boolean

## 🔄 API Endpoints

### GET /api/v1/profiles/:userId
```javascript
// Response
{
  success: true,
  data: {
    userId: "user123",
    displayName: "Nguyễn Văn A",
    // ... profile data
  },
  message: "Profile retrieved successfully"
}
```

### GET /api/v1/users/:id
```javascript
// Response với phân quyền
{
  success: true,
  data: {
    id: "user123",
    displayName: "Nguyễn Văn A",
    // Public data cho tất cả
    profile: { /* public profile data */ },
    // Private data chỉ cho authenticated users
    email: "<EMAIL>", // chỉ khi authenticated
    // Very private data chỉ cho own profile
    lastLogin: "2024-01-01", // chỉ khi là own profile
  }
}
```

## 🐛 Troubleshooting

### Lỗi thường gặp:
1. **"Không thể tải thông tin profile"**
   - Kiểm tra API backend có đang chạy không
   - Kiểm tra User ID có tồn tại trong database không
   - Kiểm tra network connection

2. **"Không tìm thấy profile"**
   - User ID không tồn tại
   - User đã bị xóa hoặc deactivated

3. **Click không hoạt động**
   - Kiểm tra User ID có hợp lệ không
   - Kiểm tra route `/freelancers/:id` đã được cấu hình

## 🚀 Roadmap

### Tính năng sắp tới:
- [ ] Portfolio gallery
- [ ] Work history timeline
- [ ] Client reviews
- [ ] Skills endorsements
- [ ] Follow/Unfollow users
- [ ] Direct messaging từ profile
- [ ] Share profile link
- [ ] Export profile as PDF

### Cải tiến:
- [ ] Lazy loading cho profile images
- [ ] Caching profile data
- [ ] Real-time updates
- [ ] Advanced filtering và search
- [ ] Profile analytics

## 📝 Notes

- Tính năng này tương thích với hệ thống authentication hiện tại
- Sử dụng Firebase token cho API calls
- Responsive design cho mobile và desktop
- SEO-friendly URLs
- Performance optimized với lazy loading 