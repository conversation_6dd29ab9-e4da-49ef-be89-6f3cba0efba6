/**
 * Animation Performance Demo Component
 * Demonstrates the optimized animation system with performance monitoring
 */

import React, { useState, useRef, useEffect } from 'react';
import { usePerformantAnimation } from '../../hooks/usePerformantAnimation';
import OptimizedParticleSystem from '../common/OptimizedParticleSystem';
import { OptimizedMorphingShapeGroup } from '../common/OptimizedMorphingShape';
import { animationOptimizer } from '../../utils/animationOptimizer';
import responsiveAutoscaling from '../../services/responsiveAutoscaling';

const AnimationPerformanceDemo = () => {
 const [performanceStats, setPerformanceStats] = useState({});
 const [isMonitoring, setIsMonitoring] = useState(false);
 const [animationEnabled, setAnimationEnabled] = useState(true);
 
 const demoRef = useRef(null);
 const statsIntervalRef = useRef(null);
 
 const {
  elementRef,
  animateIn,
  animateStagger,
  createHoverAnimation,
  performanceSettings,
  isHighPerformance,
  shouldUseFullAnimations
 } = usePerformantAnimation();

 // Demo elements for testing
 const demoElements = [
  { id: 1, title: 'Fade In Animation', color: 'bg-blue-500' },
  { id: 2, title: 'Scale Animation', color: 'bg-green-500' },
  { id: 3, title: 'Stagger Animation', color: 'bg-purple-500' },
  { id: 4, title: 'Hover Animation', color: 'bg-red-500' },
 ];

 // Update performance stats
 const updateStats = () => {
  const optimizerStats = animationOptimizer.getStats();
  const autoscalingStats = {
   performanceLevel: responsiveAutoscaling.currentPerformanceLevel,
   shouldUseFullAnimations: responsiveAutoscaling.shouldUseFullAnimations(),
   deviceCapabilities: responsiveAutoscaling.deviceCapabilities
  };
  
  setPerformanceStats({
   ...optimizerStats,
   ...autoscalingStats,
   timestamp: new Date().toLocaleTimeString()
  });
 };

 // Start/stop monitoring
 const toggleMonitoring = () => {
  if (isMonitoring) {
   if (statsIntervalRef.current) {
    clearInterval(statsIntervalRef.current);
    statsIntervalRef.current = null;
   }
   setIsMonitoring(false);
  } else {
   statsIntervalRef.current = setInterval(updateStats, 1000);
   setIsMonitoring(true);
   updateStats();
  }
 };

 // Test animations
 const testFadeIn = () => {
  const elements = demoRef.current?.querySelectorAll('.demo-card');
  if (elements) {
   animateIn(elements, {
    duration: 0.8,
    stagger: 0.1,
    from: { opacity: 0, y: 50 }
   });
  }
 };

 const testStagger = () => {
  const elements = demoRef.current?.querySelectorAll('.demo-card');
  if (elements) {
   animateStagger(elements, {
    opacity: 1,
    scale: 1.1,
    duration: 0.6,
    stagger: 0.15,
    from: { opacity: 0.5, scale: 0.9 }
   });
  }
 };

 const testHover = () => {
  const elements = demoRef.current?.querySelectorAll('.demo-card');
  elements?.forEach(element => {
   createHoverAnimation(element, {
    scale: 1.05,
    duration: 0.3
   });
  });
 };

 // Force performance level change for testing
 const changePerformanceLevel = (level) => {
  responsiveAutoscaling.currentPerformanceLevel = level;
  responsiveAutoscaling.notifyPerformanceLevelChange(level);
  updateStats();
 };

 // Cleanup
 useEffect(() => {
  return () => {
   if (statsIntervalRef.current) {
    clearInterval(statsIntervalRef.current);
   }
  };
 }, []);

 return (
  <div className="p-8 bg-gray-50 min-h-screen">
   <div className="max-w-6xl mx-auto">
    {/* Header */}
    <div className="mb-8">
     <h1 className="text-3xl font-bold text-gray-900 mb-4">
      Animation Performance Demo
     </h1>
     <p className="text-gray-600 
      Test and monitor the optimized animation system performance
     </p>
    </div>

    {/* Controls */}
    <div className="bg-white rounded-lg p-6 mb-8 shadow-lg">
     <h2 className="text-xl font-semibold mb-4 text-gray-900 
      Controls
     </h2>
     
     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <button
       onClick={testFadeIn}
       className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
       Test Fade In
      </button>
      <button
       onClick={testStagger}
       className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
      >
       Test Stagger
      </button>
      <button
       onClick={testHover}
       className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
      >
       Enable Hover
      </button>
      <button
       onClick={toggleMonitoring}
       className={`px-4 py-2 text-white rounded transition-colors ${
        isMonitoring ? 'bg-red-500 hover:bg-red-600' : 'bg-gray-500 hover:bg-gray-600'
       }`}
      >
       {isMonitoring ? 'Stop Monitor' : 'Start Monitor'}
      </button>
     </div>

     <div className="flex flex-wrap gap-2 mb-4">
      <span className="text-sm text-gray-600 mr-2">
       Force Performance Level:
      </span>
      {['high', 'medium', 'low', 'minimal'].map(level => (
       <button
        key={level}
        onClick={() => changePerformanceLevel(level)}
        className={`px-3 py-1 text-xs rounded ${
         performanceStats.performanceLevel === level
          ? 'bg-blue-500 text-white'
          : 'bg-gray-200 text-gray-700 
        }`}
       >
        {level}
       </button>
      ))}
     </div>

     <label className="flex items-center">
      <input
       type="checkbox"
       checked={animationEnabled}
       onChange={(e) => setAnimationEnabled(e.target.checked)}
       className="mr-2"
      />
      <span className="text-sm text-gray-600 
       Enable Animations
      </span>
     </label>
    </div>

    {/* Performance Stats */}
    {isMonitoring && (
     <div className="bg-white rounded-lg p-6 mb-8 shadow-lg">
      <h2 className="text-xl font-semibold mb-4 text-gray-900 
       Performance Statistics
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
       <div className="bg-gray-50 p-4 rounded">
        <div className="text-sm text-gray-600 Level</div>
        <div className="text-lg font-semibold text-gray-900 
         {performanceStats.performanceLevel}
        </div>
       </div>
       
       <div className="bg-gray-50 p-4 rounded">
        <div className="text-sm text-gray-600 Animations</div>
        <div className="text-lg font-semibold text-gray-900 
         {performanceStats.activeAnimations} / {performanceStats.maxConcurrent}
        </div>
       </div>
       
       <div className="bg-gray-50 p-4 rounded">
        <div className="text-sm text-gray-600 Animations</div>
        <div className="text-lg font-semibold text-gray-900 
         {performanceStats.queuedAnimations}
        </div>
       </div>
       
       <div className="bg-gray-50 p-4 rounded">
        <div className="text-sm text-gray-600 Animations</div>
        <div className="text-lg font-semibold text-gray-900 
         {performanceStats.shouldUseFullAnimations ? 'Enabled' : 'Disabled'}
        </div>
       </div>
      </div>
      
      <div className="mt-4 text-xs text-gray-500 
       Last updated: {performanceStats.timestamp}
      </div>
     </div>
    )}

    {/* Demo Area */}
    <div className="relative bg-white rounded-lg p-8 shadow-lg overflow-hidden">
     {/* Background Effects */}
     {animationEnabled && (
      <>
       <OptimizedParticleSystem
        particleCount={30}
        colors={['#3B82F6', '#60A5FA', '#93C5FD']}
        enabled={animationEnabled}
       />
       <OptimizedMorphingShapeGroup
        count={3}
        colors={['#3B82F6', '#10B981', '#8B5CF6']}
        enabled={animationEnabled}
       />
      </>
     )}

     <h2 className="text-xl font-semibold mb-6 text-gray-900 relative z-10">
      Animation Test Area
     </h2>
     
     <div ref={demoRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 relative z-10">
      {demoElements.map((element) => (
       <div
        key={element.id}
        className={`demo-card ${element.color} p-6 rounded-lg text-white shadow-lg cursor-pointer transform transition-transform`}
       >
        <h3 className="font-semibold mb-2">{element.title}</h3>
        <p className="text-sm opacity-90">
         Performance Level: {performanceStats.performanceLevel || 'Unknown'}
        </p>
       </div>
      ))}
     </div>
    </div>
   </div>
  </div>
 );
};

export default AnimationPerformanceDemo;
