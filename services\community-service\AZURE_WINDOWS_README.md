# 🚀 Azure Deployment - Community Service (Windows)

Hướng dẫn nhanh để deploy Community Service lên Azure App Service trên Windows.

## 📋 Yêu cầu

- Azure CLI đã cài đặt
- Đã đăng nhập Azure (`az login`)
- Firebase project credentials
- PowerShell (có sẵn trên Windows 10/11)

## 🚀 Deploy nhanh

### Bước 1: Deploy application
```cmd
cd services\community-service
deploy-azure-windows.bat vwork-community-service vwork-rg
```

### Bước 2: Setup database
```cmd
setup-azure-db-windows.bat vwork-community-service vwork-rg
```

### Bước 3: Chạy migrations
```cmd
az webapp ssh --name vwork-community-service --resource-group vwork-rg
npm run db:setup
exit
```

## ⚙️ Cấu hình Environment Variables

Sau khi deploy, cấu hình các biến môi trường trong Azure Portal:

1. Vào App Service → Configuration → Application settings
2. Thêm các biến sau:

```bash
# Firebase (Bắt buộc)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key\n-----END PRIVATE KEY-----\n"

# CORS (Bắt buộc)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Tùy chọn
LOG_LEVEL=info
LOG_FORMAT=json
```

## 🧪 Test Deployment

```cmd
# Test health endpoint
curl https://vwork-community-service.azurewebsites.net/health

# Test API
curl https://vwork-community-service.azurewebsites.net/api
```

## 📊 Monitoring

```cmd
# Xem logs real-time
az webapp log tail --name vwork-community-service --resource-group vwork-rg

# Restart app
az webapp restart --name vwork-community-service --resource-group vwork-rg

# View settings
az webapp config appsettings list --name vwork-community-service --resource-group vwork-rg
```

## 🔧 Troubleshooting

### App không start
```cmd
# Xem logs
az webapp log tail --name vwork-community-service --resource-group vwork-rg

# Kiểm tra settings
az webapp config appsettings list --name vwork-community-service --resource-group vwork-rg
```

### Database connection failed
- Kiểm tra DATABASE_URL trong Application settings
- Đảm bảo firewall rules đã được cấu hình
- Test connection qua SSH

### Environment variables không hoạt động
- Restart app sau khi thay đổi settings
- Kiểm tra format của FIREBASE_PRIVATE_KEY

### Lỗi PowerShell
Nếu gặp lỗi PowerShell execution policy:
```cmd
# Chạy PowerShell as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 📁 Files được tạo

- `deploy-azure-windows.bat` - Script deploy application
- `setup-azure-db-windows.bat` - Script setup database
- `azure-db-credentials.txt` - File chứa thông tin database (xóa sau khi deploy)

## 🎯 Kết quả

Sau khi hoàn thành, bạn sẽ có:
- ✅ App Service chạy Community Service
- ✅ PostgreSQL database
- ✅ Auto-scaling
- ✅ SSL certificate
- ✅ Monitoring và logging

## 📖 Tài liệu chi tiết

Xem `AZURE_DEPLOYMENT_GUIDE.md` để biết hướng dẫn chi tiết. 