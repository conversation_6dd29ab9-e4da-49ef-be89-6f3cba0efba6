#!/usr/bin/env node

/**
 * Get Production Database URL
 * Helps extract DATABASE_URL from Render environment for migration
 */

const https = require('https');
const { URL } = require('url');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logSection = (title) => {
  console.log('\n' + '='.repeat(60));
  log(title, 'bright');
  console.log('='.repeat(60));
};

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'VWork-Database-Migration/1.0',
        ...options.headers
      }
    };

    const req = https.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: jsonData,
            url: url
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: data,
            url: url,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function getProductionDatabaseUrl() {
  try {
    logSection('🔍 Getting Production Database URL');
    
    // Method 1: Check if we can infer from health check
    log('🏥 Checking Community Service health for database info...', 'blue');
    
    try {
      const healthResponse = await makeRequest('https://vwork-community-service.onrender.com/health');
      
      if (healthResponse.status === 200 && healthResponse.data.checks?.database) {
        const dbCheck = healthResponse.data.checks.database;
        log(`Database Status: ${dbCheck.status}`, 'green');
        log(`Database Version: ${dbCheck.result?.version || 'Unknown'}`, 'cyan');
        
        if (dbCheck.result?.message) {
          log(`Database Message: ${dbCheck.result.message}`, 'cyan');
        }
      }
    } catch (error) {
      log(`⚠️ Could not get health check: ${error.message}`, 'yellow');
    }
    
    logSection('📋 Database URL Instructions');
    
    log('To get the production DATABASE_URL, you have these options:', 'bright');
    
    log('\n🔧 Option 1: From Render Dashboard', 'blue');
    log('1. Go to: https://dashboard.render.com', 'cyan');
    log('2. Navigate to: nerafus-community-service (or vwork-community-service)', 'cyan');
    log('3. Click on: Environment tab', 'cyan');
    log('4. Find: DATABASE_URL value', 'cyan');
    log('5. Copy the full PostgreSQL connection string', 'cyan');
    
    log('\n🔧 Option 2: From Render Environment Variables (in screenshot)', 'blue');
    log('Based on your screenshot, the DATABASE_URL should be:', 'cyan');
    log('postgresql://vwork_admin:NbPcSSVmIoqOBSYJLg3qPUMzthNqpqfg-d21kqxvdiaea7dv4a2g-...', 'yellow');
    log('(This is the connection string visible in your Render dashboard)', 'yellow');
    
    log('\n🔧 Option 3: Set Environment Variable Locally', 'blue');
    log('1. Copy the DATABASE_URL from Render', 'cyan');
    log('2. Set it locally:', 'cyan');
    log('   export DATABASE_URL="postgresql://..."  # Linux/Mac', 'green');
    log('   set DATABASE_URL=postgresql://...       # Windows CMD', 'green');
    log('   $env:DATABASE_URL="postgresql://..."    # Windows PowerShell', 'green');
    log('3. Run the migration:', 'cyan');
    log('   npm run db:fix-production', 'green');
    
    logSection('🗄️ Manual Database Migration');
    
    log('If you have psql installed, you can also run migration directly:', 'blue');
    log('1. Connect to production database:', 'cyan');
    log('   psql "postgresql://vwork_admin:PASSWORD@HOST:PORT/DATABASE"', 'green');
    log('2. Run migration file:', 'cyan');
    log('   \\i migrations/clean_schema.sql', 'green');
    log('3. Check tables were created:', 'cyan');
    log('   \\dt', 'green');
    
    logSection('🚀 Expected Database URL Format');
    
    log('The DATABASE_URL should look like:', 'blue');
    log('postgresql://username:password@host:port/database_name', 'cyan');
    log('', '');
    log('From your Render environment, it should be:', 'blue');
    log('postgresql://vwork_admin:NbPcSSVmIoqOBSYJLg3qPUMzthNqpqfg-d21kqxvdiaea7dv4a2g-j/vwork-community-service', 'yellow');
    log('', '');
    log('⚠️ Note: The password may be truncated in the screenshot.', 'yellow');
    log('Please get the full DATABASE_URL from Render Dashboard.', 'yellow');
    
    logSection('🧪 Testing Migration');
    
    log('After setting DATABASE_URL, test the migration:', 'blue');
    log('1. Check if DATABASE_URL is set:', 'cyan');
    log('   echo $DATABASE_URL  # Linux/Mac', 'green');
    log('   echo %DATABASE_URL% # Windows CMD', 'green');
    log('2. Run database fix:', 'cyan');
    log('   npm run db:fix-production', 'green');
    log('3. Test production endpoints:', 'cyan');
    log('   npm run test:production', 'green');
    
  } catch (error) {
    log(`❌ Error getting database info: ${error.message}`, 'red');
    console.error('Full error:', error);
  }
}

// Run if called directly
if (require.main === module) {
  getProductionDatabaseUrl();
}

module.exports = { getProductionDatabaseUrl }; 