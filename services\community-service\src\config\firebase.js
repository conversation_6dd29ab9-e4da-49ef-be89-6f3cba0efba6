const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Use absolute path to project root
const projectRoot = 'C:\\Project\\Vwork';
const envPath = path.join(projectRoot, '.env');

console.log('🔍 Project root:', projectRoot);
console.log('🔍 Loading .env from:', envPath);
console.log('🔍 File exists:', fs.existsSync(envPath));

// Manual parsing instead of dotenv to handle multiline variables correctly
let firebaseVars = {};

if (fs.existsSync(envPath)) {
  const content = fs.readFileSync(envPath, 'utf8');
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    if (trimmedLine.includes('FIREBASE_PROJECT_ID=') && !firebaseVars.FIREBASE_PROJECT_ID) {
      firebaseVars.FIREBASE_PROJECT_ID = trimmedLine.split('=')[1];
    }
    if (trimmedLine.includes('FIREBASE_CLIENT_EMAIL=')) {
      firebaseVars.FIREBASE_CLIENT_EMAIL = trimmedLine.split('=')[1];
    }
    if (trimmedLine.includes('FIREBASE_PRIVATE_KEY=')) {
      // Handle multiline private key
      const parts = trimmedLine.split('=');
      let privateKey = parts.slice(1).join('=');
      
      // If it starts with quote, it's multiline
      if (privateKey.startsWith('"')) {
        privateKey = privateKey.substring(1); // Remove opening quote
        
        // Look for the closing quote in subsequent lines
        let currentIndex = index + 1;
        while (currentIndex < lines.length) {
          if (lines[currentIndex].includes('"') && lines[currentIndex].endsWith('"')) {
            privateKey += '\n' + lines[currentIndex].slice(0, -1); // Add line without closing quote
            break;
          } else {
            privateKey += '\n' + lines[currentIndex];
          }
          currentIndex++;
        }
      }
      
      firebaseVars.FIREBASE_PRIVATE_KEY = privateKey;
    }
  });
  
  // Set them in process.env
  Object.keys(firebaseVars).forEach(key => {
    if (firebaseVars[key]) {
      process.env[key] = firebaseVars[key];
    }
  });
  
  console.log('✅ .env file loaded and parsed manually');
} else {
  console.log('❌ .env file not found at:', envPath);
}

// Debug: Check environment variables
console.log('🔍 Environment variables loaded:');
console.log('  FIREBASE_PROJECT_ID:', process.env.FIREBASE_PROJECT_ID ? '✅ Found' : '❌ Missing');
console.log('  FIREBASE_CLIENT_EMAIL:', process.env.FIREBASE_CLIENT_EMAIL ? '✅ Found' : '❌ Missing');
console.log('  FIREBASE_PRIVATE_KEY:', process.env.FIREBASE_PRIVATE_KEY ? '✅ Found' : '❌ Missing');

/**
 * Firebase Auth Configuration
 * Used for token verification across all environments
 */
let firebaseInitialized = false;

try {
  console.log('🔍 Starting Firebase Admin SDK initialization...');
  
  if (!admin.apps.length) {
    console.log('🔍 No existing Firebase apps found, initializing new one...');
    
    // Ensure all required environment variables are present
    const requiredEnvVars = {
      FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
      FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
      FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY
    };

    // Check for missing environment variables
    const missingVars = Object.entries(requiredEnvVars)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    console.log('🔍 Missing variables check result:', missingVars);

    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    console.log('🔍 All environment variables present, creating service account...');

    const serviceAccount = {
      type: 'service_account',
      project_id: process.env.FIREBASE_PROJECT_ID,
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
    };

    console.log('🔍 Service account object created:');
    console.log('  project_id:', serviceAccount.project_id);
    console.log('  client_email:', serviceAccount.client_email);
    console.log('  private_key length:', serviceAccount.private_key?.length);

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });

    console.log('🔥 Firebase Admin SDK initialized successfully');
    console.log('📧 Project ID:', process.env.FIREBASE_PROJECT_ID);
    console.log('👤 Client Email:', process.env.FIREBASE_CLIENT_EMAIL?.substring(0, 20) + '...');
    firebaseInitialized = true;
  } else {
    console.log('✅ Firebase Admin SDK already initialized');
    firebaseInitialized = true;
  }

  console.log('✅ Community Service: Firebase Auth ready for token verification');
} catch (error) {
  console.error('❌ Community Service: Firebase Auth initialization failed:', error.message);
  console.error('💡 Please ensure all Firebase environment variables are set in .env file');
  console.error('🔍 Full error:', error);
  firebaseInitialized = false;
  
  // In development, continue without Firebase for testing
  if (process.env.NODE_ENV === 'development') {
    console.warn('🔧 Development mode: Continuing without Firebase for testing purposes');
    console.warn('⚠️ Authentication will fail, but service can be tested for other functionality');
  } else {
    // Exit process in production if Firebase cannot be initialized
    console.error('🚨 Firebase is required for authentication in production. Exiting...');
    process.exit(1);
  }
}

/**
 * Verify Firebase ID Token
 * @param {string} idToken - Firebase ID token to verify
 * @returns {Promise<Object>} Decoded token
 */
async function verifyIdToken(idToken) {
  if (!firebaseInitialized) {
    throw new Error('Firebase not initialized');
  }
  
  try {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    throw new Error(`Token verification failed: ${error.message}`);
  }
}

/**
 * Get current user info from request
 */
function getCurrentUser(req) {
  return req.user || null;
}

/**
 * Check if user is authenticated
 */
function isAuthenticated(req) {
  return !!req.user;
}

/**
 * Get admin instance for advanced operations
 */
function getAdminAuth() {
  if (!firebaseInitialized) {
    throw new Error('Firebase Admin SDK is not initialized');
  }
  return admin.auth();
}

module.exports = {
  verifyIdToken,
  getCurrentUser,
  isAuthenticated,
  getAdminAuth,
  firebaseInitialized
};
