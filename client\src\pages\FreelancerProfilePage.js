import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  UserIcon, 
  MapPinIcon, 
  GlobeAltIcon, 
  BriefcaseIcon,
  ClockIcon,
  StarIcon,
  CurrencyDollarIcon,
  AcademicCapIcon,
  LinkIcon,
  ArrowLeftIcon,
  EnvelopeIcon,
  PhoneIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import { toast } from 'react-hot-toast';

const FreelancerProfilePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const isOwnProfile = currentUser?.uid === id;

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        
        // Fetch user profile from API
        const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3001'}/api/v1/profiles/${id}`, {
          headers: {
            'Authorization': currentUser ? `Bearer ${await currentUser.getIdToken()}` : '',
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Không thể tải thông tin profile');
        }

        const data = await response.json();
        setProfile(data.data);
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError(err.message);
        toast.error('Không thể tải thông tin profile');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProfile();
    }
  }, [id, currentUser]);

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const getExperienceLevel = (years) => {
    if (years < 1) return 'Mới bắt đầu';
    if (years < 3) return 'Junior';
    if (years < 5) return 'Mid-level';
    if (years < 8) return 'Senior';
    return 'Expert';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải thông tin profile...</p>
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <UserIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy profile</h2>
            <p className="text-gray-600 mb-6">Profile này có thể không tồn tại hoặc đã bị xóa.</p>
            <button
              onClick={() => navigate(-1)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Quay lại
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Quay lại
            </button>
            <h1 className="text-xl font-semibold text-gray-900">Profile Freelancer</h1>
            <div className="w-20"></div> {/* Spacer */}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Profile Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              {/* Profile Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
                <div className="text-center">
                  <div className="relative inline-block">
                    <img
                      src={profile.avatarUrl || 'https://via.placeholder.com/120x120/6366f1/ffffff?text=U'}
                      alt={profile.displayName}
                      className="w-24 h-24 rounded-full border-4 border-white shadow-lg object-cover"
                      onError={(e) => {
                        e.target.src = 'https://via.placeholder.com/120x120/6366f1/ffffff?text=U';
                      }}
                    />
                    {profile.verified && (
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckBadgeIcon className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>
                  <h2 className="text-2xl font-bold mt-4">{profile.displayName}</h2>
                  <p className="text-blue-100">{profile.title || 'Freelancer'}</p>
                </div>
              </div>

              {/* Profile Info */}
              <div className="p-6">
                {/* Location */}
                {profile.location?.city && (
                  <div className="flex items-center mb-4">
                    <MapPinIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-gray-700">
                      {profile.location.city}, {profile.location.country}
                    </span>
                  </div>
                )}

                {/* Hourly Rate */}
                {profile.hourlyRate && (
                  <div className="flex items-center mb-4">
                    <CurrencyDollarIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-gray-700 font-semibold">
                      {formatCurrency(profile.hourlyRate, profile.currency)}/giờ
                    </span>
                  </div>
                )}

                {/* Experience */}
                {profile.yearsExperience && (
                  <div className="flex items-center mb-4">
                    <AcademicCapIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-gray-700">
                      {profile.yearsExperience} năm kinh nghiệm ({getExperienceLevel(profile.yearsExperience)})
                    </span>
                  </div>
                )}

                {/* Availability */}
                {profile.availability && (
                  <div className="flex items-center mb-4">
                    <ClockIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-gray-700 capitalize">
                      {profile.availability === 'available' ? 'Sẵn sàng nhận việc' : 
                       profile.availability === 'busy' ? 'Đang bận' : 
                       profile.availability === 'unavailable' ? 'Không nhận việc' : profile.availability}
                    </span>
                  </div>
                )}

                {/* Company */}
                {profile.company && (
                  <div className="flex items-center mb-4">
                    <BriefcaseIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-gray-700">{profile.company}</span>
                  </div>
                )}

                {/* Social Links */}
                {(profile.linkedinUrl || profile.githubUrl || profile.portfolioUrl) && (
                  <div className="border-t pt-4 mt-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Liên kết</h3>
                    <div className="space-y-2">
                      {profile.linkedinUrl && (
                        <a
                          href={profile.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                        >
                          <LinkIcon className="h-4 w-4 mr-2" />
                          LinkedIn
                        </a>
                      )}
                      {profile.githubUrl && (
                        <a
                          href={profile.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                        >
                          <LinkIcon className="h-4 w-4 mr-2" />
                          GitHub
                        </a>
                      )}
                      {profile.portfolioUrl && (
                        <a
                          href={profile.portfolioUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-purple-600 hover:text-purple-800 transition-colors"
                        >
                          <LinkIcon className="h-4 w-4 mr-2" />
                          Portfolio
                        </a>
                      )}
                    </div>
                  </div>
                )}

                {/* Contact Info (only for own profile) */}
                {isOwnProfile && (
                  <div className="border-t pt-4 mt-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3">Thông tin liên hệ</h3>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <EnvelopeIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-700 text-sm">{profile.email}</span>
                      </div>
                      {profile.phoneNumber && (
                        <div className="flex items-center">
                          <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-gray-700 text-sm">{profile.phoneNumber}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* About Section */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Giới thiệu</h3>
              {profile.bio ? (
                <p className="text-gray-700 leading-relaxed">{profile.bio}</p>
              ) : (
                <p className="text-gray-500 italic">Chưa có thông tin giới thiệu</p>
              )}
            </div>

            {/* Skills Section */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Kỹ năng & Chuyên môn</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Kỹ năng chính</h4>
                  <div className="flex flex-wrap gap-2">
                    {profile.skills?.slice(0, 6).map((skill, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    )) || (
                      <span className="text-gray-500 italic">Chưa có thông tin kỹ năng</span>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Ngôn ngữ</h4>
                  <div className="flex flex-wrap gap-2">
                    {profile.languages?.map((lang, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                      >
                        {lang}
                      </span>
                    )) || (
                      <span className="text-gray-500 italic">Chưa có thông tin ngôn ngữ</span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Reputation Section */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Đánh giá & Uy tín</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    {[...Array(5)].map((_, i) => (
                      <StarIcon
                        key={i}
                        className={`h-5 w-5 ${
                          i < Math.floor(profile.reputation?.averageRating || 0)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <p className="text-2xl font-bold text-gray-900">
                    {profile.reputation?.averageRating?.toFixed(1) || '0.0'}
                  </p>
                  <p className="text-sm text-gray-600">Điểm đánh giá trung bình</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    {profile.reputation?.totalReviews || 0}
                  </p>
                  <p className="text-sm text-gray-600">Đánh giá</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    {profile.reputation?.score || 0}
                  </p>
                  <p className="text-sm text-gray-600">Điểm uy tín</p>
                </div>
              </div>
            </div>

            {/* Recent Work Section */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Dự án gần đây</h3>
              <div className="text-center py-8">
                <BriefcaseIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Chưa có dự án nào được hiển thị</p>
                <p className="text-sm text-gray-400">Tính năng này sẽ được cập nhật sớm</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FreelancerProfilePage;
