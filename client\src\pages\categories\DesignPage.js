import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  PaintBrushIcon, 
  StarIcon, 
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  EyeIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

// Mock data cho freelancers chuy<PERSON>n ngành thiết kế
const mockDesigners = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    title: 'UI/UX Designer',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 234,
    hourlyRate: 28,
    location: '<PERSON><PERSON> Minh',
    skills: ['Figma', 'Adobe XD', 'Sketch', 'Prototyping'],
    description: '<PERSON>yên gia thiết kế UI/UX với 6+ năm kinh nghiệm. <PERSON><PERSON> thiết kế cho 300+ ứng dụng và website.',
    completedJobs: 189,
    responseTime: '30 phút',
    availability: 'Sẵn sàng',
    portfolio: [
      'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=300&h=200&fit=crop'
    ],
    likes: 1250,
    views: 15600
  },
  {
    id: 2,
    name: 'Trần Văn Bình',
    title: 'Graphic Designer',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 156,
    hourlyRate: 22,
    location: 'Hà Nội',
    skills: ['Photoshop', 'Illustrator', 'InDesign', 'Branding'],
    description: 'Chuyên thiết kế đồ họa, logo và nhận diện thương hiệu. Phong cách sáng tạo và hiện đại.',
    completedJobs: 267,
    responseTime: '1 giờ',
    availability: 'Sẵn sàng',
    portfolio: [
      'https://images.unsplash.com/photo-1626785774573-4b799315345d?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300&h=200&fit=crop'
    ],
    likes: 890,
    views: 12300
  },
  {
    id: 3,
    name: 'Lê Thị Cẩm',
    title: 'Web Designer',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 4.7,
    reviewCount: 98,
    hourlyRate: 25,
    location: 'Đà Nẵng',
    skills: ['HTML/CSS', 'WordPress', 'Responsive Design', 'Animation'],
    description: 'Thiết kế website đẹp mắt và tối ưu trải nghiệm người dùng. Chuyên responsive design.',
    completedJobs: 134,
    responseTime: '45 phút',
    availability: 'Bận',
    portfolio: [
      'https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop'
    ],
    likes: 567,
    views: 8900
  },
  {
    id: 4,
    name: 'Phạm Minh Đức',
    title: '3D Artist',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 76,
    hourlyRate: 35,
    location: 'Hồ Chí Minh',
    skills: ['Blender', '3ds Max', 'Maya', 'Cinema 4D'],
    description: 'Chuyên gia 3D modeling và animation. Tạo ra những tác phẩm 3D chất lượng cao.',
    completedJobs: 89,
    responseTime: '2 giờ',
    availability: 'Sẵn sàng',
    portfolio: [
      'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop'
    ],
    likes: 1456,
    views: 18700
  },
  {
    id: 5,
    name: 'Võ Thị Hoa',
    title: 'Illustration Artist',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 145,
    hourlyRate: 20,
    location: 'Hà Nội',
    skills: ['Digital Art', 'Character Design', 'Concept Art', 'Procreate'],
    description: 'Nghệ sĩ minh họa chuyên nghiệp. Tạo ra những tác phẩm nghệ thuật độc đáo và ấn tượng.',
    completedJobs: 178,
    responseTime: '1.5 giờ',
    availability: 'Sẵn sàng',
    portfolio: [
      'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1596548438137-d51ea5c83ca4?w=300&h=200&fit=crop'
    ],
    likes: 2340,
    views: 25600
  },
  {
    id: 6,
    name: 'Hoàng Văn Tùng',
    title: 'Motion Graphics Designer',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 4.6,
    reviewCount: 67,
    hourlyRate: 32,
    location: 'Hồ Chí Minh',
    skills: ['After Effects', 'Premiere Pro', 'Cinema 4D', 'Animation'],
    description: 'Chuyên gia motion graphics và video animation. Tạo ra những video quảng cáo ấn tượng.',
    completedJobs: 56,
    responseTime: '3 giờ',
    availability: 'Bận',
    portfolio: [
      'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=300&h=200&fit=crop'
    ],
    likes: 789,
    views: 11200
  }
];

const DesignPage = () => {
  const { t } = useLanguage();
  const [filteredDesigners, setFilteredDesigners] = useState(mockDesigners);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSkill, setSelectedSkill] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [availability, setAvailability] = useState('');

  // Get all unique skills
  const allSkills = [...new Set(mockDesigners.flatMap(designer => designer.skills))];

  // Filter function
  useEffect(() => {
    let filtered = mockDesigners;

    if (searchQuery) {
      filtered = filtered.filter(designer => 
        designer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        designer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        designer.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedSkill) {
      filtered = filtered.filter(designer => designer.skills.includes(selectedSkill));
    }

    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filtered = filtered.filter(designer => {
        if (max) {
          return designer.hourlyRate >= min && designer.hourlyRate <= max;
        } else {
          return designer.hourlyRate >= min;
        }
      });
    }

    if (availability) {
      filtered = filtered.filter(designer => designer.availability === availability);
    }

    setFilteredDesigners(filtered);
  }, [searchQuery, selectedSkill, priceRange, availability]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <div className="p-3 bg-purple-100 rounded-lg">
              <PaintBrushIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Thiết kế sáng tạo</h1>
              <p className="text-lg text-gray-600">Khám phá các nhà thiết kế tài năng và sáng tạo</p>
            </div>
          </div>
          
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input
              type="text"
              placeholder="Tìm kiếm designer..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />

            <select
              value={selectedSkill}
              onChange={(e) => setSelectedSkill(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="">Tất cả kỹ năng</option>
              {allSkills.map(skill => (
                <option key={skill} value={skill}>{skill}</option>
              ))}
            </select>

            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="">Tất cả mức giá</option>
              <option value="0-20">$0 - $20/giờ</option>
              <option value="20-30">$20 - $30/giờ</option>
              <option value="30-40">$30 - $40/giờ</option>
              <option value="40">$40+/giờ</option>
            </select>

            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Sẵn sàng">Sẵn sàng</option>
              <option value="Bận">Bận</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Tìm thấy <span className="font-semibold">{filteredDesigners.length}</span> designer phù hợp
          </p>
        </div>

        {/* Designer Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDesigners.map(designer => (
            <div key={designer.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
              {/* Portfolio Preview */}
              {designer.portfolio.length > 0 && (
                <div className="relative h-48 bg-gray-100">
                  <img
                    src={designer.portfolio[0]}
                    alt="Portfolio preview"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-2 right-2 flex space-x-2">
                    <div className="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center space-x-1">
                      <EyeIcon className="h-3 w-3" />
                      <span>{designer.views.toLocaleString()}</span>
                    </div>
                    <div className="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center space-x-1">
                      <HeartIcon className="h-3 w-3" />
                      <span>{designer.likes.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}

              <div className="p-6">
                {/* Designer Header */}
                <div className="flex items-start space-x-4 mb-4">
                  <img
                    src={designer.avatar}
                    alt={designer.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{designer.name}</h3>
                    <p className="text-purple-600 font-medium">{designer.title}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <div className="flex items-center">
                        <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">{designer.rating}</span>
                      </div>
                      <span className="text-gray-400">•</span>
                      <span className="text-sm text-gray-600">{designer.reviewCount} đánh giá</span>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{designer.description}</p>

                {/* Skills */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {designer.skills.slice(0, 3).map(skill => (
                      <span key={skill} className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                        {skill}
                      </span>
                    ))}
                    {designer.skills.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{designer.skills.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">{designer.location}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">{designer.responseTime}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">${designer.hourlyRate}/giờ</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`w-2 h-2 rounded-full ${designer.availability === 'Sẵn sàng' ? 'bg-green-400' : 'bg-red-400'}`}></span>
                    <span className="text-gray-600">{designer.availability}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <button className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors duration-200 text-sm font-medium">
                    Liên hệ
                  </button>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
                    Portfolio
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {filteredDesigners.length > 0 && (
          <div className="text-center mt-8">
            <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              Xem thêm designer
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignPage;
