import { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
 CheckBadgeIcon,
 HeartIcon,
 StarIcon,
 MapPinIcon,
 ChatBubbleLeftIcon,
 EyeIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves
const premiumEases = {
 elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
 bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
 liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
 magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
 wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1")
};

const AppleFreelancersShowcase = () => {
 const { t } = useLanguage();
 const sectionRef = useRef(null);
 const titleRef = useRef(null);
 const freelancersRef = useRef(null);
 const particleCanvasRef = useRef(null);
 const morphingShapesRef = useRef([]);
 const [likedFreelancers, setLikedFreelancers] = useState(new Set());

 // Mock data for top freelancers
 const topFreelancers = [
  {
   id: 1,
   name: 'Sarah Johnson',
   title: 'Senior UI/UX Designer',
   avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
   rating: 4.9,
   reviews: 127,
   hourlyRate: 85,
   location: 'San Francisco, CA',
   description: 'Passionate designer with 8+ years creating beautiful, user-centered digital experiences.',
   skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
   completedProjects: 156,
   responseTime: '2h',
   topRated: true,
   verified: true,
  },
  {
   id: 2,
   name: 'Michael Chen',
   title: 'Full-Stack Developer',
   avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
   rating: 4.8,
   reviews: 89,
   hourlyRate: 95,
   location: 'New York, NY',
   description: 'Experienced developer specializing in React, Node.js, and cloud architecture.',
   skills: ['React', 'Node.js', 'AWS', 'TypeScript'],
   completedProjects: 203,
   responseTime: '1h',
   topRated: true,
   verified: true,
  },
  {
   id: 3,
   name: 'Emily Rodriguez',
   title: 'Content Strategist',
   avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
   rating: 4.7,
   reviews: 156,
   hourlyRate: 65,
   location: 'Austin, TX',
   description: 'Creative content strategist helping brands tell compelling stories.',
   skills: ['Content Strategy', 'SEO', 'Copywriting', 'Branding'],
   completedProjects: 89,
   responseTime: '3h',
   topRated: false,
   verified: true,
  },
 ];

 // Advanced Talent Particle System
 const createAdvancedTalentParticles = () => {
  if (!particleCanvasRef.current) return;

  const container = particleCanvasRef.current;
  const particles = [];

  // Create 40 floating particles
  for (let i = 0; i < 40; i++) {
   const particle = document.createElement('div');
   particle.className = 'absolute w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-30';
   particle.style.left = Math.random() * 100 + '%';
   particle.style.top = Math.random() * 100 + '%';
   container.appendChild(particle);
   particles.push(particle);

   // Physics-based movement
   gsap.to(particle, {
    physics2D: {
     velocity: Math.random() * 80 + 40,
     angle: Math.random() * 360,
     gravity: 20,
     friction: 0.98
    },
    duration: 10,
    repeat: -1,
    yoyo: true,
    ease: "none"
   });

   // Floating animation
   gsap.to(particle, {
    y: "random(-30, 30)",
    x: "random(-20, 20)",
    scale: "random(0.3, 1.2)",
    opacity: "random(0.1, 0.6)",
    duration: "random(4, 8)",
    repeat: -1,
    yoyo: true,
    ease: premiumEases.wave
   });
  }
 };

 // Morphing Background Shapes
 const createMorphingTalentShapes = () => {
  morphingShapesRef.current.forEach((shape, index) => {
   if (!shape) return;

   const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
   
   morphTimeline
    .to(shape, {
     borderRadius: "50% 60% 40% 70% / 60% 40% 70% 50%",
     scale: 1.1,
     rotation: 45,
     duration: 6,
     ease: premiumEases.liquid
    })
    .to(shape, {
     borderRadius: "40% 50% 70% 60% / 50% 70% 40% 60%",
     scale: 0.9,
     rotation: -45,
     duration: 5,
     ease: premiumEases.elastic
    })
    .to(shape, {
     borderRadius: "70% 40% 50% 60% / 40% 60% 50% 70%",
     scale: 1.05,
     rotation: 90,
     duration: 7,
     ease: premiumEases.wave
    });

   // Add delay based on index
   morphTimeline.delay(index * 0.8);
  });
 };

 // Advanced Title Animation with SplitText
 const createAdvancedTitleAnimation = () => {
  if (!titleRef.current) return;

  const titleElement = titleRef.current.querySelector('h2');
  const subtitleElement = titleRef.current.querySelector('p');

  if (titleElement) {
   const titleSplit = new SplitText(titleElement, { type: "chars,words" });

   gsap.fromTo(titleSplit.chars, {
    opacity: 0,
    y: 100,
    rotationX: -90,
    transformOrigin: "center bottom"
   }, {
    opacity: 1,
    y: 0,
    rotationX: 0,
    duration: 1.2,
    stagger: 0.05,
    ease: premiumEases.bounce,
    scrollTrigger: {
     trigger: titleRef.current,
     start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });
  }

  if (subtitleElement) {
   const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

   gsap.fromTo(subtitleSplit.words, {
    opacity: 0,
    y: 30,
    scale: 0.8
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    duration: 0.8,
    stagger: 0.1,
    ease: premiumEases.elastic,
    delay: 0.5,
    scrollTrigger: {
     trigger: titleRef.current,
     start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });
  }
 };

 // Advanced Magnetic Hover Effect for Freelancers
 const createMagneticFreelancerHover = (freelancerElement, index) => {
  if (!freelancerElement) return;

  const avatar = freelancerElement.querySelector('.freelancer-avatar');
  const content = freelancerElement.querySelector('.freelancer-content');

  let isHovering = false;

  freelancerElement.addEventListener('mouseenter', () => {
   isHovering = true;

   // Magnetic hover timeline
   const hoverTL = gsap.timeline();

   hoverTL
    .to(freelancerElement, {
     scale: 1.03,
     y: -8,
     rotationY: 3,
     rotationX: 2,
     boxShadow: "0 20px 40px rgba(0,0,0,0.12)",
     duration: 0.6,
     ease: premiumEases.magnetic
    })
    .to(avatar, {
     scale: 1.1,
     rotation: 5,
     duration: 0.8,
     ease: premiumEases.bounce
    }, 0);

  });

  freelancerElement.addEventListener('mouseleave', () => {
   isHovering = false;

   gsap.to(freelancerElement, {
    scale: 1,
    y: 0,
    rotationY: 0,
    rotationX: 0,
    boxShadow: "0 10px 25px rgba(0,0,0,0.05)",
    duration: 0.8,
    ease: premiumEases.elastic
   });

   gsap.to(avatar, {
    scale: 1,
    rotation: 0,
    duration: 0.6,
    ease: premiumEases.bounce
   });
  });

  // Mouse tracking for magnetic effect
  freelancerElement.addEventListener('mousemove', (e) => {
   if (!isHovering) return;

   const rect = freelancerElement.getBoundingClientRect();
   const centerX = rect.left + rect.width / 2;
   const centerY = rect.top + rect.height / 2;
   const mouseX = e.clientX - centerX;
   const mouseY = e.clientY - centerY;

   gsap.to(freelancerElement, {
    x: mouseX * 0.08,
    y: mouseY * 0.08,
    duration: 0.3,
    ease: "power2.out"
   });

   gsap.to(avatar, {
    x: mouseX * 0.12,
    y: mouseY * 0.12,
    duration: 0.2,
    ease: "power2.out"
   });
  });
 };

 // Advanced Freelancer Grid Animation
 const createAdvancedFreelancerGrid = () => {
  if (!freelancersRef.current) return;

  const freelancerElements = Array.from(freelancersRef.current.children);

  freelancerElements.forEach((element, index) => {
   // Create magnetic hover effect
   createMagneticFreelancerHover(element, index);

   // Initial animation
   gsap.fromTo(element, {
    opacity: 0,
    y: 100,
    scale: 0.8,
    rotationY: 45
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    rotationY: 0,
    duration: 1.2,
    delay: index * 0.2,
    ease: premiumEases.elastic,
    scrollTrigger: {
     trigger: element,
     start: 'top 90%',
     toggleActions: 'play none none reverse'
    }
   });

   // Animate freelancer content with physics
   const avatar = element.querySelector('.freelancer-avatar');
   const content = element.querySelector('.freelancer-content');

   if (avatar) {
    gsap.fromTo(avatar, {
     scale: 0,
     rotation: -180
    }, {
     scale: 1,
     rotation: 0,
     duration: 1.0,
     delay: index * 0.2 + 0.3,
     ease: premiumEases.bounce,
     scrollTrigger: {
      trigger: element,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }

   if (content) {
    const contentChildren = Array.from(content.children);
    gsap.fromTo(contentChildren, {
     opacity: 0,
     x: -30
    }, {
     opacity: 1,
     x: 0,
     duration: 0.8,
     stagger: 0.1,
     delay: index * 0.2 + 0.6,
     ease: premiumEases.wave,
     scrollTrigger: {
      trigger: element,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }
  });
 };

 useEffect(() => {
  const ctx = gsap.context(() => {
   // Initialize all advanced animations
   createAdvancedTalentParticles();
   createMorphingTalentShapes();
   createAdvancedTitleAnimation();
   createAdvancedFreelancerGrid();
  }, sectionRef);

  return () => ctx.revert();
 // eslint-disable-next-line react-hooks/exhaustive-deps
 }, []);

 const toggleLikeFreelancer = (freelancerId) => {
  setLikedFreelancers(prev => {
   const newSet = new Set(prev);
   if (newSet.has(freelancerId)) {
    newSet.delete(freelancerId);
   } else {
    newSet.add(freelancerId);
   }
   return newSet;
  });
 };

 return (
  <>
   {/* Enhanced CSS */}
   <style>{`
    @keyframes liquid-talent {
     0%, 100% {
      border-radius: 50% 60% 40% 70% / 60% 40% 70% 50%;
     }
     25% {
      border-radius: 40% 50% 70% 60% / 50% 70% 40% 60%;
     }
     50% {
      border-radius: 70% 40% 50% 60% / 40% 60% 50% 70%;
     }
     75% {
      border-radius: 60% 70% 40% 50% / 70% 50% 60% 40%;
     }
    }
    
    .liquid-talent {
     animation: liquid-talent 18s ease-in-out infinite;
    }
    
    .freelancer-card {
     transform-style: preserve-3d;
     transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .talent-particles {
     will-change: transform;
    }
    
    .freelancer-card:hover {
     transform: perspective(1000px) rotateX(4deg) rotateY(8deg) translateZ(50px);
    }
   `}</style>

   <section
    ref={sectionRef}
    className='relative py-20 bg-white transition-colors duration-300 overflow-hidden'
   >
    {/* Advanced Multi-layer Background */}
    <div className='absolute inset-0'>
     <div 
      ref={el => morphingShapesRef.current[0] = el}
      className='absolute top-24 left-20 w-56 h-56 bg-gradient-to-br from-blue-400/10 to-purple-400/10 liquid-talent'
     />
     <div 
      ref={el => morphingShapesRef.current[1] = el}
      className='absolute top-48 right-24 w-48 h-48 bg-gradient-to-br from-green-400/10 to-cyan-400/10 liquid-talent'
     />
     <div 
      ref={el => morphingShapesRef.current[2] = el}
      className='absolute bottom-28 left-1/4 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-pink-400/10 liquid-talent'
     />
     <div 
      ref={el => morphingShapesRef.current[3] = el}
      className='absolute bottom-24 right-20 w-52 h-52 bg-gradient-to-br from-orange-400/10 to-red-400/10 liquid-talent'
     />
     <div 
      ref={el => morphingShapesRef.current[4] = el}
      className='absolute top-1/3 left-1/2 w-40 h-40 bg-gradient-to-br from-indigo-400/10 to-blue-400/10 liquid-talent'
     />
     <div 
      ref={el => morphingShapesRef.current[5] = el}
      className='absolute top-24 left-1/3 w-44 h-44 bg-gradient-to-br from-yellow-400/10 to-orange-400/10 liquid-talent'
     />
    </div>

    {/* Advanced Talent Particle Canvas */}
    <div 
     ref={particleCanvasRef}
     className='absolute inset-0 pointer-events-none overflow-hidden talent-particles'
    />

    <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
     {/* Enhanced Section Header */}
     <div ref={titleRef} className='text-center mb-16'>
      <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 mb-8 transition-colors duration-300'>
       {t('topFreelancers')}
      </h2>
      <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
       {t('workWithBestTalent')}
      </p>
     </div>

     {/* Enhanced Freelancers Grid */}
     <div
      ref={freelancersRef}
      className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 lg:gap-12'
     >
      {topFreelancers.map((freelancer, index) => (
       <div
        key={freelancer.id}
        className='group freelancer-card bg-white rounded-3xl p-8 border border-gray-100 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 transform-gpu'
       >
        {/* Enhanced Header */}
        <div className='flex items-start justify-between mb-6'>
         <div className='flex items-center'>
          <div className='freelancer-avatar relative'>
           <img
            src={freelancer.avatar}
            alt={freelancer.name}
            className='w-20 h-20 rounded-2xl object-cover border-4 border-gray-100 shadow-lg'
           />
           {freelancer.verified && (
            <CheckBadgeIcon className='absolute -bottom-2 -right-2 h-8 w-8 text-blue-600 bg-white rounded-full shadow-lg' />
           )}
          </div>
          <div className='ml-5'>
           <h3 className='text-2xl font-bold text-gray-900 transition-colors duration-300'>
            {freelancer.name}
           </h3>
           <p className='text-lg text-gray-600 transition-colors duration-300'>
            {freelancer.title}
           </p>
          </div>
         </div>
         <button
          onClick={() => toggleLikeFreelancer(freelancer.id)}
          className='p-3 rounded-2xl hover:bg-gray-50 transition-colors duration-200'
         >
          {likedFreelancers.has(freelancer.id) ? (
           <HeartSolidIcon className='h-6 w-6 text-red-500' />
          ) : (
           <HeartIcon className='h-6 w-6 text-gray-400' />
          )}
         </button>
        </div>

        {/* Enhanced Badges */}
        <div className='freelancer-badges flex flex-wrap gap-3 mb-6'>
         {freelancer.topRated && (
          <span className='px-3 py-1 text-sm font-bold text-yellow-600 bg-yellow-50 rounded-full'>
           {t('topRated')}
          </span>
         )}
         <span className='px-3 py-1 text-sm font-bold text-green-600 bg-green-50 rounded-full'>
          {t('available')}
         </span>
        </div>

        {/* Enhanced Rating and Stats */}
        <div className='freelancer-content space-y-4 mb-6'>
         <div className='flex items-center justify-between'>
          <div className='flex items-center'>
           <StarIcon className='h-5 w-5 text-yellow-500 mr-2' />
           <span className='text-lg font-bold text-gray-900'>
            {freelancer.rating}
           </span>
           <span className='text-gray-600 ml-2'>
            ({freelancer.reviews} {t('reviews')})
           </span>
          </div>
          <div className='text-lg font-bold text-gray-900'>
           ${freelancer.hourlyRate}/{t('hour')}
          </div>
         </div>

         <div className='flex items-center text-gray-600'>
          <MapPinIcon className='h-5 w-5 mr-2' />
          <span className='font-medium'>{freelancer.location}</span>
         </div>

         {/* Enhanced Description */}
         <p className='text-gray-600 leading-relaxed transition-colors duration-300'>
          {freelancer.description}
         </p>

         {/* Enhanced Skills */}
         <div className='flex flex-wrap gap-2'>
          {freelancer.skills.slice(0, 3).map((skill) => (
           <span
            key={skill}
            className='px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-full'
           >
            {skill}
           </span>
          ))}
          {freelancer.skills.length > 3 && (
           <span className='px-3 py-1 text-sm font-medium text-gray-600 bg-gray-50 rounded-full'>
            +{freelancer.skills.length - 3}
           </span>
          )}
         </div>
        </div>

        {/* Enhanced Stats */}
        <div className='freelancer-stats grid grid-cols-2 gap-6 mb-6 text-center'>
         <div>
          <div className='text-2xl font-bold text-gray-900'>
           {freelancer.completedProjects}
          </div>
          <div className='text-sm text-gray-600'>
           {t('completedProjects')}
          </div>
         </div>
         <div>
          <div className='text-2xl font-bold text-gray-900'>
           {freelancer.responseTime}
          </div>
          <div className='text-sm text-gray-600'>
           {t('responseTime')}
          </div>
         </div>
        </div>

        {/* Enhanced Action Buttons */}
        <div className='freelancer-actions flex gap-3'>
         <button className='flex-1 px-6 py-3 text-lg font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform group-hover:scale-105'>
          {t('hire')}
         </button>
         <button className='px-4 py-3 text-lg font-bold text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-2xl transition-colors duration-200 flex items-center'>
          <ChatBubbleLeftIcon className='h-5 w-5 mr-2' />
          {t('message')}
         </button>
         <button className='px-4 py-3 text-lg font-bold text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-2xl transition-colors duration-200'>
          <EyeIcon className='h-5 w-5' />
         </button>
        </div>
       </div>
      ))}
     </div>

     {/* Enhanced View All Button */}
     <div className='text-center mt-16'>
      <button className='group px-10 py-5 text-xl font-bold text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105'>
       <span className='mr-3'>{t('viewAllFreelancers')}</span>
       <span className='inline-block transition-transform duration-300 group-hover:translate-x-2'>→</span>
      </button>
     </div>
    </div>
   </section>
  </>
 );
};

export default AppleFreelancersShowcase;
