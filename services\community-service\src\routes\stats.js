/**
 * Community Stats API Routes
 * PostgreSQL implementation for community statistics
 */

const express = require('express');
const logger = require('../utils/logger');

const router = express.Router();

// Get PostgreSQL database connection
let db = null;

function getDatabase() {
  if (!db) {
    try {
      const { postgresqlConfig } = require('../config/postgresql');
      db = postgresqlConfig;
    } catch (error) {
      logger.error('Failed to get PostgreSQL in stats routes:', error);
      return null;
    }
  }
  return db;
}

/**
 * Get community statistics
 * GET /api/stats
 */
router.get('/', async (req, res) => {
  try {
    const db = getDatabase();
    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    logger.info('Fetching community statistics');

    // Get all statistics in parallel for better performance
    const [
      totalPosts,
      totalComments,
      totalUsers,
      totalLikes,
      recentActivity,
      topCategories,
      topUsers
    ] = await Promise.all([
      // Total posts
      db.query('SELECT COUNT(*) as count FROM posts WHERE status = $1', ['published']),
      
      // Total comments
      db.query('SELECT COUNT(*) as count FROM comments WHERE is_deleted = FALSE'),
      
      // Total users
      db.query('SELECT COUNT(*) as count FROM users WHERE is_active = TRUE'),
      
      // Total likes
      db.query('SELECT COUNT(*) as count FROM likes'),
      
      // Recent activity (posts and comments from last 7 days)
      db.query(`
        SELECT 
          'post' as type,
          p.id,
          p.title,
          p.created_at,
          u.first_name,
          u.last_name,
          u.avatar_url
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        WHERE p.status = 'published' 
          AND p.created_at >= NOW() - INTERVAL '7 days'
        UNION ALL
        SELECT 
          'comment' as type,
          c.id,
          c.content as title,
          c.created_at,
          u.first_name,
          u.last_name,
          u.avatar_url
        FROM comments c
        LEFT JOIN users u ON c.author_id = u.id
        WHERE c.is_deleted = FALSE 
          AND c.created_at >= NOW() - INTERVAL '7 days'
        ORDER BY created_at DESC
        LIMIT 10
      `),
      
      // Top categories
      db.query(`
        SELECT 
          category,
          COUNT(*) as post_count
        FROM posts 
        WHERE status = 'published'
        GROUP BY category 
        ORDER BY post_count DESC 
        LIMIT 5
      `),
      
      // Top users by post count
      db.query(`
        SELECT 
          u.id,
          u.first_name,
          u.last_name,
          u.avatar_url,
          COUNT(p.id) as post_count,
          SUM(p.upvotes) as total_upvotes
        FROM users u
        LEFT JOIN posts p ON u.id = p.author_id AND p.status = 'published'
        WHERE u.is_active = TRUE
        GROUP BY u.id, u.first_name, u.last_name, u.avatar_url
        ORDER BY post_count DESC, total_upvotes DESC
        LIMIT 5
      `)
    ]);

    // Calculate engagement metrics
    const engagementQuery = await db.query(`
      SELECT 
        AVG(p.upvotes) as avg_upvotes_per_post,
        AVG(p.view_count) as avg_views_per_post,
        AVG(p.comment_count) as avg_comments_per_post
      FROM posts p
      WHERE p.status = 'published'
    `);

    const stats = {
      overview: {
        totalPosts: parseInt(totalPosts.rows[0]?.count || 0),
        totalComments: parseInt(totalComments.rows[0]?.count || 0),
        totalUsers: parseInt(totalUsers.rows[0]?.count || 0),
        totalLikes: parseInt(totalLikes.rows[0]?.count || 0)
      },
      engagement: {
        avgUpvotesPerPost: parseFloat(engagementQuery.rows[0]?.avg_upvotes_per_post || 0).toFixed(1),
        avgViewsPerPost: parseFloat(engagementQuery.rows[0]?.avg_views_per_post || 0).toFixed(1),
        avgCommentsPerPost: parseFloat(engagementQuery.rows[0]?.avg_comments_per_post || 0).toFixed(1)
      },
      recentActivity: recentActivity.rows.map(item => ({
        type: item.type,
        id: item.id,
        title: item.title,
        createdAt: item.created_at,
        author: {
          firstName: item.first_name,
          lastName: item.last_name,
          avatarUrl: item.avatar_url
        }
      })),
      topCategories: topCategories.rows.map(cat => ({
        category: cat.category,
        postCount: parseInt(cat.post_count)
      })),
      topUsers: topUsers.rows.map(user => ({
        id: user.id,
        firstName: user.first_name,
        lastName: user.last_name,
        avatarUrl: user.avatar_url,
        postCount: parseInt(user.post_count),
        totalUpvotes: parseInt(user.total_upvotes || 0)
      })),
      timestamp: new Date().toISOString()
    };

    logger.info('Community statistics fetched successfully', {
      totalPosts: stats.overview.totalPosts,
      totalComments: stats.overview.totalComments,
      totalUsers: stats.overview.totalUsers
    });

    res.json(stats);

  } catch (error) {
    logger.error('Error fetching community statistics', { error: error.message });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch community statistics'
    });
  }
});

/**
 * Get user statistics
 * GET /api/stats/user/:userId
 */
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    const db = getDatabase();
    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    logger.info('Fetching user statistics', { userId });

    // Get user stats
    const userStatsQuery = await db.query(`
      SELECT 
        u.id,
        u.first_name,
        u.last_name,
        u.avatar_url,
        u.created_at as joined_at,
        COUNT(DISTINCT p.id) as total_posts,
        COUNT(DISTINCT c.id) as total_comments,
        COUNT(DISTINCT l.id) as total_likes_given,
        SUM(p.upvotes) as total_upvotes_received,
        SUM(p.view_count) as total_views_received,
        AVG(p.upvotes) as avg_upvotes_per_post
      FROM users u
      LEFT JOIN posts p ON u.id = p.author_id AND p.status = 'published'
      LEFT JOIN comments c ON u.id = c.author_id AND c.is_deleted = FALSE
      LEFT JOIN likes l ON u.id = l.user_id
      WHERE u.id = $1
      GROUP BY u.id, u.first_name, u.last_name, u.avatar_url, u.created_at
    `, [userId]);

    if (userStatsQuery.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = userStatsQuery.rows[0];

    // Get recent posts
    const recentPostsQuery = await db.query(`
      SELECT 
        id, title, category, upvotes, view_count, comment_count, created_at
      FROM posts 
      WHERE author_id = $1 AND status = 'published'
      ORDER BY created_at DESC 
      LIMIT 5
    `, [userId]);

    // Get recent comments
    const recentCommentsQuery = await db.query(`
      SELECT 
        c.id, c.content, c.upvotes, c.created_at, p.title as post_title, p.id as post_id
      FROM comments c
      LEFT JOIN posts p ON c.post_id = p.id
      WHERE c.author_id = $1 AND c.is_deleted = FALSE
      ORDER BY c.created_at DESC 
      LIMIT 5
    `, [userId]);

    const userStats = {
      user: {
        id: user.id,
        firstName: user.first_name,
        lastName: user.last_name,
        avatarUrl: user.avatar_url,
        joinedAt: user.joined_at
      },
      stats: {
        totalPosts: parseInt(user.total_posts || 0),
        totalComments: parseInt(user.total_comments || 0),
        totalLikesGiven: parseInt(user.total_likes_given || 0),
        totalUpvotesReceived: parseInt(user.total_upvotes_received || 0),
        totalViewsReceived: parseInt(user.total_views_received || 0),
        avgUpvotesPerPost: parseFloat(user.avg_upvotes_per_post || 0).toFixed(1)
      },
      recentPosts: recentPostsQuery.rows.map(post => ({
        id: post.id,
        title: post.title,
        category: post.category,
        upvotes: parseInt(post.upvotes || 0),
        viewCount: parseInt(post.view_count || 0),
        commentCount: parseInt(post.comment_count || 0),
        createdAt: post.created_at
      })),
      recentComments: recentCommentsQuery.rows.map(comment => ({
        id: comment.id,
        content: comment.content,
        upvotes: parseInt(comment.upvotes || 0),
        createdAt: comment.created_at,
        post: {
          id: comment.post_id,
          title: comment.post_title
        }
      })),
      timestamp: new Date().toISOString()
    };

    logger.info('User statistics fetched successfully', { userId });

    res.json(userStats);

  } catch (error) {
    logger.error('Error fetching user statistics', { error: error.message, userId: req.params.userId });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch user statistics'
    });
  }
});

module.exports = router; 