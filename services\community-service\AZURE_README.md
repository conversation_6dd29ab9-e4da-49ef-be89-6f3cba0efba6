# 🚀 Azure Deployment - Community Service

Hướng dẫn nhanh để deploy Community Service lên Azure App Service.

## 📋 Yêu cầu

- Azure CLI đã cài đặt
- Đã đăng nhập Azure (`az login`)
- Firebase project credentials

## 🚀 Deploy nhanh

### Bước 1: Deploy hoàn chỉnh (K<PERSON>y<PERSON>n <PERSON>h<PERSON>)
```bash
cd services/community-service
chmod +x deploy-azure-complete.sh
./deploy-azure-complete.sh vwork-community-service vwork-rg
```

### Bước 2: Deploy từng bước
```bash
# 1. Deploy application
chmod +x deploy-to-azure.sh
./deploy-to-azure.sh vwork-community-service vwork-rg

# 2. Setup database
chmod +x setup-azure-db.sh
./setup-azure-db.sh vwork-community-service vwork-rg

# 3. Run migrations
chmod +x run-azure-migrations.sh
./run-azure-migrations.sh vwork-community-service vwork-rg
```

## ⚙️ C<PERSON>u hình Environment Variables

Sau khi deploy, cấu hình các biến môi trường trong Azure Portal:

1. Vào App Service → Configuration → Application settings
2. Thêm các biến sau:

```bash
# Firebase (Bắt buộc)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key\n-----END PRIVATE KEY-----\n"

# CORS (Bắt buộc)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Tùy chọn
LOG_LEVEL=info
LOG_FORMAT=json
```

## 🔄 Chạy Database Migrations

```bash
# Kết nối SSH vào app
az webapp ssh --name vwork-community-service --resource-group vwork-rg

# Trong SSH session
npm run db:setup
exit
```

## 🧪 Test Deployment

```bash
# Test health endpoint
curl https://vwork-community-service.azurewebsites.net/health

# Test API
curl https://vwork-community-service.azurewebsites.net/api
```

## 📊 Monitoring

```bash
# Xem logs real-time
az webapp log tail --name vwork-community-service --resource-group vwork-rg

# Restart app
az webapp restart --name vwork-community-service --resource-group vwork-rg
```

## 🔧 Troubleshooting

### App không start
```bash
# Xem logs
az webapp log tail --name vwork-community-service --resource-group vwork-rg

# Kiểm tra settings
az webapp config appsettings list --name vwork-community-service --resource-group vwork-rg
```

### Database connection failed
- Kiểm tra DATABASE_URL trong Application settings
- Đảm bảo firewall rules đã được cấu hình
- Test connection qua SSH

### Environment variables không hoạt động
- Restart app sau khi thay đổi settings
- Kiểm tra format của FIREBASE_PRIVATE_KEY

## 📖 Tài liệu chi tiết

Xem `AZURE_DEPLOYMENT_GUIDE.md` để biết hướng dẫn chi tiết.

## 🎯 Kết quả

Sau khi hoàn thành, bạn sẽ có:
- ✅ App Service chạy Community Service
- ✅ PostgreSQL database
- ✅ Auto-scaling
- ✅ SSL certificate
- ✅ Monitoring và logging 