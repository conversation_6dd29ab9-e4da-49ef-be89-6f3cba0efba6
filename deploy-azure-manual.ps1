# Azure Manual Deployment Script for VWork
# Usage: .\deploy-azure-manual.ps1

param(
    [string]$ServiceName = "community-service",
    [string]$ResourceGroup = "vwork-rg",
    [string]$Region = "southeastasia"
)

Write-Host "🚀 Azure Manual Deployment for VWork" -ForegroundColor Blue
Write-Host "=====================================" -ForegroundColor Blue

# Check Azure CLI
$azureCliPath = "C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin\az.cmd"
if (-not (Test-Path $azureCliPath)) {
    Write-Host "❌ Azure CLI not found at: $azureCliPath" -ForegroundColor Red
    Write-Host "Please install Azure CLI first" -ForegroundColor Yellow
    exit 1
}

# Check if logged in
$account = & $azureCliPath account show 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  Not logged in to Azure. Please login first." -ForegroundColor Yellow
    & $azureCliPath login
}

Write-Host "✅ Azure CLI is ready" -ForegroundColor Green

# Create Resource Group
Write-Host "📦 Creating Resource Group: $ResourceGroup" -ForegroundColor Blue
& $azureCliPath group create --name $ResourceGroup --location $Region --output none
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Resource Group created successfully" -ForegroundColor Green
} else {
    Write-Host "⚠️  Resource Group might already exist" -ForegroundColor Yellow
}

# Create App Service Plan
$planName = "vwork-plan"
Write-Host "📋 Creating App Service Plan: $planName" -ForegroundColor Blue
& $azureCliPath appservice plan create --name $planName --resource-group $ResourceGroup --sku F1 --is-linux --output none
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ App Service Plan created successfully" -ForegroundColor Green
} else {
    Write-Host "⚠️  App Service Plan might already exist" -ForegroundColor Yellow
}

# Create Web App
$appName = "vwork-$ServiceName"
Write-Host "🌐 Creating Web App: $appName" -ForegroundColor Blue
& $azureCliPath webapp create --name $appName --resource-group $ResourceGroup --plan $planName --runtime "NODE:20-lts" --output none
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Web App created successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to create Web App" -ForegroundColor Red
    exit 1
}

# Configure basic settings
Write-Host "⚙️  Configuring basic settings" -ForegroundColor Blue
& $azureCliPath webapp config set --name $appName --resource-group $ResourceGroup --linux-fx-version "NODE:20-lts" --startup-file "npm start" --output none

# Enable logging
& $azureCliPath webapp log config --name $appName --resource-group $ResourceGroup --web-server-logging filesystem --output none

# Set basic environment variables
Write-Host "🔧 Setting basic environment variables" -ForegroundColor Blue
& $azureCliPath webapp config appsettings set --name $appName --resource-group $ResourceGroup --settings NODE_ENV=production PORT=8080 WEBSITES_PORT=8080 WEBSITES_CONTAINER_START_TIME_LIMIT=1800 WEBSITES_ENABLE_APP_SERVICE_STORAGE=true --output none

# Get the app URL
$appUrl = & $azureCliPath webapp show --name $appName --resource-group $ResourceGroup --query "defaultHostName" --output tsv

Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 Your app is available at: https://$appUrl" -ForegroundColor Blue

# Show important information
Write-Host "📋 Important Information:" -ForegroundColor Blue
Write-Host "   App Name: $appName" -ForegroundColor White
Write-Host "   Resource Group: $ResourceGroup" -ForegroundColor White
Write-Host "   App URL: https://$appUrl" -ForegroundColor White

# Show required environment variables
Write-Host "⚠️  REQUIRED: Set these environment variables in Azure Portal:" -ForegroundColor Yellow
Write-Host "   Configuration → Application settings → New application setting" -ForegroundColor Yellow
Write-Host ""
Write-Host "   FIREBASE_PROJECT_ID=your-firebase-project-id" -ForegroundColor Yellow
Write-Host "   FIREBASE_CLIENT_EMAIL=<EMAIL>" -ForegroundColor Yellow
Write-Host "   FIREBASE_PRIVATE_KEY=`"-----BEGIN PRIVATE KEY-----\nYour private key\n-----END PRIVATE KEY-----\n`"" -ForegroundColor Yellow
Write-Host "   ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com" -ForegroundColor Yellow

# Show next steps
Write-Host "📋 Next Steps:" -ForegroundColor Blue
Write-Host "1. Configure environment variables in Azure Portal" -ForegroundColor White
Write-Host "2. Deploy your code (see deploy-manual-guide.md)" -ForegroundColor White
Write-Host "3. Test the health endpoint: https://$appUrl/health" -ForegroundColor White
Write-Host "4. Run database migrations via SSH" -ForegroundColor White

# Show useful commands
Write-Host "🔧 Useful Commands:" -ForegroundColor Blue
Write-Host "   View logs: & `"$azureCliPath`" webapp log tail --name $appName --resource-group $ResourceGroup" -ForegroundColor White
Write-Host "   SSH into app: & `"$azureCliPath`" webapp ssh --name $appName --resource-group $ResourceGroup" -ForegroundColor White
Write-Host "   Restart app: & `"$azureCliPath`" webapp restart --name $appName --resource-group $ResourceGroup" -ForegroundColor White
Write-Host "   View settings: & `"$azureCliPath`" webapp config appsettings list --name $appName --resource-group $ResourceGroup" -ForegroundColor White

# Show current status
Write-Host "📊 Current App Status:" -ForegroundColor Blue
& $azureCliPath webapp show --name $appName --resource-group $ResourceGroup --query "name,state,defaultHostName,resourceGroup" --output table

Write-Host "🎉 Deployment script completed!" -ForegroundColor Green
Write-Host "📖 For detailed instructions, see: deploy-manual-guide.md" -ForegroundColor Blue 