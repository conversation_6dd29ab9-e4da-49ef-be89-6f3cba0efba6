/**
 * Insert Mock Data to PostgreSQL - Direct Configuration
 */

const { Pool } = require('pg');

// Database configuration - Direct
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'vwork_team_service',
  user: 'vwork_admin',
  password: 'VWork2024!',
  ssl: false
});

// Mock data
const mockTeams = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    name: "TechDream Team",
    slogan: "Biến ý tưởng thành hiện thực",
    description: "Đội ngũ chuyên gia phát triển ứng dụng di động và web với kinh nghiệm 5+ năm. Chúng tôi chuyên về React Native, Node.js, và UI/UX Design.",
    logo_url: "https://via.placeholder.com/150/3B82F6/FFFFFF?text=TD",
    leader_id: "user_leader_1",
    member_count: 3,
    status: "active",
    category: "development",
    rating: 4.9,
    total_projects: 8,
    total_earnings: 25000
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    name: "Creative Studio",
    slogan: "Sáng tạo không giới hạn",
    description: "Studio thiết kế đồ họa và branding chuyên nghiệp. Chúng tôi tạo ra những thiết kế độc đáo và ấn tượng.",
    logo_url: "https://via.placeholder.com/150/10B981/FFFFFF?text=CS",
    leader_id: "user_leader_2",
    member_count: 3,
    status: "active",
    category: "design",
    rating: 4.8,
    total_projects: 12,
    total_earnings: 18500
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    name: "Digital Marketing Pro",
    slogan: "Tăng trưởng doanh thu cùng chúng tôi",
    description: "Đội ngũ marketing chuyên nghiệp với kinh nghiệm SEO, SEM, Social Media Marketing và Content Marketing.",
    logo_url: "https://via.placeholder.com/150/F59E0B/FFFFFF?text=DM",
    leader_id: "user_leader_3",
    member_count: 4,
    status: "active",
    category: "marketing",
    rating: 4.7,
    total_projects: 15,
    total_earnings: 32000
  }
];

const mockTeamMembers = [
  // TechDream Team members
  {
    team_id: '550e8400-e29b-41d4-a716-446655440001',
    user_id: "user_leader_1",
    role: "leader",
    position: "Full-stack Developer",
    profit_share: 40,
    status: "active"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440001',
    user_id: "user_member_1", 
    role: "member",
    position: "Backend Developer",
    profit_share: 30,
    status: "active"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440001',
    user_id: "user_member_2",
    role: "member", 
    position: "UI/UX Designer",
    profit_share: 30,
    status: "active"
  },
  
  // Creative Studio members
  {
    team_id: '550e8400-e29b-41d4-a716-446655440002',
    user_id: "user_leader_2",
    role: "leader",
    position: "Graphic Designer",
    profit_share: 35,
    status: "active"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440002',
    user_id: "user_member_3",
    role: "member",
    position: "Brand Strategist", 
    profit_share: 35,
    status: "active"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440002',
    user_id: "user_member_4",
    role: "member",
    position: "Illustrator",
    profit_share: 30,
    status: "active"
  },
  
  // Digital Marketing Pro members
  {
    team_id: '550e8400-e29b-41d4-a716-446655440003',
    user_id: "user_leader_3",
    role: "leader",
    position: "Marketing Manager",
    profit_share: 30,
    status: "active"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440003',
    user_id: "user_member_5",
    role: "member",
    position: "SEO Specialist",
    profit_share: 25,
    status: "active"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440003',
    user_id: "user_member_6",
    role: "member",
    position: "Content Creator",
    profit_share: 25,
    status: "active"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440003',
    user_id: "user_member_7",
    role: "member",
    position: "Social Media Manager",
    profit_share: 20,
    status: "active"
  }
];

const mockChatMessages = [
  {
    team_id: '550e8400-e29b-41d4-a716-446655440001',
    sender_id: "user_leader_1",
    message: "Chào team! Hôm nay chúng ta sẽ bắt đầu dự án mới.",
    message_type: "text"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440001',
    sender_id: "user_member_1",
    message: "Tôi đã chuẩn bị sẵn sàng cho backend development.",
    message_type: "text"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440001',
    sender_id: "user_member_2",
    message: "UI/UX design đã hoàn thành 80%, sẽ xong trong 2 ngày nữa.",
    message_type: "text"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440002',
    sender_id: "user_leader_2",
    message: "Creative Studio team meeting vào 2h chiều hôm nay nhé!",
    message_type: "text"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440002',
    sender_id: "user_member_3",
    message: "Tôi sẽ chuẩn bị presentation cho brand strategy.",
    message_type: "text"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440003',
    sender_id: "user_leader_3",
    message: "Marketing campaign đang chạy tốt, KPI đạt 120%!",
    message_type: "text"
  }
];

const mockInvitations = [
  {
    team_id: '550e8400-e29b-41d4-a716-446655440001',
    inviter_id: "user_leader_1",
    invitee_id: "user_invitee_1",
    message: "Bạn được mời tham gia TechDream Team",
    status: "pending"
  },
  {
    team_id: '550e8400-e29b-41d4-a716-446655440002',
    inviter_id: "user_leader_2", 
    invitee_id: "user_invitee_2",
    message: "Bạn được mời tham gia Creative Studio",
    status: "pending"
  }
];

// Check database connection
async function checkDatabase() {
  try {
    console.log('🔍 Checking database connection...');
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    console.log('✅ Database connected successfully');
    console.log(`📅 Current time: ${result.rows[0].now}`);
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Check if tables exist
async function checkTables() {
  try {
    console.log('\n📋 Checking database tables...');
    const client = await pool.connect();
    
    const tables = ['teams', 'team_members', 'team_chat_messages', 'team_invitations'];
    const tableStatus = {};
    
    for (const table of tables) {
      try {
        const result = await client.query(`SELECT COUNT(*) FROM ${table}`);
        tableStatus[table] = { exists: true, count: parseInt(result.rows[0].count) };
        console.log(`✅ Table ${table}: ${result.rows[0].count} records`);
      } catch (error) {
        tableStatus[table] = { exists: false, count: 0 };
        console.log(`❌ Table ${table}: Does not exist`);
      }
    }
    
    client.release();
    return tableStatus;
  } catch (error) {
    console.error('❌ Error checking tables:', error.message);
    return {};
  }
}

// Insert mock data
async function insertMockData() {
  try {
    console.log('\n🚀 Inserting mock data...');
    const client = await pool.connect();
    
    // Insert teams
    console.log('📝 Inserting teams...');
    for (const team of mockTeams) {
      await client.query(`
        INSERT INTO teams (id, name, slogan, description, logo_url, leader_id, member_count, status, category, rating, total_projects, total_earnings)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        ON CONFLICT (id) DO UPDATE SET
          name = EXCLUDED.name,
          slogan = EXCLUDED.slogan,
          description = EXCLUDED.description,
          logo_url = EXCLUDED.logo_url,
          leader_id = EXCLUDED.leader_id,
          member_count = EXCLUDED.member_count,
          status = EXCLUDED.status,
          category = EXCLUDED.category,
          rating = EXCLUDED.rating,
          total_projects = EXCLUDED.total_projects,
          total_earnings = EXCLUDED.total_earnings
      `, [team.id, team.name, team.slogan, team.description, team.logo_url, team.leader_id, team.member_count, team.status, team.category, team.rating, team.total_projects, team.total_earnings]);
    }
    console.log(`✅ Inserted ${mockTeams.length} teams`);
    
    // Insert team members
    console.log('👥 Inserting team members...');
    for (const member of mockTeamMembers) {
      await client.query(`
        INSERT INTO team_members (team_id, user_id, role, position, profit_share, status)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (team_id, user_id) DO UPDATE SET
          role = EXCLUDED.role,
          position = EXCLUDED.position,
          profit_share = EXCLUDED.profit_share,
          status = EXCLUDED.status
      `, [member.team_id, member.user_id, member.role, member.position, member.profit_share, member.status]);
    }
    console.log(`✅ Inserted ${mockTeamMembers.length} team members`);
    
    // Insert chat messages
    console.log('💬 Inserting chat messages...');
    for (const message of mockChatMessages) {
      await client.query(`
        INSERT INTO team_chat_messages (team_id, sender_id, message, message_type)
        VALUES ($1, $2, $3, $4)
      `, [message.team_id, message.sender_id, message.message, message.message_type]);
    }
    console.log(`✅ Inserted ${mockChatMessages.length} chat messages`);
    
    // Insert invitations
    console.log('📨 Inserting invitations...');
    for (const invitation of mockInvitations) {
      await client.query(`
        INSERT INTO team_invitations (team_id, inviter_id, invitee_id, message, status)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (team_id, invitee_id) DO UPDATE SET
          inviter_id = EXCLUDED.inviter_id,
          message = EXCLUDED.message,
          status = EXCLUDED.status
      `, [invitation.team_id, invitation.inviter_id, invitation.invitee_id, invitation.message, invitation.status]);
    }
    console.log(`✅ Inserted ${mockInvitations.length} invitations`);
    
    client.release();
    console.log('\n🎉 Mock data insertion completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error inserting mock data:', error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('🚀 Team Service Database Check and Mock Data Insertion');
  console.log('=' .repeat(60));
  
  // Check database connection
  const dbConnected = await checkDatabase();
  if (!dbConnected) {
    console.log('\n💥 Cannot proceed without database connection');
    process.exit(1);
  }
  
  // Check tables
  const tableStatus = await checkTables();
  
  // Insert mock data
  const insertSuccess = await insertMockData();
  
  if (insertSuccess) {
    console.log('\n📊 Final Database Status:');
    await checkTables();
    
    console.log('\n🔍 Team IDs for testing:');
    mockTeams.forEach((team, index) => {
      console.log(`${index + 1}. ${team.name}: ${team.id}`);
    });
    
    console.log('\n✅ All operations completed successfully!');
  } else {
    console.log('\n💥 Mock data insertion failed');
    process.exit(1);
  }
  
  // Close pool
  await pool.end();
}

// Run the script
main().catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
}); 