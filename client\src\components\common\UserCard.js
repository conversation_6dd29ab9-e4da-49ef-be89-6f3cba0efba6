import React from 'react';
import { useNavigate } from 'react-router-dom';
import { MapPinIcon, CheckBadgeIcon } from '@heroicons/react/24/outline';

const UserCard = ({ 
  user, 
  showLocation = true, 
  showTitle = true, 
  size = 'medium',
  className = '',
  onClick,
  disabled = false 
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (disabled) return;
    
    if (onClick) {
      onClick(user);
    } else if (user?.id && user.id !== 'unknown') {
      navigate(`/freelancers/${user.id}`);
    }
  };

  const sizeClasses = {
    small: {
      avatar: 'w-8 h-8',
      name: 'text-sm',
      title: 'text-xs',
      location: 'text-xs'
    },
    medium: {
      avatar: 'w-10 h-10',
      name: 'text-sm',
      title: 'text-xs',
      location: 'text-xs'
    },
    large: {
      avatar: 'w-12 h-12',
      name: 'text-base',
      title: 'text-sm',
      location: 'text-sm'
    }
  };

  const classes = sizeClasses[size];

  return (
    <div 
      className={`flex items-center space-x-3 ${className} ${
        !disabled ? 'cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors' : ''
      }`}
      onClick={handleClick}
      title={!disabled ? "Xem profile" : undefined}
    >
      <div className="relative">
        <img
          src={user?.avatar || user?.avatarUrl || 'https://via.placeholder.com/40x40/6366f1/ffffff?text=U'}
          alt={user?.name || 'User'}
          className={`${classes.avatar} rounded-full object-cover ring-2 ring-gray-100`}
          onError={(e) => {
            e.target.src = 'https://via.placeholder.com/40x40/6366f1/ffffff?text=U';
          }}
        />
        {user?.verified && (
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
            <CheckBadgeIcon className="h-2.5 w-2.5 text-white" />
          </div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <h4 className={`${classes.name} font-semibold text-gray-900 truncate ${
            !disabled ? 'hover:text-blue-600 transition-colors' : ''
          }`}>
            {user?.name || user?.displayName || 'Anonymous User'}
          </h4>
          {user?.username && (
            <span className="text-gray-500 text-xs">
              {user.username}
            </span>
          )}
        </div>
        
        {showTitle && user?.title && (
          <p className={`${classes.title} text-gray-600 truncate`}>
            {user.title}
          </p>
        )}
        
        {showLocation && user?.location && (
          <div className="flex items-center space-x-1">
            <MapPinIcon className="h-3 w-3 text-gray-400" />
            <span className={`${classes.location} text-gray-500 truncate`}>
              {user.location}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserCard; 