/**
 * Fix and Start Team Service
 * This script fixes common issues and starts the service
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Team Service Issues...');

// Step 1: Create .env file if it doesn't exist
const envPath = path.join(__dirname, '.env');
const envExamplePath = path.join(__dirname, 'env.example');

if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file from template...');
  
  if (fs.existsSync(envExamplePath)) {
    const envContent = fs.readFileSync(envExamplePath, 'utf8');
    
    // Update with actual Firebase project ID
    const updatedContent = envContent
      .replace('your-firebase-project-id', 'vwork-786c3')
      .replace('<EMAIL>', '<EMAIL>')
      .replace('"-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"', '"-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\nAG1mGtqoEriY98yjomvWldCA/SgtCu51dv4mQkShsGCzl+eKv6c8XILZQoJGCChE\n0dkKGP3D/6oMfoMZAd/hh1JpS7LkKnHhhDk6AzlCJq5UJuiLuOVpEddT8ZndtHr/\n6gr93EXj5uaOgRffZJHzcOahZtIlte66QkXEMv9JbF3g2ZMnKzM8ow+7cDtWJiy+\nUUtHyztmJDXF7tXt0S3fopXQCv4oJFeOJ1xkzU4bF1x+0GIchGZDJaszRYJpDEf/\n7PLo4H9TWQeKnztOOIx9Yd/3ajzI9tB+9VJHpL7vC/9G3A1U9gq/NaJFtJUwC+DM\n+lyu7mjkJAgMBAAECggEBAKTmjaS6tkK8BlPXClTQ2vpz/N6uxDeS35mXpqasqskV\nlaAidgg/sWqpjXDbXr93otIMLlWsM+X0CqMDgSXKejLS2jx4GDjI1ZTXg++0AMJ8\nsJ74pWzVDOfmCEQ/7wXs3+cbnXhKriO8Z036q92Qc1+N87SI38nkGa0ABH9CN83H\nmQqt4fB7UdHzuIRe/me2PGhIq5ZBzj6h3BpoPGzEP+x3l9YmK8t/1cN0pqI+dQwY\nBgfY4qBxU8e0WaT7lF3EP9L5PbJ1GX3InJ3/ccHct/RdCkRI4V9YVLPB7mHT0mKj\nX1YzJ7L5LmbrdXYhl4jM0sJ0Qel8L4F2ahMmsqGlu8z/e5B\n-----END PRIVATE KEY-----\n"');
    
    fs.writeFileSync(envPath, updatedContent);
    console.log('✅ .env file created successfully');
  } else {
    console.log('⚠️ env.example not found, creating basic .env file...');
    
    const basicEnvContent = `# Team Service Environment Variables

# Service Configuration
NODE_ENV=development
PORT=3008
SERVICE_HOST=localhost

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_team_service
DB_USER=postgres
DB_PASSWORD=postgres

# Firebase Configuration (Mock for development)
FIREBASE_PROJECT_ID=vwork-786c3
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\nAG1mGtqoEriY98yjomvWldCA/SgtCu51dv4mQkShsGCzl+eKv6c8XILZQoJGCChE\n0dkKGP3D/6oMfoMZAd/hh1JpS7LkKnHhhDk6AzlCJq5UJuiLuOVpEddT8ZndtHr/\n6gr93EXj5uaOgRffZJHzcOahZtIlte66QkXEMv9JbF3g2ZMnKzM8ow+7cDtWJiy+\nUUtHyztmJDXF7tXt0S3fopXQCv4oJFeOJ1xkzU4bF1x+0GIchGZDJaszRYJpDEf/\n7PLo4H9TWQeKnztOOIx9Yd/3ajzI9tB+9VJHpL7vC/9G3A1U9gq/NaJFtJUwC+DM\n+lyu7mjkJAgMBAAECggEBAKTmjaS6tkK8BlPXClTQ2vpz/N6uxDeS35mXpqasqskV\nlaAidgg/sWqpjXDbXr93otIMLlWsM+X0CqMDgSXKejLS2jx4GDjI1ZTXg++0AMJ8\nsJ74pWzVDOfmCEQ/7wXs3+cbnXhKriO8Z036q92Qc1+N87SI38nkGa0ABH9CN83H\nmQqt4fB7UdHzuIRe/me2PGhIq5ZBzj6h3BpoPGzEP+x3l9YmK8t/1cN0pqI+dQwY\nBgfY4qBxU8e0WaT7lF3EP9L5PbJ1GX3InJ3/ccHct/RdCkRI4V9YVLPB7mHT0mKj\nX1YzJ7L5LmbrdXYhl4jM0sJ0Qel8L4F2ahMmsqGlu8z/e5B\n-----END PRIVATE KEY-----\n"

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
CORS_CREDENTIALS=true

# Service URLs
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
CHAT_SERVICE_URL=http://localhost:3005

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
ENABLE_COMPRESSION=true
ENABLE_HELMET=true
TRUST_PROXY=true

# Team Service Specific
MIN_FRIENDSHIP_DAYS=5
MIN_COMPLETED_PROJECTS=10
MIN_RATING=4.8
MAX_TEAM_MEMBERS=10
MIN_TEAM_MEMBERS=3`;
    
    fs.writeFileSync(envPath, basicEnvContent);
    console.log('✅ Basic .env file created successfully');
  }
} else {
  console.log('✅ .env file already exists');
}

// Step 2: Check if PostgreSQL is available
console.log('🔍 Checking PostgreSQL availability...');

const { exec } = require('child_process');

exec('psql --version', (error, stdout, stderr) => {
  if (error) {
    console.log('⚠️ PostgreSQL not found. Starting in mock mode...');
    console.log('');
    console.log('📋 To install PostgreSQL:');
    console.log('1. Run: npm run install:postgresql');
    console.log('2. Or download from: https://www.postgresql.org/download/windows/');
    console.log('');
    console.log('🚀 Starting Team Service in mock mode...');
    
    // Start mock service
    require('./test-without-db.js');
  } else {
    console.log('✅ PostgreSQL found:', stdout.trim());
    console.log('🚀 Starting Team Service with database...');
    
    // Start full service
    require('./app.js');
  }
});

console.log('');
console.log('🔧 Fix completed!');
console.log('📋 Available commands:');
console.log('  npm start          - Start with database (requires PostgreSQL)');
console.log('  npm run test:mock  - Start in mock mode (no database needed)');
console.log('  npm run db:setup   - Setup database');
console.log('  npm run install:postgresql - Install PostgreSQL'); 