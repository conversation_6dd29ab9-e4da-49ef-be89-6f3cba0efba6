@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=MedievalSharp&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;



/* Import Optimized Animation Styles */
@import './styles/optimizedAnimations.css';

/* Import Auth Page Fixes */
@import './styles/auth-page-fixes.css';

@layer base {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
  }



  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    letter-spacing: -0.025em;
  }

  p {
    line-height: 1.65;
  }

  /* Focus styles for accessibility */
  button:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  a:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }


}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md transition-all duration-200;
    white-space: nowrap !important;
    min-width: -webkit-fill-available !important;
    min-width: fit-content !important;
    justify-content: center !important;
  }

  /* Modern button styles */
  .btn-primary {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300 ease-out;
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white;
    @apply hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:-translate-y-0.5;
    @apply active:translate-y-0 active:shadow-md;
    @apply focus:outline-none focus:ring-4 focus:ring-blue-500/30;
    @apply border border-blue-700;
    white-space: nowrap !important;
    min-width: -webkit-fill-available !important;
    min-width: fit-content !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .btn-primary:disabled {
    @apply opacity-50 cursor-not-allowed;
    @apply bg-gradient-to-r from-gray-400 to-gray-500;
    @apply hover:from-gray-400 hover:to-gray-500 hover:shadow-none hover:translate-y-0;
    @apply border-gray-500;
    pointer-events: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
  }

  /* Hover states for option cards */
  .hover\:bg-blue-25:hover {
    background-color: rgba(59, 130, 246, 0.05);
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }

  /* Improved button feedback */
  .btn-primary,
  .btn-secondary {
    position: relative;
    overflow: hidden;
  }

  .btn-primary::before,
  .btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s ease;
  }

  .btn-primary:hover::before,
  .btn-secondary:hover::before {
    left: 100%;
  }

  .btn-secondary {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300 ease-out;
    @apply bg-white text-gray-700 border border-gray-300;
    @apply hover:bg-gray-50 hover:border-gray-400 hover:shadow-lg hover:-translate-y-0.5;
    @apply active:translate-y-0 active:shadow-md;
    @apply focus:outline-none focus:ring-4 focus:ring-gray-500/30;
    white-space: nowrap !important;
    min-width: -webkit-fill-available !important;
    min-width: fit-content !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .btn-secondary:disabled {
    @apply opacity-50 cursor-not-allowed;
    @apply bg-gray-100 text-gray-400 border-gray-300;
    @apply hover:bg-gray-100 hover:border-gray-300 hover:shadow-none hover:translate-y-0;
    pointer-events: none !important;
  }

  /* Responsive button styles */
  @media (max-width: 640px) {
    .btn-primary,
    .btn-secondary {
      @apply px-4 py-2 text-sm;
    }
  }

  @media (max-width: 480px) {
    .btn-primary,
    .btn-secondary {
      @apply px-3 py-2 text-xs;
      font-size: 0.75rem !important;
    }
  }

  /* Button text scaling utility */
  .btn-auto-scale {
    white-space: nowrap !important;
    min-width: -webkit-fill-available !important;
    min-width: fit-content !important;
    max-width: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-overflow: ellipsis !important;
    overflow: visible !important;
    flex-shrink: 0 !important;
    word-break: keep-all !important;
    -webkit-hyphens: none !important;
    hyphens: none !important;
  }

  /* Responsive text scaling for buttons */
  @media (max-width: 1024px) {
    .btn-auto-scale {
      font-size: 0.9em !important;
    }
  }

  @media (max-width: 768px) {
    .btn-auto-scale {
      font-size: 0.85em !important;
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }
  }

  @media (max-width: 480px) {
    .btn-auto-scale {
      font-size: 0.8em !important;
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }
  }

  /* Special handling for Vietnamese and other long text languages */
  [lang="vi"] .btn-auto-scale,
  [data-lang="vi"] .btn-auto-scale {
    font-size: 0.9em !important;
    letter-spacing: -0.025em !important;
  }

  @media (max-width: 768px) {
    [lang="vi"] .btn-auto-scale,
    [data-lang="vi"] .btn-auto-scale {
      font-size: 0.8em !important;
    }
  }

  @media (max-width: 480px) {
    [lang="vi"] .btn-auto-scale,
    [data-lang="vi"] .btn-auto-scale {
      font-size: 0.75em !important;
      padding-left: 0.4rem !important;
      padding-right: 0.4rem !important;
    }
  }

  /* Modern form input styles */
  .form-input {
    @apply w-full px-4 py-3 rounded-lg border border-gray-300;
    @apply bg-white text-gray-900 placeholder-gray-500;
    @apply focus:outline-none focus:ring-4 focus:ring-blue-500/30 focus:border-blue-500;
    @apply transition-all duration-200 ease-out;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  /* Glass morphism */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* Apple Theme Light Mode Only */
  .apple-theme-light {
    @apply bg-white text-gray-900;
  }


}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  /* Text utilities */
  .text-gradient {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Container utilities */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .section-spacing {
    @apply py-16 sm:py-20 lg:py-24;
  }

  /* Header utilities */
  .header-button-priority {
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    flex-shrink: 0 !important;
    white-space: nowrap !important;
    position: relative !important;
    z-index: 10 !important;
  }

  .header-compact-spacing {
    gap: 0.25rem !important;
  }

  @media (min-width: 1024px) {
    .header-compact-spacing {
      gap: 0.5rem !important;
    }
  }

  /* Header layout fixes for zoom issues */
  .header-container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
    min-height: 4rem !important;
    gap: 0.5rem !important;
  }

  .header-logo-section {
    flex-shrink: 0 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    max-width: 150px !important;
  }

  .header-nav-section {
    flex: 1 1 auto !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-width: 0 !important;
    overflow: hidden !important;
  }

  .header-actions-section {
    flex-shrink: 0 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.125rem !important;
  }

  @media (min-width: 1024px) {
    .header-actions-section {
      gap: 0.25rem !important;
    }
  }

  /* Ensure navigation doesn't overflow */
  .header-nav-item {
    flex-shrink: 1 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    white-space: nowrap !important;
  }

  /* Critical button protection */
  .header-critical-button {
    position: relative !important;
    z-index: 20 !important;
    flex-shrink: 0 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    white-space: nowrap !important;
  }

  /* Auth page button protection - removed to prevent header overlap */

  /* Zoom-specific fixes */
  @media screen and (min-resolution: 96dpi) and (max-resolution: 120dpi) {
    .header-container {
      gap: 0.125rem !important;
    }

    .header-nav-section {
      max-width: calc(100vw - 350px) !important;
    }

    .header-actions-section {
      min-width: 150px !important;
    }
  }

  /* High DPI / Zoom level adjustments */
  @media screen and (min-resolution: 120dpi) {
    .header-container {
      gap: 0.125rem !important;
    }

    .header-nav-section {
      max-width: calc(100vw - 350px) !important;
    }

    .header-nav-item {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }
  }

  /* Force layout stability at all zoom levels */
  .header-container * {
    box-sizing: border-box !important;
  }

  /* Prevent navigation overflow at any zoom level */
  .header-nav-section > div {
    overflow: hidden !important;
    flex-wrap: nowrap !important;
  }

  /* Ensure critical button always has space */
  .header-critical-button {
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    max-width: none !important;
    margin-right: 0 !important;
    right: 0 !important;
    order: 999 !important;
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  /* Additional compact spacing for tight layouts */
  .header-compact-mode {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }

  @media (max-width: 1280px) {
    .header-critical-button {
      font-size: 0.875rem !important;
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
      min-width: -webkit-fill-available !important;
      min-width: -moz-fit-content !important;
      min-width: fit-content !important;
      white-space: nowrap !important;
    }
  }

  @media (max-width: 768px) {
    .header-critical-button {
      font-size: 0.8rem !important;
      padding-left: 0.4rem !important;
      padding-right: 0.4rem !important;
      padding-top: 0.4rem !important;
      padding-bottom: 0.4rem !important;
    }
  }

  /* Force button to always be visible */
  .header-critical-button {
    position: relative !important;
    right: 0 !important;
    margin-right: 0 !important;
    order: 999 !important;
  }

  /* Ensure actions section has enough space */
  .header-actions-section {
    min-width: 80px !important;
    justify-content: flex-end !important;
  }

  /* Scrollbar utilities with enhanced browser compatibility */
  .scrollbar-thin {
    -ms-overflow-style: thin;
    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    /* Fallback for browsers that don't support scrollbar-width */
    overflow: auto;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }

  .scrollbar-thin::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* Keyframes for animations */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  100% { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes scaleIn {
  0% { 
    opacity: 0; 
    transform: scale(0.9); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Blob animation for hero section */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Global scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}



/* Text selection */
::selection {
  background-color: #3b82f6;
  color: white;
}



/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Modern Theme Specific Styles */
.theme-modern {
  --font-display: 'Inter', sans-serif;
  --font-heading: 'Inter', sans-serif;
  --font-serif: 'Roboto', sans-serif;
  --font-body: 'Inter', sans-serif;
}

.theme-modern .font-display {
  font-family: var(--font-display);
}

.theme-modern .font-heading {
  font-family: var(--font-heading);
}

.theme-modern .font-serif {
  font-family: var(--font-serif);
}

.theme-modern .font-body {
  font-family: var(--font-body);
}

/* Medieval specific animations */
@keyframes modern-glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    text-shadow: 0 0 15px rgba(59, 130, 246, 0.5), 0 0 25px rgba(59, 130, 246, 0.3);
  }
}

.theme-modern .animate-glow {
  animation: modern-glow 2s ease-in-out infinite;
}

/* Modern scrollbar */
.theme-modern::-webkit-scrollbar {
  width: 8px;
}

.theme-modern::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.theme-modern::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
  border-radius: 4px;
}

.theme-modern::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #1d4ed8, #1e40af);
}

/* Modern text selection */
.theme-modern ::selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1d4ed8;
}

/* Medieval ornate borders */
.ornate-border {
  position: relative;
}

.ornate-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #b79739, #6b0f1a, #1f3e5c, #b79739);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.7;
}

/* Modern card hover effects */
.theme-modern .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Modern button enhancements */
.theme-modern .btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: 1px solid #1d4ed8;
  color: white;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.theme-modern .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.theme-modern .btn-primary:hover::before {
  left: 100%;
}

.theme-modern .btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* Modern layout adjustments */
.theme-modern .bg-primary-600 {
  background-color: #3b82f6 !important;
}

.theme-modern .bg-secondary-600 {
  background-color: #64748b !important;
}

.theme-modern .text-primary-600 {
  color: #3b82f6 !important;
}

.theme-modern .text-secondary-600 {
  color: #64748b !important;
}

.theme-modern .border-primary-600 {
  border-color: #3b82f6 !important;
}

/* Medieval spacing and typography adjustments */
.theme-modern h1,
.theme-modern h2,
.theme-modern h3,
.theme-modern h4,
.theme-modern h5,
.theme-modern h6 {
  font-family: var(--font-display);
  letter-spacing: 0.5px;
}

.theme-modern p,
.theme-modern span,
.theme-modern div {
  font-family: var(--font-serif);
}

/* Medieval navigation enhancements */
.theme-modern .nav-link {
  position: relative;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.theme-modern .nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #b79739, #d4af37);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.theme-modern .nav-link:hover::after,
.theme-modern .nav-link.active::after {
  width: 100%;
}

/* Medieval form styling */
.theme-modern .form-input {
  border: 2px solid rgba(183, 151, 57, 0.3);
  background: rgba(31, 62, 92, 0.1);
  color: #b79739;
}

.theme-modern .form-input:focus {
  border-color: #b79739;
  box-shadow: 0 0 0 3px rgba(183, 151, 57, 0.1);
}

.theme-modern .form-input::placeholder {
  color: rgba(183, 151, 57, 0.6);
}

/* Medieval modal and popup styling */
.theme-modern .modal-overlay {
  background: rgba(31, 62, 92, 0.8);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

.theme-modern .modal-content {
  background: linear-gradient(135deg, #1f3e5c 0%, #6b0f1a 100%);
  border: 2px solid #b79739;
  box-shadow: 0 25px 50px rgba(183, 151, 57, 0.3);
}

.mx-auto.max-w-8xl.px-2.sm\:px-3.lg\:px-4.xl\:px-6 {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

/* Hide debug information overlays */
.debug-overlay,
[class*="debug-panel"],
[class*="device-detection"],
div[style*="position: fixed"][style*="z-index"]:has-text("Device Detection"),
div[style*="position: fixed"][style*="z-index"]:has-text("Debug"),
div[style*="position: fixed"][style*="z-index"]:has-text("Detected:"),
div[style*="position: fixed"][style*="z-index"]:has-text("Method:"),
div[style*="position: fixed"][style*="z-index"]:has-text("Browser:"),
div[style*="position: fixed"][style*="z-index"]:has-text("Performance:") {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Hide any fixed positioned debug elements */
body > div[style*="position: fixed"]:not(.header):not(.modal):not(.toast):not(.notification) {
  display: none !important;
}

/* Hide debug info that might contain device detection information */
.debug-info,
.device-info,
.performance-debug,
.detection-debug {
  display: none !important;
}
