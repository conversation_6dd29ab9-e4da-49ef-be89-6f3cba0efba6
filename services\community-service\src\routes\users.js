/**
 * Community Users API Routes
 * PostgreSQL implementation for user management
 */

const express = require('express');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

const router = express.Router();

// Get PostgreSQL database connection
let db = null;
try {
  const { postgresqlConfig } = require('../config/postgresql');
  db = postgresqlConfig;
} catch (error) {
  logger.error('Failed to initialize PostgreSQL in users routes:', error);
}

/**
 * Simple users endpoint without subqueries for debugging
 * GET /api/users/simple
 */
router.get('/simple', async (req, res) => {
  try {
    const { limit = 1 } = req.query;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    const query = `
      SELECT id, email, first_name, last_name, avatar_url, email_verified, is_active, created_at, updated_at
      FROM users
      LIMIT $1
    `;

    console.log('🔍 Executing simple users query:', query);
    console.log('🔍 Query parameters:', [parseInt(limit)]);
    
    const result = await db.query(query, [parseInt(limit)]);
    console.log('✅ Simple query executed successfully, rows:', result.rows.length);

    const users = result.rows.map(user => ({
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      isVerified: user.email_verified,
      isActive: user.is_active,
      createdAt: user.created_at,
      updatedAt: user.updated_at
    }));

    res.json({
      users,
      count: users.length
    });

  } catch (error) {
    logger.error('Simple users query error', { error: error.message, stack: error.stack });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch user',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Schema check endpoint for debugging
 * GET /api/users/schema-check
 */
router.get('/schema-check', async (req, res) => {
  try {
    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if users table exists
    const tableCheck = await db.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);

    const usersTableExists = tableCheck.rows[0].exists;

    if (!usersTableExists) {
      return res.status(404).json({ 
        error: 'Users table does not exist',
        message: 'Database schema is missing the users table'
      });
    }

    // Get table structure
    const structureCheck = await db.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'users'
      ORDER BY ordinal_position;
    `);

    // Try a simple count query
    const countCheck = await db.query('SELECT COUNT(*) as user_count FROM users');

    res.json({
      message: 'Schema check completed',
      usersTableExists,
      tableStructure: structureCheck.rows,
      userCount: countCheck.rows[0].user_count
    });

  } catch (error) {
    logger.error('Schema check error', { error: error.message, stack: error.stack });
    res.status(500).json({ 
      error: 'Schema check failed',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

/**
 * Get all users with pagination and filtering
 * GET /api/users
 */
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      sortBy = 'created_at', 
      order = 'desc',
      search,
      isActive = true
    } = req.query;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    let query = `
      SELECT u.id, u.email, u.first_name, u.last_name, u.avatar_url, u.email_verified, u.is_active, u.created_at, u.updated_at,
             COUNT(*) OVER() as total_count,
             (SELECT COUNT(*) FROM posts WHERE author_id = u.id) as post_count,
             (SELECT COUNT(*) FROM comments WHERE author_id = u.id) as comment_count
      FROM users u
      WHERE 1=1
    `;

    const params = [];

    // Add search filter
    if (search) {
      const paramIndex = params.length + 1;
      query += ` AND (u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex + 1} OR u.email ILIKE $${paramIndex + 2})`;
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Add sorting
    const validSortFields = ['created_at', 'first_name', 'email'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const sortOrder = order.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
    
    query += ` ORDER BY u.${sortField} ${sortOrder}`;
    
    // Add pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const limitIndex = params.length + 1;
    query += ` LIMIT $${limitIndex} OFFSET $${limitIndex + 1}`;
    params.push(parseInt(limit), offset);

    console.log('🔍 Executing users query:', query);
    console.log('🔍 Query parameters:', params);
    
    const result = await db.query(query, params);
    console.log('✅ Query executed successfully, rows:', result.rows.length);

    const users = result.rows.map(user => ({
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      isVerified: user.email_verified,
      isActive: user.is_active,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      stats: {
        postCount: user.post_count,
        commentCount: user.comment_count
      }
    }));

    const totalCount = result.rows.length > 0 ? result.rows[0].total_count : 0;

    res.json({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });

  } catch (error) {
    logger.error('Error fetching users', { error: error.message, stack: error.stack });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch users',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Get user by ID
 * GET /api/users/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Get user with stats
    const userQuery = `
      SELECT u.id, u.email, u.first_name, u.last_name, u.avatar_url, u.email_verified, u.is_active, u.created_at, u.updated_at,
             (SELECT COUNT(*) FROM posts WHERE author_id = u.id) as post_count,
             (SELECT COUNT(*) FROM comments WHERE author_id = u.id) as comment_count
      FROM users u
      WHERE u.id = $1
    `;

    const userResult = await db.query(userQuery, [id]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = userResult.rows[0];

    // Get recent posts
    const recentPostsResult = await db.query(`
      SELECT id, title, post_type, category, upvotes, view_count, created_at
      FROM posts 
      WHERE author_id = $1 AND status = 'published'
      ORDER BY created_at DESC 
      LIMIT 5
    `, [id]);

    // Get recent comments
    const recentCommentsResult = await db.query(`
      SELECT c.id, c.content, c.upvotes, c.created_at, p.title as post_title
      FROM comments c
      LEFT JOIN posts p ON c.post_id = p.id
      WHERE c.author_id = $1 AND c.is_deleted = FALSE
      ORDER BY c.created_at DESC 
      LIMIT 5
    `, [id]);

    const responseUser = {
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      isVerified: user.email_verified,
      isActive: user.is_active,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      stats: {
        postCount: user.post_count,
        commentCount: user.comment_count
      },
      recentPosts: recentPostsResult.rows,
      recentComments: recentCommentsResult.rows
    };

    res.json({ user: responseUser });

  } catch (error) {
    logger.error('Error fetching user', { error: error.message, stack: error.stack, userId: req.params.id });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch user',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Create new user
 * POST /api/users
 */
router.post('/', async (req, res) => {
  try {
    const {
      id,
      email,
      username,
      firstName,
      lastName,
      avatarUrl,
      bio,
      location,
      website
    } = req.body;

    if (!email || !firstName || !lastName) {
      return res.status(400).json({
        error: 'Missing required fields: email, firstName, lastName'
      });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Use provided ID or generate new one
    const userId = id || uuidv4();

    // Insert new user
    const query = `
      INSERT INTO users (
        id, email, first_name, last_name, avatar_url
      ) VALUES ($1, $2, $3, $4, $5)
    `;

    await db.query(query, [
      userId, email, firstName, lastName, avatarUrl
    ]);

    // Get the created user
    const userResult = await db.query('SELECT * FROM users WHERE id = $1', [userId]);
    const user = userResult.rows[0];

    const responseUser = {
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      createdAt: user.created_at
    };

    logger.info('User created', { userId });

    res.status(201).json({
      message: 'User created successfully',
      user: responseUser
    });

  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      return res.status(409).json({ error: 'Email or username already exists' });
    }
    
    logger.error('Error creating user', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update user profile
 * PUT /api/users/:id
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      firstName,
      lastName,
      avatarUrl,
      bio,
      location,
      website
    } = req.body;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    const query = `
      UPDATE users
      SET first_name = $1, last_name = $2, avatar_url = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
    `;

    const result = await db.query(query, [
      firstName, lastName, avatarUrl, id
    ]);

    if (result.rowCount === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    logger.info('User updated', { userId: id });

    res.json({ message: 'User updated successfully' });

  } catch (error) {
    logger.error('Error updating user', { error: error.message, userId: req.params.id });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Fix existing users with placeholder data
 * POST /api/users/fix-existing
 */
router.post('/fix-existing', async (req, res) => {
  try {
    console.log('🔄 Fixing existing users with placeholder data...');

    const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2MzY2RjEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iOCIgeT0iOCI+CjxwYXRoIGQ9Ik0xMiAxMkM5Ljc5IDEyIDggMTAuMjEgOCA4UzkuNzkgNiAxMiA2UzE2IDcuNzkgMTYgOFMxNC4yMSAxMiAxMiAxMlpNMTIgMTRDMTYuNDIgMTQgMjAgMTUuNzkgMjAgMThWMjBIMFYxOEMwIDE1Ljc5IDMuNTggMTQgOCAxNEgxMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K';

    // Update users with placeholder avatars
    const avatarUpdateResult = await db.query(`
      UPDATE users
      SET avatar_url = $1
      WHERE avatar_url = 'https://via.placeholder.com/40'
    `, [defaultAvatar]);

    // Update users with "Firebase User" names to have more realistic names
    const nameUpdateResult = await db.query(`
      UPDATE users
      SET
        first_name = CASE
          WHEN id LIKE 'fb-%' THEN 'User'
          ELSE first_name
        END,
        last_name = CASE
          WHEN id LIKE 'fb-%' THEN SUBSTRING(id FROM 4 FOR 6)
          ELSE last_name
        END
      WHERE first_name = 'Firebase' AND last_name = 'User'
    `);

    console.log(`✅ Updated ${avatarUpdateResult.rowCount} users' avatars`);
    console.log(`✅ Updated ${nameUpdateResult.rowCount} users' names`);

    res.json({
      message: 'Existing users fixed successfully!',
      avatarsUpdated: avatarUpdateResult.rowCount,
      namesUpdated: nameUpdateResult.rowCount
    });

  } catch (error) {
    console.error('❌ Error fixing existing users:', error.message);
    res.status(500).json({ error: 'Failed to fix existing users: ' + error.message });
  }
});

/**
 * Sync Firebase user
 * POST /api/users/sync-firebase
 */
router.post('/sync-firebase', async (req, res) => {
  try {
    const { firebaseUID, email, displayName } = req.body;

    if (!firebaseUID) {
      return res.status(400).json({ error: 'Firebase UID is required' });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if user already exists
    const existingUser = await db.query('SELECT * FROM users WHERE id = $1', [firebaseUID]);

    if (existingUser.rows.length > 0) {
      // Update existing user with latest info
      let firstName = existingUser.rows[0].first_name;
      let lastName = existingUser.rows[0].last_name;

      if (displayName && displayName !== 'Firebase User') {
        const nameParts = displayName.split(' ');
        firstName = nameParts[0] || firstName;
        lastName = nameParts.slice(1).join(' ') || lastName;
      } else if (email && email !== existingUser.rows[0].email) {
        firstName = email.split('@')[0];
      }

      const updateQuery = `
        UPDATE users
        SET first_name = $1, last_name = $2, email = $3, updated_at = CURRENT_TIMESTAMP
        WHERE id = $4
      `;

      await db.query(updateQuery, [firstName, lastName, email || existingUser.rows[0].email, firebaseUID]);

      return res.json({
        message: 'User updated successfully',
        user: {
          id: firebaseUID,
          firstName: firstName,
          lastName: lastName,
          email: email || existingUser.rows[0].email
        }
      });
    }

    // Create user with Firebase UID
    let firstName = 'Firebase';
    let lastName = 'User';

    if (displayName && displayName !== 'Firebase User') {
      const nameParts = displayName.split(' ');
      firstName = nameParts[0] || 'Firebase';
      lastName = nameParts.slice(1).join(' ') || 'User';
    } else if (email) {
      // Use email username as first name
      firstName = email.split('@')[0];
      lastName = 'User';
    }

    const userData = {
      id: firebaseUID,
      email: email || `${firebaseUID}@firebase.com`,
      first_name: firstName,
      last_name: lastName,
      avatar_url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2MzY2RjEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iOCIgeT0iOCI+CjxwYXRoIGQ9Ik0xMiAxMkM5Ljc5IDEyIDggMTAuMjEgOCA4UzkuNzkgNiAxMiA2UzE2IDcuNzkgMTYgOFMxNC4yMSAxMiAxMiAxMlpNMTIgMTRDMTYuNDIgMTQgMjAgMTUuNzkgMjAgMThWMjBIMFYxOEMwIDE1Ljc5IDMuNTggMTQgOCAxNEgxMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K',
      email_verified: true,
      is_active: true
    };

    const query = `
      INSERT INTO users (
        id, email, first_name, last_name, avatar_url, email_verified, is_active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `;

    await db.query(query, [
      userData.id, userData.email, userData.first_name, userData.last_name,
      userData.avatar_url, userData.email_verified, userData.is_active
    ]);

    res.json({
      message: 'Firebase user synced successfully',
      user: { id: firebaseUID, email: userData.email, firstName: userData.first_name, lastName: userData.last_name }
    });

  } catch (error) {
    console.error('Error syncing Firebase user:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Fix existing users with placeholder data
 * POST /api/users/fix-existing
 */
router.post('/fix-existing', async (req, res) => {
  try {
    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Find users with placeholder names or avatars
    const usersToFix = await db.query(`
      SELECT id, first_name, last_name, avatar_url
      FROM users
      WHERE first_name = 'Firebase'
         OR last_name = 'User'
         OR avatar_url LIKE 'https://via.placeholder.com%'
    `);

    let avatarsUpdated = 0;
    let namesUpdated = 0;

    for (const user of usersToFix.rows) {
      const updates = [];
      const params = [];
      let paramIndex = 1;

      // Fix avatar if it's a placeholder URL
      if (user.avatar_url && user.avatar_url.includes('via.placeholder.com')) {
        const newAvatar = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2MzY2RjEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iOCIgeT0iOCI+CjxwYXRoIGQ9Ik0xMiAxMkM5Ljc5IDEyIDggMTAuMjEgOCA4UzkuNzkgNiAxMiA2UzE2IDcuNzkgMTYgOFMxNC4yMSAxMiAxMiAxMlpNMTIgMTRDMTYuNDIgMTQgMjAgMTUuNzkgMjAgMThWMjBIMFYxOEMwIDE1Ljc5IDMuNTggMTQgOCAxNEgxMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K`;
        updates.push(`avatar_url = $${paramIndex++}`);
        params.push(newAvatar);
        avatarsUpdated++;
      }

      // Fix name if it's "Firebase User"
      if (user.first_name === 'Firebase' && user.last_name === 'User') {
        const userIdSuffix = user.id.substring(user.id.length - 6);
        updates.push(`first_name = $${paramIndex++}`);
        updates.push(`last_name = $${paramIndex++}`);
        params.push('User');
        params.push(userIdSuffix);
        namesUpdated++;
      }

      // Apply updates if any
      if (updates.length > 0) {
        params.push(user.id);
        const updateQuery = `
          UPDATE users
          SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
          WHERE id = $${paramIndex}
        `;

        await db.query(updateQuery, params);
        logger.info('Fixed user data', { userId: user.id, updates: updates.length });
      }
    }

    res.json({
      message: 'Existing users fixed successfully!',
      avatarsUpdated,
      namesUpdated
    });

  } catch (error) {
    logger.error('Error fixing existing users', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Fix specific user by ID
 * POST /api/users/fix/:userId
 */
router.post('/fix/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Get user
    const userResult = await db.query('SELECT * FROM users WHERE id = $1', [userId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = userResult.rows[0];
    const userIdSuffix = user.id.substring(user.id.length - 6);

    // Update user with better name
    await db.query(`
      UPDATE users
      SET first_name = $1, last_name = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, ['User', userIdSuffix, userId]);

    logger.info('Fixed specific user', { userId });

    res.json({
      message: 'User fixed successfully!',
      userId,
      newName: `User ${userIdSuffix}`
    });

  } catch (error) {
    logger.error('Error fixing specific user', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
