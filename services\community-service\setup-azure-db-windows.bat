@echo off
REM Azure Database Setup Script for Windows
REM Usage: setup-azure-db-windows.bat [app-name] [resource-group]

setlocal enabledelayedexpansion

REM Default values
set APP_NAME=%1
if "%APP_NAME%"=="" set APP_NAME=vwork-community-service

set RESOURCE_GROUP=%2
if "%RESOURCE_GROUP%"=="" set RESOURCE_GROUP=vwork-rg

echo 🗄️  Azure Database Setup for Community Service (Windows)
echo ======================================================

REM Check Azure CLI
where az >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Azure CLI is not installed. Please install it first.
    pause
    exit /b 1
)

REM Check if logged in
az account show >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Not logged in to Azure. Please login first.
    az login
)

echo ✅ Azure CLI is ready

REM Generate password
set DB_PASSWORD=VWork2024!%RANDOM%%RANDOM%
echo 🔐 Generated database password

set DB_SERVER_NAME=%APP_NAME%-db-server
set DB_NAME=vwork_community_service
set DB_USER=vwork_admin

REM Check if database server already exists
az postgres flexible-server show --resource-group %RESOURCE_GROUP% --name %DB_SERVER_NAME% >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ⚠️  Database server %DB_SERVER_NAME% already exists
    echo 📋 Getting existing database information...
    
    for /f "tokens=*" %%i in ('az postgres flexible-server show --resource-group %RESOURCE_GROUP% --name %DB_SERVER_NAME% --query "fullyQualifiedDomainName" --output tsv') do set DB_HOST=%%i
    
    echo ✅ Using existing database server: %DB_HOST%
) else (
    REM Create PostgreSQL Flexible Server
    echo 🗄️  Creating PostgreSQL Flexible Server: %DB_SERVER_NAME%
    az postgres flexible-server create --name %DB_SERVER_NAME% --resource-group %RESOURCE_GROUP% --location eastus --admin-user %DB_USER% --admin-password "%DB_PASSWORD%" --sku-name "Standard_B1ms" --tier "Burstable" --storage-size 32 --version 14 --output none

    echo ✅ PostgreSQL server created successfully
    
    REM Get server hostname
    for /f "tokens=*" %%i in ('az postgres flexible-server show --resource-group %RESOURCE_GROUP% --name %DB_SERVER_NAME% --query "fullyQualifiedDomainName" --output tsv') do set DB_HOST=%%i
)

REM Check if database exists
az postgres flexible-server db show --resource-group %RESOURCE_GROUP% --server-name %DB_SERVER_NAME% --database-name %DB_NAME% >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ⚠️  Database %DB_NAME% already exists
) else (
    REM Create database
    echo 📊 Creating database: %DB_NAME%
    az postgres flexible-server db create --resource-group %RESOURCE_GROUP% --server-name %DB_SERVER_NAME% --database-name %DB_NAME% --output none

    echo ✅ Database created successfully
)

REM Configure firewall rules
echo 🔥 Configuring firewall rules
az postgres flexible-server firewall-rule create --resource-group %RESOURCE_GROUP% --name %DB_SERVER_NAME% --rule-name "AllowAzureServices" --start-ip-address "0.0.0.0" --end-ip-address "***************" --output none

echo ✅ Firewall rules configured

REM Create connection string
set DATABASE_URL=postgresql://%DB_USER%:%DB_PASSWORD%@%DB_HOST%:5432/%DB_NAME%?sslmode=require

echo ✅ Database setup completed!
echo 📋 Database Information:
echo    Server: %DB_HOST%
echo    Database: %DB_NAME%
echo    Username: %DB_USER%
echo    Password: %DB_PASSWORD%

echo 🔗 Connection String:
echo %DATABASE_URL%

REM Update App Service with database configuration
echo ⚙️  Updating App Service configuration
az webapp config appsettings set --name %APP_NAME% --resource-group %RESOURCE_GROUP% --settings DATABASE_URL="%DATABASE_URL%" DB_HOST="%DB_HOST%" DB_NAME="%DB_NAME%" DB_USER="%DB_USER%" DB_PASSWORD="%DB_PASSWORD%" --output none

echo ✅ App Service configuration updated

REM Save credentials to file
echo # Azure PostgreSQL Database Credentials > azure-db-credentials.txt
echo # Generated on: %DATE% %TIME% >> azure-db-credentials.txt
echo. >> azure-db-credentials.txt
echo Server: %DB_HOST% >> azure-db-credentials.txt
echo Database: %DB_NAME% >> azure-db-credentials.txt
echo Username: %DB_USER% >> azure-db-credentials.txt
echo Password: %DB_PASSWORD% >> azure-db-credentials.txt
echo. >> azure-db-credentials.txt
echo Connection String: >> azure-db-credentials.txt
echo %DATABASE_URL% >> azure-db-credentials.txt
echo. >> azure-db-credentials.txt
echo # Important: Keep this file secure and delete after deployment >> azure-db-credentials.txt

echo ✅ Credentials saved to azure-db-credentials.txt

REM Show next steps
echo 📋 Next Steps:
echo 1. Configure remaining environment variables in Azure Portal:
echo    - FIREBASE_PROJECT_ID
echo    - FIREBASE_CLIENT_EMAIL
echo    - FIREBASE_PRIVATE_KEY
echo    - ALLOWED_ORIGINS
echo 2. Run database migrations:
echo    az webapp ssh --name %APP_NAME% --resource-group %RESOURCE_GROUP%
echo    npm run db:setup
echo 3. Test the application:
echo    curl https://%APP_NAME%.azurewebsites.net/health

REM Show database status
echo 📊 Database Status:
az postgres flexible-server show --resource-group %RESOURCE_GROUP% --name %DB_SERVER_NAME% --query "{name:name, state:state, fullyQualifiedDomainName:fullyQualifiedDomainName, version:version}" --output table

echo 🎉 Database setup completed!
echo ⚠️  Remember to delete azure-db-credentials.txt after deployment

pause 