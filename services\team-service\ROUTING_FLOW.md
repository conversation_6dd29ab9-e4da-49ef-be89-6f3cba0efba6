# Routing Flow: Frontend → API Gateway → Team Service

## 🔄 Tổng quan Routing Flow

```
Frontend (React) 
    ↓ HTTP Request
API Gateway (Port 8080)
    ↓ Proxy Request
Team Service (Port 3008)
    ↓ Database Query
PostgreSQL Database
```

## 📡 API Gateway Configuration

### Base URL: `http://localhost:8080/api/v1`

### Team Service Proxies:

#### 1. **Teams Management**
```javascript
// API Gateway Route
app.use('/api/v1/teams', createProxyMiddleware({
  target: TEAM_SERVICE_URL, // http://localhost:3008
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/teams': '/api/teams'
  }
}));
```

**Frontend → API Gateway → Team Service:**
- Frontend calls: `GET /api/v1/teams`
- API Gateway proxies to: `GET http://localhost:3008/api/teams`
- Team Service handles: `GET /api/teams`

#### 2. **Team Invitations**
```javascript
// API Gateway Route
app.use('/api/v1/team-invitations', createProxyMiddleware({
  target: TEAM_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/team-invitations': '/api/invitations'
  }
}));
```

**Frontend → API Gateway → Team Service:**
- Frontend calls: `GET /api/v1/team-invitations/my-invitations`
- API Gateway proxies to: `GET http://localhost:3008/api/invitations/my-invitations`
- Team Service handles: `GET /api/invitations/my-invitations`

#### 3. **Team Chat**
```javascript
// API Gateway Route
app.use('/api/v1/team-chat', createProxyMiddleware({
  target: TEAM_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/team-chat': '/api/chat'
  }
}));
```

**Frontend → API Gateway → Team Service:**
- Frontend calls: `GET /api/v1/team-chat/team/123`
- API Gateway proxies to: `GET http://localhost:3008/api/chat/team/123`
- Team Service handles: `GET /api/chat/team/123`

## 🛣️ Complete Routing Map

### Teams Management Endpoints

| Frontend Call | API Gateway | Team Service | Description |
|---------------|-------------|--------------|-------------|
| `GET /api/v1/teams` | ✅ Proxy | `GET /api/teams` | Lấy danh sách teams |
| `GET /api/v1/teams/featured` | ✅ Proxy | `GET /api/teams/featured` | Lấy featured teams |
| `GET /api/v1/teams/123` | ✅ Proxy | `GET /api/teams/123` | Lấy chi tiết team |
| `POST /api/v1/teams` | ✅ Proxy | `POST /api/teams` | Tạo team mới |
| `PUT /api/v1/teams/123` | ✅ Proxy | `PUT /api/teams/123` | Cập nhật team |
| `DELETE /api/v1/teams/123` | ✅ Proxy | `DELETE /api/teams/123` | Xóa team |
| `GET /api/v1/teams/user/my-teams` | ✅ Proxy | `GET /api/teams/user/my-teams` | Teams của user |
| `GET /api/v1/teams/categories/list` | ✅ Proxy | `GET /api/teams/categories/list` | Danh sách categories |
| `GET /api/v1/teams/requirements` | ✅ Proxy | `GET /api/teams/requirements` | Yêu cầu tạo team |
| `GET /api/v1/teams/user/friends` | ✅ Proxy | `GET /api/teams/user/friends` | Danh sách bạn bè |
| `GET /api/v1/teams/search` | ✅ Proxy | `GET /api/teams/search` | Tìm kiếm teams |
| `GET /api/v1/teams/123/projects` | ✅ Proxy | `GET /api/teams/123/projects` | Dự án của team |
| `GET /api/v1/teams/123/earnings` | ✅ Proxy | `GET /api/teams/123/earnings` | Thu nhập team |
| `PUT /api/v1/teams/123/profit-sharing` | ✅ Proxy | `PUT /api/teams/123/profit-sharing` | Cập nhật chia lợi nhuận |
| `GET /api/v1/teams/123/stats` | ✅ Proxy | `GET /api/teams/123/stats` | Thống kê team |
| `DELETE /api/v1/teams/123/members/456` | ✅ Proxy | `DELETE /api/teams/123/members/456` | Xóa thành viên |
| `POST /api/v1/teams/123/leave` | ✅ Proxy | `POST /api/teams/123/leave` | Rời team |
| `PUT /api/v1/teams/123/transfer-leadership` | ✅ Proxy | `PUT /api/teams/123/transfer-leadership` | Chuyển quyền leader |
| `GET /api/v1/teams/123/settings` | ✅ Proxy | `GET /api/v1/teams/123/settings` | Cài đặt team |
| `PUT /api/v1/teams/123/settings` | ✅ Proxy | `PUT /api/v1/teams/123/settings` | Cập nhật cài đặt |

### Team Invitations Endpoints

| Frontend Call | API Gateway | Team Service | Description |
|---------------|-------------|--------------|-------------|
| `GET /api/v1/team-invitations/my-invitations` | ✅ Proxy | `GET /api/invitations/my-invitations` | Lời mời của user |
| `GET /api/v1/team-invitations/team/123` | ✅ Proxy | `GET /api/invitations/team/123` | Lời mời của team |
| `POST /api/v1/team-invitations/team/123` | ✅ Proxy | `POST /api/invitations/team/123` | Gửi lời mời |
| `PUT /api/v1/team-invitations/456/accept` | ✅ Proxy | `PUT /api/invitations/456/accept` | Chấp nhận lời mời |
| `PUT /api/v1/team-invitations/456/reject` | ✅ Proxy | `PUT /api/invitations/456/reject` | Từ chối lời mời |
| `DELETE /api/v1/team-invitations/456` | ✅ Proxy | `DELETE /api/invitations/456` | Hủy lời mời |
| `POST /api/v1/team-invitations/456/resend` | ✅ Proxy | `POST /api/invitations/456/resend` | Gửi lại lời mời |

### Team Chat Endpoints

| Frontend Call | API Gateway | Team Service | Description |
|---------------|-------------|--------------|-------------|
| `GET /api/v1/team-chat/team/123` | ✅ Proxy | `GET /api/chat/team/123` | Lấy tin nhắn chat |
| `POST /api/v1/team-chat/team/123` | ✅ Proxy | `POST /api/chat/team/123` | Gửi tin nhắn |
| `DELETE /api/v1/team-chat/message/456` | ✅ Proxy | `DELETE /api/chat/message/456` | Xóa tin nhắn |
| `GET /api/v1/team-chat/team/123/unread` | ✅ Proxy | `GET /api/chat/team/123/unread` | Số tin nhắn chưa đọc |
| `GET /api/v1/team-chat/my-teams/recent` | ✅ Proxy | `GET /api/chat/my-teams/recent` | Tin nhắn gần đây |
| `GET /api/v1/team-chat/team/123/stats` | ✅ Proxy | `GET /api/chat/team/123/stats` | Thống kê chat |
| `GET /api/v1/team-chat/team/123/search` | ✅ Proxy | `GET /api/chat/team/123/search` | Tìm kiếm tin nhắn |
| `PUT /api/v1/team-chat/message/456/pin` | ✅ Proxy | `PUT /api/chat/message/456/pin` | Ghim tin nhắn |
| `GET /api/v1/team-chat/team/123/pinned` | ✅ Proxy | `GET /api/chat/team/123/pinned` | Tin nhắn đã ghim |
| `PUT /api/v1/team-chat/message/456/read` | ✅ Proxy | `PUT /api/chat/message/456/read` | Đánh dấu đã đọc |
| `GET /api/v1/team-chat/message/456/reactions` | ✅ Proxy | `GET /api/chat/message/456/reactions` | Reactions tin nhắn |
| `POST /api/v1/team-chat/message/456/reactions` | ✅ Proxy | `POST /api/chat/message/456/reactions` | Thêm reaction |

## 🔧 Team Service Internal Routes

### Base Path: `/api`

```javascript
// Team Service app.js
app.use('/api/teams', require('./src/routes/teams'));
app.use('/api/invitations', require('./src/routes/invitations'));
app.use('/api/chat', require('./src/routes/chat'));
```

### Route Files:
- `src/routes/teams.js` - Team management endpoints
- `src/routes/invitations.js` - Invitation system endpoints  
- `src/routes/chat.js` - Chat functionality endpoints

## 🌐 Environment Variables

### API Gateway (.env)
```bash
TEAM_SERVICE_URL=http://localhost:3008
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
```

### Team Service (.env)
```bash
PORT=3008
SERVICE_HOST=localhost
DATABASE_URL=postgresql://...
FIREBASE_PROJECT_ID=...
```

## 🔍 Request Flow Example

### 1. Frontend Request
```javascript
// Frontend code
const response = await fetch('/api/v1/teams', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

### 2. API Gateway Processing
```javascript
// API Gateway receives: GET /api/v1/teams
// Proxies to: GET http://localhost:3008/api/teams
// Headers forwarded: Authorization, Content-Type, etc.
```

### 3. Team Service Processing
```javascript
// Team Service receives: GET /api/teams
// Route handler: teams.js -> router.get('/', ...)
// Database query: SELECT * FROM teams WHERE ...
// Response: JSON with teams data
```

### 4. Response Flow
```
Team Service → API Gateway → Frontend
```

## ⚠️ Error Handling

### API Gateway Error Handling
```javascript
onError: (err, req, res) => {
  console.log('❌ Team service error:', err.message);
  res.status(503).json({
    error: 'Team service unavailable',
    message: 'Team service is currently unavailable. Please try again later.'
  });
}
```

### Team Service Error Handling
```javascript
// Global error handler
app.use((error, req, res, next) => {
  logger.error('🚨 Team Service Error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal Server Error',
    message: 'Something went wrong on the server',
    service: service.name
  });
});
```

## 🧪 Testing the Flow

### 1. Test API Gateway Health
```bash
curl http://localhost:8080/health
```

### 2. Test Team Service Health
```bash
curl http://localhost:3008/health
```

### 3. Test Complete Flow
```bash
# Frontend → API Gateway → Team Service
curl http://localhost:8080/api/v1/teams
```

## 📊 Monitoring & Logging

### API Gateway Logs
```
🌐 API Gateway: GET /api/v1/teams
🔀 Proxying to team service: GET /api/teams
✅ Team service response: 200 for GET /api/teams
```

### Team Service Logs
```
📡 GET /api/teams - Retrieved 5 teams
🔍 Database query executed successfully
📤 Response sent: 200 OK
```

## ✅ Kết luận

**Tất cả routing flow đã được cấu hình đúng:**

1. ✅ **API Gateway** đã proxy đúng tất cả team endpoints
2. ✅ **Team Service** đã expose đúng tất cả routes
3. ✅ **Path rewriting** đã được cấu hình chính xác
4. ✅ **Error handling** đã được implement đầy đủ
5. ✅ **CORS** đã được cấu hình cho cross-origin requests

Frontend có thể gọi trực tiếp các endpoints thông qua API Gateway mà không cần thay đổi gì thêm. 