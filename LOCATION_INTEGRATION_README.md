# 🌍 Location Data Integration với LanguageContext

## Tổng quan

Đã tích hợp thành công dữ liệu quốc gia và thành phố từ API vào `LanguageContext` để sử dụng trong các dropdown search của form onboarding. Tính năng này giúp:

- **Tập trung hóa dữ liệu**: Tất cả dữ liệu location được quản lý trong LanguageContext
- **Tối ưu performance**: Cache dữ liệu để tránh gọi API nhiều lần
- **Dễ sử dụng**: Các component có thể truy cập dữ liệu location thông qua hook `useLanguage()`
- **Đa ngôn ngữ**: Hỗ trợ cả tiếng Việt và tiếng Anh

## 🚀 Tính năng đã tích hợp

### 1. LanguageContext Extensions

```javascript
// Các function mới được thêm vào LanguageContext
const {
  // Dữ liệu location
  locationData,        // { countries: [], cities: {}, isLoading: false, error: null }
  
  // Helper functions
  getCountries,        // () => Country[]
  getCities,           // (countryCode) => Promise<string[]>
  searchCountries,     // (query) => Promise<Country[]>
  searchCities,        // (countryCode, query) => Promise<string[]>
  getCountryByCode,    // (code) => Promise<Country|null>
  getCountryByName,    // (name) => Promise<Country|null>
} = useLanguage();
```

### 2. LocationDropdown Component

Component `LocationDropdown` đã được cập nhật để sử dụng dữ liệu từ LanguageContext:

```javascript
import { useLanguage } from '../../contexts/LanguageContext';

const LocationDropdown = ({ value, onChange, error }) => {
  const { t, getCountries, getCities, locationData } = useLanguage();
  
  // Component tự động load countries và cities từ LanguageContext
  // Không cần gọi trực tiếp locationService
};
```

### 3. Auto-loading Countries

Khi LanguageContext được khởi tạo, nó sẽ tự động:
- Load danh sách tất cả quốc gia từ REST Countries API
- Cache dữ liệu để tối ưu performance
- Xử lý lỗi và loading states

## 📊 Cấu trúc dữ liệu

### Country Object
```javascript
{
  code: "VN",           // ISO 3166-1 alpha-2 code
  name: "Vietnam",      // Tên quốc gia
  flag: "🇻🇳",          // Flag emoji
  region: "Asia",       // Khu vực
  subregion: "Southeast Asia",
  population: 97338579,
  capital: "Hanoi"
}
```

### LocationData State
```javascript
{
  countries: Country[],     // Danh sách tất cả quốc gia
  cities: {                 // Cache thành phố theo country code
    "VN": ["Ho Chi Minh City", "Hanoi", ...],
    "US": ["New York", "Los Angeles", ...],
    // ...
  },
  isLoading: false,         // Trạng thái loading
  error: null              // Lỗi nếu có
}
```

## 🔧 Cách sử dụng

### 1. Trong Component

```javascript
import { useLanguage } from '../contexts/LanguageContext';

const MyComponent = () => {
  const { 
    t, 
    locationData, 
    getCountries, 
    getCities,
    searchCountries 
  } = useLanguage();

  // Lấy danh sách quốc gia
  const countries = getCountries();
  
  // Lấy thành phố của một quốc gia
  const loadCities = async (countryCode) => {
    const cities = await getCities(countryCode);
    console.log('Cities:', cities);
  };

  // Tìm kiếm quốc gia
  const searchCountriesByName = async (query) => {
    const results = await searchCountries(query);
    console.log('Search results:', results);
  };

  return (
    <div>
      <p>Countries loaded: {locationData.countries.length}</p>
      <p>Loading: {locationData.isLoading ? 'Yes' : 'No'}</p>
      {locationData.error && <p>Error: {locationData.error}</p>}
    </div>
  );
};
```

### 2. Trong Form Onboarding

```javascript
import LocationDropdown from '../common/LocationDropdown';

const OnboardingStep = () => {
  const { t } = useLanguage();
  const [location, setLocation] = useState({ country: '', city: '' });

  return (
    <LocationDropdown
      value={location}
      onChange={setLocation}
      error={errors}
    />
  );
};
```

## 🎯 Lợi ích

### 1. **Performance**
- Cache dữ liệu countries ngay khi app khởi động
- Cache cities theo từng country để tránh gọi API nhiều lần
- Lazy loading cities chỉ khi cần thiết

### 2. **User Experience**
- Dropdown search nhanh và mượt mà
- Hiển thị flag emoji cho từng quốc gia
- Validation real-time
- Responsive design

### 3. **Developer Experience**
- API đơn giản và dễ sử dụng
- Tự động xử lý loading states
- Error handling tích hợp
- TypeScript support (nếu cần)

### 4. **Maintainability**
- Code tập trung trong LanguageContext
- Dễ dàng thay đổi API source
- Consistent với pattern hiện tại của app

## 🔍 Demo Components

### LocationDemo
Component demo hoàn chỉnh để test tính năng:
- Form với LocationDropdown
- Hiển thị thông tin LanguageContext
- Sample data và statistics

### OnboardingLocationDemo
Demo cụ thể cho onboarding flow:
- Tích hợp với form onboarding
- Validation và error handling
- Real-time feedback

## 🛠️ API Sources

### Countries
- **Source**: REST Countries API (https://restcountries.com/)
- **Free**: Không cần API key
- **Data**: 250+ quốc gia với đầy đủ thông tin

### Cities
- **Current**: Mock data cho các quốc gia chính
- **Future**: Có thể tích hợp GeoDB Cities API hoặc OpenCage Geocoding API
- **Extensible**: Dễ dàng thay đổi source data

## 📝 Migration Guide

### Từ locationService trực tiếp sang LanguageContext

**Trước:**
```javascript
import locationService from '../services/locationService';

const countries = await locationService.getCountries();
const cities = await locationService.getCities('VN');
```

**Sau:**
```javascript
import { useLanguage } from '../contexts/LanguageContext';

const { getCountries, getCities } = useLanguage();
const countries = getCountries();
const cities = await getCities('VN');
```

## 🚀 Roadmap

### Phase 1 ✅ (Đã hoàn thành)
- [x] Tích hợp countries API vào LanguageContext
- [x] Cập nhật LocationDropdown component
- [x] Tạo demo components
- [x] Cache system

### Phase 2 🔄 (Đang phát triển)
- [ ] Tích hợp real cities API
- [ ] Thêm more countries data
- [ ] Performance optimization
- [ ] Unit tests

### Phase 3 📋 (Kế hoạch)
- [ ] Geocoding features
- [ ] Timezone integration
- [ ] Advanced search filters
- [ ] Offline support

## 🐛 Troubleshooting

### Common Issues

1. **Countries không load**
   - Kiểm tra network connection
   - Xem console logs cho error messages
   - Đảm bảo LanguageContext được wrap đúng cách

2. **Cities không hiển thị**
   - Kiểm tra country code có đúng format không
   - Xem mock data có country code đó không
   - Check console cho API errors

3. **Performance issues**
   - Đảm bảo cache đang hoạt động
   - Kiểm tra không gọi API nhiều lần
   - Monitor memory usage

### Debug Tips

```javascript
// Debug location data
console.log('Location Data:', locationData);

// Debug specific country
const country = await getCountryByCode('VN');
console.log('Vietnam:', country);

// Debug cities
const cities = await getCities('VN');
console.log('Vietnamese cities:', cities);
```

## 📞 Support

Nếu gặp vấn đề hoặc cần hỗ trợ:
1. Kiểm tra console logs
2. Xem demo components để hiểu cách sử dụng
3. Đọc documentation này
4. Tạo issue với detailed error information

---

**Tác giả**: NERAFUS Team  
**Ngày tạo**: 2024  
**Version**: 1.0.0 