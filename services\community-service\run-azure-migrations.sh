#!/bin/bash

# Run Database Migrations on Azure App Service
# Usage: ./run-azure-migrations.sh [app-name] [resource-group]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
APP_NAME=${1:-"vwork-community-service"}
RESOURCE_GROUP=${2:-"vwork-rg"}

echo -e "${BLUE}🔄 Running Database Migrations on Azure${NC}"
echo -e "${BLUE}========================================${NC}"

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check Azure CLI
if ! command -v az &> /dev/null; then
    echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if logged in
if ! az account show &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Azure. Please login first.${NC}"
    az login
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Check if app exists
if ! az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
    echo -e "${RED}❌ App Service '$APP_NAME' not found in resource group '$RESOURCE_GROUP'${NC}"
    echo -e "${YELLOW}💡 Make sure you've deployed the app first using deploy-to-azure.sh${NC}"
    exit 1
fi

echo -e "${GREEN}✅ App Service found: $APP_NAME${NC}"

# Get app URL
APP_URL=$(az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "defaultHostName" --output tsv)
echo -e "${BLUE}🌐 App URL: https://$APP_URL${NC}"

# Check if app is running
echo -e "${BLUE}🔍 Checking app status...${NC}"
APP_STATE=$(az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "state" --output tsv)

if [ "$APP_STATE" != "Running" ]; then
    echo -e "${YELLOW}⚠️  App is not running (state: $APP_STATE). Starting...${NC}"
    az webapp start --name $APP_NAME --resource-group $RESOURCE_GROUP
    echo -e "${BLUE}⏳ Waiting for app to start...${NC}"
    sleep 30
fi

# Test health endpoint
echo -e "${BLUE}🏥 Testing health endpoint...${NC}"
HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "https://$APP_URL/health" || echo "000")

if [ "$HEALTH_RESPONSE" != "200" ]; then
    echo -e "${YELLOW}⚠️  Health check failed (HTTP $HEALTH_RESPONSE). App might still be starting...${NC}"
    echo -e "${BLUE}⏳ Waiting a bit more...${NC}"
    sleep 30
fi

# Connect to app via SSH and run migrations
echo -e "${BLUE}🔗 Connecting to app via SSH...${NC}"
echo -e "${YELLOW}⚠️  This will open an SSH session. Run the following commands:${NC}"
echo -e "${BLUE}${NC}"
echo -e "${BLUE}   npm run db:setup${NC}"
echo -e "${BLUE}   # or${NC}"
echo -e "${BLUE}   npm run db:migrate${NC}"
echo -e "${BLUE}   # then${NC}"
echo -e "${BLUE}   exit${NC}"
echo -e "${BLUE}${NC}"

# Open SSH session
az webapp ssh --name $APP_NAME --resource-group $RESOURCE_GROUP

echo -e "${GREEN}✅ SSH session completed${NC}"

# Test the application again
echo -e "${BLUE}🧪 Testing application after migrations...${NC}"

# Test health endpoint
HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "https://$APP_URL/health" || echo "000")
if [ "$HEALTH_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${YELLOW}⚠️  Health check failed (HTTP $HEALTH_RESPONSE)${NC}"
fi

# Test API endpoint
API_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "https://$APP_URL/api" || echo "000")
if [ "$API_RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ API endpoint working${NC}"
else
    echo -e "${YELLOW}⚠️  API endpoint failed (HTTP $API_RESPONSE)${NC}"
fi

# Show useful commands
echo -e "${BLUE}🔧 Useful Commands:${NC}"
echo -e "${BLUE}   View logs: az webapp log tail --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   Restart app: az webapp restart --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   View settings: az webapp config appsettings list --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"

# Show app status
echo -e "${BLUE}📊 App Status:${NC}"
az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "{name:name, state:state, defaultHostName:defaultHostName, resourceGroup:resourceGroup}" --output table

echo -e "${GREEN}🎉 Migration process completed!${NC}"
echo -e "${GREEN}🌐 Your app is available at: https://$APP_URL${NC}" 