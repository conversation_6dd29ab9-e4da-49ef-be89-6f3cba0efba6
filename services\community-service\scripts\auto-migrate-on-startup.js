#!/usr/bin/env node

/**
 * Auto Database Migration on Startup
 * This script will be called from app.js to ensure database schema exists
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Load config
let config;
try {
  config = require('../src/config/config');
} catch (error) {
  console.log('⚠️ Config not available, using environment variables directly');
  config = {
    DATABASE_URL: process.env.DATABASE_URL,
    DB_SSL: process.env.NODE_ENV === 'production',
    DB_POOL_MAX: parseInt(process.env.DB_POOL_MAX) || 10,
    DB_POOL_MIN: parseInt(process.env.DB_POOL_MIN) || 2,
    DB_IDLE_TIMEOUT: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
    DB_CONNECTION_TIMEOUT: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 5000
  };
}

// Simple logging
const log = (message, level = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : '✅';
  console.log(`${timestamp} ${prefix} [AUTO-MIGRATE] ${message}`);
};

async function autoMigrate() {
  let pool;
  
  try {
    log('Starting automatic database migration check...');
    
    if (!config.DATABASE_URL) {
      log('DATABASE_URL not found, skipping migration', 'warn');
      return false;
    }
    
    // Create database connection
    pool = new Pool({
      connectionString: config.DATABASE_URL,
      ssl: config.DB_SSL ? { rejectUnauthorized: false } : false,
      max: config.DB_POOL_MAX,
      min: config.DB_POOL_MIN,
      idleTimeoutMillis: config.DB_IDLE_TIMEOUT,
      connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT
    });
    
    log('Connecting to database for migration check...');
    const client = await pool.connect();
    
    // Check if required tables exist
    const requiredTables = ['users', 'posts', 'comments', 'likes'];
    const missingTables = [];
    
    for (const table of requiredTables) {
      try {
        const result = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [table]);
        
        if (!result.rows[0].exists) {
          missingTables.push(table);
        }
      } catch (error) {
        log(`Error checking table '${table}': ${error.message}`, 'error');
        missingTables.push(table);
      }
    }
    
    // If tables are missing, create them
    if (missingTables.length > 0) {
      log(`Missing tables detected: ${missingTables.join(', ')}`);
      log('Running automatic database migration...');
      
      try {
        // Read and execute schema file
        const schemaPath = path.join(__dirname, '..', 'migrations', 'clean_schema.sql');
        
        if (!fs.existsSync(schemaPath)) {
          log(`Schema file not found: ${schemaPath}`, 'error');
          client.release();
          return false;
        }
        
        const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
        
        log('Executing database schema migration...');
        await client.query(schemaSQL);
        log('Database migration completed successfully!');
        
        // Verify tables were created
        for (const table of missingTables) {
          const result = await client.query(`
            SELECT EXISTS (
              SELECT FROM information_schema.tables 
              WHERE table_schema = 'public' 
              AND table_name = $1
            );
          `, [table]);
          
          if (result.rows[0].exists) {
            log(`Table '${table}' created successfully`);
          } else {
            log(`Table '${table}' still missing after migration`, 'error');
          }
        }
        
      } catch (error) {
        log(`Database migration failed: ${error.message}`, 'error');
        client.release();
        return false;
      }
    } else {
      log('All required database tables exist');
    }
    
    client.release();
    log('Database migration check completed successfully');
    return true;
    
  } catch (error) {
    log(`Database migration check failed: ${error.message}`, 'error');
    return false;
  } finally {
    if (pool) {
      await pool.end();
    }
  }
}

// Export for use in app.js
module.exports = { autoMigrate };

// Run if called directly
if (require.main === module) {
  autoMigrate()
    .then(success => {
      if (success) {
        log('Auto migration completed successfully');
        process.exit(0);
      } else {
        log('Auto migration failed', 'error');
        process.exit(1);
      }
    })
    .catch(error => {
      log(`Auto migration error: ${error.message}`, 'error');
      process.exit(1);
    });
} 