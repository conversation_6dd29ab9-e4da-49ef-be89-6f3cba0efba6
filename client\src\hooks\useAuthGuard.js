/**
 * Authentication Guard Hook
 * Handles authentication requirements for community interactions
 */

import { useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

export const useAuthGuard = () => {
 const { isAuthenticated, user, loading } = useAuth();

 /**
  * Check if user is authenticated and handle unauthenticated actions
  * @param {Function} callback - Function to execute if authenticated
  * @param {Object} options - Configuration options
  * @returns {Function} - Wrapped function that checks auth before execution
  */
 const requireAuth = useCallback((callback, options = {}) => {
  const {
   showLoginModal = true,
   customMessage = 'Bạn cần đăng nhập để thực hiện hành động này',
   action = 'interact'
  } = options;

  return (...args) => {
   // Don't proceed if still loading
   if (loading) {
    return;
   }

   // Check if user is authenticated
   if (!isAuthenticated || !user) {
    // Show toast message
    toast.error(customMessage, {
     duration: 4000,
     position: 'top-center',
     style: {
      background: '#FEF2F2',
      color: '#DC2626',
      border: '1px solid #FECACA',
     },
    });

    // Trigger login modal if requested
    if (showLoginModal) {
     // Dispatch custom event to show login modal
     window.dispatchEvent(new CustomEvent('show-login-modal', {
      detail: { 
       action,
       message: customMessage,
       returnUrl: window.location.pathname
      }
     }));
    }

    return false;
   }

   // Check if email is verified
   if (!user.emailVerified) {
    toast.error('Vui lòng xác thực email trước khi tương tác', {
     duration: 4000,
     position: 'top-center',
     style: {
      background: '#FEF2F2',
      color: '#DC2626',
      border: '1px solid #FECACA',
     },
    });

    // Redirect to email verification
    window.dispatchEvent(new CustomEvent('show-email-verification', {
     detail: { email: user.email }
    }));

    return false;
   }

   // User is authenticated and verified, execute callback
   return callback(...args);
  };
 }, [isAuthenticated, user, loading]);

 /**
  * Specific guards for different community actions
  */
 const guards = {
  // Like/Unlike posts
  like: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'like',
    customMessage: 'Đăng nhập để thích bài viết này'
   });
  }, [requireAuth]),

  // Dislike posts
  dislike: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'dislike',
    customMessage: 'Đăng nhập để không thích bài viết này'
   });
  }, [requireAuth]),

  // Comment on posts
  comment: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'comment',
    customMessage: 'Đăng nhập để bình luận'
   });
  }, [requireAuth]),

  // Share posts
  share: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'share',
    customMessage: 'Đăng nhập để chia sẻ bài viết'
   });
  }, [requireAuth]),

  // Bookmark posts
  bookmark: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'bookmark',
    customMessage: 'Đăng nhập để lưu bài viết'
   });
  }, [requireAuth]),

  // Create posts
  createPost: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'create-post',
    customMessage: 'Đăng nhập để tạo bài viết mới'
   });
  }, [requireAuth]),

  // React to posts (emoji reactions)
  react: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'react',
    customMessage: 'Đăng nhập để thả cảm xúc'
   });
  }, [requireAuth]),

  // Follow users
  follow: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'follow',
    customMessage: 'Đăng nhập để theo dõi người dùng'
   });
  }, [requireAuth]),

  // Vote in polls
  vote: useCallback((callback) => {
   return requireAuth(callback, {
    action: 'vote',
    customMessage: 'Đăng nhập để bình chọn'
   });
  }, [requireAuth])
 };

 /**
  * Check authentication status without executing callback
  */
 const checkAuth = useCallback((options = {}) => {
  const { showMessage = true } = options;

  if (loading) {
   return { authenticated: false, loading: true };
  }

  if (!isAuthenticated || !user) {
   if (showMessage) {
    toast.error('Bạn cần đăng nhập để thực hiện hành động này');
   }
   return { authenticated: false, loading: false, reason: 'not-authenticated' };
  }

  if (!user.emailVerified) {
   if (showMessage) {
    toast.error('Vui lòng xác thực email trước khi tương tác');
   }
   return { authenticated: false, loading: false, reason: 'email-not-verified' };
  }

  return { authenticated: true, loading: false, user };
 }, [isAuthenticated, user, loading]);

 /**
  * Get user authentication state for UI rendering
  */
 const getAuthState = useCallback(() => {
  return {
   isAuthenticated,
   user,
   loading,
   isEmailVerified: user?.emailVerified || false,
   canInteract: isAuthenticated && user?.emailVerified
  };
 }, [isAuthenticated, user, loading]);

 return {
  requireAuth,
  guards,
  checkAuth,
  getAuthState,
  isAuthenticated,
  user,
  loading
 };
};

export default useAuthGuard;
