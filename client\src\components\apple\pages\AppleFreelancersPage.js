import React, { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
 MagnifyingGlassIcon,
 FunnelIcon,
 UserGroupIcon,
 StarIcon,
 MapPinIcon,

 ChevronDownIcon,
} from '@heroicons/react/24/outline';

const AppleFreelancersPage = () => {
 const { t } = useLanguage();
 const [searchQuery, setSearchQuery] = useState('');
 const [selectedCategory, setSelectedCategory] = useState('all');
 const [selectedLocation, setSelectedLocation] = useState('all');
 const [selectedRate, setSelectedRate] = useState('all');
 const [sortBy, setSortBy] = useState('rating');
 const [showFilters, setShowFilters] = useState(false);

 const headerRef = useRef(null);

 // Apple-style animations
 useEffect(() => {
  if (headerRef.current) {
   gsap.fromTo(
    headerRef.current,
    { y: 30, opacity: 0 },
    { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
   );
  }
 }, []);

 // Mock freelancers data
 const freelancers = [
  {
   id: 1,
   name: 'Sarah <PERSON>',
   title: 'Full-Stack Developer',
   avatar: null,
   rating: 4.9,
   reviewCount: 127,
   hourlyRate: 85,
   location: 'San Francisco, CA',
   skills: ['React', 'Node.js', 'TypeScript', 'AWS'],
   description:
    'Experienced full-stack developer with 8+ years building scalable web applications. Specialized in React, Node.js, and cloud architecture.',
   completedProjects: 89,
   responseTime: '1 hour',
   verified: true,
   availability: 'Available',
  },
  {
   id: 2,
   name: 'Michael Chen',
   title: 'UI/UX Designer',
   avatar: null,
   rating: 4.8,
   reviewCount: 94,
   hourlyRate: 75,
   location: 'Remote',
   skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
   description:
    'Creative UI/UX designer passionate about creating intuitive and beautiful user experiences. 6+ years of experience with top tech companies.',
   completedProjects: 156,
   responseTime: '2 hours',
   verified: true,
   availability: 'Available',
  },
  {
   id: 3,
   name: 'Emily Rodriguez',
   title: 'Content Writer & SEO Specialist',
   avatar: null,
   rating: 4.7,
   reviewCount: 203,
   hourlyRate: 45,
   location: 'Austin, TX',
   skills: ['Content Writing', 'SEO', 'Copywriting', 'Marketing'],
   description:
    'Professional content writer and SEO specialist helping businesses grow their online presence through engaging content and strategic optimization.',
   completedProjects: 312,
   responseTime: '30 minutes',
   verified: true,
   availability: 'Busy',
  },
  {
   id: 4,
   name: 'David Kim',
   title: 'Mobile App Developer',
   avatar: null,
   rating: 4.9,
   reviewCount: 76,
   hourlyRate: 90,
   location: 'Seattle, WA',
   skills: ['React Native', 'Flutter', 'iOS', 'Android'],
   description:
    'Mobile app developer with expertise in cross-platform development. Built 50+ apps with millions of downloads across iOS and Android.',
   completedProjects: 67,
   responseTime: '1 hour',
   verified: true,
   availability: 'Available',
  },
  {
   id: 5,
   name: 'Lisa Thompson',
   title: 'Digital Marketing Strategist',
   avatar: null,
   rating: 4.8,
   reviewCount: 145,
   hourlyRate: 65,
   location: 'New York, NY',
   skills: ['Digital Marketing', 'PPC', 'Social Media', 'Analytics'],
   description:
    'Results-driven digital marketing strategist with proven track record of increasing ROI by 300%+ for clients across various industries.',
   completedProjects: 198,
   responseTime: '1 hour',
   verified: true,
   availability: 'Available',
  },
  {
   id: 6,
   name: 'Alex Johnson',
   title: 'Data Scientist',
   avatar: null,
   rating: 4.9,
   reviewCount: 89,
   hourlyRate: 95,
   location: 'Remote',
   skills: ['Python', 'Machine Learning', 'SQL', 'Tableau'],
   description:
    'Data scientist with PhD in Statistics and 7+ years experience turning complex data into actionable business insights using ML and AI.',
   completedProjects: 54,
   responseTime: '2 hours',
   verified: true,
   availability: 'Available',
  },
 ];

 const categories = [
  { value: 'all', label: t('allCategories') },
  { value: 'development', label: t('development') },
  { value: 'design', label: t('designCategory') },
  { value: 'writing', label: t('writingCategory') },
  { value: 'marketing', label: t('marketingCategory') },
  { value: 'data-science', label: t('dataScience') },
 ];

 const locations = [
  { value: 'all', label: t('allLocations') },
  { value: 'remote', label: t('remote') },
  { value: 'us', label: t('unitedStates') },
  { value: 'europe', label: t('europe') },
  { value: 'asia', label: t('asia') },
 ];

 const rateRanges = [
  { value: 'all', label: t('anyRate') },
  { value: '0-25', label: t('rate0_25') },
  { value: '25-50', label: t('rate25_50') },
  { value: '50-75', label: t('rate50_75') },
  { value: '75+', label: t('rate75Plus') },
 ];

 const sortOptions = [
  { value: 'rating', label: t('highestRated') },
  { value: 'rate-low', label: t('lowestRate') },
  { value: 'rate-high', label: t('highestRate') },
  { value: 'reviews', label: t('mostReviews') },
 ];



 return (
  <div className='min-h-screen bg-gray-50 transition-colors duration-300'>
   <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8'>
    {/* Header */}
    <div ref={headerRef} className='text-center mb-12'>
     <div className='inline-flex items-center space-x-3 mb-6'>
      <div className='w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center shadow-sm'>
       <UserGroupIcon className='h-6 w-6 text-white' />
      </div>
     </div>

     <h1 className='text-4xl md:text-5xl font-bold text-gray-900 mb-4 transition-colors duration-300'>
      {t('findTalentedFreelancers')}
     </h1>
     <p className='text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed transition-colors duration-300'>
      {t('connectWithProfessionals')}
     </p>
    </div>

    {/* Search and Filters */}
    <div className='bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8 transition-colors duration-300'>
     {/* Search Bar */}
     <div className='relative mb-6'>
      <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
       <MagnifyingGlassIcon className='h-5 w-5 text-gray-400' />
      </div>
      <input
       type='text'
       placeholder={t('searchFreelancersPlaceholder')}
       value={searchQuery}
       onChange={e => setSearchQuery(e.target.value)}
       className='block w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl text-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-900 transition-colors duration-300'
      />
     </div>

     {/* Filter Toggle */}
     <div className='flex items-center justify-between'>
      <button
       onClick={() => setShowFilters(!showFilters)}
       className='inline-flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200'
      >
       <FunnelIcon className='h-4 w-4' />
       <span>{t('filters')}</span>
       <ChevronDownIcon
        className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`}
       />
      </button>

      <div className='flex items-center space-x-4'>
       <span className='text-sm text-gray-600 transition-colors duration-300'>
        {freelancers.length} {t('freelancersFound')}
       </span>
       <select
        value={sortBy}
        onChange={e => setSortBy(e.target.value)}
        className='px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 transition-colors duration-300'
       >
        {sortOptions.map(option => (
         <option key={option.value} value={option.value}>
          {option.label}
         </option>
        ))}
       </select>
      </div>
     </div>

     {/* Filters */}
     {showFilters ? (
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-200 transition-colors duration-300'>
       <div>
        <label className='form-label text-gray-700 transition-colors duration-300'>{t('category')}</label>
        <select
         value={selectedCategory}
         onChange={e => setSelectedCategory(e.target.value)}
         className='form-input bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300'
        >
         {categories.map(category => (
          <option key={category.value} value={category.value}>
           {category.label}
          </option>
         ))}
        </select>
       </div>

       <div>
        <label className='form-label text-gray-700 transition-colors duration-300'>{t('location')}</label>
        <select
         value={selectedLocation}
         onChange={e => setSelectedLocation(e.target.value)}
         className='form-input bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300'
        >
         {locations.map(location => (
          <option key={location.value} value={location.value}>
           {location.label}
          </option>
         ))}
        </select>
       </div>

       <div>
        <label className='form-label text-gray-700 transition-colors duration-300'>{t('hourlyRate')}</label>
        <select
         value={selectedRate}
         onChange={e => setSelectedRate(e.target.value)}
         className='form-input bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300'
        >
         {rateRanges.map(range => (
          <option key={range.value} value={range.value}>
           {range.label}
          </option>
         ))}
        </select>
       </div>
      </div>
     ) : null}
    </div>

    {/* Freelancers Grid */}
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
     {freelancers.map(freelancer => (
      <div
       key={freelancer.id}
       className='bg-white rounded-2xl p-6 border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-lg'
      >
       {/* Header */}
       <div className='flex items-start justify-between mb-4'>
        <div className='flex items-center space-x-3'>
         <div className='w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center'>
          <span className='text-lg font-semibold text-gray-600'>
           {freelancer.name.charAt(0)}
          </span>
         </div>
         <div>
          <h3 className='font-semibold text-gray-900 transition-colors duration-300'>
           {freelancer.name}
          </h3>
          <p className='text-sm text-gray-600 transition-colors duration-300'>
           {freelancer.title}
          </p>
         </div>
        </div>
        <div className='flex items-center space-x-1'>
         <StarIcon className='h-4 w-4 text-yellow-400' />
         <span className='text-sm font-medium text-gray-900 transition-colors duration-300'>
          {freelancer.rating}
         </span>
         <span className='text-sm text-gray-500 transition-colors duration-300'>
          ({freelancer.reviewCount})
         </span>
        </div>
       </div>

       {/* Description */}
       <p className='text-gray-600 text-sm mb-4 leading-relaxed transition-colors duration-300'>
        {freelancer.description}
       </p>

       {/* Skills */}
       <div className='mb-4'>
        <div className='flex flex-wrap gap-2'>
         {freelancer.skills.slice(0, 4).map((skill, idx) => (
          <span
           key={idx}
           className='inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 transition-colors duration-300'
          >
           {skill}
          </span>
         ))}
         {freelancer.skills.length > 4 && (
          <span className='inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-500 transition-colors duration-300'>
           +{freelancer.skills.length - 4} more
          </span>
         )}
        </div>
       </div>

       {/* Stats */}
       <div className='grid grid-cols-3 gap-4 pt-4 border-t border-gray-100 transition-colors duration-300'>
        <div className='text-center'>
         <div className='text-lg font-semibold text-gray-900 transition-colors duration-300'>
          {freelancer.completedProjects}
         </div>
         <div className='text-xs text-gray-600 transition-colors duration-300'>{t('projectsCount')}</div>
        </div>
        <div className='text-center'>
         <div className='text-lg font-semibold text-gray-900 transition-colors duration-300'>
          {freelancer.responseTime}
         </div>
         <div className='text-xs text-gray-600 transition-colors duration-300'>{t('response')}</div>
        </div>
        <div className='text-center'>
         <div className='flex items-center justify-center space-x-1'>
          <MapPinIcon className='h-4 w-4 text-gray-400' />
          <span className='text-sm text-gray-600 truncate transition-colors duration-300'>
           {freelancer.location}
          </span>
         </div>
        </div>
       </div>
      </div>
     ))}
    </div>

    {/* Load More */}
    <div className='text-center mt-12'>
     <button className='btn btn-primary btn-large'>
      {t('loadMoreFreelancers')}
     </button>
    </div>
   </div>
  </div>
 );
};

export default AppleFreelancersPage;
