.freelancer-team-mock {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Main View Styles */
.team-main-view {
  text-align: center;
}

.team-hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 20px;
  border-radius: 20px;
  margin-bottom: 40px;
}

.team-hero-section h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.team-slogan {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.team-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.team-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 40px;
}

.btn-create-team, .btn-browse-teams {
  padding: 15px 30px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-team {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-browse-teams {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-create-team:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-browse-teams:hover {
  background: #667eea;
  color: white;
}

.team-requirements {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.team-requirements h3 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 1.5rem;
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.requirement-item:hover {
  transform: translateY(-5px);
}

.requirement-icon {
  font-size: 2rem;
  min-width: 50px;
}

.requirement-item h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.requirement-item p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Team Creation View Styles */
.team-creation-view {
  max-width: 800px;
  margin: 0 auto;
}

.creation-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

.btn-back {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #667eea;
  font-size: 1rem;
  cursor: pointer;
  padding: 10px;
}

.creation-header h2 {
  color: #333;
  margin-bottom: 20px;
}

.step-indicator {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step.active {
  background: #667eea;
  color: white;
}

.creation-step {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.creation-step h3 {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.btn-next, .btn-confirm {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 20px;
}

.btn-next:hover, .btn-confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.step-buttons {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.step-buttons .btn-back,
.step-buttons .btn-next {
  flex: 1;
  margin-top: 0;
}

.member-info {
  margin-bottom: 30px;
}

.member-info h4 {
  color: #333;
  margin-bottom: 15px;
}

.leader-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 15px;
  margin-bottom: 20px;
}

.member-avatar {
  font-size: 2rem;
  min-width: 50px;
}

.member-details p {
  margin: 5px 0;
  color: #333;
}

.member-input {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 20px;
}

.profit-sharing {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.profit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
}

.profit-item input {
  width: 80px;
  text-align: center;
}

.terms-notice {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
}

.link-terms {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.team-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.team-summary h4 {
  margin-bottom: 15px;
  color: #333;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
  border-bottom: none;
}

/* Browse Teams View Styles */
.browse-teams-view {
  max-width: 1200px;
  margin: 0 auto;
}

.browse-header {
  margin-bottom: 40px;
  position: relative;
}

.browse-header h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.search-filters {
  display: flex;
  gap: 15px;
  max-width: 600px;
  margin: 0 auto;
}

.search-input, .filter-select {
  flex: 1;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
}

.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.team-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.team-card:hover {
  transform: translateY(-5px);
}

.team-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.team-logo {
  font-size: 3rem;
  min-width: 60px;
}

.team-info h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.team-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.team-members h4 {
  color: #333;
  margin-bottom: 15px;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.member-name {
  font-weight: 600;
  color: #333;
}

.member-role {
  color: #666;
  font-size: 0.9rem;
}

.member-rating {
  color: #ffc107;
  font-weight: 600;
}

.team-stats {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
  padding: 15px 0;
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.stat {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  font-weight: 600;
  color: #333;
}

.team-actions {
  display: flex;
  gap: 10px;
}

.team-actions button {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-hire {
  background: #667eea;
  color: white;
}

.btn-chat {
  background: #28a745;
  color: white;
}

.btn-save {
  background: #6c757d;
  color: white;
}

.btn-hire:hover, .btn-chat:hover, .btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Team Profile View Styles */
.team-profile-view {
  max-width: 1000px;
  margin: 0 auto;
}

.profile-header {
  position: relative;
  margin-bottom: 40px;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 30px;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
}

.team-logo-large {
  font-size: 4rem;
  min-width: 80px;
}

.team-details h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
}

.team-rating {
  display: flex;
  gap: 20px;
  margin-top: 15px;
  opacity: 0.9;
}

.profile-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.profile-section {
  margin-bottom: 40px;
}

.profile-section:last-child {
  margin-bottom: 0;
}

.profile-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.member-profile {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 15px;
}

.member-profile .member-avatar {
  font-size: 2.5rem;
}

.member-profile .member-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.member-profile .member-info p {
  margin: 5px 0;
  color: #666;
}

.member-stats {
  margin-top: 10px;
}

.member-stats span {
  margin-right: 15px;
  color: #667eea;
  font-weight: 600;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.project-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  position: relative;
}

.project-item h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.project-item p {
  color: #666;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.project-budget {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #667eea;
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.profile-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.profile-actions button {
  padding: 15px 30px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-hire-large {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-chat-large {
  background: #28a745;
  color: white;
}

.btn-save-large {
  background: #6c757d;
  color: white;
}

.profile-actions button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

/* Floating animation for hero elements */
.hero-float {
  animation: float 6s ease-in-out infinite;
}

/* Pulse glow for active elements */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Shimmer effect for loading states */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced glass morphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Improved focus states */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Smooth transitions for all interactive elements */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover states for cards */
.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Modern scrollbar */
.modern-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.modern-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.6);
  border-radius: 10px;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.8);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-title {
    font-size: 4rem !important;
  }
  
  .hero-subtitle {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem !important;
  }
  
  .hero-subtitle {
    font-size: 1.125rem !important;
  }
  
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Stack stats vertically on mobile */
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  /* Adjust card padding on mobile */
  .mobile-card-padding {
    padding: 1rem;
  }
  
  /* Stack action buttons on mobile */
  .mobile-stack {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  /* Reduce spacing on mobile */
  .mobile-spacing {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: 2rem !important;
  }
  
  /* Hide secondary text on very small screens */
  .hide-on-small {
    display: none;
  }
  
  /* Adjust step indicator for mobile */
  .step-mobile {
    flex-direction: column;
    gap: 1rem;
  }
  
  /* Single column layout for very small screens */
  .force-single-column {
    grid-template-columns: 1fr !important;
  }
}



/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .hero-float,
  .pulse-glow,
  .shimmer {
    animation: none;
  }
  
  .smooth-transition {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glass {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }
} 