import { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import {
 UserGroupIcon,
 ChatBubbleLeftRightIcon,
 HeartIcon,
 ShareIcon,
 BookmarkIcon,
 ClockIcon,
 EyeIcon,
 PlusIcon,
 FireIcon,
 ArrowTrendingUpIcon,
 MagnifyingGlassIcon,
 FunnelIcon,
 SparklesIcon,
 TrophyIcon,
 BoltIcon,
 CodeBracketIcon,
 LightBulbIcon,
 NewspaperIcon,
 BriefcaseIcon,
 QuestionMarkCircleIcon,
 AcademicCapIcon,
 HandThumbUpIcon,
 HandThumbDownIcon,
 ChatBubbleOvalLeftEllipsisIcon,
 PhotoIcon,
 LinkIcon,
 CheckBadgeIcon,
 StarIcon,
 GlobeAltIcon,
 CalendarIcon,
 UsersIcon,
 ChartBarIcon,
 XMarkIcon,
} from '@heroicons/react/24/outline';
import {
 HeartIcon as HeartSolidIcon,
 BookmarkIcon as BookmarkSolidIcon,
 FireIcon as FireSolidIcon,
} from '@heroicons/react/24/solid';
import { useLanguage } from '../../../contexts/LanguageContext';
import dataService from '../../../services/dataService';
import ApiStatusIndicator from '../../common/ApiStatusIndicator';
import LoginModal from '../../common/LoginModal';
import useAuthGuard from '../../../hooks/useAuthGuard';
import CommunityInteractions from '../community/CommunityInteractions';
import CommentSection from '../community/CommentSection';
import UserProfileModal from '../community/UserProfileModal';
import { toast } from 'react-hot-toast';
import { likesApi, communityUtils } from '../../../services/communityApiService';
import CommunitySearch from '../community/CommunitySearch';
import CommunityResponsive, {
 ResponsiveCard,
 ResponsiveText,
 ResponsiveButton
} from '../community/CommunityResponsive';
import { useAuth } from '../../../contexts/AuthContext';
import { getIdToken } from 'firebase/auth';
import { auth } from '../../../config/firebase';
import { useNavigate } from 'react-router-dom';

// Generate sample comments for posts
const generateSampleComments = (postId) => {
 const sampleComments = {
  'post1': [
   {
    id: 'comment-1',
    content: 'Chúc mừng bạn! Sản phẩm trông rất tuyệt vời 🎉',
    author: {
     id: 'user2',
     name: 'Marcus Chen',
     avatar: '/api/placeholder/32/32',
     username: '@marcus_design'
    },
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    likes: 5,
    replies: []
   },
   {
    id: 'comment-2',
    content: 'Có thể chia sẻ tech stack bạn đã sử dụng không?',
    author: {
     id: 'user3',
     name: 'Sarah Kim',
     avatar: '/api/placeholder/32/32',
     username: '@sarah_mobile'
    },
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    likes: 2,
    replies: []
   }
  ],
  'post2': [
   {
    id: 'comment-3',
    content: 'Thử nghiệm với các dự án nhỏ trước khi nhận dự án lớn. Điều này giúp xây dựng portfolio và kinh nghiệm.',
    author: {
     id: 'user4',
     name: 'David Thompson',
     avatar: '/api/placeholder/32/32',
     username: '@david_backend'
    },
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
    likes: 8,
    replies: []
   }
  ]
 };

 return sampleComments[postId] || [];
};

const AppleCommunityPage = () => {
 const { t } = useLanguage();
 const navigate = useNavigate();
 const { guards, getAuthState } = useAuthGuard();
 const { user } = useAuth();
 const [selectedCategory, setSelectedCategory] = useState('all');
 const [selectedSort, setSelectedSort] = useState('recent');
 const [searchQuery, setSearchQuery] = useState('');
 const [showFilters, setShowFilters] = useState(false);
 const [posts, setPosts] = useState([]);
 const [categories, setCategories] = useState([]);
 const [stats, setStats] = useState({});
 const [loading, setLoading] = useState(true);
 const [likedPosts, setLikedPosts] = useState(() => {
  // Try to restore liked posts from localStorage on initial load
  try {
   const authState = getAuthState();
   if (authState.isAuthenticated && authState.user?.uid) {
    const stored = localStorage.getItem(`likedPosts_${authState.user.uid}`);
    if (stored) {
     const likedArray = JSON.parse(stored);
     console.log('🔄 Restored liked posts from localStorage:', likedArray);
     return new Set(likedArray);
    }
   }
  } catch (error) {
   console.error('Error restoring liked posts from localStorage:', error);
  }
  return new Set();
 });

 const [bookmarkedPosts, setBookmarkedPosts] = useState(new Set());
 const [userInteractions, setUserInteractions] = useState({});
 const [searchSuggestions, setSearchSuggestions] = useState([
  { text: 'React hooks', type: 'topic', count: 45 },
  { text: 'JavaScript tips', type: 'topic', count: 67 },
  { text: '@elena_dev', type: 'user', count: 12 },
  { text: '#freelancing', type: 'tag', count: 234 },
  { text: 'Node.js best practices', type: 'topic', count: 89 }
 ]);
 const [recentSearches, setRecentSearches] = useState([
  'React performance optimization',
  'Freelance pricing strategies',
  'Remote work tools'
 ]);
 const [showMobileMenu, setShowMobileMenu] = useState(null);
 const [showProfileModal, setShowProfileModal] = useState(false);
 const [showCreatePostModal, setShowCreatePostModal] = useState(false);
 const [createPostForm, setCreatePostForm] = useState({
  title: '',
  content: '',
  category: 'general',
  tags: []
 });
 const [submittingPost, setSubmittingPost] = useState(false);



 const headerRef = useRef(null);
 const postsRef = useRef(null);
 const statsRef = useRef(null);

 // Load posts data
 useEffect(() => {
  const loadPosts = async () => {
   setLoading(true);
   try {
    console.log('🔄 Loading community posts...');
    
    // Only load posts and categories - skip stats for better performance
    const [postsData, categoriesData] = await Promise.all([
     dataService.getCommunityPosts({
      category: selectedCategory === 'all' ? undefined : selectedCategory,
      sort: selectedSort
     }),
     dataService.getCommunityCategories()
    ]);

    console.log('📊 Raw posts data from API:', postsData);

    // Transform API data to match component expectations
    const postsWithComments = postsData.map(post => {
     // Map author data correctly
     const firstName = post.author?.firstName || '';
     const lastName = post.author?.lastName || '';
     const fullName = `${firstName} ${lastName}`.trim();
     
     const author = {
      id: post.author?.id || 'unknown',
      name: fullName || 'Anonymous User',
      avatar: post.author?.avatarUrl || 'https://via.placeholder.com/40x40/6366f1/ffffff?text=U',
      username: post.author?.username || `@${(firstName || 'user').toLowerCase()}`,
      title: post.author?.title || 'Community Member',
      location: post.author?.location || 'Unknown',
      verified: post.author?.verified || false
     };

     // Parse tags from JSON string to array
     let tags = [];
     try {
      if (post.tags) {
       tags = typeof post.tags === 'string' ? JSON.parse(post.tags) : post.tags;
      }
     } catch (error) {
      console.warn('Failed to parse tags for post:', post.id, error);
      tags = [];
     }

     return {
      ...post,
      // Map post fields
      comments: post.commentCount || 0,
      shares: post.shareCount || 0,
      views: post.viewCount || 0,
      category: post.category || 'general',
      tags: tags, // Use parsed tags
      timestamp: post.createdAt || new Date().toISOString(), // Map createdAt to timestamp for compatibility
      
      // Map author
      author,
      
      // Add sample comments and handlers
      existingComments: generateSampleComments(post.id),
      onCommentAdded: (postId) => {
       // Update comment count when new comment is added
       setPosts(prevPosts =>
        prevPosts.map(p =>
         p.id === postId
          ? { ...p, comments: p.comments + 1 }
          : p
        )
       );
      }
     };
    });

    console.log('✅ Transformed posts data:', postsWithComments);
    setPosts(postsWithComments);
    setCategories(categoriesData);
    
    // Set basic stats without API call for better performance
    setStats({
     totalPosts: postsData.length,
     totalUsers: 50, // Placeholder
     totalComments: 200, // Placeholder
     activeUsers: 25 // Placeholder
    });
   } catch (error) {
    console.error('❌ Error loading community data:', error);
    
    // Show user-friendly error message
    toast.error('Không thể tải bài viết. Vui lòng thử lại!', {
     duration: 4000,
     position: 'bottom-center'
    });
    
    // Set empty arrays as fallback
    setPosts([]);
    setCategories([]);
    setStats({});
   } finally {
    setLoading(false);
   }
  };

  loadPosts();
 }, [selectedCategory, selectedSort]);

 // Optimized like status loading - only when authenticated
 useEffect(() => {
  const loadLikeStatus = async () => {
   if (posts.length === 0) return; // Wait for posts to load first

   const authState = getAuthState();
   
   // Skip like status loading if not authenticated
   if (!authState.isAuthenticated) {
    console.log('⏭️ Skipping like status loading - user not authenticated');
    setLikedPosts(new Set());
    return;
   }

   const userId = communityUtils.getCurrentUserId(authState.user);
   if (!userId) {
    console.log('⚠️ No valid user ID found');
    setLikedPosts(new Set());
    return;
   }

   console.log('🔍 Loading like status for posts, userId:', userId);
   console.log('📝 Posts to check:', posts.map(p => p.id));

   try {
    const likePromises = posts.map(post =>
     likesApi.checkLikeStatus(post.id, 'post', userId)
      .then(status => ({ postId: post.id, liked: status.liked }))
      .catch(error => {
       console.error(`Error checking like status for post ${post.id}:`, error);
       return { postId: post.id, liked: false };
      })
    );

    const likeStatuses = await Promise.all(likePromises);
    const likedSet = new Set();
    likeStatuses.forEach(status => {
     if (status.liked) {
      likedSet.add(status.postId);
     }
    });

    console.log('✅ Loaded like statuses:', likeStatuses);
    console.log('🔍 Setting likedPosts to:', Array.from(likedSet));
    setLikedPosts(likedSet);
   } catch (error) {
    console.error('Error loading like status:', error);
    setLikedPosts(new Set());
   }
  };

  loadLikeStatus();
 }, [posts, getAuthState().isAuthenticated, getAuthState().user?.uid]); // Depend on posts and auth state

 // Save liked posts to localStorage whenever it changes
 useEffect(() => {
  const authState = getAuthState();
  if (authState.isAuthenticated && authState.user?.uid && likedPosts.size > 0) {
   try {
    const likedArray = Array.from(likedPosts);
    localStorage.setItem(`likedPosts_${authState.user.uid}`, JSON.stringify(likedArray));
    console.log('💾 Saved liked posts to localStorage:', likedArray);
   } catch (error) {
    console.error('Error saving liked posts to localStorage:', error);
   }
  }
 }, [likedPosts, getAuthState().user?.uid]);

 // Function to refresh like status for a specific post
 const refreshPostLikeStatus = async (postId) => {
  const authState = getAuthState();
  if (!authState.isAuthenticated) return;

  const userId = communityUtils.getCurrentUserId(authState.user);
  if (!userId) return;

  try {
   const likeStatus = await likesApi.checkLikeStatus(postId, 'post', userId);

   setLikedPosts(prev => {
    const newSet = new Set(prev);
    if (likeStatus.liked) {
     newSet.add(postId);
    } else {
     newSet.delete(postId);
    }
    return newSet;
   });

   console.log(`🔄 Refreshed like status for post ${postId}:`, likeStatus.liked);
  } catch (error) {
   console.error(`Error refreshing like status for post ${postId}:`, error);
  }
 };

 // Apple-style animations
 useEffect(() => {
  if (headerRef.current) {
   gsap.fromTo(
    headerRef.current,
    { y: 30, opacity: 0 },
    { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
   );
  }

  if (statsRef.current) {
   gsap.fromTo(
    statsRef.current.children,
    { y: 20, opacity: 0 },
    { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, delay: 0.3, ease: 'power2.out' }
   );
  }

  if (postsRef.current && !loading) {
   gsap.fromTo(
    postsRef.current.children,
    { y: 30, opacity: 0 },
    { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, delay: 0.5, ease: 'power2.out' }
   );
  }
 }, [loading]);

 // Enhanced interaction handlers with API integration
 const handleLike = guards.like(async (postId) => {
  const isCurrentlyLiked = likedPosts.has(postId);

  // Store original like count for potential rollback
  const originalPost = posts.find(p => p.id === postId);
  const originalLikeCount = originalPost?.like_count || originalPost?.likes || 0;

  // Get user from AuthContext
  const authState = getAuthState();
  const userId = communityUtils.getCurrentUserId(authState.user);

  console.log('🔍 handleLike Debug:', {
   isAuthenticated: authState.isAuthenticated,
   canInteract: authState.canInteract,
   user: authState.user ? { uid: authState.user.uid, email: authState.user.email } : null,
   userId: userId
  });

  // Check if user is properly authenticated
  if (!userId || !authState.isAuthenticated) {
   toast.error('Vui lòng đăng nhập để thích bài viết', {
    duration: 3000,
    position: 'bottom-center'
   });
   return;
  }

  try {
   console.log('🎯 Optimistic update:', {
    postId,
    isCurrentlyLiked,
    userId,
    action: isCurrentlyLiked ? 'unlike' : 'like',
    currentLikedPosts: Array.from(likedPosts)
   });

   // Optimistic update - update UI immediately
   setLikedPosts(prev => {
    const newSet = new Set(prev);
    if (isCurrentlyLiked) {
     newSet.delete(postId);
    } else {
     newSet.add(postId);
    }
    console.log('🎯 New likedPosts state:', Array.from(newSet));
    return newSet;
   });

   // Optimistic update for like count (will be corrected by server response)
   setPosts(prevPosts =>
    prevPosts.map(post => {
     if (post.id === postId) {
      const newLikes = isCurrentlyLiked
       ? Math.max(0, (post.like_count || post.likes || 0) - 1)
       : (post.like_count || post.likes || 0) + 1;
      return { ...post, likes: newLikes, like_count: newLikes };
     }
     return post;
    })
   );

   // Update user interactions
   setUserInteractions(prev => ({
    ...prev,
    [postId]: {
     ...prev[postId],
     liked: !isCurrentlyLiked
    }
   }));

   // Call API to persist the change
   const result = await likesApi.toggleLike(postId, 'post');

   console.log('✅ Like API result:', result);

   // Always update with server data to ensure accuracy
   setLikedPosts(prev => {
    const newSet = new Set(prev);
    if (result.liked) {
     newSet.add(postId);
    } else {
     newSet.delete(postId);
    }
    return newSet;
   });

   // Update like count with server data
   setPosts(prevPosts =>
    prevPosts.map(post => {
     if (post.id === postId) {
      const serverLikeCount = result.likeCount || 0;
      console.log(`📊 Updated post ${postId} like count to:`, serverLikeCount);
      return {
       ...post,
       likes: serverLikeCount,
       like_count: serverLikeCount
      };
     }
     return post;
    })
   );

   // Update user interactions (redundant but kept for consistency)
   setUserInteractions(prev => ({
    ...prev,
    [postId]: {
     ...prev[postId],
     liked: result.liked
    }
   }));

   console.log('🎯 Final state after like:', {
    postId,
    serverLiked: result.liked,
    likedPostsHas: result.liked ? 'will have' : 'will not have',
    likeCount: result.likeCount
   });

   // Show feedback toast
   if (result.liked) {
    toast.success('👍 Đã thích bài viết!', {
     duration: 2000,
     position: 'bottom-center'
    });
   } else {
    toast.success('💔 Đã bỏ thích bài viết', {
     duration: 2000,
     position: 'bottom-center'
    });
   }

   // Refresh like status to ensure consistency (optional, for extra safety)
   setTimeout(() => {
    refreshPostLikeStatus(postId);
   }, 500);

  } catch (error) {
   // Revert optimistic update on error
   setLikedPosts(prev => {
    const newSet = new Set(prev);
    if (isCurrentlyLiked) {
     newSet.add(postId);
    } else {
     newSet.delete(postId);
    }
    return newSet;
   });

   // Revert post count to original
   setPosts(prevPosts =>
    prevPosts.map(post => {
     if (post.id === postId) {
      console.log(`🔄 Reverted post ${postId} likes to original:`, originalLikeCount);
      return { ...post, likes: originalLikeCount, like_count: originalLikeCount };
     }
     return post;
    })
   );

   // Revert user interactions
   setUserInteractions(prev => ({
    ...prev,
    [postId]: {
     ...prev[postId],
     liked: isCurrentlyLiked
    }
   }));

   console.error('❌ Error toggling like:', error);

   // Show error message based on error type
   const errorMessage = error.message.includes('logged in')
    ? 'Vui lòng đăng nhập để thích bài viết'
    : 'Không thể thích bài viết. Vui lòng thử lại!';

   toast.error(errorMessage, {
    duration: 3000,
    position: 'bottom-center'
   });
  }
 });



 const handleBookmark = guards.bookmark((postId) => {
  setBookmarkedPosts(prev => {
   const newSet = new Set(prev);
   if (newSet.has(postId)) {
    newSet.delete(postId);
   } else {
    newSet.add(postId);
   }
   return newSet;
  });

  // Update user interactions
  setUserInteractions(prev => ({
   ...prev,
   [postId]: {
    ...prev[postId],
    bookmarked: !prev[postId]?.bookmarked
   }
  }));
 });

 const handleShare = guards.share((postId) => {
  // Update share count in posts
  setPosts(prevPosts =>
   prevPosts.map(post =>
    post.id === postId
     ? { ...post, shares: (post.shares || 0) + 1 }
     : post
   )
  );
 });

 const handleReaction = guards.react((postId, reaction) => {
  // Update reactions in posts
  setPosts(prevPosts =>
   prevPosts.map(post => {
    if (post.id === postId) {
     const newReactions = { ...post.reactions };
     newReactions[reaction.emoji] = (newReactions[reaction.emoji] || 0) + 1;
     return { ...post, reactions: newReactions };
    }
    return post;
   })
  );
 });

 const handleFiltersChange = (newFilters) => {
  // This would trigger a new data fetch with filters
  console.log('Filters changed:', newFilters);
  // For now, we'll just log it. In a real app, this would refetch data
 };

 // Utility functions
 const getCategoryIcon = (categoryId) => {
  const iconMap = {
   'all': <ChatBubbleLeftRightIcon className="h-5 w-5" />,
   'tips': <LightBulbIcon className="h-5 w-5" />,
   'showcase': <SparklesIcon className="h-5 w-5" />,
   'discussion': <ChatBubbleOvalLeftEllipsisIcon className="h-5 w-5" />,
   'help': <QuestionMarkCircleIcon className="h-5 w-5" />,
   'news': <NewspaperIcon className="h-5 w-5" />,
   'jobs': <BriefcaseIcon className="h-5 w-5" />,
   'tools': <CodeBracketIcon className="h-5 w-5" />,
   'success': <TrophyIcon className="h-5 w-5" />,
   'learning': <AcademicCapIcon className="h-5 w-5" />
  };
  return iconMap[categoryId] || <ChatBubbleLeftRightIcon className="h-5 w-5" />;
 };

 const getPostTypeIcon = (type) => {
  const iconMap = {
   'text': <ChatBubbleLeftRightIcon className="h-4 w-4" />,
   'discussion': <ChatBubbleOvalLeftEllipsisIcon className="h-4 w-4" />,
   'tip': <LightBulbIcon className="h-4 w-4" />,
   'help': <QuestionMarkCircleIcon className="h-4 w-4" />,
   'news': <NewspaperIcon className="h-4 w-4" />,
   'job': <BriefcaseIcon className="h-4 w-4" />,
   'showcase': <SparklesIcon className="h-4 w-4" />
  };
  return iconMap[type] || <ChatBubbleLeftRightIcon className="h-4 w-4" />;
 };

 const sortOptions = [
  { value: 'recent', label: t('mostRecent') || 'Most Recent' },
  { value: 'popular', label: t('mostPopular') || 'Most Popular' },
  { value: 'trending', label: t('trendingFilter') || 'Trending' },
  { value: 'discussed', label: t('mostDiscussed') || 'Most Discussed' },
 ];

 const formatNumber = num => {
  if (num >= 1000000) {
   return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
   return `${(num / 1000).toFixed(1)}k`;
  }
  return num.toString();
 };

 const formatTimeAgo = (timestamp) => {
  try {
   if (!timestamp) return 'Unknown';
   
   const now = new Date();
   const postTime = new Date(timestamp);
   
   // Check if date is valid
   if (isNaN(postTime.getTime())) {
    console.warn('Invalid timestamp:', timestamp);
    return 'Unknown';
   }
   
   const diffInMinutes = Math.floor((now - postTime) / (1000 * 60));

   if (diffInMinutes < 1) return 'Just now';
   if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

   const diffInHours = Math.floor(diffInMinutes / 60);
   if (diffInHours < 24) return `${diffInHours}h ago`;

   const diffInDays = Math.floor(diffInHours / 24);
   if (diffInDays < 7) return `${diffInDays}d ago`;

   const diffInWeeks = Math.floor(diffInDays / 7);
   if (diffInWeeks < 4) return `${diffInWeeks}w ago`;

   return postTime.toLocaleDateString();
  } catch (error) {
   console.error('Error formatting time:', error, 'timestamp:', timestamp);
   return 'Unknown';
  }
 };

 const truncateContent = (content, maxLength = 200) => {
  if (content.length <= maxLength) return content;
  return content.substring(0, maxLength) + '...';
 };

 const handleCreatePost = () => {
  if (!user) {
   toast.error('Vui lòng đăng nhập để tạo bài viết', {
    duration: 3000,
    position: 'bottom-center'
   });
   return;
  }
  setShowCreatePostModal(true);
 };

 const handleSubmitPost = async (e) => {
  e.preventDefault();
  
  if (!createPostForm.title.trim() || !createPostForm.content.trim()) {
   toast.error('Vui lòng nhập tiêu đề và nội dung bài viết', {
    duration: 3000,
    position: 'bottom-center'
   });
   return;
  }

  try {
   setSubmittingPost(true);
   
   const postData = {
    title: createPostForm.title,
    content: createPostForm.content,
    category: createPostForm.category,
    tags: createPostForm.tags
   };

   // Get current Firebase user and token
   const currentUser = auth.currentUser;
   if (!currentUser) {
    toast.error('Vui lòng đăng nhập để tạo bài viết', {
     duration: 3000,
     position: 'bottom-center'
    });
    return;
   }

   const firebaseToken = await getIdToken(currentUser);

   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/community/posts`, {
    method: 'POST',
    headers: {
     'Content-Type': 'application/json',
     'Authorization': `Bearer ${firebaseToken}`
    },
    body: JSON.stringify(postData)
   });

   if (response.ok) {
    const result = await response.json();
    toast.success('Tạo bài viết thành công!', {
     duration: 2000,
     position: 'bottom-center'
    });
    setShowCreatePostModal(false);
    setCreatePostForm({ title: '', content: '', category: 'general', tags: [] });
    // Reload posts
    loadPosts();
   } else {
    const error = await response.json();
    toast.error('Lỗi: ' + (error.error || 'Không thể tạo bài viết'), {
     duration: 4000,
     position: 'bottom-center'
    });
   }
  } catch (error) {
   console.error('Error creating post:', error);
   toast.error('Có lỗi xảy ra khi tạo bài viết', {
    duration: 4000,
    position: 'bottom-center'
   });
  } finally {
   setSubmittingPost(false);
  }
 };

 if (loading) {
  return (
   <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
    <div className='text-center'>
     <div className='w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4'></div>
     <p className='text-gray-600'>Đang tải community...</p>
    </div>
   </div>
  );
 }

 return (
  <CommunityResponsive
   showMobileMenu={showMobileMenu}
   setShowMobileMenu={setShowMobileMenu}
   searchComponent={
    <CommunitySearch
     searchQuery={searchQuery}
     onSearchChange={setSearchQuery}
     filters={{ category: selectedCategory, sort: selectedSort }}
     onFiltersChange={handleFiltersChange}
     categories={categories}
     searchSuggestions={searchSuggestions}
     recentSearches={recentSearches}
    />
   }
  >
    {/* Responsive Modern Header */}
    <div ref={headerRef} className='text-center mb-8 sm:mb-12 relative'>
     {/* Background Decoration */}
     <div className='absolute inset-0 -z-10'>
      <div className='absolute top-0 left-1/2 transform -translate-x-1/2 w-64 h-64 sm:w-96 sm:h-96 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl'></div>
      <div className='absolute top-10 sm:top-20 left-1/4 w-32 h-32 sm:w-64 sm:h-64 bg-gradient-to-r from-pink-400/10 to-orange-400/10 rounded-full blur-2xl'></div>
     </div>

     <ResponsiveCard className='inline-flex items-center space-x-3 sm:space-x-4 mb-6 sm:mb-8 glass backdrop-blur-xl border border-white/20'>
      <div className='w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg'>
       <UserGroupIcon className='h-6 w-6 sm:h-7 sm:w-7 text-white' />
      </div>
      <div className='text-left'>
       <ResponsiveText size='xl' weight='bold' className='text-gray-900'>
        {t('communityHubTitle') || 'Community Hub'}
       </ResponsiveText>
       <ResponsiveText size='sm' className='text-gray-600'>
        Connect • Learn • Grow
       </ResponsiveText>
      </div>
      <div className='ml-auto'>
       <ApiStatusIndicator />
      </div>
     </ResponsiveCard>

     <ResponsiveText
      size='lg'
      className='text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6 sm:mb-8 px-4'
     >
      {t('connectLearnGrow') || 'Join thousands of freelancers sharing knowledge, experiences, and opportunities in our vibrant community.'}
     </ResponsiveText>

     {/* Responsive Quick Action Buttons */}
     <div className='flex flex-col sm:flex-row flex-wrap justify-center gap-3 sm:gap-4 mb-6 sm:mb-8 px-4'>
      <ResponsiveButton
       size='md'
       className='bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white'
       onClick={handleCreatePost}
      >
       <PlusIcon className='h-4 w-4 sm:h-5 sm:w-5 mr-2' />
       Create Post
      </ResponsiveButton>
      <ResponsiveButton
       variant='secondary'
       size='md'
      >
       <FireIcon className='h-4 w-4 sm:h-5 sm:w-5 mr-2 text-orange-500' />
       Trending
      </ResponsiveButton>
      <ResponsiveButton
       variant='secondary'
       size='md'
      >
       <TrophyIcon className='h-4 w-4 sm:h-5 sm:w-5 mr-2 text-yellow-500' />
       Top Contributors
      </ResponsiveButton>
     </div>
    </div>

    {/* Responsive Enhanced Stats Cards */}
    <div ref={statsRef} className='grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8 sm:mb-12'>
     <div className='group bg-white rounded-2xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300 hover:border-blue-200'>
      <div className='w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300'>
       <UsersIcon className='h-6 w-6 text-white' />
      </div>
      <div className='text-3xl font-bold text-gray-900 mb-2'>
       {formatNumber(stats.totalMembers || 0)}
      </div>
      <div className='text-sm text-gray-600'>
       {t('membersCount') || 'Total Members'}
      </div>
      <div className='text-xs text-green-600 mt-1 flex items-center justify-center'>
       <ArrowTrendingUpIcon className='h-3 w-3 mr-1' />
       +{stats.weeklyGrowth || 0}% this week
      </div>
     </div>

     <div className='group bg-white rounded-2xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300 hover:border-purple-200'>
      <div className='w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300'>
       <ChatBubbleLeftRightIcon className='h-6 w-6 text-white' />
      </div>
      <div className='text-3xl font-bold text-gray-900 mb-2'>
       {formatNumber(stats.totalPosts || 0)}
      </div>
      <div className='text-sm text-gray-600'>
       {t('postsCount') || 'Total Posts'}
      </div>
      <div className='text-xs text-blue-600 mt-1'>
       +{stats.todayPosts || 0} today
      </div>
     </div>

     <div className='group bg-white rounded-2xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300 hover:border-green-200'>
      <div className='w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300'>
       <ChatBubbleOvalLeftEllipsisIcon className='h-6 w-6 text-white' />
      </div>
      <div className='text-3xl font-bold text-gray-900 mb-2'>
       {formatNumber(stats.totalComments || 0)}
      </div>
      <div className='text-sm text-gray-600'>
       {t('commentsCount') || 'Total Comments'}
      </div>
     </div>

     <div className='group bg-white rounded-2xl p-6 text-center shadow-lg border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300 hover:border-orange-200'>
      <div className='w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300'>
       <BoltIcon className='h-6 w-6 text-white' />
      </div>
      <div className='text-3xl font-bold text-gray-900 mb-2'>
       {formatNumber(stats.onlineNow || 0)}
      </div>
      <div className='text-sm text-gray-600'>
       {t('onlineNow') || 'Online Now'}
      </div>
      <div className='w-2 h-2 bg-green-500 rounded-full mx-auto mt-2 animate-pulse'></div>
     </div>
    </div>

    {/* Enhanced Search Component */}
    <CommunitySearch
     searchQuery={searchQuery}
     onSearchChange={setSearchQuery}
     filters={{ category: selectedCategory, sort: selectedSort }}
     onFiltersChange={handleFiltersChange}
     categories={categories}
     searchSuggestions={searchSuggestions}
     recentSearches={recentSearches}
    />

    {/* Category Pills */}
    <div className='flex flex-wrap gap-3 mb-8 mt-6'>
     {categories.map(category => (
      <button
       key={category.id}
       onClick={() => setSelectedCategory(category.id)}
       className={`inline-flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105 ${
        selectedCategory === category.id
         ? 'bg-blue-600 text-white shadow-lg'
         : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
       }`}
      >
       <span className='text-lg'>{category.icon}</span>
       <span>{category.name}</span>
      </button>
     ))}
    </div>

    {/* Enhanced Posts Grid */}
    <div ref={postsRef} className='space-y-6'>
     {loading ? (
      <div className="flex justify-center items-center py-12">
       <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
       <span className="ml-3 text-gray-600">Đang tải bài viết...</span>
      </div>
     ) : posts.length === 0 ? (
      <div className="text-center py-12">
       <div className="mx-auto w-24 h-24 mb-4 text-gray-400">
        <NewspaperIcon className="w-full h-full" />
       </div>
       <h3 className="text-lg font-medium text-gray-900 mb-2">
        Chưa có bài viết nào
       </h3>
       <p className="text-gray-500 mb-4">
        Hãy là người đầu tiên chia sẻ trong cộng đồng!
       </p>
       <button 
        onClick={() => {
         console.log('🔄 Retry loading posts...');
         // Force reload posts by triggering the effect
         setLoading(true);
         const loadPosts = async () => {
          try {
           const postsData = await dataService.getCommunityPosts({
            category: selectedCategory === 'all' ? undefined : selectedCategory,
            sort: selectedSort
           });
           console.log('🔄 Retry - Raw posts data:', postsData);
           // ... same transformation logic
          } catch (error) {
           console.error('🔄 Retry failed:', error);
          } finally {
           setLoading(false);
          }
         };
         loadPosts();
        }}
        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
       >
        Thử lại
       </button>
      </div>
     ) : (
      <>
       {console.log('🎨 Rendering posts:', posts)}
       {posts.map(post => (
        <div
         key={post.id}
         className='group bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl hover:border-blue-200 transition-all duration-300 overflow-hidden'
        >
         {/* Debug info */}
         {process.env.NODE_ENV === 'development' && (
          <div className="bg-yellow-100 text-xs p-2 border-b">
           DEBUG: Post ID: {post.id} | Author: {post.author?.name || 'Unknown'}
          </div>
         )}
         
         {/* Post Header */}
         <div className='p-6 pb-4'>
          <div className='flex items-start justify-between mb-4'>
           <div className='flex items-center space-x-4'>
            <div className='relative'>
             <img
              src={post.author?.avatar || 'https://via.placeholder.com/40x40/6366f1/ffffff?text=U'}
              alt={post.author?.name || 'Unknown User'}
              className='w-12 h-12 rounded-full object-cover ring-2 ring-gray-100'
              onError={(e) => {
               e.target.src = 'https://via.placeholder.com/40x40/6366f1/ffffff?text=U';
              }}
             />
             {post.author?.verified && (
              <div className='absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center'>
               <CheckBadgeIcon className='h-3 w-3 text-white' />
              </div>
             )}
            </div>
            <div className='flex-1'>
             <div className='flex items-center space-x-2 mb-1'>
              <h3 
               className='font-semibold text-gray-900 hover:text-blue-600 cursor-pointer transition-colors duration-200'
               onClick={() => {
                if (post.author?.id && post.author.id !== 'unknown') {
                 navigate(`/freelancers/${post.author.id}`);
                }
               }}
               title="Xem profile"
              >
               {post.author?.name || 'Anonymous User'}
              </h3>
              <span className='text-gray-400'>•</span>
              <span className='text-sm text-gray-500'>
               {post.author?.username || '@user'}
              </span>
             </div>
             <div className='flex items-center space-x-2 text-sm text-gray-600'>
              <span>{post.author?.title || 'Community Member'}</span>
              <span>•</span>
              <div className='flex items-center space-x-1'>
               <GlobeAltIcon className='h-3 w-3' />
               <span>{post.author?.location || 'Unknown'}</span>
              </div>
              <span>•</span>
              <div className='flex items-center space-x-1'>
               <CalendarIcon className='h-3 w-3' />
               <span>{formatTimeAgo(post.timestamp)}</span>
              </div>
             </div>
            </div>
           </div>

           <div className='flex items-center space-x-2'>
            {post.trending && (
             <div className='flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-orange-100 to-red-100 text-orange-600 rounded-full text-xs font-medium animate-pulse'>
              <FireSolidIcon className='h-3 w-3' />
              <span>Trending</span>
             </div>
            )}
            {post.featured && (
             <div className='flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-600 rounded-full text-xs font-medium'>
              <StarIcon className='h-3 w-3' />
              <span>Featured</span>
             </div>
            )}
            <button
             onClick={() => handleBookmark(post.id)}
             className={`p-2 rounded-lg transition-all duration-200 ${
              bookmarkedPosts.has(post.id)
               ? 'text-blue-600 bg-blue-50'
               : 'text-gray-400 hover:text-blue-600 hover:bg-blue-50'
             }`}
            >
             {bookmarkedPosts.has(post.id) ? (
              <BookmarkSolidIcon className='h-5 w-5' />
             ) : (
              <BookmarkIcon className='h-5 w-5' />
             )}
            </button>
           </div>
          </div>

          {/* Post Type and Category */}
          <div className='flex items-center space-x-3 mb-4'>
           <div className='flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-full text-sm'>
            {getPostTypeIcon(post.type)}
            <span className='text-gray-700 capitalize'>{post.type}</span>
           </div>
           <div className='flex items-center space-x-2 px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm'>
            {getCategoryIcon(post.category)}
            <span className='capitalize'>{post.category}</span>
           </div>
          </div>

          {/* Post Title */}
          <h2 className='text-xl font-bold text-gray-900 mb-3 leading-tight hover:text-blue-600 cursor-pointer transition-colors duration-200'>
           {post.title}
          </h2>

          {/* Post Content */}
          <div className='text-gray-700 leading-relaxed mb-4'>
           <p>{truncateContent(post.content, 300)}</p>
          </div>

          {/* Post Images */}
          {post.images && post.images.length > 0 && (
           <div className='grid grid-cols-2 gap-3 mb-4 rounded-xl overflow-hidden'>
            {post.images.slice(0, 2).map((image, index) => (
             <img
              key={index}
              src={image}
              alt={`Post image ${index + 1}`}
              className='w-full h-48 object-cover hover:scale-105 transition-transform duration-300 cursor-pointer'
             />
            ))}
           </div>
          )}

          {/* Code Snippet */}
          {post.codeSnippet && (
           <div className='bg-gray-900 rounded-xl p-4 mb-4 overflow-x-auto'>
            <div className='flex items-center justify-between mb-2'>
             <span className='text-xs text-gray-400 uppercase tracking-wide'>
              {post.codeSnippet.language}
             </span>
             <button className='text-xs text-blue-400 hover:text-blue-300'>
              Copy
             </button>
            </div>
            <pre className='text-sm text-gray-300'>
             <code>{post.codeSnippet.code}</code>
            </pre>
           </div>
          )}

          {/* External Link */}
          {post.link && (
           <div className='border border-gray-200 rounded-xl p-4 mb-4 hover:bg-gray-50 transition-colors duration-200 cursor-pointer'>
            <div className='flex items-start space-x-3'>
             <LinkIcon className='h-5 w-5 text-gray-400 mt-1 flex-shrink-0' />
             <div className='flex-1'>
              <h4 className='font-medium text-gray-900 mb-1'>
               {post.link.title}
              </h4>
              <p className='text-sm text-gray-600 mb-2'>
               {post.link.description}
              </p>
              <span className='text-xs text-blue-600'>
               {post.link.url}
              </span>
             </div>
            </div>
           </div>
          )}

          {/* Tags */}
          <div className='flex flex-wrap gap-2 mb-4'>
           {Array.isArray(post.tags) && post.tags.map(tag => (
            <span
             key={tag}
             className='px-3 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 text-sm rounded-full hover:from-blue-100 hover:to-indigo-100 cursor-pointer transition-all duration-200'
            >
             #{tag}
            </span>
           ))}
          </div>
         </div>

         {/* Enhanced Post Interactions */}
         <CommunityInteractions
          key={`${post.id}-${likedPosts.has(post.id)}-${post.likes || post.like_count || 0}`}
          post={post}
          onLike={handleLike}
          onBookmark={handleBookmark}
          onShare={handleShare}
          onReaction={handleReaction}
          userInteractions={{
           ...userInteractions[post.id],
           liked: likedPosts.has(post.id), // Always use likedPosts as source of truth
           bookmarked: bookmarkedPosts.has(post.id)
          }}
          authState={getAuthState()}
         />
         {/* Debug: Show like status */}
         {process.env.NODE_ENV === 'development' && (
          <div className="px-6 text-xs text-gray-500">
           Debug: Post {post.id} - Liked: {likedPosts.has(post.id) ? 'YES' : 'NO'}
          </div>
         )}

         {/* Comment Section - Separated for better UX */}
         <div className="px-6 pb-6">
          <div className="border-t border-gray-200 pt-4">
           <CommentSection
            post={post}
            authState={getAuthState()}
           />
          </div>
         </div>

         {/* Poll (if exists) */}
         {post.poll && (
          <div className='px-6 pb-6'>
           <div className='p-4 bg-white rounded-xl border border-gray-200'>
            <h4 className='font-medium text-gray-900 mb-3'>
             {post.poll.question}
            </h4>
            <div className='space-y-2'>
             {post.poll.options.map(option => (
              <button
               key={option.id}
               className='w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200'
              >
               <div className='flex items-center justify-between'>
                <span className='text-gray-700'>{option.text}</span>
                <div className='flex items-center space-x-2'>
                 <div className='w-16 bg-gray-200 rounded-full h-2'>
                  <div
                   className='bg-blue-500 h-2 rounded-full'
                   style={{ width: `${(option.votes / post.poll.totalVotes) * 100}%` }}
                  ></div>
                 </div>
                 <span className='text-sm text-gray-500'>
                  {option.votes}
                 </span>
                </div>
               </div>
              </button>
             ))}
            </div>
            <div className='mt-3 text-sm text-gray-500'>
             {post.poll.totalVotes} votes
            </div>
           </div>
          </div>
         )}
        </div>
       ))}
      </>
     )}
    </div>

    {/* Enhanced Load More Section */}
    <div className='text-center mt-16'>
     <div className='inline-flex flex-col items-center space-y-4'>
      <button className='group bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl flex items-center space-x-3'>
       <span>{t('loadMorePosts') || 'Load More Posts'}</span>
       <ArrowTrendingUpIcon className='h-5 w-5 group-hover:translate-x-1 transition-transform duration-300' />
      </button>
      <p className='text-sm text-gray-500'>
       Showing {posts.length} of {stats.totalPosts || 0} posts
      </p>
     </div>
    </div>

    {/* Community Guidelines */}
    <div className='mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100'>
     <div className='text-center'>
      <div className='w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4'>
       <SparklesIcon className='h-8 w-8 text-white' />
      </div>
      <h3 className='text-2xl font-bold text-gray-900 mb-4'>
       Welcome to Our Community
      </h3>
      <p className='text-gray-600 max-w-2xl mx-auto mb-6'>
       Join thousands of freelancers sharing knowledge, experiences, and opportunities.
       Be respectful, helpful, and let's grow together!
      </p>
      <div className='flex flex-wrap justify-center gap-4'>
       <button className='px-6 py-3 bg-white text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all duration-300 shadow-md border border-gray-200'>
        Community Guidelines
       </button>
       <button className='px-6 py-3 bg-white text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all duration-300 shadow-md border border-gray-200'>
        Report Content
       </button>
       <button className='px-6 py-3 bg-white text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all duration-300 shadow-md border border-gray-200'>
        Get Help
       </button>
      </div>
     </div>
    </div>

    {/* Login Modal */}
    <LoginModal />

    {/* Create Post Modal */}
    {showCreatePostModal && (
     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
       <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
         Tạo bài viết mới
        </h2>
                 <button
          onClick={() => setShowCreatePostModal(false)}
          className="text-gray-500 hover:text-gray-700"
         >
         <XMarkIcon className="h-6 w-6" />
        </button>
       </div>

       <form onSubmit={handleSubmitPost} className="space-y-6">
        {/* Title */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Tiêu đề *
         </label>
         <input
          type="text"
          value={createPostForm.title}
          onChange={(e) => setCreatePostForm(prev => ({ ...prev, title: e.target.value }))}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Nhập tiêu đề bài viết..."
          maxLength={200}
          required
         />
         <p className="text-xs text-gray-500 mt-1">
          {createPostForm.title.length}/200 ký tự
         </p>
        </div>

        {/* Category */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Danh mục
         </label>
         <select
          value={createPostForm.category}
          onChange={(e) => setCreatePostForm(prev => ({ ...prev, category: e.target.value }))}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
         >
          <option value="general">Chung</option>
          <option value="discussion">Thảo luận</option>
          <option value="question">Câu hỏi</option>
          <option value="announcement">Thông báo</option>
         </select>
        </div>

        {/* Content */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Nội dung *
         </label>
         <textarea
          value={createPostForm.content}
          onChange={(e) => setCreatePostForm(prev => ({ ...prev, content: e.target.value }))}
          rows={8}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          placeholder="Viết nội dung bài viết của bạn..."
          maxLength={10000}
          required
         />
         <p className="text-xs text-gray-500 mt-1">
          {createPostForm.content.length}/10000 ký tự
         </p>
        </div>

        {/* Tags */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Tags (tùy chọn)
         </label>
         <input
          type="text"
          value={createPostForm.tags.join(', ')}
          onChange={(e) => {
           const tags = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag);
           setCreatePostForm(prev => ({ ...prev, tags }));
          }}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Nhập tags, phân cách bằng dấu phẩy..."
         />
         <p className="text-xs text-gray-500 mt-1">
          Ví dụ: javascript, react, freelancing
         </p>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-4">
         <button
          type="button"
          onClick={() => setShowCreatePostModal(false)}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
         >
          Hủy
         </button>
         <button
          type="submit"
          disabled={submittingPost}
          className="px-8 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
         >
          {submittingPost ? (
           <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>Đang tạo...</span>
           </>
          ) : (
           <>
            <PlusIcon className="h-5 w-5" />
            <span>Tạo bài viết</span>
           </>
          )}
         </button>
        </div>
       </form>
      </div>
     </div>
    )}
  </CommunityResponsive>
 );
};

export default AppleCommunityPage;
