import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { enableNetwork } from 'firebase/firestore';

// Check if all required environment variables are available
const requiredEnvVars = [
 'REACT_APP_FIREBASE_API_KEY',
 'REACT_APP_FIREBASE_AUTH_DOMAIN',
 'REACT_APP_FIREBASE_PROJECT_ID',
 'REACT_APP_FIREBASE_STORAGE_BUCKET',
 'REACT_APP_FIREBASE_MESSAGING_SENDER_ID',
 'REACT_APP_FIREBASE_APP_ID',
];

// Debug: Log all environment variables
console.log('🔍 Firebase Environment Variables Check:');
requiredEnvVars.forEach(envVar => {
 console.log(`${envVar}: ${process.env[envVar] ? '✅ Found' : '❌ Missing'}`);
 if (process.env[envVar]) {
  console.log(` Value: ${process.env[envVar]}`);
 }
});

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
 console.log('⚠️ Missing Firebase environment variables:', missingEnvVars);
 console.log(
  '📋 Please create a .env file in the client directory with the following variables:'
 );
 console.log(`
# Firebase Configuration (VWork Project)
REACT_APP_FIREBASE_API_KEY=AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
REACT_APP_FIREBASE_AUTH_DOMAIN=vwork-786c3.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=vwork-786c3
REACT_APP_FIREBASE_STORAGE_BUCKET=vwork-786c3.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=1050922072615
REACT_APP_FIREBASE_APP_ID=1:1050922072615:web:dfeae89c9ba66c77aeec02

# Environment
NODE_ENV=development
 `);
}

// Firebase configuration with fallback values for development
const firebaseConfig = {
 apiKey: process.env.REACT_APP_FIREBASE_API_KEY || 'demo-api-key',
 authDomain:
  process.env.REACT_APP_FIREBASE_AUTH_DOMAIN ||
  'demo-project.firebaseapp.com',
 projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || 'demo-project',
 storageBucket:
  process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || 'demo-project.appspot.com',
 messagingSenderId:
  process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || '123456789',
 appId: process.env.REACT_APP_FIREBASE_APP_ID || 'demo-app-id',
};

// Initialize Firebase Services
const initializeFirebaseServices = () => {
 try {
  // Initialize Firebase
  const app = initializeApp(firebaseConfig);

  // Initialize Firebase Authentication and get a reference to the service
  const auth = getAuth(app);

  // Initialize Cloud Firestore and get a reference to the service
  const db = getFirestore(app);
  
  // Configure Firestore settings for better error handling
  if (db) {
   // Enable offline persistence
   enableNetwork(db).catch(error => {
    console.log('⚠️ Firestore network error:', error);
    if (error.message?.includes('ERR_BLOCKED_BY_CLIENT')) {
     console.log('🔒 Firestore bị chặn bởi client. Vui lòng kiểm tra ad blocker.');
    }
   });
  }
  
  if (missingEnvVars.length === 0) {
   console.log('✅ Firebase initialized successfully');
   console.log('🔥 Firestore connected - ready for database operations');
  }

  return { app, auth, db };
 } catch (error) {
  console.log('❌ Firebase initialization failed:', error);

  // Create mock objects for development without Firebase
  const mockAuth = {
   currentUser: null,
   onAuthStateChanged: () => () => {},
   signInWithEmailAndPassword: () =>
    Promise.reject(new Error('Firebase not configured')),
   createUserWithEmailAndPassword: () =>
    Promise.reject(new Error('Firebase not configured')),
   signOut: () => Promise.reject(new Error('Firebase not configured')),
   updateProfile: () => Promise.reject(new Error('Firebase not configured')),
   sendPasswordResetEmail: () =>
    Promise.reject(new Error('Firebase not configured')),
  };

  const mockDb = {
   collection: () => ({
    doc: () => ({
     get: () => Promise.reject(new Error('Firebase not configured')),
     set: () => Promise.reject(new Error('Firebase not configured')),
     update: () => Promise.reject(new Error('Firebase not configured')),
    }),
   }),
  };

  return { app: null, auth: mockAuth, db: mockDb };
 }
};

// Initialize services
const { app, auth, db } = initializeFirebaseServices();

export { auth, db };
export default app || null;
