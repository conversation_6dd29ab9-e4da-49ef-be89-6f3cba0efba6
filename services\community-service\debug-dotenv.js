const fs = require('fs');
const path = require('path');

console.log('=== Debug dotenv parsing issues ===');

const envPath = 'C:\\Project\\Vwork\\.env';
console.log('Reading .env from:', envPath);

if (fs.existsSync(envPath)) {
  const content = fs.readFileSync(envPath, 'utf8');
  console.log('File size:', content.length, 'bytes');
  
  // Split into lines and analyze Firebase lines
  const lines = content.split('\n');
  console.log('Total lines:', lines.length);
  
  console.log('\n=== Firebase lines analysis ===');
  lines.forEach((line, index) => {
    if (line.includes('FIREBASE_CLIENT_EMAIL')) {
      console.log(`Line ${index + 1} (CLIENT_EMAIL):`, line);
      console.log('  Length:', line.length);
      console.log('  Ends with:', line.slice(-10));
    }
    if (line.includes('FIREBASE_PRIVATE_KEY')) {
      console.log(`Line ${index + 1} (PRIVATE_KEY):`, line.substring(0, 50) + '...');
      console.log('  Length:', line.length);
      console.log('  Ends with:', line.slice(-10));
    }
  });
  
  console.log('\n=== Testing dotenv ===');
  // Clear environment first
  delete process.env.FIREBASE_CLIENT_EMAIL;
  delete process.env.FIREBASE_PRIVATE_KEY;
  delete process.env.FIREBASE_PROJECT_ID;
  
  require('dotenv').config({ path: envPath });
  
  console.log('After dotenv.config():');
  console.log('  FIREBASE_PROJECT_ID:', process.env.FIREBASE_PROJECT_ID);
  console.log('  FIREBASE_CLIENT_EMAIL:', process.env.FIREBASE_CLIENT_EMAIL);
  console.log('  FIREBASE_PRIVATE_KEY exists:', !!process.env.FIREBASE_PRIVATE_KEY);
  
  if (process.env.FIREBASE_PRIVATE_KEY) {
    console.log('  FIREBASE_PRIVATE_KEY length:', process.env.FIREBASE_PRIVATE_KEY.length);
    console.log('  FIREBASE_PRIVATE_KEY starts:', process.env.FIREBASE_PRIVATE_KEY.substring(0, 30));
  }
  
  console.log('\n=== Manual parsing ===');
  // Manual parsing to see what's happening
  const envVars = {};
  let currentKey = null;
  let currentValue = '';
  let inQuotes = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line || line.startsWith('#')) continue;
    
    if (line.includes('=') && !inQuotes) {
      // Save previous key-value if exists
      if (currentKey) {
        envVars[currentKey] = currentValue.trim();
      }
      
      const [key, ...valueParts] = line.split('=');
      currentKey = key.trim();
      currentValue = valueParts.join('=');
      
      // Check if value starts with quote
      if (currentValue.startsWith('"')) {
        inQuotes = true;
        currentValue = currentValue.substring(1); // Remove opening quote
        
        // Check if it also ends with quote on same line
        if (currentValue.endsWith('"')) {
          inQuotes = false;
          currentValue = currentValue.slice(0, -1); // Remove closing quote
        }
      }
    } else if (inQuotes) {
      // Continue multiline value
      currentValue += '\n' + line;
      
      // Check if this line ends the quote
      if (line.endsWith('"')) {
        inQuotes = false;
        currentValue = currentValue.slice(0, -1); // Remove closing quote
      }
    }
  }
  
  // Save last key-value
  if (currentKey) {
    envVars[currentKey] = currentValue.trim();
  }
  
  console.log('Manual parsing results:');
  Object.keys(envVars).forEach(key => {
    if (key.includes('FIREBASE')) {
      console.log(`  ${key}:`, envVars[key] ? '✅ Found' : '❌ Missing');
      if (key === 'FIREBASE_CLIENT_EMAIL') {
        console.log(`    Value: ${envVars[key]}`);
      }
      if (key === 'FIREBASE_PRIVATE_KEY') {
        console.log(`    Length: ${envVars[key]?.length || 0}`);
        console.log(`    Starts: ${envVars[key]?.substring(0, 30) || 'N/A'}`);
      }
    }
  });
  
} else {
  console.log('File does not exist');
} 