#!/usr/bin/env node

/**
 * Unified start script for NERAFUS platform
 * Replaces multiple start scripts with a single, configurable solution
 */

const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..', '..');
const SERVICES_DIR = path.join(ROOT_DIR, 'services');
const CLIENT_DIR = path.join(ROOT_DIR, 'client');

// Service configurations
const SERVICES = {
  'api-gateway': { port: 8080, priority: 1 },
  'user-service': { port: 3001, priority: 2 },
  'project-service': { port: 3002, priority: 3 },
  'job-service': { port: 3003, priority: 3 },
  'chat-service': { port: 3004, priority: 3 },
  'community-service': { port: 3005, priority: 3 },
  'payment-service': { port: 3006, priority: 3 },
  'team-service': { port: 3007, priority: 3 }
};

const CLIENT_CONFIG = { port: 3000, priority: 4 };

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Process management
 */
class ProcessManager {
  constructor() {
    this.processes = new Map();
    this.setupSignalHandlers();
  }

  setupSignalHandlers() {
    process.on('SIGINT', () => this.stopAll());
    process.on('SIGTERM', () => this.stopAll());
  }

  async startService(serviceName, options = {}) {
    const servicePath = path.join(SERVICES_DIR, serviceName);
    
    if (!fs.existsSync(servicePath)) {
      log.warn(`Service ${serviceName} not found, skipping...`);
      return false;
    }

    const config = SERVICES[serviceName];
    const port = options.port || config?.port;
    
    log.info(`Starting ${serviceName} on port ${port}...`);

    if (process.platform === 'win32') {
      // Windows: Open in new CMD window
      const title = `VWork - ${serviceName} (Port ${port})`;
      const command = `start "${title}" cmd /k "cd /d "${servicePath}" && set PORT=${port} && set NODE_ENV=${options.production ? 'production' : 'development'} && npm run ${options.dev ? 'dev' : 'start'}"`;
      
      try {
        execSync(command, { stdio: 'inherit' });
        log.success(`${serviceName} started in new CMD window`);
        
        // Store process info for tracking
        this.processes.set(serviceName, {
          type: 'windows-cmd',
          serviceName,
          port,
          title
        });
        
        // Wait a bit for service to start
        await new Promise(resolve => setTimeout(resolve, 3000));
        return true;
      } catch (error) {
        log.error(`Failed to start ${serviceName}: ${error.message}`);
        return false;
      }
    } else {
      // Unix/Linux/Mac: Use spawn as before
      const npmCommand = 'npm';
      const child = spawn(npmCommand, ['run', options.dev ? 'dev' : 'start'], {
        cwd: servicePath,
        stdio: ['inherit', 'pipe', 'pipe'],
        shell: true,
        env: {
          ...process.env,
          PORT: port,
          NODE_ENV: options.production ? 'production' : 'development'
        }
      });

      // Handle output
      child.stdout.on('data', (data) => {
        console.log(`[${serviceName}] ${data.toString().trim()}`);
      });

      child.stderr.on('data', (data) => {
        console.error(`[${serviceName}] ${data.toString().trim()}`);
      });

      child.on('close', (code) => {
        if (code !== 0) {
          log.error(`${serviceName} exited with code ${code}`);
        }
        this.processes.delete(serviceName);
      });

      this.processes.set(serviceName, child);
      
      // Wait a bit for service to start
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      log.success(`${serviceName} started successfully`);
      return true;
    }
  }

  async startClient(options = {}) {
    if (!fs.existsSync(CLIENT_DIR)) {
      log.warn('Client directory not found, skipping...');
      return false;
    }

    const port = options.port || CLIENT_CONFIG.port;
    
    log.info(`Starting client application on port ${port}...`);

    if (process.platform === 'win32') {
      // Windows: Open in new CMD window
      const title = `VWork - Client (Port ${port})`;
      const command = `start "${title}" cmd /k "cd /d "${CLIENT_DIR}" && set PORT=${port} && set BROWSER=none && npm run ${options.dev ? 'dev' : 'start:clean'}"`;
      
      try {
        execSync(command, { stdio: 'inherit' });
        log.success(`Client started in new CMD window`);
        
        // Store process info for tracking
        this.processes.set('client', {
          type: 'windows-cmd',
          serviceName: 'client',
          port,
          title
        });
        
        return true;
      } catch (error) {
        log.error(`Failed to start client: ${error.message}`);
        return false;
      }
    } else {
      // Unix/Linux/Mac: Use spawn as before
      const npmCommand = 'npm';
      const child = spawn(npmCommand, ['run', options.dev ? 'dev' : 'start'], {
        cwd: CLIENT_DIR,
        stdio: ['inherit', 'pipe', 'pipe'],
        shell: true,
        env: {
          ...process.env,
          PORT: port,
          BROWSER: 'none' // Prevent auto-opening browser
        }
      });

      // Handle output
      child.stdout.on('data', (data) => {
        console.log(`[client] ${data.toString().trim()}`);
      });

      child.stderr.on('data', (data) => {
        console.error(`[client] ${data.toString().trim()}`);
      });

      child.on('close', (code) => {
        if (code !== 0) {
          log.error(`Client exited with code ${code}`);
        }
        this.processes.delete('client');
      });

      this.processes.set('client', child);
      
      log.success('Client started successfully');
      return true;
    }
  }

  async startAll(options = {}) {
    log.info('Starting all services...');

    // Sort services by priority
    const sortedServices = Object.entries(SERVICES)
      .sort(([,a], [,b]) => a.priority - b.priority)
      .map(([name]) => name);

    // Start services in order
    for (const serviceName of sortedServices) {
      if (options.services && !options.services.includes(serviceName)) {
        continue;
      }
      await this.startService(serviceName, options);
    }

    // Start client if requested
    if (options.client !== false) {
      await this.startClient(options);
    }

    log.success('All services started successfully');
    
    // Keep process alive
    console.log('\n🚀 VWork Platform is running!');
    console.log('─'.repeat(60));
    console.log('📱 Each service runs in its own CMD window for easy monitoring');
    console.log('🔗 Access the application at: http://localhost:3000');
    console.log('⚡ Press Ctrl+C in this window to stop all services');
    console.log('─'.repeat(60));
    
    // Display service URLs
    this.displayServiceUrls();
    
    // Keep the process alive
    if (process.platform === 'win32') {
      // On Windows, keep the process running
      console.log('\n⏳ Keeping main process alive...');
      console.log('💡 Press Ctrl+C to stop all services\n');
      
      // Keep process alive indefinitely
      process.stdin.resume();
      
      // Set up periodic health check
      setInterval(() => {
        // Just keep the process alive
      }, 30000); // Check every 30 seconds
    }
  }

  displayServiceUrls() {
    console.log('📋 Service URLs:');
    console.log('─'.repeat(60));
    
    Object.entries(SERVICES).forEach(([name, config]) => {
      if (this.processes.has(name)) {
        const processInfo = this.processes.get(name);
        const windowInfo = processInfo.type === 'windows-cmd' ? ' (CMD Window)' : '';
        console.log(`   ${name.padEnd(20)} http://localhost:${config.port}${windowInfo}`);
      }
    });
    
    if (this.processes.has('client')) {
      const processInfo = this.processes.get('client');
      const windowInfo = processInfo.type === 'windows-cmd' ? ' (CMD Window)' : '';
      console.log(`   ${'client'.padEnd(20)} http://localhost:${CLIENT_CONFIG.port}${windowInfo}`);
    }
    
    console.log('');
    console.log('💡 Tips:');
    console.log('   - Each service runs in its own CMD window for easy log monitoring');
    console.log('   - Use "npm run stop" to close all services');
    console.log('   - Press Ctrl+C in this window to stop all services');
    console.log('');
  }

  async stopAll() {
    log.info('Stopping all NERAFUS services...');

    if (process.platform === 'win32') {
      // Windows: Kill CMD windows by title
      this.processes.forEach((processInfo, name) => {
        log.info(`Stopping ${name}...`);
        try {
          if (processInfo.type === 'windows-cmd') {
            // Kill CMD window by title
            const killCommand = `taskkill /F /FI "WINDOWTITLE eq ${processInfo.title}*"`;
            execSync(killCommand, { stdio: 'pipe' });
            log.success(`Killed ${name} CMD window`);
          } else if (processInfo.kill) {
            // For spawned processes
            processInfo.kill('SIGTERM');
          }
        } catch (error) {
          log.warn(`Failed to stop ${name}: ${error.message}`);
        }
      });
    } else {
      // Unix/Linux/Mac: Stop spawned processes
      this.processes.forEach((child, name) => {
        log.info(`Stopping ${name}...`);
        try {
          child.kill('SIGTERM');
        } catch (error) {
          log.warn(`Failed to stop ${name}: ${error.message}`);
        }
      });
    }

    // Also kill any other NERAFUS-related processes
    await this.killNerafusProcesses();

    // Clear the processes map
    this.processes.clear();
    
    log.success('All NERAFUS services stopped');
  }

  async killNerafusProcesses() {
    try {
      if (process.platform === 'win32') {
        // Windows: Kill by CMD window titles and ports
        log.info('Killing Windows CMD windows and processes...');
        
        // Kill CMD windows by title pattern - More aggressive approach
        const windowTitles = [
          'VWork*',
          '*VWork*',
          '*api-gateway*',
          '*user-service*',
          '*project-service*',
          '*job-service*',
          '*chat-service*',
          '*community-service*',
          '*payment-service*',
          '*team-service*',
          '*Client*',
          '*Port 3000*',
          '*Port 3001*',
          '*Port 3002*',
          '*Port 3003*',
          '*Port 3004*',
          '*Port 3005*',
          '*Port 3006*',
          '*Port 3007*',
          '*Port 8080*'
        ];
        
        for (const title of windowTitles) {
          try {
            execSync(`taskkill /F /FI "WINDOWTITLE eq ${title}"`, { stdio: 'pipe' });
            log.success(`Killed CMD windows with title: ${title}`);
          } catch (error) {
            // No matching windows, which is fine
          }
        }

        // Kill all cmd.exe processes that might be VWork related
        try {
          execSync('taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq *VWork*"', { stdio: 'pipe' });
          log.success('Killed VWork CMD processes');
        } catch (error) {
          // No matching processes, which is fine
        }

        // Kill processes by port
        const ports = [3000, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 8080];
        
        for (const port of ports) {
          try {
            const result = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8', stdio: 'pipe' });
            const lines = result.split('\n').filter(line => line.includes('LISTENING'));

            for (const line of lines) {
              const parts = line.trim().split(/\s+/);
              const pid = parts[parts.length - 1];
              if (pid && pid !== '0' && !isNaN(pid)) {
                execSync(`taskkill /F /PID ${pid}`, { stdio: 'pipe' });
                log.success(`Killed process on port ${port} (PID: ${pid})`);
              }
            }
          } catch (error) {
            // Port not in use, which is fine
          }
        }

        // Kill any remaining Node.js processes that might be VWork related
        try {
          execSync('taskkill /F /IM node.exe /FI "WINDOWTITLE eq *VWork*"', { stdio: 'pipe' });
        } catch (error) {
          // No matching processes, which is fine
        }

        // Final cleanup: Kill any remaining cmd.exe processes with VWork in title
        try {
          execSync('wmic process where "name=\'cmd.exe\' and commandline like \'%VWork%\'" call terminate', { stdio: 'pipe' });
          log.success('Final cleanup: Killed remaining VWork CMD processes');
        } catch (error) {
          // No matching processes, which is fine
        }

      } else {
        // Unix/Linux/Mac: Kill by ports and process names
        const ports = [3000, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 8080];

        for (const port of ports) {
          try {
            execSync(`lsof -ti:${port} | xargs kill -9`, { stdio: 'pipe' });
            log.success(`Killed process on port ${port}`);
          } catch (error) {
            // Port not in use, which is fine
          }
        }

        // Kill by process name pattern
        try {
          execSync('pkill -f "node.*nerafus"', { stdio: 'pipe' });
        } catch (error) {
          // No matching processes, which is fine
        }
      }

    } catch (error) {
      log.warn(`Some processes may still be running: ${error.message}`);
    }
  }

  stop(serviceName) {
    const child = this.processes.get(serviceName);
    if (child) {
      log.info(`Stopping ${serviceName}...`);
      child.kill('SIGTERM');
      this.processes.delete(serviceName);
    } else {
      log.warn(`Service ${serviceName} is not running`);
    }
  }
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';
  
  const options = {
    dev: args.includes('--dev'),
    production: args.includes('--production'),
    client: !args.includes('--no-client'),
    services: args.includes('--services') ? 
      args[args.indexOf('--services') + 1]?.split(',') : null
  };

  const manager = new ProcessManager();

  switch (command) {
    case 'all':
      await manager.startAll(options);
      break;

    case 'services':
      options.client = false;
      await manager.startAll(options);
      break;

    case 'client':
      await manager.startClient(options);
      break;

    case 'stop':
      await manager.stopAll();
      log.success('All processes stopped');
      process.exit(0);
      break;

    default:
      // Try to start specific service
      if (SERVICES[command]) {
        await manager.startService(command, options);
      } else {
        console.log(`
Usage: node start.js [command] [options]

Commands:
  all             Start all services and client (default)
  services        Start only services
  client          Start only client
  stop            Stop all running processes
  <service-name>  Start specific service

Options:
  --dev           Start in development mode
  --production    Start in production mode
  --no-client     Don't start client
  --services      Comma-separated list of services to start

Examples:
  node start.js
  node start.js all --dev
  node start.js services --no-client
  node start.js stop
  node start.js auth-service --dev
  node start.js --services auth-service,user-service
        `);
        process.exit(1);
      }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { ProcessManager };
