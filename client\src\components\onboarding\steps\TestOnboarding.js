import React from 'react';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useAuth } from '../../../contexts/AuthContext';

const TestOnboarding = () => {
 const { user } = useAuth();
 const { 
  currentStep, 
  isOnboardingRequired, 
  loading,
  getCurrentStepIndex,
  getTotalSteps
 } = useOnboarding();

 return (
  <div className="p-8 bg-white rounded-lg shadow-lg">
   <h2 className="text-2xl font-bold mb-4">Onboarding Test</h2>
   
   <div className="space-y-4">
    <div>
     <strong>User:</strong> {user?.email || 'No user'}
    </div>
    
    <div>
     <strong>User Type:</strong> {user?.userType || 'Unknown'}
    </div>
    
    <div>
     <strong>Current Step:</strong> {currentStep}
    </div>
    
    <div>
     <strong>Step Index:</strong> {getCurrentStepIndex() + 1} / {getTotalSteps()}
    </div>
    
    <div>
     <strong>Onboarding Required:</strong> {isOnboardingRequired ? 'Yes' : 'No'}
    </div>
    
    <div>
     <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
    </div>
    
    <div>
     <strong>Profile Complete:</strong> {user?.profile?.isComplete ? 'Yes' : 'No'}
    </div>
   </div>
  </div>
 );
};

export default TestOnboarding; 
