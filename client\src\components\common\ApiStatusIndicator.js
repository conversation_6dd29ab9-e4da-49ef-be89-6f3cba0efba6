/**
 * API Status Indicator Component
 * Shows current API status and allows switching between mock and real data
 */

import { useState, useEffect } from 'react';
import { 
 CloudIcon, 
 ServerIcon, 
 ExclamationTriangleIcon,
 CheckCircleIcon 
} from '@heroicons/react/24/outline';
import dataService, { switchToRealApi, switchToMockData, getApiStatus } from '../../services/dataService';
import { communityUtils } from '../../services/communityApiService';

const ApiStatusIndicator = ({ className = '' }) => {
 const [apiStatus, setApiStatus] = useState(getApiStatus());
 const [backendHealth, setBackendHealth] = useState(null);
 const [isLoading, setIsLoading] = useState(false);
 const [showDetails, setShowDetails] = useState(false);

 // Check backend health
 const checkBackendHealth = async () => {
  try {
   setIsLoading(true);
   const health = await communityUtils.checkApiHealth();
   setBackendHealth(health);
  } catch (error) {
   setBackendHealth({ status: 'unhealthy', error: error.message });
  } finally {
   setIsLoading(false);
  }
 };

 // Update API status
 const updateApiStatus = () => {
  setApiStatus(getApiStatus());
 };

 // Switch to real API
 const handleSwitchToReal = async () => {
  switchToRealApi();
  updateApiStatus();
  await checkBackendHealth();
 };

 // Switch to mock data
 const handleSwitchToMock = () => {
  switchToMockData();
  updateApiStatus();
  setBackendHealth(null);
 };

 // Check health on mount if using real API
 useEffect(() => {
  if (apiStatus.useRealApi) {
   checkBackendHealth();
  }
 }, [apiStatus.useRealApi]);

 // Get status color
 const getStatusColor = () => {
  if (!apiStatus.useRealApi) return 'text-blue-500';
  if (isLoading) return 'text-yellow-500';
  if (backendHealth?.status === 'healthy') return 'text-green-500';
  return 'text-red-500';
 };

 // Get status icon
 const getStatusIcon = () => {
  if (!apiStatus.useRealApi) {
   return <ServerIcon className="h-4 w-4" />;
  }
  if (isLoading) {
   return <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />;
  }
  if (backendHealth?.status === 'healthy') {
   return <CheckCircleIcon className="h-4 w-4" />;
  }
  return <ExclamationTriangleIcon className="h-4 w-4" />;
 };

 // Get status text
 const getStatusText = () => {
  if (!apiStatus.useRealApi) return 'Mock Data';
  if (isLoading) return 'Checking...';
  if (backendHealth?.status === 'healthy') return 'Real API';
  return 'API Error';
 };

 return (
  <div className={`relative ${className}`}>
   {/* Status Indicator */}
   <button
    onClick={() => setShowDetails(!showDetails)}
    className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium transition-colors ${getStatusColor()} bg-gray-100 hover:bg-gray-200`}
   >
    {getStatusIcon()}
    <span>{getStatusText()}</span>
    <CloudIcon className="h-3 w-3" />
   </button>

   {/* Details Dropdown */}
   {showDetails && (
    <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
     <div className="p-4">
      <h3 className="text-sm font-semibold text-gray-900 mb-3">
       API Status
      </h3>

      {/* Current Status */}
      <div className="space-y-2 mb-4">
       <div className="flex items-center justify-between">
        <span className="text-xs text-gray-600">Source</span>
        <span className={`text-xs font-medium ${getStatusColor()}`}>
         {apiStatus.apiType}
        </span>
       </div>

       {apiStatus.useRealApi && backendHealth && (
        <>
         <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Backend:</span>
          <span className={`text-xs font-medium ${backendHealth.status === 'healthy' ? 'text-green-500' : 'text-red-500'}`}>
           {backendHealth.status}
          </span>
         </div>

         {backendHealth.checks?.database && (
          <div className="flex items-center justify-between">
           <span className="text-xs text-gray-600">Database:</span>
           <span className={`text-xs font-medium ${backendHealth.checks.database.result.type === 'sqlite' ? 'text-green-500' : 'text-red-500'}`}>
            {backendHealth.checks.database.result.type}
           </span>
          </div>
         )}

         {backendHealth.checks?.database?.result?.stats && (
          <div className="flex items-center justify-between">
           <span className="text-xs text-gray-600">Queries:</span>
           <span className="text-xs text-gray-900">
            {backendHealth.checks.database.result.stats.queries}
           </span>
          </div>
         )}
        </>
       )}
      </div>

      {/* Switch Buttons */}
      <div className="space-y-2">
       <button
        onClick={handleSwitchToReal}
        disabled={isLoading}
        className={`w-full px-3 py-2 text-xs rounded-md transition-colors ${
         apiStatus.useRealApi
          ? 'bg-green-100 text-green-800'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}
       >
        <div className="flex items-center justify-center space-x-2">
         <CloudIcon className="h-3 w-3" />
         <span>Use Real API</span>
         {apiStatus.useRealApi && <CheckCircleIcon className="h-3 w-3" />}
        </div>
       </button>

       <button
        onClick={handleSwitchToMock}
        className={`w-full px-3 py-2 text-xs rounded-md transition-colors ${
         !apiStatus.useRealApi
          ? 'bg-blue-100 text-blue-800'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}
       >
        <div className="flex items-center justify-center space-x-2">
         <ServerIcon className="h-3 w-3" />
         <span>Use Mock Data</span>
         {!apiStatus.useRealApi && <CheckCircleIcon className="h-3 w-3" />}
        </div>
       </button>
      </div>

      {/* Refresh Button */}
      {apiStatus.useRealApi && (
       <button
        onClick={checkBackendHealth}
        disabled={isLoading}
        className="w-full mt-2 px-3 py-1 text-xs text-gray-600 hover:text-gray-900 transition-colors"
       >
        {isLoading ? 'Checking...' : 'Refresh Status'}
       </button>
      )}

      {/* Error Message */}
      {backendHealth?.error && (
       <div className="mt-3 p-2 bg-red-50 rounded text-xs text-red-600">
        {backendHealth.error}
       </div>
      )}
     </div>
    </div>
   )}
  </div>
 );
};

export default ApiStatusIndicator;
