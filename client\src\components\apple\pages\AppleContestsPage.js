import { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import {
 TrophyIcon,
 ClockIcon,
 CurrencyDollarIcon,
 UserGroupIcon,
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../../contexts/LanguageContext';

const AppleContestsPage = () => {
 const { t } = useLanguage();
 const [selectedCategory, setSelectedCategory] = useState('all');
 const [selectedStatus, setSelectedStatus] = useState('all');

 const headerRef = useRef(null);

 // Apple-style animations
 useEffect(() => {
  if (headerRef.current) {
   gsap.fromTo(
    headerRef.current,
    { y: 30, opacity: 0 },
    { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
   );
  }
 }, []);

 // Mock contests data
 const contests = [
  {
   id: 1,
   title: 'Design a Modern Logo for Tech Startup',
   description:
    'We need a clean, modern logo that represents innovation and technology. The logo should work well in both digital and print formats.',
   prize: 500,
   entries: 47,
   timeLeft: '3 days',
   category: 'Design',
   status: 'Active',
   difficulty: 'Intermediate',
   client: 'TechFlow Inc.',
   requirements: [
    'Vector format',
    'Multiple variations',
    'Color and B&W versions',
   ],
   featured: true,
  },
  {
   id: 2,
   title: 'Mobile App UI Design Contest',
   description:
    'Create an intuitive and beautiful UI design for a fitness tracking mobile application. Focus on user experience and modern design trends.',
   prize: 750,
   entries: 23,
   timeLeft: '5 days',
   category: 'Design',
   status: 'Active',
   difficulty: 'Advanced',
   client: 'FitLife App',
   requirements: ['Figma files', 'Responsive design', 'Dark/Light modes'],
   featured: false,
  },
  {
   id: 3,
   title: 'Website Landing Page Design',
   description:
    'Design a conversion-focused landing page for our SaaS product. The design should be clean, professional, and optimized for conversions.',
   prize: 400,
   entries: 31,
   timeLeft: '1 day',
   category: 'Web Design',
   status: 'Ending Soon',
   difficulty: 'Intermediate',
   client: 'CloudSync Solutions',
   requirements: ['HTML/CSS', 'Mobile responsive', 'Fast loading'],
   featured: false,
  },
  {
   id: 4,
   title: 'Brand Identity Package',
   description:
    'Complete brand identity package including logo, business cards, letterhead, and brand guidelines for a consulting firm.',
   prize: 1000,
   entries: 12,
   timeLeft: '7 days',
   category: 'Branding',
   status: 'Active',
   difficulty: 'Advanced',
   client: 'Strategic Advisors',
   requirements: [
    'Complete brand guide',
    'Print-ready files',
    'Multiple formats',
   ],
   featured: true,
  },
  {
   id: 5,
   title: 'E-commerce Website Design',
   description:
    'Design a modern e-commerce website for fashion brand. Focus on user experience, product showcase, and conversion optimization.',
   prize: 600,
   entries: 19,
   timeLeft: '4 days',
   category: 'Web Design',
   status: 'Active',
   difficulty: 'Advanced',
   client: 'Fashion Forward',
   requirements: ['Responsive design', 'Shopping cart UI', 'Product pages'],
   featured: false,
  },
  {
   id: 6,
   title: 'Social Media Graphics Package',
   description:
    'Create a set of social media graphics templates for Instagram, Facebook, and Twitter. Modern, engaging, and brand-consistent designs.',
   prize: 300,
   entries: 56,
   timeLeft: '2 days',
   category: 'Social Media',
   status: 'Active',
   difficulty: 'Beginner',
   client: 'Social Buzz Agency',
   requirements: [
    'Multiple platforms',
    'Editable templates',
    'Brand guidelines',
   ],
   featured: false,
  },
 ];

 const categories = [
  { value: 'all', label: t('allContests') },
  { value: 'design', label: t('designCategory') },
  { value: 'web-design', label: t('webDesign') },
  { value: 'branding', label: t('branding') },
  { value: 'social-media', label: 'Social Media' },
 ];

 const statuses = [
  { value: 'all', label: t('allContests') },
  { value: 'active', label: t('activeContests') },
  { value: 'ending-soon', label: t('endingSoon') },
  { value: 'completed', label: t('completedContests') },
 ];

 const getStatusColor = status => {
  switch (status) {
   case 'Active':
    return 'text-green-600 bg-green-100';
   case 'Ending Soon':
    return 'text-orange-600 bg-orange-100';
   case 'Completed':
    return 'text-gray-600 bg-gray-100';
   default:
    return 'text-blue-600 bg-blue-100';
  }
 };

 const getDifficultyColor = difficulty => {
  switch (difficulty) {
   case 'Beginner':
    return 'text-green-600 bg-green-100';
   case 'Intermediate':
    return 'text-yellow-600 bg-yellow-100';
   case 'Advanced':
    return 'text-red-600 bg-red-100';
   default:
    return 'text-gray-600 bg-gray-100';
  }
 };

 const getStatusText = (status) => {
  switch (status) {
   case 'Active':
    return t('active');
   case 'Ending Soon':
    return t('endingSoonStatus');
   case 'Completed':
    return t('completedStatus');
   default:
    return status;
  }
 };

 const getDifficultyText = (difficulty) => {
  switch (difficulty) {
   case 'Beginner':
    return t('beginner');
   case 'Intermediate':
    return t('intermediate');
   case 'Advanced':
    return t('advanced');
   default:
    return difficulty;
  }
 };

 return (
  <div className='min-h-screen bg-gray-50 transition-colors duration-300'>
   <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8'>
    {/* Header */}
    <div ref={headerRef} className='text-center mb-12'>
     <div className='inline-flex items-center space-x-3 mb-6'>
      <div className='w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center shadow-sm'>
       <TrophyIcon className='h-6 w-6 text-white' />
      </div>
     </div>

     <h1 className='text-4xl md:text-5xl font-bold text-gray-900 mb-4 transition-colors duration-300'>
      {t('designContests')}
     </h1>
     <p className='text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed transition-colors duration-300'>
      {t('competeWinPrizes')}
     </p>
    </div>

    {/* Stats */}
    <div className='grid grid-cols-2 md:grid-cols-4 gap-6 mb-8'>
     <div className='bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 transition-colors duration-300'>
      <div className='text-2xl font-bold text-gray-900 mb-2 transition-colors duration-300'>
       156
      </div>
      <div className='text-sm text-gray-600 transition-colors duration-300'>
       {t('activeContests')}
      </div>
     </div>
     <div className='bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 transition-colors duration-300'>
      <div className='text-2xl font-bold text-gray-900 mb-2 transition-colors duration-300'>
       $45,230
      </div>
      <div className='text-sm text-gray-600 transition-colors duration-300'>
       {t('totalPrizes')}
      </div>
     </div>
     <div className='bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 transition-colors duration-300'>
      <div className='text-2xl font-bold text-gray-900 mb-2 transition-colors duration-300'>
       2,847
      </div>
      <div className='text-sm text-gray-600 transition-colors duration-300'>
       {t('participants')}
      </div>
     </div>
     <div className='bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 transition-colors duration-300'>
      <div className='text-2xl font-bold text-gray-900 mb-2 transition-colors duration-300'>
       89
      </div>
      <div className='text-sm text-gray-600 transition-colors duration-300'>
       {t('winners')}
      </div>
     </div>
    </div>

    {/* Filters */}
    <div className='bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8 transition-colors duration-300'>
     <div className='flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0'>
      <div className='flex items-center space-x-4'>
       <h3 className='text-lg font-semibold text-gray-900 transition-colors duration-300'>
        {t('filterContests')}
       </h3>
      </div>

      <div className='flex items-center space-x-4'>
       <div className='flex items-center space-x-2'>
        <span className='text-sm font-medium text-gray-700 transition-colors duration-300'>
         {t('category')}:
        </span>
        <select
         value={selectedCategory}
         onChange={e => setSelectedCategory(e.target.value)}
         className='px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 transition-colors duration-300'
        >
         {categories.map(category => (
          <option key={category.value} value={category.value}>
           {category.label}
          </option>
         ))}
        </select>
       </div>

       <div className='flex items-center space-x-2'>
        <span className='text-sm font-medium text-gray-700 transition-colors duration-300'>
         {t('status')}:
        </span>
        <select
         value={selectedStatus}
         onChange={e => setSelectedStatus(e.target.value)}
         className='px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 transition-colors duration-300'
        >
         {statuses.map(status => (
          <option key={status.value} value={status.value}>
           {status.label}
          </option>
         ))}
        </select>
       </div>
      </div>
     </div>
    </div>

    {/* Contests Grid */}
    <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
     {contests.map(contest => (
      <div
       key={contest.id}
       className={`bg-white rounded-2xl p-6 border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-lg ${
        contest.featured ? 'ring-2 ring-yellow-400' : ''
       }`}
      >
       {/* Header */}
       <div className='flex items-start justify-between mb-4'>
        <div className='flex-1'>
         <div className='flex items-center space-x-2 mb-2'>
          {contest.featured && (
           <span className='px-2 py-1 bg-yellow-100 text-yellow-600 text-xs font-medium rounded-full transition-colors duration-300'>
            {t('featured')}
           </span>
          )}
          <span className={`px-2 py-1 text-xs font-medium rounded-full transition-colors duration-300 ${getStatusColor(contest.status)}`}>
           {getStatusText(contest.status)}
          </span>
          <span className={`px-2 py-1 text-xs font-medium rounded-full transition-colors duration-300 ${getDifficultyColor(contest.difficulty)}`}>
           {getDifficultyText(contest.difficulty)}
          </span>
         </div>
         <h3 className='text-lg font-semibold text-gray-900 mb-2 transition-colors duration-300'>
          {contest.title}
         </h3>
         <p className='text-gray-600 text-sm leading-relaxed transition-colors duration-300'>
          {contest.description}
         </p>
        </div>
       </div>

       {/* Prize and Stats */}
       <div className='flex items-center justify-between mb-4'>
        <div className='flex items-center space-x-4'>
         <div className='flex items-center space-x-1'>
          <CurrencyDollarIcon className='h-5 w-5 text-green-600' />
          <span className='text-lg font-bold text-green-600 transition-colors duration-300'>
           ${contest.prize}
          </span>
         </div>
         <div className='flex items-center space-x-1'>
          <UserGroupIcon className='h-4 w-4 text-gray-500' />
          <span className='text-sm text-gray-600 transition-colors duration-300'>
           {contest.entries} {t('entries')}
          </span>
         </div>
        </div>
        <div className='flex items-center space-x-1'>
         <ClockIcon className='h-4 w-4 text-orange-500' />
         <span className='text-sm text-orange-600 font-medium transition-colors duration-300'>
          {contest.timeLeft}
         </span>
        </div>
       </div>

       {/* Requirements */}
       <div className='mb-4'>
        <h4 className='text-sm font-medium text-gray-700 mb-2 transition-colors duration-300'>
         {t('requirements')}:
        </h4>
        <div className='flex flex-wrap gap-2'>
         {contest.requirements.map((req, index) => (
          <span
           key={index}
           className='px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full transition-colors duration-300'
          >
           {req}
          </span>
         ))}
        </div>
       </div>

       {/* Footer */}
       <div className='flex items-center justify-between pt-4 border-t border-gray-100 transition-colors duration-300'>
        <div className='flex items-center space-x-2'>
         <span className='text-sm text-gray-600 transition-colors duration-300'>
          {t('by')} {contest.client}
         </span>
        </div>
        <button className='bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl'>
         {t('participate')}
        </button>
       </div>
      </div>
     ))}
    </div>

    {/* Load More */}
    <div className='text-center mt-12'>
     <button className='bg-blue-600 text-white px-8 py-3 rounded-xl font-medium hover:bg-blue-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl'>
      {t('loadMoreContests')}
     </button>
    </div>

    {/* CTA Section */}
    <div className='bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-8 mt-12 text-center border border-yellow-200'>
     <TrophyIcon className='h-12 w-12 text-yellow-600 mx-auto mb-4' />
     <h2 className='text-2xl font-bold text-gray-900 mb-4'>
      {t('readyToShowcaseTalent')}
     </h2>
     <p className='text-gray-600 mb-6 max-w-2xl mx-auto'>
      {t('joinThousandsDesigners')}
     </p>
     <div className='flex flex-col sm:flex-row gap-4 justify-center'>
      <button className='btn btn-primary btn-large'>
       {t('browseActiveContests')}
      </button>
      <button className='btn btn-secondary btn-large'>
       {t('startYourOwnContest')}
      </button>
     </div>
    </div>
   </div>
  </div>
 );
};

export default AppleContestsPage;
