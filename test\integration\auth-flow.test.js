/**
 * Authentication flow integration tests
 * Tests the complete authentication workflow across services
 */

describe('Authentication Flow Integration', () => {
  let authClient;
  let userClient;
  let testUser;

  beforeAll(async () => {
    // Create HTTP clients for services
    authClient = global.integrationTestUtils.createHttpClient(
      'http://localhost:3001'
    );
    userClient = global.integrationTestUtils.createHttpClient(
      'http://localhost:3002'
    );

    // Create test user data
    testUser = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Integration Test User',
      role: 'freelancer'
    };
  });

  afterEach(async () => {
    // Clean up test data after each test
    try {
      // Delete test user if it exists
      const authToken = await global.integrationTestUtils.authenticateUser({
        email: '<EMAIL>',
        password: 'AdminPassword123!'
      });
      
      const adminClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3001',
        {
          headers: { 'Authorization': `Bearer ${authToken}` }
        }
      );
      
      await adminClient.delete(`/api/v1/users/email/${testUser.email}`);
    } catch (error) {
      // User might not exist, ignore error
    }
  });

  describe('User Registration', () => {
    it('should register a new user successfully', async () => {
      const response = await authClient.post('/api/v1/auth/register', testUser);
      
      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data).toHaveProperty('user');
      expect(response.data.data).toHaveProperty('token');
      
      const { user, token } = response.data.data;
      expect(user.email).toBe(testUser.email);
      expect(user.name).toBe(testUser.name);
      expect(user.role).toBe(testUser.role);
      expect(token).toBeDefined();
    });

    it('should reject registration with invalid email', async () => {
      const invalidUser = { ...testUser, email: 'invalid-email' };
      
      const response = await authClient.post('/api/v1/auth/register', invalidUser);
      
      expect(response.status).toBe(422);
      expect(response.data.success).toBe(false);
      expect(response.data.errors).toContain(
        expect.objectContaining({
          field: 'email',
          message: expect.stringContaining('valid email')
        })
      );
    });

    it('should reject registration with weak password', async () => {
      const weakPasswordUser = { ...testUser, password: '123' };
      
      const response = await authClient.post('/api/v1/auth/register', weakPasswordUser);
      
      expect(response.status).toBe(422);
      expect(response.data.success).toBe(false);
      expect(response.data.errors).toContain(
        expect.objectContaining({
          field: 'password',
          message: expect.stringContaining('password')
        })
      );
    });

    it('should reject duplicate email registration', async () => {
      // Register user first time
      await authClient.post('/api/v1/auth/register', testUser);
      
      // Try to register same email again
      const response = await authClient.post('/api/v1/auth/register', testUser);
      
      expect(response.status).toBe(409);
      expect(response.data.success).toBe(false);
      expect(response.data.message).toContain('already exists');
    });
  });

  describe('User Login', () => {
    beforeEach(async () => {
      // Register user before each login test
      await authClient.post('/api/v1/auth/register', testUser);
    });

    it('should login with valid credentials', async () => {
      const loginData = {
        email: testUser.email,
        password: testUser.password
      };
      
      const response = await authClient.post('/api/v1/auth/login', loginData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data).toHaveProperty('user');
      expect(response.data.data).toHaveProperty('token');
      
      const { user, token } = response.data.data;
      expect(user.email).toBe(testUser.email);
      expect(token).toBeDefined();
    });

    it('should reject login with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: testUser.password
      };
      
      const response = await authClient.post('/api/v1/auth/login', loginData);
      
      expect(response.status).toBe(401);
      expect(response.data.success).toBe(false);
      expect(response.data.message).toContain('Invalid credentials');
    });

    it('should reject login with invalid password', async () => {
      const loginData = {
        email: testUser.email,
        password: 'WrongPassword123!'
      };
      
      const response = await authClient.post('/api/v1/auth/login', loginData);
      
      expect(response.status).toBe(401);
      expect(response.data.success).toBe(false);
      expect(response.data.message).toContain('Invalid credentials');
    });
  });

  describe('Protected Routes', () => {
    let authToken;

    beforeEach(async () => {
      // Register and login user
      await authClient.post('/api/v1/auth/register', testUser);
      
      const loginResponse = await authClient.post('/api/v1/auth/login', {
        email: testUser.email,
        password: testUser.password
      });
      
      authToken = loginResponse.data.data.token;
    });

    it('should access protected route with valid token', async () => {
      const authenticatedClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3002',
        {
          headers: { 'Authorization': `Bearer ${authToken}` }
        }
      );
      
      const response = await authenticatedClient.get('/api/v1/profile');
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.user.email).toBe(testUser.email);
    });

    it('should reject access to protected route without token', async () => {
      const response = await userClient.get('/api/v1/profile');
      
      expect(response.status).toBe(401);
      expect(response.data.success).toBe(false);
      expect(response.data.message).toContain('Authentication required');
    });

    it('should reject access to protected route with invalid token', async () => {
      const invalidTokenClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3002',
        {
          headers: { 'Authorization': 'Bearer invalid-token' }
        }
      );
      
      const response = await invalidTokenClient.get('/api/v1/profile');
      
      expect(response.status).toBe(401);
      expect(response.data.success).toBe(false);
      expect(response.data.message).toContain('Invalid token');
    });
  });

  describe('Cross-Service Communication', () => {
    let authToken;
    let userId;

    beforeEach(async () => {
      // Register and login user
      const registerResponse = await authClient.post('/api/v1/auth/register', testUser);
      userId = registerResponse.data.data.user.id;
      
      const loginResponse = await authClient.post('/api/v1/auth/login', {
        email: testUser.email,
        password: testUser.password
      });
      
      authToken = loginResponse.data.data.token;
    });

    it('should sync user data between auth and user services', async () => {
      // Get user from auth service
      const authUserClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3001',
        {
          headers: { 'Authorization': `Bearer ${authToken}` }
        }
      );
      
      const authUserResponse = await authUserClient.get('/api/v1/auth/me');
      
      // Get user from user service
      const userServiceClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3002',
        {
          headers: { 'Authorization': `Bearer ${authToken}` }
        }
      );
      
      const userServiceResponse = await userServiceClient.get('/api/v1/profile');
      
      // Both services should return the same user data
      expect(authUserResponse.data.data.user.id).toBe(userServiceResponse.data.data.user.id);
      expect(authUserResponse.data.data.user.email).toBe(userServiceResponse.data.data.user.email);
    });

    it('should handle service communication failures gracefully', async () => {
      // This test would simulate one service being down
      // and verify that the other service handles it gracefully
      
      // For now, we'll test timeout handling
      const slowClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3002',
        {
          timeout: 1, // Very short timeout
          headers: { 'Authorization': `Bearer ${authToken}` }
        }
      );
      
      try {
        await slowClient.get('/api/v1/profile');
      } catch (error) {
        expect(error.code).toBe('ECONNABORTED');
      }
    });
  });

  describe('Session Management', () => {
    let authToken;

    beforeEach(async () => {
      await authClient.post('/api/v1/auth/register', testUser);
      
      const loginResponse = await authClient.post('/api/v1/auth/login', {
        email: testUser.email,
        password: testUser.password
      });
      
      authToken = loginResponse.data.data.token;
    });

    it('should logout user successfully', async () => {
      const authenticatedClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3001',
        {
          headers: { 'Authorization': `Bearer ${authToken}` }
        }
      );
      
      const logoutResponse = await authenticatedClient.post('/api/v1/auth/logout');
      
      expect(logoutResponse.status).toBe(200);
      expect(logoutResponse.data.success).toBe(true);
      
      // Token should be invalid after logout
      const profileResponse = await authenticatedClient.get('/api/v1/auth/me');
      expect(profileResponse.status).toBe(401);
    });

    it('should handle token refresh', async () => {
      // This test would verify token refresh functionality
      // Implementation depends on your token refresh strategy
      
      const authenticatedClient = global.integrationTestUtils.createHttpClient(
        'http://localhost:3001',
        {
          headers: { 'Authorization': `Bearer ${authToken}` }
        }
      );
      
      const refreshResponse = await authenticatedClient.post('/api/v1/auth/refresh');
      
      expect(refreshResponse.status).toBe(200);
      expect(refreshResponse.data.data).toHaveProperty('token');
      expect(refreshResponse.data.data.token).not.toBe(authToken);
    });
  });
});
