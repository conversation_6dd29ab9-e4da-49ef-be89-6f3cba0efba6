import React from 'react';
import { useOnboarding } from '../../contexts/OnboardingContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { motion, AnimatePresence } from 'framer-motion';

// Import step components
import WelcomeStep from './steps/WelcomeStep';
import BasicInfoStep from './steps/BasicInfoStep';
import ProfileDetailsStep from './steps/ProfileDetailsStep';
import SkillsSetupStep from './steps/SkillsSetupStep';
import PreferencesStep from './steps/PreferencesStep';
import CompleteStep from './steps/CompleteStep';
// Client specific steps
import ClientPurposeStep from './steps/ClientPurposeStep';
import ClientExperienceStep from './steps/ClientExperienceStep';
import ClientNeedsStep from './steps/ClientNeedsStep';

const OnboardingFlow = () => {
 const { 
  currentStep, 
  getCurrentStepIndex, 
  getTotalSteps,
  ONBOARDING_STEPS 
 } = useOnboarding();
 const { t } = useLanguage();

 const renderStep = () => {
  switch (currentStep) {
   case ONBOARDING_STEPS.WELCOME:
    return <WelcomeStep />;
   case ONBOARDING_STEPS.BASIC_INFO:
    return <BasicInfoStep />;
   case ONBOARDING_STEPS.PROFILE_DETAILS:
    return <ProfileDetailsStep />;
   case ONBOARDING_STEPS.SKILLS_SETUP:
    return <SkillsSetupStep />;
   case ONBOARDING_STEPS.CLIENT_PURPOSE:
    return <ClientPurposeStep />;
   case ONBOARDING_STEPS.CLIENT_EXPERIENCE:
    return <ClientExperienceStep />;
   case ONBOARDING_STEPS.CLIENT_NEEDS:
    return <ClientNeedsStep />;
   case ONBOARDING_STEPS.PREFERENCES:
    return <PreferencesStep />;
   case ONBOARDING_STEPS.COMPLETE:
    return <CompleteStep />;
   default:
    return <WelcomeStep />;
  }
 };

 const progressPercentage = ((getCurrentStepIndex() + 1) / getTotalSteps()) * 100;

 return (
  <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-gold-50 flex items-center justify-center p-4">
   <div className="w-full max-w-4xl">
    {/* Progress Bar */}
    <div className="mb-8">
     <div className="flex justify-between items-center mb-2">
      <span className="text-sm font-medium text-gray-600">
       {t('onboardingProgress')}
      </span>
      <span className="text-sm font-medium text-gray-600">
       {getCurrentStepIndex() + 1} / {getTotalSteps()}
      </span>
     </div>
     <div className="w-full bg-medieval-brown-200 rounded-full h-2">
      <motion.div
       className="bg-gradient-to-r from-medieval-gold-500 to-medieval-gold-600 h-2 rounded-full"
       initial={{ width: 0 }}
       animate={{ width: `${progressPercentage}%` }}
       transition={{ duration: 0.5, ease: "easeInOut" }}
      />
     </div>
    </div>

    {/* Step Content */}
    <motion.div
     className="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden"
     initial={{ opacity: 0, y: 20 }}
     animate={{ opacity: 1, y: 0 }}
     transition={{ duration: 0.6 }}
    >
     <AnimatePresence mode="wait">
      <motion.div
       key={currentStep}
       initial={{ opacity: 0, x: 20 }}
       animate={{ opacity: 1, x: 0 }}
       exit={{ opacity: 0, x: -20 }}
       transition={{ duration: 0.3 }}
      >
       {renderStep()}
      </motion.div>
     </AnimatePresence>
    </motion.div>

    {/* Footer */}
    <div className="mt-6 text-center">
     <p className="text-sm text-gray-500 font-medium">
      {t('onboardingFooterText')}
     </p>
    </div>
   </div>
  </div>
 );
};

export default OnboardingFlow;
