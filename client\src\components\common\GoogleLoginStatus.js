import React from 'react';
import { CheckCircleIcon, UserPlusIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

const GoogleLoginStatus = ({ isVisible, userStatus, onClose }) => {
 if (!isVisible) return null;

 const getStatusContent = () => {
  switch (userStatus) {
   case 'existing_user':
    return {
     icon: CheckCircleIcon,
     iconColor: 'text-green-500',
     bgColor: 'bg-green-50',
     borderColor: 'border-green-200',
     textColor: 'text-green-800',
     title: 'Chào mừng bạn trở lại!',
     message: '<PERSON>ăng nhập thành công với tài khoản Google đã tồn tại.',
     action: 'Đang chuyển đến dashboard...'
    };
   
   case 'migrated_user':
    return {
     icon: ArrowPathIcon,
     iconColor: 'text-blue-500',
     bgColor: 'bg-blue-50',
     borderColor: 'border-blue-200',
     textColor: 'text-blue-800',
     title: 'T<PERSON><PERSON> khoản đã được kích ho<PERSON>t!',
     message: 'T<PERSON><PERSON> khoản của bạn đã được xác thực và kích hoạt thành công.',
     action: 'Đang chuyển đến trang hoàn thiện hồ sơ...'
    };
   
   case 'new_user':
    return {
     icon: UserPlusIcon,
     iconColor: 'text-purple-500',
     bgColor: 'bg-purple-50',
     borderColor: 'border-purple-200',
     textColor: 'text-purple-800',
     title: 'Chào mừng bạn đến với VWork!',
     message: 'Tài khoản Google mới đã được tạo thành công.',
     action: 'Đang chuyển đến trang thiết lập hồ sơ...'
    };
   
   default:
    return {
     icon: CheckCircleIcon,
     iconColor: 'text-green-500',
     bgColor: 'bg-green-50',
     borderColor: 'border-green-200',
     textColor: 'text-green-800',
     title: 'Đăng nhập thành công!',
     message: 'Đang xử lý thông tin tài khoản...',
     action: 'Vui lòng đợi...'
    };
  }
 };

 const content = getStatusContent();
 const { icon: Icon, iconColor, bgColor, borderColor, textColor, title, message, action } = content;

 return (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-in fade-in duration-300">
   <div className={`${bgColor} border ${borderColor} rounded-lg p-6 m-4 max-w-md w-full animate-in zoom-in duration-300`}>
    <div className="flex items-start space-x-4">
     <div className="flex-shrink-0">
      <Icon className={`w-8 h-8 ${iconColor}`} />
     </div>
     <div className={`flex-1 ${textColor}`}>
      <h3 className="font-semibold text-lg mb-2">{title}</h3>
      <p className="text-sm mb-3 opacity-90">{message}</p>
      <div className="flex items-center space-x-2 text-xs opacity-75">
       <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
       <span>{action}</span>
      </div>
     </div>
    </div>
    
    {/* Auto close after 3 seconds */}
    <div className="mt-4">
     <div className="w-full bg-gray-200 rounded-full h-1">
      <div
       className="bg-current h-1 rounded-full"
       style={{
        animation: 'progressBar 3s linear forwards',
        width: '0%'
       }}
      ></div>
     </div>
    </div>
   </div>

   <style>{`
    @keyframes progressBar {
     from { width: 0% }
     to { width: 100% }
    }
   `}</style>
  </div>
 );
};

export default GoogleLoginStatus;
