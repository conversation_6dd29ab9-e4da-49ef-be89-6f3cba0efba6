/**
 * User Routes for User Service
 * Handles user CRUD operations, search, and authentication
 */

const express = require('express');
const { verifyFirebaseToken, requireAuth, requireAdmin, optionalAuth } = require('../middleware/auth');
const database = require('../config/database');
const { getUserByUid, getUserByEmail } = require('../config/firebase');
const config = require('../config/config');

const router = express.Router();

/**
 * @route GET /api/v1/users
 * @desc Get users list with optional filters
 * @access Public (with optional auth for enhanced data)
 */
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      userType,
      isActive,
      search,
      skills,
      location,
      availability,
      minRating,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (page - 1) * limit;
    const isAuthenticated = !!req.user;

    console.log('👥 Get users list:', {
      page,
      limit,
      filters: { userType, isActive, search, skills, location, availability, minRating },
      authenticated: isAuthenticated
    });

    // Build query
    let query = `
      SELECT 
        u.id,
        u.email,
        u.display_name,
        u.first_name,
        u.last_name,
        u.avatar_url,
        u.user_type,
        u.is_active,
        u.profile_completed,
        u.created_at,
        u.last_login,
        ${isAuthenticated ? 'u.email_verified,' : ''}
        p.bio,
        p.title,
        p.country,
        p.city,
        p.hourly_rate,
        p.currency,
        p.availability,
        p.years_experience,
        r.reputation_score,
        r.level as reputation_level,
        r.average_rating,
        r.total_reviews
      FROM users u
      LEFT JOIN user_profiles p ON u.id = p.user_id
      LEFT JOIN user_reputation r ON u.id = r.user_id
      WHERE 1=1
    `;

    const queryParams = [];
    let paramIndex = 1;

    // Apply filters
    if (userType) {
      query += ` AND u.user_type = $${paramIndex}`;
      queryParams.push(userType);
      paramIndex++;
    }

    if (isActive !== undefined) {
      query += ` AND u.is_active = $${paramIndex}`;
      queryParams.push(isActive === 'true');
      paramIndex++;
    }

    if (search) {
      query += ` AND (
        u.display_name ILIKE $${paramIndex} OR 
        u.first_name ILIKE $${paramIndex} OR 
        u.last_name ILIKE $${paramIndex} OR
        u.email ILIKE $${paramIndex} OR
        p.title ILIKE $${paramIndex} OR
        p.bio ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (location) {
      query += ` AND (p.country ILIKE $${paramIndex} OR p.city ILIKE $${paramIndex})`;
      queryParams.push(`%${location}%`);
      paramIndex++;
    }

    if (availability) {
      query += ` AND p.availability = $${paramIndex}`;
      queryParams.push(availability);
      paramIndex++;
    }

    if (minRating) {
      query += ` AND r.average_rating >= $${paramIndex}`;
      queryParams.push(parseFloat(minRating));
      paramIndex++;
    }

    // Add sorting
    const validSortFields = ['created_at', 'reputation_score', 'average_rating', 'display_name', 'last_login'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
    
    query += ` ORDER BY ${sortField} ${order} NULLS LAST`;

    // Add pagination
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(parseInt(limit), offset);

    // Execute main query
    const result = await database.query(query, queryParams);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(DISTINCT u.id) as total
      FROM users u
      LEFT JOIN user_profiles p ON u.id = p.user_id
      LEFT JOIN user_reputation r ON u.id = r.user_id
      WHERE 1=1
    `;

    // Apply same filters to count query
    const countParams = queryParams.slice(0, -2); // Remove limit and offset
    let countParamIndex = 1;

    if (userType) {
      countQuery += ` AND u.user_type = $${countParamIndex}`;
      countParamIndex++;
    }

    if (isActive !== undefined) {
      countQuery += ` AND u.is_active = $${countParamIndex}`;
      countParamIndex++;
    }

    if (search) {
      countQuery += ` AND (
        u.display_name ILIKE $${countParamIndex} OR 
        u.first_name ILIKE $${countParamIndex} OR 
        u.last_name ILIKE $${countParamIndex} OR
        u.email ILIKE $${countParamIndex} OR
        p.title ILIKE $${countParamIndex} OR
        p.bio ILIKE $${countParamIndex}
      )`;
      countParamIndex++;
    }

    if (location) {
      countQuery += ` AND (p.country ILIKE $${countParamIndex} OR p.city ILIKE $${countParamIndex})`;
      countParamIndex++;
    }

    if (availability) {
      countQuery += ` AND p.availability = $${countParamIndex}`;
      countParamIndex++;
    }

    if (minRating) {
      countQuery += ` AND r.average_rating >= $${countParamIndex}`;
      countParamIndex++;
    }

    const countResult = await database.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    // Format response
    const users = result.rows.map(user => ({
      id: user.id,
      email: isAuthenticated ? user.email : undefined,
      emailVerified: isAuthenticated ? user.email_verified : undefined,
      displayName: user.display_name,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      userType: user.user_type,
      isActive: user.is_active,
      profileCompleted: user.profile_completed,
      createdAt: user.created_at,
      lastLogin: user.last_login,
      profile: {
        bio: user.bio,
        title: user.title,
        location: {
          country: user.country,
          city: user.city
        },
        hourlyRate: user.hourly_rate,
        currency: user.currency,
        availability: user.availability,
        yearsExperience: user.years_experience
      },
      reputation: {
        score: user.reputation_score || 0,
        level: user.reputation_level || 'newcomer',
        averageRating: user.average_rating || 0,
        totalReviews: user.total_reviews || 0
      }
    }));

    const pagination = {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      totalPages: Math.ceil(total / limit),
      hasMore: offset + users.length < total
    };

    res.success({
      users,
      pagination,
      filters: {
        userType,
        isActive,
        search,
        skills,
        location,
        availability,
        minRating,
        sortBy: sortField,
        sortOrder: order
      }
    }, 'Users retrieved successfully');

  } catch (error) {
    console.error('❌ Get users error:', error);
    res.error('Failed to retrieve users', 500, error.message);
  }
});

/**
 * @route GET /api/v1/users/:id
 * @desc Get user by ID
 * @access Public (limited data without auth)
 */
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const isAuthenticated = !!req.user;
    const isOwnProfile = req.user?.uid === id;

    console.log('👤 Get user by ID:', { id, authenticated: isAuthenticated, ownProfile: isOwnProfile });

    // Get user from database
    const query = `
      SELECT 
        u.*,
        p.*,
        r.*
      FROM users u
      LEFT JOIN user_profiles p ON u.id = p.user_id
      LEFT JOIN user_reputation r ON u.id = r.user_id
      WHERE u.id = $1 AND u.is_active = true
    `;

    const result = await database.query(query, [id]);

    if (result.rows.length === 0) {
      return res.error('User not found', 404);
    }

    const user = result.rows[0];

    // Format response based on authentication and ownership
    const response = {
      id: user.id,
      displayName: user.display_name,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      userType: user.user_type,
      profileCompleted: user.profile_completed,
      createdAt: user.created_at,
      
      // Only show sensitive data to authenticated users or own profile
      ...(isAuthenticated && {
        email: user.email,
        emailVerified: user.email_verified,
        phoneNumber: user.phone_number
      }),

      // Only show very sensitive data to own profile
      ...(isOwnProfile && {
        lastLogin: user.last_login,
        onboardingCompleted: user.onboarding_completed
      }),

      profile: {
        bio: user.bio,
        title: user.title,
        company: user.company,
        website: user.website,
        location: {
          country: user.country,
          city: user.city,
          timezone: user.timezone
        },
        hourlyRate: user.hourly_rate,
        currency: user.currency,
        availability: user.availability,
        yearsExperience: user.years_experience,
        
        // Social links (public)
        linkedinUrl: user.linkedin_url,
        githubUrl: user.github_url,
        portfolioUrl: user.portfolio_url,

        // Settings (only own profile)
        ...(isOwnProfile && {
          languagePreference: user.language_preference,
          emailNotifications: user.email_notifications,
          marketingEmails: user.marketing_emails
        })
      },

      reputation: {
        score: user.reputation_score || 0,
        level: user.reputation_level || 'newcomer',
        averageRating: user.average_rating || 0,
        totalReviews: user.total_reviews || 0,
        totalPosts: user.total_posts || 0,
        totalComments: user.total_comments || 0,
        totalLikesReceived: user.total_likes_received || 0,
        totalProjectsCompleted: user.total_projects_completed || 0
      }
    };

    // Get user skills
    const skillsQuery = `
      SELECT skill_name, skill_level, years_experience, is_verified
      FROM user_skills
      WHERE user_id = $1
      ORDER BY skill_level DESC, years_experience DESC
    `;
    
    const skillsResult = await database.query(skillsQuery, [id]);
    response.skills = skillsResult.rows;

    res.success(response, 'User retrieved successfully');

  } catch (error) {
    console.error('❌ Get user by ID error:', error);
    res.error('Failed to retrieve user', 500, error.message);
  }
});

/**
 * @route POST /api/v1/users
 * @desc Create or sync user from Firebase (backward compatibility)
 * @access Protected (Firebase Auth)
 */
router.post('/', verifyFirebaseToken, async (req, res) => {
  try {
    const firebaseUser = req.user;
    const { 
      userType = 'freelancer',
      firstName,
      lastName,
      phoneNumber,
      profile = {}
    } = req.body;

    console.log('👤 Create/sync user:', {
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      userType
    });

    // Check if user already exists
    const existingQuery = `SELECT * FROM users WHERE id = $1`;
    const existingResult = await database.query(existingQuery, [firebaseUser.uid]);

    if (existingResult.rows.length > 0) {
      // User exists, update their info
      const updateQuery = `
        UPDATE users SET
          email = $2,
          email_verified = $3,
          display_name = $4,
          first_name = $5,
          last_name = $6,
          phone_number = $7,
          avatar_url = $8,
          last_login = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `;

      const updateResult = await database.query(updateQuery, [
        firebaseUser.uid,
        firebaseUser.email,
        firebaseUser.emailVerified,
        firebaseUser.displayName || `${firstName || ''} ${lastName || ''}`.trim(),
        firstName || firebaseUser.displayName?.split(' ')[0],
        lastName || firebaseUser.displayName?.split(' ').slice(1).join(' '),
        phoneNumber || firebaseUser.phoneNumber,
        firebaseUser.photoURL
      ]);

      const user = updateResult.rows[0];
      
      res.success({
        user: {
          id: user.id,
          email: user.email,
          displayName: user.display_name,
          userType: user.user_type,
          profileCompleted: user.profile_completed,
          isActive: user.is_active
        },
        isNewUser: false
      }, 'User synchronized successfully');

    } else {
      // Create new user
      await database.transaction(async (client) => {
        // Insert user
        const insertUserQuery = `
          INSERT INTO users (
            id, email, email_verified, display_name, first_name, last_name,
            phone_number, avatar_url, user_type, last_login
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
          RETURNING *
        `;

        const userResult = await client.query(insertUserQuery, [
          firebaseUser.uid,
          firebaseUser.email,
          firebaseUser.emailVerified,
          firebaseUser.displayName || `${firstName || ''} ${lastName || ''}`.trim(),
          firstName || firebaseUser.displayName?.split(' ')[0],
          lastName || firebaseUser.displayName?.split(' ').slice(1).join(' '),
          phoneNumber || firebaseUser.phoneNumber,
          firebaseUser.photoURL,
          userType
        ]);

        const user = userResult.rows[0];

        // Create user profile
        const insertProfileQuery = `
          INSERT INTO user_profiles (user_id, bio, language_preference)
          VALUES ($1, $2, $3)
        `;

        await client.query(insertProfileQuery, [
          user.id,
          profile.bio || '',
          profile.languagePreference || 'vi'
        ]);

        // Create user reputation
        const insertReputationQuery = `
          INSERT INTO user_reputation (user_id)
          VALUES ($1)
        `;

        await client.query(insertReputationQuery, [user.id]);

        res.success({
          user: {
            id: user.id,
            email: user.email,
            displayName: user.display_name,
            userType: user.user_type,
            profileCompleted: user.profile_completed,
            isActive: user.is_active
          },
          isNewUser: true
        }, 'User created successfully', 201);
      });
    }

  } catch (error) {
    console.error('❌ Create/sync user error:', error);
    res.error('Failed to create/sync user', 500, error.message);
  }
});

/**
 * @route POST /api/v1/users/login
 * @desc Login user (sync with Firebase)
 * @access Public (Firebase Auth)
 */
router.post('/login', verifyFirebaseToken, async (req, res) => {
  try {
    const firebaseUser = req.user;

    console.log('👤 Login user:', {
      uid: firebaseUser.uid,
      email: firebaseUser.email
    });

    // Check if user exists
    const existingQuery = `SELECT * FROM users WHERE id = $1`;
    const existingResult = await database.query(existingQuery, [firebaseUser.uid]);

    if (existingResult.rows.length === 0) {
      return res.error('User not found. Please register first.', 404);
    }

    const user = existingResult.rows[0];

    // Update last login
    const updateQuery = `
      UPDATE users SET
        last_login = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `;

    await database.query(updateQuery, [firebaseUser.uid]);

    res.success({
      user: {
        id: user.id,
        email: user.email,
        displayName: user.display_name,
        userType: user.user_type,
        profileCompleted: user.profile_completed,
        isActive: user.is_active
      },
      message: 'Login successful'
    }, 'Login successful');

  } catch (error) {
    console.error('❌ User login error:', error);
    res.error('Failed to login user', 500, error.message);
  }
});

/**
 * @route GET /api/v1/users/me
 * @desc Get current user profile
 * @access Protected (Firebase Auth)
 */
router.get('/me', verifyFirebaseToken, requireAuth, async (req, res) => {
  try {
    const firebaseUser = req.user;

    const query = `
      SELECT 
        u.*,
        p.bio, p.title, p.company, p.website, p.country, p.city, p.timezone,
        p.hourly_rate, p.currency, p.availability, p.years_experience,
        p.linkedin_url, p.github_url, p.portfolio_url,
        r.reputation_score, r.level, r.average_rating, r.total_reviews,
        r.total_posts, r.total_comments, r.total_likes_received
      FROM users u
      LEFT JOIN user_profiles p ON u.id = p.user_id
      LEFT JOIN user_reputation r ON u.id = r.user_id
      WHERE u.id = $1
    `;

    const result = await database.query(query, [firebaseUser.uid]);

    if (result.rows.length === 0) {
      return res.error('User not found', 404);
    }

    const user = result.rows[0];

    res.success({
      id: user.id,
      email: user.email,
      displayName: user.display_name,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      userType: user.user_type,
      profileCompleted: user.profile_completed,
      onboardingCompleted: user.onboarding_completed,
      isActive: user.is_active,
      createdAt: user.created_at,
      lastLogin: user.last_login,
      
      profile: {
        bio: user.bio,
        title: user.title,
        company: user.company,
        website: user.website,
        location: {
          country: user.country,
          city: user.city,
          timezone: user.timezone
        },
        hourlyRate: user.hourly_rate,
        currency: user.currency,
        availability: user.availability,
        yearsExperience: user.years_experience,
        linkedinUrl: user.linkedin_url,
        githubUrl: user.github_url,
        portfolioUrl: user.portfolio_url
      },

      reputation: {
        score: user.reputation_score || 0,
        level: user.level || 'newcomer',
        averageRating: user.average_rating || 0,
        totalReviews: user.total_reviews || 0,
        totalPosts: user.total_posts || 0,
        totalComments: user.total_comments || 0,
        totalLikesReceived: user.total_likes_received || 0
      }
    }, 'User profile retrieved successfully');

  } catch (error) {
    console.error('❌ Get user profile error:', error);
    res.error('Failed to get user profile', 500, error.message);
  }
});

/**
 * @route PUT /api/v1/users/:id
 * @desc Update user basic information
 * @access Protected (own profile only)
 */
router.put('/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    const firebaseUser = req.user;

    // Check ownership
    if (firebaseUser.uid !== id) {
      return res.error('You can only update your own profile', 403);
    }

    const {
      displayName,
      firstName,
      lastName,
      phoneNumber,
      userType
    } = req.body;

    console.log('📝 Update user:', { id, updates: Object.keys(req.body) });

    const updateQuery = `
      UPDATE users SET
        display_name = COALESCE($2, display_name),
        first_name = COALESCE($3, first_name),
        last_name = COALESCE($4, last_name),
        phone_number = COALESCE($5, phone_number),
        user_type = COALESCE($6, user_type),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_active = true
      RETURNING *
    `;

    const result = await database.query(updateQuery, [
      id,
      displayName,
      firstName,
      lastName,
      phoneNumber,
      userType
    ]);

    if (result.rows.length === 0) {
      return res.error('User not found', 404);
    }

    const user = result.rows[0];

    res.success({
      id: user.id,
      email: user.email,
      displayName: user.display_name,
      firstName: user.first_name,
      lastName: user.last_name,
      phoneNumber: user.phone_number,
      userType: user.user_type,
      updatedAt: user.updated_at
    }, 'User updated successfully');

  } catch (error) {
    console.error('❌ Update user error:', error);
    res.error('Failed to update user', 500, error.message);
  }
});

/**
 * @route DELETE /api/v1/users/:id
 * @desc Deactivate user account
 * @access Protected (own profile only)
 */
router.delete('/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    const firebaseUser = req.user;

    // Check ownership
    if (firebaseUser.uid !== id) {
      return res.error('You can only deactivate your own account', 403);
    }

    console.log('🗑️ Deactivate user:', { id });

    const updateQuery = `
      UPDATE users SET
        is_active = false,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING id, email, is_active
    `;

    const result = await database.query(updateQuery, [id]);

    if (result.rows.length === 0) {
      return res.error('User not found', 404);
    }

    res.success({
      id: result.rows[0].id,
      isActive: result.rows[0].is_active
    }, 'User account deactivated successfully');

  } catch (error) {
    console.error('❌ Deactivate user error:', error);
    res.error('Failed to deactivate user', 500, error.message);
  }
});

/**
 * @route GET /api/v1/users/search
 * @desc Search users with advanced filters
 * @access Public
 */
router.get('/search', optionalAuth, async (req, res) => {
  try {
    const { 
      q, 
      skills, 
      location, 
      userType = 'freelancer',
      availability,
      minRating,
      maxRate,
      minRate,
      page = 1,
      limit = 20 
    } = req.query;

    console.log('🔍 Search users:', { q, skills, location, userType });

    // Build search query
    let query = `
      SELECT DISTINCT
        u.id, u.display_name, u.first_name, u.last_name, u.avatar_url,
        u.user_type, u.created_at,
        p.bio, p.title, p.country, p.city, p.hourly_rate, p.currency, p.availability,
        r.reputation_score, r.level, r.average_rating, r.total_reviews
      FROM users u
      LEFT JOIN user_profiles p ON u.id = p.user_id
      LEFT JOIN user_reputation r ON u.id = r.user_id
      LEFT JOIN user_skills s ON u.id = s.user_id
      WHERE u.is_active = true AND u.user_type = $1
    `;

    const queryParams = [userType];
    let paramIndex = 2;

    if (q) {
      query += ` AND (
        u.display_name ILIKE $${paramIndex} OR 
        p.title ILIKE $${paramIndex} OR 
        p.bio ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${q}%`);
      paramIndex++;
    }

    if (skills) {
      const skillArray = skills.split(',').map(skill => skill.trim());
      query += ` AND s.skill_name = ANY($${paramIndex})`;
      queryParams.push(skillArray);
      paramIndex++;
    }

    if (location) {
      query += ` AND (p.country ILIKE $${paramIndex} OR p.city ILIKE $${paramIndex})`;
      queryParams.push(`%${location}%`);
      paramIndex++;
    }

    if (availability) {
      query += ` AND p.availability = $${paramIndex}`;
      queryParams.push(availability);
      paramIndex++;
    }

    if (minRating) {
      query += ` AND r.average_rating >= $${paramIndex}`;
      queryParams.push(parseFloat(minRating));
      paramIndex++;
    }

    if (minRate) {
      query += ` AND p.hourly_rate >= $${paramIndex}`;
      queryParams.push(parseFloat(minRate));
      paramIndex++;
    }

    if (maxRate) {
      query += ` AND p.hourly_rate <= $${paramIndex}`;
      queryParams.push(parseFloat(maxRate));
      paramIndex++;
    }

    query += ` ORDER BY r.reputation_score DESC NULLS LAST, r.average_rating DESC NULLS LAST`;

    const offset = (page - 1) * limit;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(parseInt(limit), offset);

    const result = await database.query(query, queryParams);

    const users = result.rows.map(user => ({
      id: user.id,
      displayName: user.display_name,
      firstName: user.first_name,
      lastName: user.last_name,
      avatarUrl: user.avatar_url,
      userType: user.user_type,
      profile: {
        bio: user.bio,
        title: user.title,
        location: {
          country: user.country,
          city: user.city
        },
        hourlyRate: user.hourly_rate,
        currency: user.currency,
        availability: user.availability
      },
      reputation: {
        score: user.reputation_score || 0,
        level: user.level || 'newcomer',
        averageRating: user.average_rating || 0,
        totalReviews: user.total_reviews || 0
      }
    }));

    res.success({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: users.length,
        hasMore: users.length === parseInt(limit)
      },
      query: { q, skills, location, userType, availability, minRating, maxRate, minRate }
    }, 'Search completed successfully');

  } catch (error) {
    console.error('❌ Search users error:', error);
    res.error('Failed to search users', 500, error.message);
  }
});

/**
 * @route POST /api/v1/users/sync-firebase
 * @desc Sync Firebase user with database (temporary mock implementation)
 * @access Public (Firebase Auth)
 */
router.post('/sync-firebase', async (req, res) => {
  try {
    const { firebaseUID, email, displayName } = req.body;

    if (!firebaseUID) {
      return res.status(400).json({ error: 'Firebase UID is required' });
    }

    console.log('🔄 Syncing Firebase user (mock):', {
      firebaseUID,
      email,
      displayName
    });

    // Mock user data (temporary solution until database is properly configured)
    let firstName = 'Firebase';
    let lastName = 'User';

    if (displayName && displayName !== 'Firebase User') {
      const nameParts = displayName.split(' ');
      firstName = nameParts[0] || 'Firebase';
      lastName = nameParts.slice(1).join(' ') || 'User';
    } else if (email) {
      // Use email username as first name
      firstName = email.split('@')[0];
      lastName = 'User';
    }

    const mockUser = {
      id: firebaseUID,
      email: email || `${firebaseUID}@firebase.com`,
      firstName: firstName,
      lastName: lastName,
      displayName: displayName || `${firstName} ${lastName}`,
      avatarUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2MzY2RjEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iOCIgeT0iOCI+CjxwYXRoIGQ9Ik0xMiAxMkM5Ljc5IDEyIDggMTAuMjEgOCA4UzkuNzkgNiAxMiA2UzE2IDcuNzkgMTYgOFMxNC4yMSAxMiAxMiAxMlpNMTIgMTRDMTYuNDIgMTQgMjAgMTUuNzkgMjAgMThWMjBIMFYxOEMwIDE1Ljc5IDMuNTggMTQgOCAxNEgxMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K',
      emailVerified: true,
      isActive: true,
      userType: 'freelancer',
      profileCompleted: false,
      onboardingCompleted: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // TODO: Replace with actual database operations when PostgreSQL is properly configured
    console.log('✅ Mock user created:', mockUser);

    res.json({
      message: 'Firebase user synced successfully (mock)',
      user: mockUser
    });

  } catch (error) {
    console.error('❌ Error syncing Firebase user:', error);
    res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message,
      note: 'This is a mock implementation. Database connection needs to be configured.'
    });
  }
});

module.exports = router; 