@echo off
REM VWork Platform - Install All Dependencies (Batch)
REM Usage: install-all.bat [clean] [audit]

setlocal enabledelayedexpansion

echo.
echo ========================================
echo VWork Platform - Install All Dependencies
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not accessible
    echo Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed or not accessible
    pause
    exit /b 1
)

echo [INFO] Checking prerequisites...
node --version
npm --version
echo.

REM Check arguments
set "CLEAN_MODE="
set "AUDIT_ONLY="

for %%a in (%*) do (
    if /i "%%a"=="clean" set "CLEAN_MODE=1"
    if /i "%%a"=="audit" set "AUDIT_ONLY=1"
    if /i "%%a"=="help" goto :show_help
)

if "%AUDIT_ONLY%"=="1" goto :audit_only

REM Clean mode
if "%CLEAN_MODE%"=="1" (
    echo [INFO] Cleaning all node_modules and package-lock files...
    
    REM Root project
    if exist "node_modules" (
        echo [INFO] Removing node_modules from Root Project...
        rmdir /s /q "node_modules" 2>nul
    )
    if exist "package-lock.json" (
        echo [INFO] Removing package-lock.json from Root Project...
        del "package-lock.json" 2>nul
    )
    
    REM Client
    if exist "client\node_modules" (
        echo [INFO] Removing node_modules from Client...
        rmdir /s /q "client\node_modules" 2>nul
    )
    if exist "client\package-lock.json" (
        echo [INFO] Removing package-lock.json from Client...
        del "client\package-lock.json" 2>nul
    )
    
    REM Services
    for %%s in (api-gateway chat-service community-service job-service payment-service project-service team-service user-service) do (
        if exist "services\%%s\node_modules" (
            echo [INFO] Removing node_modules from %%s...
            rmdir /s /q "services\%%s\node_modules" 2>nul
        )
        if exist "services\%%s\package-lock.json" (
            echo [INFO] Removing package-lock.json from %%s...
            del "services\%%s\package-lock.json" 2>nul
        )
    )
    
    echo [SUCCESS] Cleanup completed
    echo.
)

echo [INFO] Installing dependencies...
echo.

REM Install Root Project
echo [INFO] Installing Root Project dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Root Project dependencies
    goto :error_exit
)
echo [SUCCESS] Root Project dependencies installed successfully
echo.

REM Install Client
echo [INFO] Installing Client dependencies...
cd client
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Client dependencies
    cd ..
    goto :error_exit
)
echo [SUCCESS] Client dependencies installed successfully
cd ..
echo.

REM Install Services
for %%s in (api-gateway chat-service community-service job-service payment-service project-service team-service user-service) do (
    echo [INFO] Installing %%s dependencies...
    cd services\%%s
    call npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install %%s dependencies
        cd ..\..
        goto :error_exit
    )
    echo [SUCCESS] %%s dependencies installed successfully
    cd ..\..
    echo.
)

goto :run_audit

:audit_only
echo [INFO] Running security audits only...
echo.

:run_audit
echo [INFO] Running security audits...
echo.

REM Audit Root Project
echo [INFO] Auditing Root Project...
call npm audit --audit-level=high
if %errorlevel% neq 0 (
    echo [WARNING] Root Project - Vulnerabilities found. Run 'npm audit fix' to fix them.
) else (
    echo [SUCCESS] Root Project - No high/critical vulnerabilities found
)
echo.

REM Audit Client
echo [INFO] Auditing Client...
cd client
call npm audit --audit-level=high
if %errorlevel% neq 0 (
    echo [WARNING] Client - Vulnerabilities found. Run 'npm audit fix' to fix them.
) else (
    echo [SUCCESS] Client - No high/critical vulnerabilities found
)
cd ..
echo.

REM Audit Services
for %%s in (api-gateway chat-service community-service job-service payment-service project-service team-service user-service) do (
    echo [INFO] Auditing %%s...
    cd services\%%s
    call npm audit --audit-level=high
    if %errorlevel% neq 0 (
        echo [WARNING] %%s - Vulnerabilities found. Run 'npm audit fix' to fix them.
    ) else (
        echo [SUCCESS] %%s - No high/critical vulnerabilities found
    )
    cd ..\..
    echo.
)

echo ========================================
echo Installation Summary
echo ========================================
echo [SUCCESS] All dependencies installed successfully!
echo [INFO] You can now run: npm start
echo.
echo Setup Complete!
echo ========================================
pause
exit /b 0

:show_help
echo Usage: install-all.bat [options]
echo.
echo Options:
echo   clean    Clean all node_modules before installing
echo   audit    Only run security audits
echo   help     Show this help message
echo.
echo Examples:
echo   install-all.bat
echo   install-all.bat clean
echo   install-all.bat audit
echo.
pause
exit /b 0

:error_exit
echo.
echo [ERROR] Installation failed. Please check the errors above.
pause
exit /b 1 