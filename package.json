{"name": "nerafus-platform", "version": "1.0.0", "description": "NERAFUS - New Era For Us", "private": true, "workspaces": ["client", "services/*"], "main": "scripts/unified-start.js", "scripts": {"start": "node scripts/core/start.js all", "start:dev": "node scripts/core/start.js all --dev", "start:services": "node scripts/core/start.js services", "start:client": "node scripts/core/start.js client", "start:client-only": "cd client && npm start", "stop": "node scripts/core/start.js stop", "build": "cd client && npm run build", "build:services": "node scripts/core/build.js service", "build:client": "node scripts/core/build.js client", "build:production": "node scripts/core/build.js all --production", "deploy": "node scripts/core/deploy.js all", "deploy:service": "node scripts/core/deploy.js service", "deploy:client": "node scripts/core/deploy.js client", "deploy:prepare": "node scripts/core/deploy.js prepare", "test": "cd client && npm test", "test:unit": "node scripts/core/test.js unit", "test:integration": "node scripts/core/test.js integration", "test:e2e": "node scripts/core/test.js e2e", "test:watch": "node scripts/core/test.js unit --watch", "test:coverage": "node scripts/core/test.js coverage --open", "test:performance": "node scripts/core/test.js performance", "test:security": "node scripts/core/test.js security", "test:services": "npm run test --workspaces --if-present", "test:client": "npm run test --workspace=client", "test:registration": "node scripts/test-registration-flow.js", "install:all": "node scripts/install-all.js", "install:all:ps": "powershell -ExecutionPolicy Bypass -File install-all.ps1", "install:all:bat": "install-all-simple.bat", "install:clean": "npm run clean:all && npm run install:all", "install:clean:ps": "powershell -ExecutionPolicy Bypass -File install-all.ps1 -Clean", "install:clean:bat": "install-all.bat clean", "install:audit": "node scripts/install-all.js --audit-only", "install:audit:ps": "powershell -ExecutionPolicy Bypass -File install-all.ps1 -AuditOnly", "install:audit:bat": "install-all.bat audit", "setup": "npm run setup:all", "setup:service": "node scripts/setup-all.js service", "architecture": "node scripts/show-architecture.js", "architecture:service": "node scripts/show-architecture.js service", "architecture:env": "node scripts/show-architecture.js env", "setup:workspace": "node scripts/migrate-to-workspace.js migrate", "config:validate": "node scripts/core/config.js validate", "config:generate": "node scripts/core/config.js generate", "config:compare": "node scripts/core/config.js compare", "config:template": "node scripts/core/config.js template", "clean": "node scripts/core/build.js clean", "clean:all": "npm run clean --workspaces --if-present && rm -rf node_modules", "lint": "node scripts/core/quality.js lint", "lint:fix": "node scripts/core/quality.js lint --fix", "lint:check": "node scripts/core/quality.js lint", "format": "node scripts/core/quality.js format", "format:check": "node scripts/core/quality.js format --check", "type-check": "node scripts/core/quality.js type-check", "quality": "node scripts/core/quality.js all", "quality:report": "node scripts/core/quality.js report", "quality:setup": "node scripts/core/quality.js setup-hooks", "audit:scripts": "node scripts/audit-scripts.js", "health": "node scripts/core/monitor.js health", "monitor": "node scripts/core/monitor.js watch", "monitor:report": "node scripts/core/monitor.js report", "monitor:test": "node scripts/core/monitor.js test", "metrics": "node scripts/core/monitor.js metrics", "cleanup": "node scripts/core/cleanup.js clean --backup", "cleanup:list": "node scripts/core/cleanup.js list", "cleanup:force": "node scripts/core/cleanup.js clean --force", "deploy:community": "node scripts/deploy-community-service.js", "deploy:community:test": "node scripts/deploy-community-service.js --test-only", "deploy:community:init": "node scripts/deploy-community-service.js --init-only", "verify:community": "node scripts/verify-community-deployment.js"}, "keywords": ["freelancing", "platform", "microservices", "react", "nodejs", "firebase"], "author": "VWork Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/VinkRasengan/Vwork.git"}, "bugs": {"url": "https://github.com/VinkRasengan/Vwork/issues"}, "homepage": "https://github.com/VinkRasengan/Vwork#readme", "devDependencies": {"@babel/preset-env": "^7.23.3", "@babel/preset-react": "^7.23.3", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "supertest": "^6.3.3"}, "dependencies": {"axios": "^1.6.0", "dotenv": "^16.6.1", "node-fetch": "^3.3.2"}}