import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';
import { apiService } from '../../../services/api';
import {
 ArrowLeftIcon,
 CurrencyDollarIcon,
 CalendarIcon,
 DocumentTextIcon,
 CheckCircleIcon,
 ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const AppleJobCreatePage = () => {
 const { t } = useLanguage();
 const { user } = useAuth();
 const navigate = useNavigate();
 const [submitting, setSubmitting] = useState(false);
 const [formData, setFormData] = useState({
  title: '',
  description: '',
  category: '',
  budget: { min: '', max: '' },
  location: '',
  experience: '',
  deadline: '',
  skills: [],
  requirements: [''],
  benefits: [''],
  isUrgent: false,
  isFeatured: false,
 });

 const categories = [
  'Web Development',
  'Mobile Development', 
  'Design',
  'Content Writing',
  'AI/ML',
  'Data Entry',
  'Video & Animation',
  'Translation',
  'Marketing',
  'Other'
 ];

 const experienceLevels = [
  '0-1 years',
  '1-3 years', 
  '3-5 years',
  '5+ years'
 ];

 const handleInputChange = (e) => {
  const { name, value, type, checked } = e.target;
  
  if (name === 'budget.min' || name === 'budget.max') {
   setFormData(prev => ({
    ...prev,
    budget: {
     ...prev.budget,
     [name.split('.')[1]]: value
    }
   }));
  } else if (type === 'checkbox') {
   setFormData(prev => ({
    ...prev,
    [name]: checked
   }));
  } else {
   setFormData(prev => ({
    ...prev,
    [name]: value
   }));
  }
 };

 const handleArrayChange = (field, index, value) => {
  setFormData(prev => ({
   ...prev,
   [field]: prev[field].map((item, i) => i === index ? value : item)
  }));
 };

 const addArrayItem = (field) => {
  setFormData(prev => ({
   ...prev,
   [field]: [...prev[field], '']
  }));
 };

 const removeArrayItem = (field, index) => {
  setFormData(prev => ({
   ...prev,
   [field]: prev[field].filter((_, i) => i !== index)
  }));
 };

 const handleSkillsChange = (e) => {
  const skills = e.target.value.split(',').map(skill => skill.trim()).filter(skill => skill);
  setFormData(prev => ({
   ...prev,
   skills
  }));
 };

 const validateForm = () => {
  if (!formData.title.trim()) return 'Vui lòng nhập tiêu đề công việc';
  if (!formData.description.trim()) return 'Vui lòng nhập mô tả công việc';
  if (!formData.category) return 'Vui lòng chọn danh mục';
  if (!formData.budget.min || !formData.budget.max) return 'Vui lòng nhập ngân sách';
  if (parseInt(formData.budget.min) > parseInt(formData.budget.max)) return 'Ngân sách tối thiểu không thể lớn hơn tối đa';
  if (!formData.location.trim()) return 'Vui lòng nhập địa điểm';
  if (!formData.experience) return 'Vui lòng chọn mức độ kinh nghiệm';
  if (!formData.deadline) return 'Vui lòng chọn hạn nộp';
  if (formData.skills.length === 0) return 'Vui lòng nhập ít nhất một kỹ năng';
  if (formData.requirements.length === 0 || !formData.requirements[0].trim()) return 'Vui lòng nhập ít nhất một yêu cầu';
  return null;
 };

 const handleSubmit = async (e) => {
  e.preventDefault();
  
  const error = validateForm();
  if (error) {
   alert(error);
   return;
  }

  try {
   setSubmitting(true);
   const response = await apiService.jobs.create(formData);
   
   if (response.success) {
    alert('Tạo công việc thành công!');
    navigate('/jobs');
   } else {
    alert('Có lỗi xảy ra: ' + response.message);
   }
  } catch (error) {
   console.error('Error creating job:', error);
   alert('Có lỗi xảy ra khi tạo công việc');
  } finally {
   setSubmitting(false);
  }
 };

 return (
  <div className="min-h-screen bg-gray-50">
   {/* Header */}
   <div className="bg-white border-b border-gray-200">
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
     <div className="flex items-center space-x-4">
      <button
       onClick={() => navigate('/jobs')}
       className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
      >
       <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
      </button>
      <div>
       <h1 className="text-2xl font-bold text-gray-900">
        Đăng công việc mới
       </h1>
       <p className="text-gray-600">
        Tạo cơ hội việc làm cho freelancer
       </p>
      </div>
     </div>
    </div>
   </div>

   {/* Main Content */}
   <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
     <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div>
       <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Thông tin cơ bản
       </h2>
       
       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Title */}
        <div className="md:col-span-2">
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Tiêu đề công việc *
         </label>
         <input
          type="text"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Ví dụ: Thiết kế Website E-commerce"
          required
         />
        </div>

        {/* Category */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Danh mục *
         </label>
         <select
          name="category"
          value={formData.category}
          onChange={handleInputChange}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          required
         >
          <option value="">Chọn danh mục</option>
          {categories.map(category => (
           <option key={category} value={category}>{category}</option>
          ))}
         </select>
        </div>

        {/* Location */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Địa điểm *
         </label>
         <input
          type="text"
          name="location"
          value={formData.location}
          onChange={handleInputChange}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Ví dụ: Hồ Chí Minh, Remote"
          required
         />
        </div>

        {/* Experience */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Mức độ kinh nghiệm *
         </label>
         <select
          name="experience"
          value={formData.experience}
          onChange={handleInputChange}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          required
         >
          <option value="">Chọn mức độ</option>
          {experienceLevels.map(level => (
           <option key={level} value={level}>{level}</option>
          ))}
         </select>
        </div>

        {/* Deadline */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Hạn nộp *
         </label>
         <div className="relative">
          <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
           type="date"
           name="deadline"
           value={formData.deadline}
           onChange={handleInputChange}
           className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
           required
          />
         </div>
        </div>
       </div>
      </div>

      {/* Budget */}
      <div>
       <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Ngân sách
       </h2>
       
       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Ngân sách tối thiểu (VNĐ) *
         </label>
         <div className="relative">
          <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
           type="number"
           name="budget.min"
           value={formData.budget.min}
           onChange={handleInputChange}
           className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
           placeholder="1000000"
           required
          />
         </div>
        </div>

        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          Ngân sách tối đa (VNĐ) *
         </label>
         <div className="relative">
          <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
           type="number"
           name="budget.max"
           value={formData.budget.max}
           onChange={handleInputChange}
           className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
           placeholder="5000000"
           required
          />
         </div>
        </div>
       </div>
      </div>

      {/* Description */}
      <div>
       <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Mô tả công việc
       </h2>
       
       <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
         Mô tả chi tiết *
        </label>
        <textarea
         name="description"
         value={formData.description}
         onChange={handleInputChange}
         rows={8}
         className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
         placeholder="Mô tả chi tiết về công việc, yêu cầu, và kỳ vọng..."
         required
        />
       </div>
      </div>

      {/* Skills */}
      <div>
       <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Kỹ năng yêu cầu
       </h2>
       
       <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
         Kỹ năng (phân cách bằng dấu phẩy) *
        </label>
        <input
         type="text"
         value={formData.skills.join(', ')}
         onChange={handleSkillsChange}
         className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
         placeholder="Ví dụ: React, Node.js, MongoDB"
         required
        />
       </div>
      </div>

      {/* Requirements */}
      <div>
       <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Yêu cầu
       </h2>
       
       <div className="space-y-3">
        {formData.requirements.map((requirement, index) => (
         <div key={index} className="flex space-x-2">
          <input
           type="text"
           value={requirement}
           onChange={(e) => handleArrayChange('requirements', index, e.target.value)}
           className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
           placeholder="Nhập yêu cầu..."
           required={index === 0}
          />
          {formData.requirements.length > 1 && (
           <button
            type="button"
            onClick={() => removeArrayItem('requirements', index)}
            className="px-3 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
           >
            Xóa
           </button>
          )}
         </div>
        ))}
        <button
         type="button"
         onClick={() => addArrayItem('requirements')}
         className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
        >
         + Thêm yêu cầu
        </button>
       </div>
      </div>

      {/* Benefits */}
      <div>
       <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Quyền lợi (tùy chọn)
       </h2>
       
       <div className="space-y-3">
        {formData.benefits.map((benefit, index) => (
         <div key={index} className="flex space-x-2">
          <input
           type="text"
           value={benefit}
           onChange={(e) => handleArrayChange('benefits', index, e.target.value)}
           className="flex-1 px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
           placeholder="Nhập quyền lợi..."
          />
          <button
           type="button"
           onClick={() => removeArrayItem('benefits', index)}
           className="px-3 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
           Xóa
          </button>
         </div>
        ))}
        <button
         type="button"
         onClick={() => addArrayItem('benefits')}
         className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
        >
         + Thêm quyền lợi
        </button>
       </div>
      </div>

      {/* Options */}
      <div>
       <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Tùy chọn
       </h2>
       
       <div className="space-y-4">
        <div className="flex items-center space-x-3">
         <input
          type="checkbox"
          name="isUrgent"
          checked={formData.isUrgent}
          onChange={handleInputChange}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
         />
         <label className="text-sm font-medium text-gray-700">
          Công việc khẩn cấp
         </label>
        </div>

        <div className="flex items-center space-x-3">
         <input
          type="checkbox"
          name="isFeatured"
          checked={formData.isFeatured}
          onChange={handleInputChange}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
         />
         <label className="text-sm font-medium text-gray-700">
          Công việc nổi bật
         </label>
        </div>
       </div>
      </div>

      {/* Submit */}
      <div className="flex items-center justify-between pt-6">
       <button
        type="button"
        onClick={() => navigate('/jobs')}
        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
       >
        Hủy
       </button>
       <button
        type="submit"
        disabled={submitting}
        className="px-8 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
       >
        {submitting ? (
         <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>Đang tạo...</span>
         </>
        ) : (
         <>
          <CheckCircleIcon className="h-5 w-5" />
          <span>Tạo công việc</span>
         </>
        )}
       </button>
      </div>
     </form>
    </div>
   </div>
  </div>
 );
};

export default AppleJobCreatePage; 
