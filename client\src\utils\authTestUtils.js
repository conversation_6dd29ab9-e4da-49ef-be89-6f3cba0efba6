/**
 * Auth Testing Utilities
 * Utilities to help test and debug auth-related issues
 */

export const authTestUtils = {
 /**
  * Test rapid mode switching to check for race conditions
  */
 testRapidModeSwitch: (toggleFunction, iterations = 10) => {
  console.log('🧪 Testing rapid mode switching...');
  
  let switchCount = 0;
  const interval = setInterval(() => {
   if (switchCount >= iterations) {
    clearInterval(interval);
    console.log('✅ Rapid mode switch test completed');
    return;
   }
   
   console.log(`🔄 Switch ${switchCount + 1}/${iterations}`);
   toggleFunction();
   switchCount++;
  }, 100); // Switch every 100ms
 },

 /**
  * Monitor button states
  */
 monitorButtonStates: () => {
  const buttons = document.querySelectorAll('button');
  const states = Array.from(buttons).map(button => ({
   text: button.textContent?.trim(),
   disabled: button.disabled,
   className: button.className,
   pointerEvents: window.getComputedStyle(button).pointerEvents
  }));
  
  console.log('🔍 Button states:', states);
  return states;
 },

 /**
  * Check for animation conflicts
  */
 checkAnimationConflicts: () => {
  // Check for GSAP tweens
  const gsapTweens = window.gsap?.globalTimeline?.getChildren() || [];
  console.log('🎬 Active GSAP tweens:', gsapTweens.length);
  
  // Check for CSS animations
  const animatedElements = document.querySelectorAll('*');
  const cssAnimations = Array.from(animatedElements).filter(el => {
   const style = window.getComputedStyle(el);
   return style.animationName !== 'none' || style.transitionProperty !== 'none';
  });
  
  console.log('🎨 Elements with CSS animations/transitions:', cssAnimations.length);
  
  return {
   gsapTweens: gsapTweens.length,
   cssAnimations: cssAnimations.length
  };
 },

 /**
  * Test form submission states
  */
 testFormSubmission: () => {
  const form = document.querySelector('form');
  const submitButton = document.querySelector('button[type="submit"]');
  
  if (!form || !submitButton) {
   console.warn('⚠️ Form or submit button not found');
   return;
  }
  
  console.log('📝 Form state:', {
   disabled: submitButton.disabled,
   className: submitButton.className,
   formValid: form.checkValidity()
  });
 },

 /**
  * Simulate loading state
  */
 simulateLoadingState: (duration = 3000) => {
  console.log('⏳ Simulating loading state...');
  
  // Dispatch a custom event to trigger loading state
  window.dispatchEvent(new CustomEvent('auth-loading-test', {
   detail: { loading: true }
  }));
  
  setTimeout(() => {
   window.dispatchEvent(new CustomEvent('auth-loading-test', {
    detail: { loading: false }
   }));
   console.log('✅ Loading state simulation completed');
  }, duration);
 },

 /**
  * Check URL synchronization
  */
 checkUrlSync: () => {
  const url = new URL(window.location);
  const mode = url.searchParams.get('mode');

  console.log('🔗 URL state:', {
   pathname: url.pathname,
   mode: mode,
   fullUrl: url.toString()
  });

  return { pathname: url.pathname, mode };
 },

 /**
  * Test URL navigation and state sync
  */
 testUrlNavigation: () => {
  console.log('🧪 Testing URL navigation and state sync...');

  const testUrls = [
   '/auth?mode=signup',
   '/auth?mode=signin',
   '/auth?mode=login',
   '/auth'
  ];

  let currentIndex = 0;
  const interval = setInterval(() => {
   if (currentIndex >= testUrls.length) {
    clearInterval(interval);
    console.log('✅ URL navigation test completed');
    return;
   }

   const testUrl = testUrls[currentIndex];
   console.log(`🔗 Navigating to: ${testUrl}`);

   // Simulate navigation
   window.history.pushState(null, '', testUrl);

   // Trigger popstate event to simulate browser navigation
   window.dispatchEvent(new PopStateEvent('popstate'));

   // Check state after navigation
   setTimeout(() => {
    authTestUtils.checkUrlSync();
    authTestUtils.monitorButtonStates();
   }, 500);

   currentIndex++;
  }, 2000);
 },

 /**
  * Test Get Started button functionality
  */
 testGetStartedButton: () => {
  console.log('🧪 Testing Get Started button functionality...');

  // Find Get Started button
  const getStartedButton = document.querySelector('a[href*="auth?mode=signup"]');

  if (!getStartedButton) {
   console.warn('⚠️ Get Started button not found');
   return;
  }

  console.log('✅ Get Started button found:', {
   href: getStartedButton.href,
   text: getStartedButton.textContent?.trim()
  });

  // Simulate click
  console.log('🖱️ Simulating Get Started button click...');
  getStartedButton.click();

  // Check result after a delay
  setTimeout(() => {
   const currentUrl = window.location.href;
   const expectedMode = 'signup';
   const actualMode = new URLSearchParams(window.location.search).get('mode');

   console.log('🔍 Get Started button test result:', {
    currentUrl,
    expectedMode,
    actualMode,
    success: actualMode === expectedMode
   });
  }, 1000);
 },

 /**
  * Test login flow specifically
  */
 testLoginFlow: () => {
  console.log('🧪 Testing login flow...');

  // Check if we're on the right page
  const currentUrl = window.location.href;
  const isAuthPage = currentUrl.includes('/auth');

  if (!isAuthPage) {
   console.warn('⚠️ Not on auth page, navigating...');
   window.location.href = '/auth?mode=signin';
   return;
  }

  // Check current mode
  const mode = new URLSearchParams(window.location.search).get('mode');
  console.log('🔍 Current mode:', mode);

  // Check if form is in login mode
  const submitButton = document.querySelector('button[type="submit"]');
  const isLoginForm = submitButton?.textContent?.includes('Đăng Nhập');

  console.log('🔍 Login flow test results:', {
   currentUrl,
   mode,
   isLoginForm,
   submitButtonText: submitButton?.textContent?.trim()
  });

  // Check form fields
  const emailField = document.querySelector('input[name="email"]');
  const passwordField = document.querySelector('input[name="password"]');
  const signupFields = document.querySelectorAll('input[name="firstName"], input[name="lastName"], input[name="confirmPassword"]');

  console.log('🔍 Form fields:', {
   hasEmailField: !!emailField,
   hasPasswordField: !!passwordField,
   hasSignupFields: signupFields.length > 0,
   signupFieldsVisible: Array.from(signupFields).some(field => field.offsetParent !== null)
  });
 },

 /**
  * Run comprehensive auth test suite
  */
 runAuthTestSuite: () => {
  console.log('🧪 Running comprehensive auth test suite...');

  // Test 1: Check initial state
  console.log('1️⃣ Checking initial state...');
  authTestUtils.monitorButtonStates();
  authTestUtils.checkUrlSync();

  // Test 2: Check animations
  console.log('2️⃣ Checking animations...');
  authTestUtils.checkAnimationConflicts();

  // Test 3: Form validation
  console.log('3️⃣ Checking form state...');
  authTestUtils.testFormSubmission();

  // Test 4: URL synchronization
  console.log('4️⃣ Testing URL synchronization...');
  setTimeout(() => {
   authTestUtils.testUrlNavigation();
  }, 1000);

  // Test 5: Login flow
  console.log('5️⃣ Testing login flow...');
  setTimeout(() => {
   authTestUtils.testLoginFlow();
  }, 2000);

  console.log('✅ Auth test suite completed');
 }
};

// Make available globally in development
if (process.env.NODE_ENV === 'development') {
 window.authTestUtils = authTestUtils;
}

export default authTestUtils;
