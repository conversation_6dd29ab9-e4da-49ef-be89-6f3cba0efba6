#!/usr/bin/env node

/**
 * Setup script for VWork Platform
 * Installs all dependencies and sets up databases
 */

const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..');
const SERVICES_DIR = path.join(ROOT_DIR, 'services');
const CLIENT_DIR = path.join(ROOT_DIR, 'client');

// Service configurations (updated without auth-service)
const SERVICES = {
  'api-gateway': { port: 8080, priority: 1 },
  'user-service': { port: 3001, priority: 2 },
  'project-service': { port: 3002, priority: 3 },
  'job-service': { port: 3003, priority: 3 },
  'chat-service': { port: 3004, priority: 3 },
  'community-service': { port: 3005, priority: 3 },
  'payment-service': { port: 3006, priority: 3 },
  'team-service': { port: 3007, priority: 3 }
};

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Check if command exists
 */
function commandExists(command) {
  try {
    execSync(`which ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

/**
 * Install dependencies for a service
 */
async function installDependencies(servicePath, serviceName) {
  return new Promise((resolve, reject) => {
    log.info(`Installing dependencies for ${serviceName}...`);
    
    const npmCommand = process.platform === 'win32' ? 'npm.cmd' : 'npm';
    
    const child = spawn(npmCommand, ['install'], {
      cwd: servicePath,
      stdio: 'inherit',
      shell: true
    });

    child.on('close', (code) => {
      if (code === 0) {
        log.success(`${serviceName} dependencies installed successfully`);
        resolve();
      } else {
        log.error(`${serviceName} dependencies installation failed`);
        reject(new Error(`Installation failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      log.error(`Failed to install ${serviceName} dependencies: ${error.message}`);
      reject(error);
    });
  });
}

/**
 * Setup database for a service
 */
async function setupDatabase(servicePath, serviceName) {
  const packageJsonPath = path.join(servicePath, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    return;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Check if service has database setup script
  if (packageJson.scripts && packageJson.scripts['db:setup']) {
    log.info(`Setting up database for ${serviceName}...`);
    
    try {
      const npmCommand = process.platform === 'win32' ? 'npm.cmd' : 'npm';
      execSync(`${npmCommand} run db:setup`, {
        cwd: servicePath,
        stdio: 'inherit'
      });
      log.success(`${serviceName} database setup completed`);
    } catch (error) {
      log.warn(`${serviceName} database setup failed: ${error.message}`);
    }
  }
}

/**
 * Copy environment file if it doesn't exist
 */
function copyEnvFile(servicePath, serviceName) {
  const envExamplePath = path.join(servicePath, '.env.example');
  const envPath = path.join(servicePath, '.env');
  
  if (fs.existsSync(envExamplePath) && !fs.existsSync(envPath)) {
    log.info(`Copying .env.example to .env for ${serviceName}...`);
    fs.copyFileSync(envExamplePath, envPath);
    log.success(`${serviceName} environment file created`);
  }
}

/**
 * Main setup function
 */
async function setupAll() {
  log.info('🚀 Starting VWork Platform setup...');
  
  // Check prerequisites
  log.info('Checking prerequisites...');
  
  if (!commandExists('node')) {
    log.error('Node.js is not installed. Please install Node.js 16+ first.');
    process.exit(1);
  }
  
  if (!commandExists('npm')) {
    log.error('npm is not installed. Please install npm first.');
    process.exit(1);
  }
  
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  log.success(`Node.js version: ${nodeVersion}`);
  
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  log.success(`npm version: ${npmVersion}`);
  
  // Install root dependencies
  log.info('Installing root dependencies...');
  try {
    execSync('npm install', { cwd: ROOT_DIR, stdio: 'inherit' });
    log.success('Root dependencies installed successfully');
  } catch (error) {
    log.error('Failed to install root dependencies');
    process.exit(1);
  }
  
  // Install client dependencies
  log.info('Installing client dependencies...');
  try {
    await installDependencies(CLIENT_DIR, 'client');
  } catch (error) {
    log.error('Failed to install client dependencies');
    process.exit(1);
  }
  
  // Install service dependencies
  log.info('Installing service dependencies...');
  
  const serviceNames = Object.keys(SERVICES);
  
  for (const serviceName of serviceNames) {
    const servicePath = path.join(SERVICES_DIR, serviceName);
    
    if (!fs.existsSync(servicePath)) {
      log.warn(`Service ${serviceName} not found, skipping...`);
      continue;
    }
    
    try {
      await installDependencies(servicePath, serviceName);
      copyEnvFile(servicePath, serviceName);
    } catch (error) {
      log.error(`Failed to setup ${serviceName}: ${error.message}`);
      // Continue with other services
    }
  }
  
  // Setup databases
  log.info('Setting up databases...');
  
  for (const serviceName of serviceNames) {
    const servicePath = path.join(SERVICES_DIR, serviceName);
    
    if (fs.existsSync(servicePath)) {
      await setupDatabase(servicePath, serviceName);
    }
  }
  
  log.success('🎉 VWork Platform setup completed successfully!');
  
  console.log('\n📋 Next steps:');
  console.log('1. Configure environment variables in .env files');
  console.log('2. Set up Firebase project and credentials');
  console.log('3. Set up PostgreSQL databases');
  console.log('4. Run: npm start');
  
  console.log('\n🔧 Service Ports:');
  Object.entries(SERVICES).forEach(([name, config]) => {
    console.log(`   ${name}: http://localhost:${config.port}`);
  });
  console.log(`   client: http://localhost:3000`);
  
  console.log('\n📚 Documentation:');
  console.log('   - User Service: services/user-service/README.md');
  console.log('   - Community Service: services/community-service/README.md');
  console.log('   - Team Service: services/team-service/README.md');
}

/**
 * Setup specific service
 */
async function setupService(serviceName) {
  if (!SERVICES[serviceName]) {
    log.error(`Unknown service: ${serviceName}`);
    console.log('Available services:', Object.keys(SERVICES).join(', '));
    process.exit(1);
  }
  
  const servicePath = path.join(SERVICES_DIR, serviceName);
  
  if (!fs.existsSync(servicePath)) {
    log.error(`Service ${serviceName} not found`);
    process.exit(1);
  }
  
  log.info(`Setting up ${serviceName}...`);
  
  try {
    await installDependencies(servicePath, serviceName);
    copyEnvFile(servicePath, serviceName);
    await setupDatabase(servicePath, serviceName);
    
    log.success(`${serviceName} setup completed successfully!`);
  } catch (error) {
    log.error(`Failed to setup ${serviceName}: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'service':
        const serviceName = args[1];
        if (!serviceName) {
          log.error('Please specify a service name');
          console.log('Usage: node setup-all.js service <service-name>');
          console.log('Available services:', Object.keys(SERVICES).join(', '));
          process.exit(1);
        }
        await setupService(serviceName);
        break;
        
      case 'help':
        console.log(`
Usage: node setup-all.js [command]

Commands:
  (no command)    Setup all services and client
  service <name>  Setup specific service
  help           Show this help

Examples:
  node setup-all.js
  node setup-all.js service user-service
  node setup-all.js service community-service
        `);
        break;
        
      default:
        await setupAll();
        break;
    }
  } catch (error) {
    log.error(`Setup failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { setupAll, setupService }; 