import React, { useState } from 'react';
import { motion } from 'framer-motion';
import LocationDropdown from '../common/LocationDropdown';
import { useLanguage } from '../../contexts/LanguageContext';

const LocationDemo = () => {
  const { t, locationData, getCountries, getCities } = useLanguage();
  const [formData, setFormData] = useState({
    displayName: '',
    bio: '',
    location: {
      country: '',
      city: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    },
    phoneNumber: '',
    website: ''
  });
  const [errors, setErrors] = useState({});
  const [demoInfo, setDemoInfo] = useState({
    countriesLoaded: 0,
    citiesCache: {},
    lastSelectedCountry: null
  });

  const handleLocationChange = (location) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        country: location.country,
        city: location.city
      }
    }));

    // Update demo info
    setDemoInfo(prev => ({
      ...prev,
      lastSelectedCountry: location.country,
      countriesLoaded: locationData.countries.length
    }));

    // Clear location errors when user makes changes
    if (errors.country || errors.city) {
      setErrors(prev => ({
        ...prev,
        country: null,
        city: null
      }));
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.displayName.trim()) {
      newErrors.displayName = t('nameRequired');
    }

    if (!formData.bio.trim()) {
      newErrors.bio = t('bioRequired');
    } else if (formData.bio.length < 50) {
      newErrors.bio = t('bioTooShort');
    }

    if (!formData.location.country.trim()) {
      newErrors.country = t('countryRequired');
    }

    if (!formData.location.city.trim()) {
      newErrors.city = t('cityRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      console.log('Form submitted:', formData);
      alert('Form submitted successfully! Check console for data.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-brown-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-medium font-bold text-gray-800 mb-4">
            🌍 Location Integration Demo
          </h1>
          <p className="text-gray-600">
            Demo tích hợp dữ liệu quốc gia và thành phố từ API vào LanguageContext
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Section */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-medium font-semibold text-gray-800 mb-4">
              📝 Form Demo
            </h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name */}
              <div>
                <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                  <span className="inline mr-1">👤</span>
                  {t('fullName')} *
                </label>
                <input
                  type="text"
                  value={formData.displayName}
                  onChange={(e) => handleInputChange('displayName', e.target.value)}
                  className={`form-input w-full ${errors.displayName ? 'border-red-500' : ''}`}
                  placeholder={t('enterFullName')}
                />
                {errors.displayName && (
                  <p className="text-red-500 text-sm mt-1">{errors.displayName}</p>
                )}
              </div>

              {/* Bio */}
              <div>
                <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                  <span className="inline mr-1">📝</span>
                  {t('bio')} *
                </label>
                <textarea
                  value={formData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  rows={4}
                  className={`form-input w-full resize-none ${errors.bio ? 'border-red-500' : ''}`}
                  placeholder={t('bioPlaceholder')}
                />
                <div className="flex justify-between items-center mt-1">
                  {errors.bio && (
                    <p className="text-red-500 text-sm">{errors.bio}</p>
                  )}
                  <p className="text-gray-500 text-sm ml-auto">
                    {formData.bio.length}/500
                  </p>
                </div>
              </div>

              {/* Location Dropdown */}
              <LocationDropdown
                value={{
                  country: formData.location.country,
                  city: formData.location.city
                }}
                onChange={handleLocationChange}
                error={{
                  country: errors.country,
                  city: errors.city
                }}
              />

              {/* Optional Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                    <span className="inline mr-1">📞</span>
                    {t('phoneNumber')} ({t('optional')})
                  </label>
                  <input
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                    className="form-input w-full"
                    placeholder={t('enterPhoneNumber')}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                    <span className="inline mr-1">🌐</span>
                    {t('website')} ({t('optional')})
                  </label>
                  <input
                    type="url"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    className="form-input w-full"
                    placeholder="https://example.com"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                {t('submit')}
              </button>
            </form>
          </div>

          {/* Info Section */}
          <div className="space-y-6">
            {/* LanguageContext Info */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-medium font-semibold text-gray-800 mb-4">
                🔧 LanguageContext Integration
              </h2>
              
              <div className="space-y-4">
                <div className="p-4 bg-medieval-brown-50 rounded-lg">
                  <h3 className="font-semibold text-gray-800 mb-2">📊 Location Data Status</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Countries Loaded:</span>
                      <span className="font-semibold">{locationData.countries.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Loading Status:</span>
                      <span className={`font-semibold ${locationData.isLoading ? 'text-yellow-600' : 'text-green-600'}`}>
                        {locationData.isLoading ? 'Loading...' : 'Ready'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cached Cities:</span>
                      <span className="font-semibold">{Object.keys(locationData.cities).length} countries</span>
                    </div>
                    {locationData.error && (
                      <div className="flex justify-between">
                        <span>Error:</span>
                        <span className="font-semibold text-red-600">{locationData.error}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="p-4 bg-medieval-brown-50 rounded-lg">
                  <h3 className="font-semibold text-gray-800 mb-2">🎯 Demo Info</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Last Selected Country:</span>
                      <span className="font-semibold">{demoInfo.lastSelectedCountry || 'None'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Current Language:</span>
                      <span className="font-semibold">{t('language')}</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-medieval-brown-50 rounded-lg">
                  <h3 className="font-semibold text-gray-800 mb-2">🔍 Available Functions</h3>
                  <div className="space-y-1 text-sm">
                    <div>• getCountries() - Lấy danh sách quốc gia</div>
                    <div>• getCities(countryCode) - Lấy thành phố theo quốc gia</div>
                    <div>• searchCountries(query) - Tìm kiếm quốc gia</div>
                    <div>• searchCities(countryCode, query) - Tìm kiếm thành phố</div>
                    <div>• getCountryByCode(code) - Lấy quốc gia theo mã</div>
                    <div>• getCountryByName(name) - Lấy quốc gia theo tên</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sample Data */}
            {locationData.countries.length > 0 && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-medium font-semibold text-gray-800 mb-4">
                  📋 Sample Countries
                </h2>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {locationData.countries.slice(0, 10).map((country, index) => (
                    <div key={country.code} className="flex items-center space-x-3 p-2 bg-medieval-brown-50 rounded">
                      <span className="text-lg">{country.flag}</span>
                      <div className="flex-1">
                        <div className="font-medium">{country.name}</div>
                        <div className="text-sm text-gray-600">
                          {country.region} • {country.code}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Showing first 10 of {locationData.countries.length} countries
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationDemo; 