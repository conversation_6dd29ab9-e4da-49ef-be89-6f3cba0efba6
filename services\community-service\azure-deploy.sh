#!/bin/bash

# Azure Deployment Script for Community Service
# Usage: ./azure-deploy.sh [app-name] [resource-group]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
APP_NAME=${1:-"vwork-community-service"}
RESOURCE_GROUP=${2:-"vwork-rg"}
LOCATION="eastus"
NODE_VERSION="18.x"

echo -e "${BLUE}🚀 Azure Deployment Script for Community Service${NC}"
echo -e "${BLUE}================================================${NC}"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check if logged in to Azure
if ! az account show &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Azure. Please login first.${NC}"
    az login
fi

echo -e "${GREEN}✅ Azure CLI is ready${NC}"

# Create resource group if it doesn't exist
echo -e "${BLUE}📦 Creating resource group: $RESOURCE_GROUP${NC}"
az group create --name $RESOURCE_GROUP --location $LOCATION --output none

# Create App Service Plan
PLAN_NAME="${APP_NAME}-plan"
echo -e "${BLUE}📋 Creating App Service Plan: $PLAN_NAME${NC}"
az appservice plan create \
    --name $PLAN_NAME \
    --resource-group $RESOURCE_GROUP \
    --sku B1 \
    --is-linux \
    --output none

# Create Web App
echo -e "${BLUE}🌐 Creating Web App: $APP_NAME${NC}"
az webapp create \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --plan $PLAN_NAME \
    --runtime "NODE|18-lts" \
    --output none

# Configure environment variables
echo -e "${BLUE}⚙️  Configuring environment variables${NC}"

# Set Node.js version
az webapp config set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --linux-fx-version "NODE|18-lts" \
    --output none

# Set startup command
az webapp config set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --startup-file "npm start" \
    --output none

# Enable logging
az webapp log config \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --web-server-logging filesystem \
    --output none

# Set application settings
echo -e "${BLUE}🔧 Setting application settings${NC}"

# Required environment variables (you'll need to update these with your actual values)
az webapp config appsettings set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --settings \
    NODE_ENV=production \
    PORT=8080 \
    WEBSITES_PORT=8080 \
    WEBSITES_CONTAINER_START_TIME_LIMIT=1800 \
    WEBSITES_ENABLE_APP_SERVICE_STORAGE=true \
    --output none

echo -e "${YELLOW}⚠️  IMPORTANT: You need to set the following environment variables manually:${NC}"
echo -e "${YELLOW}   - DATABASE_URL (PostgreSQL connection string)${NC}"
echo -e "${YELLOW}   - FIREBASE_PROJECT_ID${NC}"
echo -e "${YELLOW}   - FIREBASE_CLIENT_EMAIL${NC}"
echo -e "${YELLOW}   - FIREBASE_PRIVATE_KEY${NC}"
echo -e "${YELLOW}   - ALLOWED_ORIGINS${NC}"

# Deploy the application
echo -e "${BLUE}📤 Deploying application...${NC}"

# Create deployment package
echo -e "${BLUE}📦 Creating deployment package${NC}"
rm -rf .deploy
mkdir .deploy

# Copy necessary files
cp -r src .deploy/
cp -r scripts .deploy/
cp -r migrations .deploy/
cp app.js .deploy/
cp package.json .deploy/
cp package-lock.json .deploy/
cp web.config .deploy/
cp .dockerignore .deploy/

# Deploy to Azure
echo -e "${BLUE}🚀 Deploying to Azure...${NC}"
az webapp deployment source config-zip \
    --resource-group $RESOURCE_GROUP \
    --name $APP_NAME \
    --src .deploy.zip

# Clean up
rm -rf .deploy

# Get the app URL
APP_URL=$(az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "defaultHostName" --output tsv)

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}🌐 Your app is available at: https://$APP_URL${NC}"
echo -e "${GREEN}📊 Azure Portal: https://portal.azure.com/#@/resource/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$APP_NAME${NC}"

# Show next steps
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${BLUE}1. Set up PostgreSQL database in Azure${NC}"
echo -e "${BLUE}2. Configure environment variables in Azure Portal${NC}"
echo -e "${BLUE}3. Test the health endpoint: https://$APP_URL/health${NC}"
echo -e "${BLUE}4. Monitor logs: az webapp log tail --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"

# Show current status
echo -e "${BLUE}📊 Current App Status:${NC}"
az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "{name:name, state:state, defaultHostName:defaultHostName, resourceGroup:resourceGroup}" --output table 