#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${colors.cyan}${step}${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Project structure
const projects = [
  { name: 'Root Project', path: '.' },
  { name: 'Client', path: 'client' },
  { name: 'API Gateway', path: 'services/api-gateway' },
  { name: 'Chat Service', path: 'services/chat-service' },
  { name: 'Community Service', path: 'services/community-service' },
  { name: 'Job Service', path: 'services/job-service' },
  { name: 'Payment Service', path: 'services/payment-service' },
  { name: 'Project Service', path: 'services/project-service' },
  { name: 'Team Service', path: 'services/team-service' },
  { name: 'User Service', path: 'services/user-service' }
];

function checkNodeVersion() {
  try {
    const version = execSync('node --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(version.replace('v', '').split('.')[0]);
    
    if (majorVersion < 18) {
      logError(`Node.js version ${version} is too old. Please install Node.js 18+`);
      process.exit(1);
    }
    
    logSuccess(`Node.js version: ${version}`);
    return true;
  } catch (error) {
    logError('Node.js is not installed or not accessible');
    process.exit(1);
  }
}

function checkNpmVersion() {
  try {
    const version = execSync('npm --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(version.split('.')[0]);
    
    if (majorVersion < 9) {
      logWarning(`npm version ${version} is old. Consider upgrading to npm 9+`);
    } else {
      logSuccess(`npm version: ${version}`);
    }
    return true;
  } catch (error) {
    logError('npm is not installed or not accessible');
    process.exit(1);
  }
}

function installDependencies(projectPath, projectName) {
  const fullPath = path.resolve(projectPath);
  
  if (!fs.existsSync(fullPath)) {
    logWarning(`Skipping ${projectName} - directory not found: ${projectPath}`);
    return false;
  }
  
  if (!fs.existsSync(path.join(fullPath, 'package.json'))) {
    logWarning(`Skipping ${projectName} - no package.json found`);
    return false;
  }
  
  try {
    logStep('Installing', `${projectName} dependencies...`);
    
    // Change to project directory
    process.chdir(fullPath);
    
    // Run npm install
    const result = execSync('npm install', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    logSuccess(`${projectName} dependencies installed successfully`);
    return true;
  } catch (error) {
    logError(`Failed to install ${projectName} dependencies: ${error.message}`);
    return false;
  }
}

function runAudit(projectPath, projectName) {
  const fullPath = path.resolve(projectPath);
  
  if (!fs.existsSync(fullPath) || !fs.existsSync(path.join(fullPath, 'package.json'))) {
    return;
  }
  
  try {
    process.chdir(fullPath);
    const auditResult = execSync('npm audit --audit-level=high', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    if (auditResult.includes('found 0 vulnerabilities')) {
      logSuccess(`${projectName} - No high/critical vulnerabilities found`);
    } else {
      logWarning(`${projectName} - Vulnerabilities found. Run 'npm audit fix' to fix them.`);
    }
  } catch (error) {
    // npm audit returns non-zero exit code when vulnerabilities are found
    logWarning(`${projectName} - Vulnerabilities found. Run 'npm audit fix' to fix them.`);
  }
}

function main() {
  log(`\n${colors.bright}${colors.cyan}🚀 VWork Platform - Install All Dependencies${colors.reset}\n`);
  
  // Check prerequisites
  logStep('Checking', 'prerequisites...');
  checkNodeVersion();
  checkNpmVersion();
  
  // Store original directory
  const originalDir = process.cwd();
  
  let successCount = 0;
  let totalCount = 0;
  
  // Install dependencies for each project
  for (const project of projects) {
    totalCount++;
    if (installDependencies(project.path, project.name)) {
      successCount++;
    }
    
    // Return to original directory
    process.chdir(originalDir);
  }
  
  // Run security audit for each project
  logStep('Running', 'security audits...');
  for (const project of projects) {
    runAudit(project.path, project.name);
    process.chdir(originalDir);
  }
  
  // Summary
  log(`\n${colors.bright}${colors.cyan}📊 Installation Summary${colors.reset}`);
  log(`✅ Successfully installed: ${successCount}/${totalCount} projects`);
  
  if (successCount === totalCount) {
    logSuccess('All dependencies installed successfully!');
    logInfo('You can now run: npm start');
  } else {
    logWarning(`${totalCount - successCount} projects failed to install. Check the errors above.`);
  }
  
  log(`\n${colors.bright}${colors.cyan}🎉 Setup Complete!${colors.reset}\n`);
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  log(`${colors.bright}Usage:${colors.reset} npm run install:all [options]`);
  log(`${colors.bright}Options:${colors.reset}`);
  log('  --help, -h     Show this help message');
  log('  --audit-only   Only run security audits');
  process.exit(0);
}

if (args.includes('--audit-only')) {
  log(`\n${colors.bright}${colors.cyan}🔍 Running Security Audits Only${colors.reset}\n`);
  const originalDir = process.cwd();
  
  for (const project of projects) {
    runAudit(project.path, project.name);
    process.chdir(originalDir);
  }
  
  log(`\n${colors.bright}${colors.cyan}✅ Security audits completed!${colors.reset}\n`);
  process.exit(0);
}

// Run main installation
main(); 