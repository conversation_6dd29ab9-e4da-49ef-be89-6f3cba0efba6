// Mock Data Service for VWork Guild Platform
// Realistic medieval-themed freelance data

// ================================
// USER DATA & GUILD SYSTEM
// ================================

export const mockUsers = [
 {
  id: 'user1',
  email: '<EMAIL>',
  displayName: '<PERSON> the Web Mage',
  photoURL:
   'https://ui-avatars.com/api/?name=<PERSON>+Mage&size=150&background=4F46E5&color=fff',
  userType: 'freelancer',
  guild: {
   level: 'master',
   title: 'Master of Web Sorcery',
   experiencePoints: 2850,
   joinDate: new Date('2023-01-15'),
   completedQuests: 47,
   successRate: 98,
  },
  profile: {
   bio: 'Crafting digital realms with React magic and Node.js alchemy. Specializing in responsive castles and PWA fortresses.',
   location: 'Ho Chi Minh City, Vietnam',
   hourlyRate: 45,
   skills: [
    'React',
    'Node.js',
    'MongoDB',
    'TypeScript',
    'GSAP',
    'Tailwind CSS',
   ],
   languages: ['Vietnamese', 'English', 'Japanese'],
   availability: 'available',
  },
  stats: {
   totalEarnings: 18500,
   activeProjects: 3,
   rating: 4.9,
   responseTime: '2 hours',
   completionRate: 98,
  },
 },
 {
  id: 'user2',
  email: '<EMAIL>',
  displayName: 'Sir Marcus the Designer',
  photoURL:
   'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
  userType: 'freelancer',
  guild: {
   level: 'journeyman',
   title: 'Journeyman of Visual Arts',
   experiencePoints: 1200,
   joinDate: new Date('2023-06-10'),
   completedQuests: 23,
   successRate: 95,
  },
  profile: {
   bio: 'Creating stunning visual experiences through UI/UX design and brand identity. Adobe Creative Suite master.',
   location: 'Da Nang, Vietnam',
   hourlyRate: 35,
   skills: [
    'Figma',
    'Photoshop',
    'Illustrator',
    'UI/UX Design',
    'Branding',
    'Motion Graphics',
   ],
   languages: ['Vietnamese', 'English'],
   availability: 'busy',
  },
  stats: {
   totalEarnings: 12300,
   activeProjects: 5,
   rating: 4.8,
   responseTime: '4 hours',
   completionRate: 95,
  },
 },
 {
  id: 'client1',
  email: '<EMAIL>',
  displayName: 'Lady Catherine of StartupLand',
  photoURL:
   'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150',
  userType: 'client',
  guild: {
   level: 'lord',
   title: 'Noble Patron of Innovation',
   experiencePoints: 3200,
   joinDate: new Date('2022-11-20'),
   completedQuests: 0,
   successRate: 100,
  },
  profile: {
   bio: 'Building the future of e-commerce. Always seeking talented guild members for ambitious projects.',
   location: 'Singapore',
   company: 'InnovateTech Solutions',
   industry: 'Technology',
  },
  stats: {
   totalSpent: 45000,
   activeProjects: 2,
   rating: 4.7,
   responseTime: '1 hour',
   projectsPosted: 12,
  },
 },
];

// ================================
// PROJECT DATA
// ================================

export const mockProjects = [
 {
  id: 'proj1',
  title: 'E-commerce Enchantment Portal',
  description:
   'Seeking a master web sorcerer to craft a magnificent e-commerce castle with React magic. The portal shall include mystical features like real-time inventory tracking, payment processing through ancient gateways, and responsive design that adapts to all viewing crystals.',
  category: 'web-development',
  clientId: 'client1',
  client: {
   name: 'Lady Catherine of StartupLand',
   rating: 4.7,
   avatar:
    'https://ui-avatars.com/api/?name=Lady+Catherine&size=50&background=10B981&color=fff',
   company: 'InnovateTech Solutions',
   verified: true,
  },
  budget: {
   type: 'fixed',
   amount: 2500,
   currency: 'USD',
  },
  skills: ['React', 'Node.js', 'MongoDB', 'Stripe API', 'Tailwind CSS'],
  status: 'open',
  urgency: 'normal',
  isNDA: true,
  deadline: new Date('2024-03-15'),
  createdAt: new Date('2024-01-10'),
  updatedAt: new Date('2024-01-12'),
  stats: {
   totalBids: 12,
   averageBid: 2200,
   viewCount: 89,
  },
  features: [
   'User authentication & profiles',
   'Product catalog with search & filters',
   'Shopping cart & wishlist',
   'Payment integration (Stripe)',
   'Admin dashboard',
   'Order tracking system',
   'Responsive design',
  ],
 },
 {
  id: 'proj2',
  title: 'Mobile App Enchantment for Food Delivery',
  description:
   'Noble guild seeks skilled mobile enchanters to forge a React Native application for swift food delivery across the kingdom. The app must include real-time tracking, payment spells, and intuitive navigation.',
  category: 'mobile-development',
  clientId: 'client1',
  client: {
   name: 'Lord William of TechRealm',
   rating: 4.5,
   avatar:
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50',
   company: 'FoodieKingdom Ltd',
   verified: true,
  },
  budget: {
   type: 'hourly',
   amount: 40,
   currency: 'USD',
   estimatedHours: 80,
  },
  skills: [
   'React Native',
   'Firebase',
   'Google Maps API',
   'Push Notifications',
  ],
  status: 'open',
  urgency: 'urgent',
  isNDA: false,
  deadline: new Date('2024-02-28'),
  createdAt: new Date('2024-01-08'),
  updatedAt: new Date('2024-01-10'),
  stats: {
   totalBids: 8,
   averageBid: 3200,
   viewCount: 56,
  },
 },
 {
  id: 'proj3',
  title: 'Brand Identity Design Quest',
  description:
   'A mystical tech startup requires a master of visual arts to forge their complete brand identity. This sacred quest includes logo design, color palettes, typography selection, and brand guidelines.',
  category: 'design',
  clientId: 'client1',
  client: {
   name: 'Princess Sarah of Innovation',
   rating: 4.9,
   avatar:
    'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=50',
   company: 'MysticTech Ventures',
   verified: true,
  },
  budget: {
   type: 'fixed',
   amount: 1200,
   currency: 'USD',
  },
  skills: [
   'Logo Design',
   'Brand Identity',
   'Figma',
   'Illustrator',
   'Typography',
  ],
  status: 'in-progress',
  urgency: 'normal',
  isNDA: true,
  deadline: new Date('2024-03-01'),
  createdAt: new Date('2024-01-05'),
  updatedAt: new Date('2024-01-11'),
  stats: {
   totalBids: 15,
   averageBid: 1100,
   viewCount: 123,
  },
  assignedTo: 'user2',
  progress: 65,
 },
 {
  id: 'proj4',
  title: 'Content Writing for Ancient Wisdom Blog',
  description:
   'Seeking scribes skilled in the ancient arts of SEO and content creation. Write compelling articles about technology trends, coding tutorials, and digital transformation.',
  category: 'writing',
  clientId: 'client1',
  client: {
   name: 'Master David the Wise',
   rating: 4.6,
   avatar: 'https://images.unsplash.com/photo-1556157382-97eda2d62296?w=50',
   company: 'TechWisdom Publications',
   verified: false,
  },
  budget: {
   type: 'per-piece',
   amount: 150,
   currency: 'USD',
   pieces: 10,
  },
  skills: ['Content Writing', 'SEO', 'Technical Writing', 'Research'],
  status: 'open',
  urgency: 'normal',
  isNDA: false,
  deadline: new Date('2024-04-15'),
  createdAt: new Date('2024-01-12'),
  updatedAt: new Date('2024-01-12'),
  stats: {
   totalBids: 6,
   averageBid: 140,
   viewCount: 34,
  },
 },
 {
  id: 'proj5',
  title: 'Video Production for Product Launch',
  description:
   'Create epic promotional videos for our new SaaS product launch. Need 3 videos: product demo, customer testimonials, and animated explainer.',
  category: 'video',
  clientId: 'client1',
  client: {
   name: 'Duke Alexander of Marketing',
   rating: 4.4,
   avatar:
    'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=50',
   company: 'LaunchPad Studios',
   verified: true,
  },
  budget: {
   type: 'fixed',
   amount: 3500,
   currency: 'USD',
  },
  skills: [
   'Video Editing',
   'Motion Graphics',
   'After Effects',
   'Premiere Pro',
  ],
  status: 'open',
  urgency: 'urgent',
  isNDA: true,
  deadline: new Date('2024-02-20'),
  createdAt: new Date('2024-01-11'),
  updatedAt: new Date('2024-01-11'),
  stats: {
   totalBids: 4,
   averageBid: 3200,
   viewCount: 28,
  },
 },
];

// ================================
// DASHBOARD DATA
// ================================

export const mockDashboardData = {
 currentUser: mockUsers[0], // Elena the Web Mage
 stats: {
  totalEarnings: 18500,
  thisMonthEarnings: 4200,
  activeProjects: 3,
  completedProjects: 47,
  averageRating: 4.9,
  responseTime: '2 hours',
  successRate: 98,
  totalBids: 156,
  acceptedBids: 47,
 },
 recentActivity: [
  {
   id: 'act1',
   type: 'project_completed',
   title: 'Quest Complete: Restaurant Website',
   description:
    'Successfully delivered responsive website for Golden Dragon Restaurant',
   timestamp: new Date('2024-01-11T14:30:00'),
   amount: 1800,
   status: 'positive',
  },
  {
   id: 'act2',
   type: 'bid_accepted',
   title: 'Bid Accepted: E-commerce Portal',
   description:
    'Your proposal for the e-commerce enchantment project has been accepted!',
   timestamp: new Date('2024-01-10T09:15:00'),
   amount: 2500,
   status: 'positive',
  },
  {
   id: 'act3',
   type: 'payment_received',
   title: 'Payment Received',
   description: 'Milestone payment for Mobile App Development project',
   timestamp: new Date('2024-01-09T16:45:00'),
   amount: 1200,
   status: 'positive',
  },
  {
   id: 'act4',
   type: 'new_message',
   title: 'New Message from Client',
   description: 'Lady Catherine sent updates on project requirements',
   timestamp: new Date('2024-01-08T11:20:00'),
   status: 'neutral',
  },
  {
   id: 'act5',
   type: 'review_received',
   title: 'Received 5-Star Review',
   description:
    '"Elena delivered exceptional work on our website. Highly recommended!"',
   timestamp: new Date('2024-01-07T13:10:00'),
   status: 'positive',
  },
 ],
 activeProjects: [
  {
   id: 'proj1',
   title: 'E-commerce Enchantment Portal',
   client: 'Lady Catherine of StartupLand',
   progress: 25,
   deadline: new Date('2024-03-15'),
   budget: 2500,
   status: 'on-track',
  },
  {
   id: 'proj6',
   title: 'Mobile Banking App UI/UX',
   client: 'Sir Thomas of FinTech',
   progress: 60,
   deadline: new Date('2024-02-28'),
   budget: 3200,
   status: 'on-track',
  },
  {
   id: 'proj7',
   title: 'SaaS Dashboard Redesign',
   client: 'Duchess Emma of Analytics',
   progress: 80,
   deadline: new Date('2024-02-15'),
   budget: 1800,
   status: 'ahead',
  },
 ],
 upcomingDeadlines: [
  {
   projectId: 'proj7',
   title: 'SaaS Dashboard Redesign',
   deadline: new Date('2024-02-15'),
   daysLeft: 5,
   priority: 'high',
  },
  {
   projectId: 'proj6',
   title: 'Mobile Banking App UI/UX',
   deadline: new Date('2024-02-28'),
   daysLeft: 18,
   priority: 'medium',
  },
  {
   projectId: 'proj1',
   title: 'E-commerce Enchantment Portal',
   deadline: new Date('2024-03-15'),
   daysLeft: 34,
   priority: 'low',
  },
 ],
 guildProgress: {
  currentLevel: 'master',
  currentXP: 2850,
  nextLevelXP: 3500,
  progressToNext: 82,
  badges: [
   {
    id: 'web-master',
    name: 'Web Sorcery Master',
    icon: '🧙‍♀️',
    earned: true,
   },
   {
    id: 'fast-responder',
    name: 'Swift Messenger',
    icon: '⚡',
    earned: true,
   },
   {
    id: 'quality-work',
    name: 'Quality Craftsman',
    icon: '⭐',
    earned: true,
   },
   {
    id: 'client-favorite',
    name: 'Client Champion',
    icon: '💎',
    earned: true,
   },
   { id: 'mentor', name: 'Guild Mentor', icon: '🎓', earned: false },
  ],
  weeklyGoals: {
   bidsSent: { current: 3, target: 5, progress: 60 },
   projectsCompleted: { current: 1, target: 2, progress: 50 },
   clientRating: { current: 4.9, target: 4.8, progress: 100 },
  },
 },
};

// ================================
// SETTINGS DATA
// ================================

export const mockSettingsData = {
 profile: {
  avatar:
   'https://images.unsplash.com/photo-1494790108755-2616b612b3fd?w=150',
  displayName: 'Elena the Web Mage',
  bio: 'Crafting digital realms with React magic and Node.js alchemy. Specializing in responsive castles and PWA fortresses.',
  location: 'Ho Chi Minh City, Vietnam',
  website: 'https://elena-webmage.dev',
  hourlyRate: 45,
  skills: [
   'React',
   'Node.js',
   'MongoDB',
   'TypeScript',
   'GSAP',
   'Tailwind CSS',
  ],
  languages: ['Vietnamese', 'English', 'Japanese'],
  portfolio: [
   {
    id: 'port1',
    title: 'E-commerce Fashion Store',
    description: 'Modern e-commerce platform with React & Node.js',
    image:
     'https://picsum.photos/300/200?random=1',
    url: 'https://fashion-store-demo.com',
    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
   },
   {
    id: 'port2',
    title: 'Restaurant Management System',
    description: 'Complete POS and management system for restaurants',
    image:
     'https://picsum.photos/300/200?random=2',
    url: 'https://restaurant-system-demo.com',
    technologies: ['React', 'Express', 'PostgreSQL', 'Real-time'],
   },
  ],
 },
 account: {
  email: '<EMAIL>',
  emailVerified: true,
  phoneNumber: '+84 123 456 789',
  phoneVerified: false,
  twoFactorEnabled: false,
  lastLogin: new Date('2024-01-12T08:30:00'),
  accountCreated: new Date('2023-01-15T10:00:00'),
 },
 preferences: {
 language: 'vi',
 timezone: 'Asia/Ho_Chi_Minh',
 theme: 'modern',
  emailNotifications: {
   newProjects: true,
   bidAccepted: true,
   messages: true,
   payments: true,
   newsletter: false,
  },
  pushNotifications: {
   enabled: true,
   newMessages: true,
   projectUpdates: true,
   deadlineReminders: true,
  },
  privacy: {
   profileVisible: true,
   showRating: true,
   showEarnings: false,
   showLastSeen: true,
  },
 },
 payment: {
  methods: [
   {
    id: 'pay1',
    type: 'bank',
    name: 'Vietcombank',
    accountNumber: '****1234',
    isDefault: true,
    verified: true,
   },
   {
    id: 'pay2',
    type: 'paypal',
    email: '<EMAIL>',
    isDefault: false,
    verified: true,
   },
  ],
  earnings: {
   totalEarnings: 18500,
   availableBalance: 2340,
   pendingBalance: 1200,
   withdrawnThisMonth: 3500,
  },
 },
};

// ================================
// MESSAGES & CONVERSATIONS
// ================================

export const mockConversations = [
 {
  id: 'conv1',
  participants: ['user1', 'client1'],
  projectId: 'proj1',
  projectTitle: 'E-commerce Enchantment Portal',
  lastMessage: {
   content:
    'The wireframes look fantastic! Can we schedule a call to discuss the database schema?',
   timestamp: new Date('2024-01-12T14:30:00'),
   senderId: 'client1',
  },
  unreadCount: 2,
  updatedAt: new Date('2024-01-12T14:30:00'),
 },
 {
  id: 'conv2',
  participants: ['user1', 'user2'],
  projectId: null,
  projectTitle: null,
  lastMessage: {
   content:
    'Hey! Would you like to collaborate on a React Native project? I could use a UI/UX expert.',
   timestamp: new Date('2024-01-11T16:45:00'),
   senderId: 'user1',
  },
  unreadCount: 0,
  updatedAt: new Date('2024-01-11T18:20:00'),
 },
];

export const mockMessages = {
 conv1: [
  {
   id: 'msg1',
   senderId: 'client1',
   content:
    'Hello Elena! I reviewed your proposal for the e-commerce project. Very impressive work history!',
   timestamp: new Date('2024-01-10T09:30:00'),
   type: 'text',
  },
  {
   id: 'msg2',
   senderId: 'user1',
   content:
    "Thank you! I'm excited about this project. I've worked on similar e-commerce platforms before.",
   timestamp: new Date('2024-01-10T10:15:00'),
   type: 'text',
  },
  {
   id: 'msg3',
   senderId: 'user1',
   content:
    "I've prepared some initial wireframes based on your requirements. Would you like me to share them?",
   timestamp: new Date('2024-01-12T11:20:00'),
   type: 'text',
  },
  {
   id: 'msg4',
   senderId: 'client1',
   content:
    'The wireframes look fantastic! Can we schedule a call to discuss the database schema?',
   timestamp: new Date('2024-01-12T14:30:00'),
   type: 'text',
  },
 ],
};

// ================================
// COMMUNITY DATA
// ================================

// Community Users with diverse profiles
export const mockCommunityUsers = [
 {
  id: 'user1',
  name: 'Elena Rodriguez',
  username: '@elena_dev',
  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b3fd?w=100',
  title: 'Senior Full-Stack Developer',
  location: 'San Francisco, CA',
  verified: true,
  level: 'Expert',
  reputation: 2847,
  joinedDate: '2022-03-15',
  specialties: ['React', 'Node.js', 'TypeScript', 'AWS'],
  bio: 'Passionate about creating scalable web applications and mentoring junior developers.',
  stats: {
   posts: 156,
   followers: 1240,
   following: 387,
   projects: 89
  }
 },
 {
  id: 'user2',
  name: 'Marcus Chen',
  username: '@marcus_design',
  avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
  title: 'UI/UX Designer & Creative Director',
  location: 'New York, NY',
  verified: true,
  level: 'Expert',
  reputation: 3156,
  joinedDate: '2021-08-22',
  specialties: ['UI Design', 'UX Research', 'Figma', 'Branding'],
  bio: 'Award-winning designer focused on human-centered design and digital experiences.',
  stats: {
   posts: 203,
   followers: 2100,
   following: 456,
   projects: 127
  }
 },
 {
  id: 'user3',
  name: 'Sarah Kim',
  username: '@sarah_mobile',
  avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
  title: 'Mobile App Developer',
  location: 'Toronto, Canada',
  verified: false,
  level: 'Advanced',
  reputation: 1892,
  joinedDate: '2023-01-10',
  specialties: ['Flutter', 'React Native', 'iOS', 'Android'],
  bio: 'Building beautiful mobile experiences that users love. Always learning new technologies.',
  stats: {
   posts: 87,
   followers: 634,
   following: 298,
   projects: 45
  }
 },
 {
  id: 'user4',
  name: 'David Thompson',
  username: '@david_backend',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
  title: 'Backend Engineer & DevOps Specialist',
  location: 'London, UK',
  verified: true,
  level: 'Expert',
  reputation: 2634,
  joinedDate: '2022-06-03',
  specialties: ['Python', 'Docker', 'Kubernetes', 'PostgreSQL'],
  bio: 'Scaling systems and optimizing performance. Love solving complex technical challenges.',
  stats: {
   posts: 134,
   followers: 987,
   following: 234,
   projects: 78
  }
 },
 {
  id: 'user5',
  name: 'Lisa Wang',
  username: '@lisa_data',
  avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100',
  title: 'Data Scientist & AI Engineer',
  location: 'Seattle, WA',
  verified: true,
  level: 'Expert',
  reputation: 3421,
  joinedDate: '2021-11-18',
  specialties: ['Python', 'Machine Learning', 'TensorFlow', 'Data Analysis'],
  bio: 'Turning data into insights and building intelligent systems. PhD in Computer Science.',
  stats: {
   posts: 178,
   followers: 1567,
   following: 345,
   projects: 92
  }
 },
 {
  id: 'user6',
  name: 'Alex Johnson',
  username: '@alex_freelance',
  avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',
  title: 'Freelance Consultant & Entrepreneur',
  location: 'Austin, TX',
  verified: false,
  level: 'Intermediate',
  reputation: 1456,
  joinedDate: '2023-04-12',
  specialties: ['Business Strategy', 'Digital Marketing', 'WordPress', 'SEO'],
  bio: 'Helping small businesses grow their online presence. 5+ years in digital marketing.',
  stats: {
   posts: 67,
   followers: 423,
   following: 567,
   projects: 34
  }
 }
];

// Community Categories
export const mockCommunityCategories = [
 { id: 'all', name: 'All Posts', icon: '📋', color: 'gray' },
 { id: 'tips', name: 'Tips & Advice', icon: '💡', color: 'yellow' },
 { id: 'showcase', name: 'Project Showcase', icon: '🎨', color: 'purple' },
 { id: 'discussion', name: 'General Discussion', icon: '💬', color: 'blue' },
 { id: 'help', name: 'Help & Support', icon: '🆘', color: 'red' },
 { id: 'news', name: 'Industry News', icon: '📰', color: 'green' },
 { id: 'jobs', name: 'Job Opportunities', icon: '💼', color: 'indigo' },
 { id: 'tools', name: 'Tools & Resources', icon: '🛠️', color: 'orange' },
 { id: 'success', name: 'Success Stories', icon: '🏆', color: 'emerald' },
 { id: 'learning', name: 'Learning & Education', icon: '📚', color: 'pink' }
];

// Rich Community Posts with diverse content types
export const mockCommunityPosts = [
 {
  id: 'post1',
  type: 'text',
  title: '🚀 Just launched my first SaaS product after 8 months of development!',
  content: `After countless nights of coding, debugging, and iterating, I'm thrilled to announce the launch of TaskFlow - a project management tool specifically designed for freelancers and small teams.

Key features:
• Intuitive drag-and-drop interface
• Real-time collaboration
• Time tracking with detailed analytics
• Client portal for seamless communication
• Mobile-first responsive design

The journey wasn't easy - I faced challenges with scalability, user authentication, and payment integration. But the community here has been incredibly supportive with advice and feedback.

Special thanks to @marcus_design for the amazing UI/UX consultation and @david_backend for helping me optimize the database queries!

Would love to hear your thoughts and feedback. Link in my bio! 🙏`,
  author: mockCommunityUsers[0],
  category: 'showcase',
  timestamp: new Date('2024-01-15T09:30:00'),
  likes: 247,
  dislikes: 3,
  comments: 34,
  shares: 18,
  views: 1847,
  bookmarks: 67,
  tags: ['saas', 'launch', 'project-management', 'react', 'nodejs'],
  trending: true,
  featured: true,
  images: [
   'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800',
   'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800'
  ],
  reactions: {
   '👏': 89,
   '🔥': 45,
   '💯': 23,
   '🚀': 67,
   '❤️': 34
  }
 },
 {
  id: 'post2',
  type: 'discussion',
  title: 'What\'s your biggest challenge as a freelancer in 2024?',
  content: `I've been freelancing for 3 years now, and I'm curious about what challenges other freelancers are facing this year.

For me, the biggest challenges are:
1. **Client acquisition** - Finding quality clients who value good work
2. **Pricing strategy** - Knowing when and how to raise rates
3. **Work-life balance** - Setting boundaries with clients
4. **Staying updated** - Keeping up with rapidly changing technologies

What about you? What's keeping you up at night? Let's discuss and maybe we can help each other out! 💪

#FreelanceLife #Community #Discussion`,
  author: mockCommunityUsers[2],
  category: 'discussion',
  timestamp: new Date('2024-01-14T16:45:00'),
  likes: 156,
  dislikes: 2,
  comments: 89,
  shares: 23,
  views: 2341,
  bookmarks: 45,
  tags: ['freelancing', 'challenges', 'discussion', 'community'],
  trending: true,
  poll: {
   question: 'What\'s your biggest freelance challenge?',
   options: [
    { id: 1, text: 'Finding clients', votes: 45 },
    { id: 2, text: 'Pricing work', votes: 67 },
    { id: 3, text: 'Work-life balance', votes: 34 },
    { id: 4, text: 'Staying updated', votes: 28 }
   ],
   totalVotes: 174,
   userVoted: false
  }
 },
 {
  id: 'post3',
  type: 'tip',
  title: '💡 5 Essential VS Code Extensions Every Developer Should Use',
  content: `After years of development, these are the VS Code extensions that have significantly boosted my productivity:

**1. GitLens** 🔍
- See git blame information inline
- Visualize code authorship
- Navigate through git history effortlessly

**2. Prettier** ✨
- Automatic code formatting
- Consistent code style across team
- Supports multiple languages

**3. Thunder Client** ⚡
- REST API testing directly in VS Code
- No need for external tools like Postman
- Lightweight and fast

**4. Auto Rename Tag** 🏷️
- Automatically renames paired HTML/XML tags
- Saves tons of time in frontend development

**5. Bracket Pair Colorizer** 🌈
- Colors matching brackets
- Makes code structure more readable
- Essential for complex nested code

What are your must-have extensions? Drop them in the comments! 👇`,
  author: mockCommunityUsers[3],
  category: 'tips',
  timestamp: new Date('2024-01-13T11:20:00'),
  likes: 189,
  dislikes: 1,
  comments: 42,
  shares: 31,
  views: 1567,
  bookmarks: 89,
  tags: ['vscode', 'productivity', 'tools', 'development'],
  trending: false,
  codeSnippet: {
   language: 'json',
   code: `{
 "editor.formatOnSave": true,
 "editor.codeActionsOnSave": {
  "source.fixAll.eslint": true
 },
 "prettier.singleQuote": true,
 "prettier.semi": false
}`
  },
  reactions: {
   '💡': 67,
   '👍': 45,
   '🔥': 23,
   '💯': 18
  }
 },
 {
  id: 'post4',
  type: 'help',
  title: '🆘 Need help with React state management - Redux vs Context API',
  content: `I'm working on a medium-sized React application (about 15 components) and I'm struggling to decide between Redux and Context API for state management.

**Current situation:**
- User authentication state
- Shopping cart data
- Theme preferences
- API loading states

**My concerns:**
- Redux seems overkill for this size
- Context API might cause performance issues
- Team has mixed experience levels

Has anyone faced a similar decision? What factors helped you choose? Any real-world performance comparisons?

Thanks in advance! 🙏`,
  author: mockCommunityUsers[5],
  category: 'help',
  timestamp: new Date('2024-01-12T14:15:00'),
  likes: 67,
  dislikes: 0,
  comments: 28,
  shares: 8,
  views: 892,
  bookmarks: 34,
  tags: ['react', 'state-management', 'redux', 'context-api', 'help'],
  trending: false,
  solved: false,
  bounty: 50,
  reactions: {
   '🤔': 23,
   '👍': 18,
   '💭': 12
  }
 },
 {
  id: 'post5',
  type: 'news',
  title: '📰 GitHub Copilot X: The Future of AI-Powered Development',
  content: `GitHub just announced Copilot X, and it's a game-changer for developers! Here's what's new:

🔥 **Key Features:**
• **Chat interface** - Ask questions about your code directly
• **Pull request summaries** - AI-generated PR descriptions
• **Documentation generation** - Automatic README and docs
• **Test generation** - AI writes unit tests for your functions
• **CLI integration** - Terminal assistance for complex commands

💭 **My thoughts:**
This could revolutionize how we write code, but I'm curious about:
- Code quality and reliability
- Impact on junior developer learning
- Pricing for individual developers
- Privacy concerns with proprietary code

What do you think? Will this make us better developers or more dependent on AI?

#AI #GitHub #CopilotX #Development #Future`,
  author: mockCommunityUsers[4],
  category: 'news',
  timestamp: new Date('2024-01-11T08:30:00'),
  likes: 234,
  dislikes: 12,
  comments: 67,
  shares: 45,
  views: 3421,
  bookmarks: 123,
  tags: ['github', 'ai', 'copilot', 'development', 'news'],
  trending: true,
  featured: false,
  link: {
   url: 'https://github.blog/2023-03-22-github-copilot-x-the-ai-powered-developer-experience/',
   title: 'GitHub Copilot X: The AI-powered developer experience',
   description: 'GitHub Copilot X is our vision for the future of AI-powered software development.',
   image: 'https://github.blog/wp-content/uploads/2023/03/copilotx-header.png'
  },
  reactions: {
   '🤖': 89,
   '🔥': 67,
   '🚀': 45,
   '💭': 23,
   '👏': 34
  }
 },
 {
  id: 'post6',
  type: 'job',
  title: '💼 Remote Senior React Developer - $120k-150k (US/EU)',
  content: `🚀 **Amazing opportunity at TechFlow Inc!**

We're looking for a Senior React Developer to join our growing team. This is a fully remote position with flexible hours and amazing benefits.

**What you'll do:**
• Build scalable React applications
• Mentor junior developers
• Collaborate with design and product teams
• Contribute to technical architecture decisions

**Requirements:**
• 5+ years React experience
• Strong TypeScript skills
• Experience with Next.js
• Knowledge of testing frameworks
• Excellent communication skills

**What we offer:**
• $120k-150k salary (based on experience)
• Fully remote work
• Flexible hours
• Health insurance
• $2k learning budget
• Latest MacBook Pro
• 4 weeks vacation

Interested? DM me or apply through our careers page!

#RemoteWork #ReactJobs #SeniorDeveloper #Hiring`,
  author: mockCommunityUsers[1],
  category: 'jobs',
  timestamp: new Date('2024-01-10T10:00:00'),
  likes: 145,
  dislikes: 3,
  comments: 23,
  shares: 67,
  views: 2156,
  bookmarks: 89,
  tags: ['jobs', 'react', 'remote', 'senior', 'hiring'],
  trending: false,
  jobDetails: {
   company: 'TechFlow Inc',
   location: 'Remote (US/EU)',
   salary: '$120k-150k',
   type: 'Full-time',
   experience: 'Senior Level',
   applications: 47
  },
  reactions: {
   '💼': 45,
   '🚀': 34,
   '💰': 28,
   '👍': 23
  }
 }
];

// ================================
// EXPORT FUNCTIONS
// ================================

export const mockDataService = {
 // Users
 getUsers: () => Promise.resolve(mockUsers),
 getUserById: id => Promise.resolve(mockUsers.find(u => u.id === id)),

 // Projects
 getProjects: (filters = {}) => {
  let filtered = [...mockProjects];

  if (filters.category && filters.category !== 'All Quests') {
   filtered = filtered.filter(
    p => p.category === filters.category.toLowerCase().replace(' ', '-')
   );
  }

  if (filters.limit) {
   filtered = filtered.slice(0, filters.limit);
  }

  return Promise.resolve(filtered);
 },
 getProjectById: id => Promise.resolve(mockProjects.find(p => p.id === id)),

 // Dashboard
 getDashboardData: () => Promise.resolve(mockDashboardData),

 // Settings
 getSettingsData: () => Promise.resolve(mockSettingsData),
 updateSettings: data => {
  // Simulate API call
  return new Promise(resolve => {
   setTimeout(() => resolve({ success: true, data }), 500);
  });
 },

 // Messages
 getConversations: () => Promise.resolve(mockConversations),
 getMessages: conversationId =>
  Promise.resolve(mockMessages[conversationId] || []),

 // Community
 getCommunityPosts: (filters = {}) => {
  let filtered = [...mockCommunityPosts];

  // Filter by category
  if (filters.category && filters.category !== 'all') {
   filtered = filtered.filter(post => post.category === filters.category);
  }

  // Filter by trending
  if (filters.trending) {
   filtered = filtered.filter(post => post.trending);
  }

  // Sort posts
  if (filters.sort) {
   switch (filters.sort) {
    case 'popular':
     filtered.sort((a, b) => (b.likes + b.comments) - (a.likes + a.comments));
     break;
    case 'recent':
     filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
     break;
    case 'discussed':
     filtered.sort((a, b) => b.comments - a.comments);
     break;
    case 'trending':
     filtered = filtered.filter(post => post.trending);
     break;
    default:
     break;
   }
  }

  // Limit results
  if (filters.limit) {
   filtered = filtered.slice(0, filters.limit);
  }

  return Promise.resolve(filtered);
 },
 getCommunityUsers: () => Promise.resolve(mockCommunityUsers),
 getCommunityCategories: () => Promise.resolve(mockCommunityCategories),
 getCommunityStats: () => Promise.resolve({
  totalMembers: 25847,
  totalPosts: 1234,
  totalComments: 5678,
  onlineNow: 892,
  todayPosts: 23,
  weeklyGrowth: 12.5
 }),
};
