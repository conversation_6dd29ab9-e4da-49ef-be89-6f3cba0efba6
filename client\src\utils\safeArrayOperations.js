/**
 * Safe Array Operations Utility
 * Provides safe methods for array operations to prevent runtime errors
 */

/**
 * Safely filter an array, returning empty array if input is not an array
 * @param {any} array - The array to filter
 * @param {Function} predicate - The filter predicate function
 * @returns {Array} Filtered array or empty array
 */
export const safeFilter = (array, predicate) => {
 if (!Array.isArray(array)) {
  console.warn('safeFilter: Input is not an array, returning empty array', array);
  return [];
 }
 
 try {
  return array.filter(predicate);
 } catch (error) {
  console.error('safeFilter: Error during filtering', error);
  return [];
 }
};

/**
 * Safely map an array, returning empty array if input is not an array
 * @param {any} array - The array to map
 * @param {Function} mapper - The map function
 * @returns {Array} Mapped array or empty array
 */
export const safeMap = (array, mapper) => {
 if (!Array.isArray(array)) {
  console.warn('safeMap: Input is not an array, returning empty array', array);
  return [];
 }
 
 try {
  return array.map(mapper);
 } catch (error) {
  console.error('safeMap: Error during mapping', error);
  return [];
 }
};

/**
 * Safely reduce an array, returning initial value if input is not an array
 * @param {any} array - The array to reduce
 * @param {Function} reducer - The reducer function
 * @param {any} initialValue - The initial value
 * @returns {any} Reduced value or initial value
 */
export const safeReduce = (array, reducer, initialValue) => {
 if (!Array.isArray(array)) {
  console.warn('safeReduce: Input is not an array, returning initial value', array);
  return initialValue;
 }
 
 try {
  return array.reduce(reducer, initialValue);
 } catch (error) {
  console.error('safeReduce: Error during reduction', error);
  return initialValue;
 }
};

/**
 * Safely get array length, returning 0 if input is not an array
 * @param {any} array - The array to get length from
 * @returns {number} Array length or 0
 */
export const safeLength = (array) => {
 return Array.isArray(array) ? array.length : 0;
};

/**
 * Ensure input is an array, return empty array if not
 * @param {any} input - The input to ensure is an array
 * @returns {Array} The input if it's an array, otherwise empty array
 */
export const ensureArray = (input) => {
 return Array.isArray(input) ? input : [];
};

/**
 * Safely find an item in an array
 * @param {any} array - The array to search
 * @param {Function} predicate - The find predicate function
 * @returns {any} Found item or undefined
 */
export const safeFind = (array, predicate) => {
 if (!Array.isArray(array)) {
  console.warn('safeFind: Input is not an array, returning undefined', array);
  return undefined;
 }
 
 try {
  return array.find(predicate);
 } catch (error) {
  console.error('safeFind: Error during find', error);
  return undefined;
 }
};

/**
 * Safely check if array includes an item
 * @param {any} array - The array to check
 * @param {any} item - The item to look for
 * @returns {boolean} True if found, false otherwise
 */
export const safeIncludes = (array, item) => {
 if (!Array.isArray(array)) {
  console.warn('safeIncludes: Input is not an array, returning false', array);
  return false;
 }
 
 try {
  return array.includes(item);
 } catch (error) {
  console.error('safeIncludes: Error during includes check', error);
  return false;
 }
};

/**
 * Safely sort an array (returns new array)
 * @param {any} array - The array to sort
 * @param {Function} compareFn - Optional compare function
 * @returns {Array} Sorted array or empty array
 */
export const safeSort = (array, compareFn) => {
 if (!Array.isArray(array)) {
  console.warn('safeSort: Input is not an array, returning empty array', array);
  return [];
 }
 
 try {
  return [...array].sort(compareFn);
 } catch (error) {
  console.error('safeSort: Error during sorting', error);
  return [...array]; // Return copy of original array if sort fails
 }
};

/**
 * Safely slice an array
 * @param {any} array - The array to slice
 * @param {number} start - Start index
 * @param {number} end - End index
 * @returns {Array} Sliced array or empty array
 */
export const safeSlice = (array, start, end) => {
 if (!Array.isArray(array)) {
  console.warn('safeSlice: Input is not an array, returning empty array', array);
  return [];
 }
 
 try {
  return array.slice(start, end);
 } catch (error) {
  console.error('safeSlice: Error during slice', error);
  return [];
 }
};
