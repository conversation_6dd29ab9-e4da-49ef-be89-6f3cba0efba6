/**
 * Optimized Particle System Component
 * Provides high-performance particle animations with automatic scaling
 */

import React, { useRef, useEffect, useCallback } from 'react';
import { gsap } from 'gsap';
import responsiveAutoscaling from '../../services/responsiveAutoscaling';

const OptimizedParticleSystem = ({ 
 particleCount = 50,
 colors = ['#3B82F6', '#60A5FA', '#93C5FD'],
 size = { min: 2, max: 8 },
 speed = { min: 0.5, max: 2 },
 opacity = { min: 0.3, max: 0.8 },
 className = '',
 enabled = true
}) => {
 const containerRef = useRef(null);
 const particlesRef = useRef([]);
 const animationsRef = useRef([]);
 const isActiveRef = useRef(false);

 // Calculate optimized particle count based on performance
 const getOptimizedParticleCount = useCallback(() => {
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  const multipliers = {
   high: 1,
   medium: 0.6,
   low: 0.3,
   minimal: 0.1
  };
  
  return Math.max(5, Math.floor(particleCount * multipliers[performanceLevel]));
 }, [particleCount]);

 // Create particle element
 const createParticle = useCallback((index) => {
  const particle = document.createElement('div');
  const optimizedCount = getOptimizedParticleCount();
  const color = colors[index % colors.length];
  const particleSize = size.min + Math.random() * (size.max - size.min);
  
  particle.className = `absolute rounded-full pointer-events-none`;
  particle.style.cssText = `
   width: ${particleSize}px;
   height: ${particleSize}px;
   background: ${color};
   opacity: ${opacity.min + Math.random() * (opacity.max - opacity.min)};
   will-change: transform;
   transform: translate3d(0, 0, 0);
  `;
  
  // Random initial position
  const containerRect = containerRef.current?.getBoundingClientRect();
  if (containerRect) {
   particle.style.left = Math.random() * containerRect.width + 'px';
   particle.style.top = Math.random() * containerRect.height + 'px';
  }
  
  return particle;
 }, [colors, size, opacity, getOptimizedParticleCount]);

 // Animate particle with performance optimization
 const animateParticle = useCallback((particle, index) => {
  if (!particle || !isActiveRef.current) return;

  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  const containerRect = containerRef.current?.getBoundingClientRect();
  
  if (!containerRect) return;

  // Simplified animation for low performance
  if (performanceLevel === 'minimal') {
   const simpleAnimation = gsap.to(particle, {
    y: -containerRect.height,
    duration: 3 + Math.random() * 2,
    ease: 'none',
    repeat: -1,
    delay: Math.random() * 2,
    onRepeat: () => {
     gsap.set(particle, {
      y: containerRect.height + 20,
      x: Math.random() * containerRect.width
     });
    }
   });
   
   animationsRef.current.push(simpleAnimation);
   return;
  }

  // Full animation for higher performance levels
  const particleSpeed = speed.min + Math.random() * (speed.max - speed.min);
  const direction = Math.random() * Math.PI * 2;
  const distance = 100 + Math.random() * 200;
  
  const timeline = gsap.timeline({ repeat: -1 });
  
  // Movement animation
  timeline.to(particle, {
   x: `+=${Math.cos(direction) * distance}`,
   y: `+=${Math.sin(direction) * distance}`,
   duration: 2 / particleSpeed,
   ease: 'none'
  });
  
  // Opacity pulse (only on medium+ performance)
  if (performanceLevel !== 'low') {
   timeline.to(particle, {
    opacity: opacity.min,
    duration: 1,
    ease: 'power2.inOut',
    yoyo: true,
    repeat: -1
   }, 0);
  }
  
  // Scale pulse (only on high performance)
  if (performanceLevel === 'high') {
   timeline.to(particle, {
    scale: 1.2,
    duration: 1.5,
    ease: 'power2.inOut',
    yoyo: true,
    repeat: -1
   }, 0);
  }
  
  // Boundary check and reset
  timeline.call(() => {
   const rect = particle.getBoundingClientRect();
   const containerRect = containerRef.current?.getBoundingClientRect();
   
   if (containerRect && (
    rect.left > containerRect.right ||
    rect.right < containerRect.left ||
    rect.top > containerRect.bottom ||
    rect.bottom < containerRect.top
   )) {
    gsap.set(particle, {
     x: Math.random() * containerRect.width,
     y: Math.random() * containerRect.height
    });
   }
  }, null, null, 2);
  
  animationsRef.current.push(timeline);
 }, [speed, opacity, getOptimizedParticleCount]);

 // Initialize particle system
 const initializeParticles = useCallback(() => {
  if (!containerRef.current || !enabled || isActiveRef.current) return;
  
  isActiveRef.current = true;
  const optimizedCount = getOptimizedParticleCount();
  
  // Clear existing particles
  particlesRef.current.forEach(particle => {
   if (particle && particle.parentNode) {
    particle.parentNode.removeChild(particle);
   }
  });
  particlesRef.current = [];
  
  // Create new particles
  for (let i = 0; i < optimizedCount; i++) {
   const particle = createParticle(i);
   containerRef.current.appendChild(particle);
   particlesRef.current.push(particle);
   
   // Stagger particle animation start
   setTimeout(() => {
    animateParticle(particle, i);
   }, i * 100);
  }
 }, [enabled, createParticle, animateParticle, getOptimizedParticleCount]);

 // Cleanup function
 const cleanup = useCallback(() => {
  isActiveRef.current = false;
  
  // Kill all animations
  animationsRef.current.forEach(animation => {
   if (animation && animation.kill) {
    animation.kill();
   }
  });
  animationsRef.current = [];
  
  // Remove particles
  particlesRef.current.forEach(particle => {
   if (particle && particle.parentNode) {
    particle.parentNode.removeChild(particle);
   }
  });
  particlesRef.current = [];
 }, []);

 // Handle performance level changes
 useEffect(() => {
  const handlePerformanceChange = () => {
   cleanup();
   setTimeout(initializeParticles, 100);
  };

  window.addEventListener('performanceLevelChange', handlePerformanceChange);
  
  return () => {
   window.removeEventListener('performanceLevelChange', handlePerformanceChange);
  };
 }, [cleanup, initializeParticles]);

 // Initialize on mount
 useEffect(() => {
  if (enabled && responsiveAutoscaling.shouldUseFullAnimations()) {
   initializeParticles();
  }
  
  return cleanup;
 }, [enabled, initializeParticles, cleanup]);

 // Handle visibility changes to pause/resume animations
 useEffect(() => {
  const handleVisibilityChange = () => {
   if (document.hidden) {
    // Pause animations when tab is not visible
    animationsRef.current.forEach(animation => {
     if (animation && animation.pause) {
      animation.pause();
     }
    });
   } else {
    // Resume animations when tab becomes visible
    animationsRef.current.forEach(animation => {
     if (animation && animation.resume) {
      animation.resume();
     }
    });
   }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  return () => {
   document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
 }, []);

 if (!enabled || !responsiveAutoscaling.shouldUseFullAnimations()) {
  return null;
 }

 return (
  <div
   ref={containerRef}
   className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}
   style={{ zIndex: 0 }}
  />
 );
};

export default OptimizedParticleSystem;
