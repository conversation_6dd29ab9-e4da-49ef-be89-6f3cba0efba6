import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { ScrambleTextPlugin } from 'gsap/ScrambleTextPlugin';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';
import { useOptimizedAnimation } from '../../../hooks/useOptimizedAnimation';
import responsiveAutoscaling from '../../../services/responsiveAutoscaling';
import {
 ArrowRightIcon,
 PlayIcon,
 UserGroupIcon,
 BriefcaseIcon,
} from '@heroicons/react/24/outline';

// Register GSAP plugins only once
if (!gsap.plugins.ScrollTrigger) {
 gsap.registerPlugin(ScrollTrigger, SplitText, ScrambleTextPlugin, MotionPathPlugin, Physics2DPlugin, CustomEase);
}

// Memoized custom eases for premium animations - created only once
const premiumEases = (() => {
 let eases = null;
 return () => {
  if (!eases) {
   eases = {
    elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
    bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
    liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1")
   };
  }
  return eases;
 };
})();

const AppleHeroSection = () => {
 const { t } = useLanguage();
 const { isAuthenticated } = useAuth();
 const { isHighPerformance } = useOptimizedAnimation();

 const heroRef = useRef(null);
 const titleRef = useRef(null);
 const subtitleRef = useRef(null);
 const ctaRef = useRef(null);
 const floatingElementsRef = useRef(null);
 const mainTextRef = useRef(null);
 const marketplaceTextRef = useRef(null);
 const dynamicTextRef = useRef(null);
 const particleCanvasRef = useRef(null);
 const magneticElementsRef = useRef([]);
 const parallaxLayersRef = useRef([]);

 // Performance monitoring refs
 const animationTimelineRef = useRef(null);
 const cleanupFunctionsRef = useRef([]);
 const isAnimatingRef = useRef(false);

 // Memoized dynamic text for advanced animations
 const dynamicTexts = useMemo(() => [
  t('connectSubtitle'),
  t('findYourDreamJob'),
  t('hireTopTalent'),
  t('buildYourCareer'),
  t('growYourBusiness'),
  t('perfectMatches'),
  t('creativeSolutions'),
 ], [t]);

 // Optimized Magnetic Effect with throttling and performance checks
 const createMagneticEffect = useCallback((element) => {
  if (!element || !responsiveAutoscaling.shouldUseFullAnimations()) return;

  let animationFrame = null;
  let isHovering = false;
  let lastUpdateTime = 0;
  const throttleDelay = 16; // ~60fps throttling

  const handleMouseMove = (e) => {
   if (!isHovering) return;

   const now = performance.now();
   if (now - lastUpdateTime < throttleDelay) return; // Throttle updates
   lastUpdateTime = now;

   if (animationFrame) {
    cancelAnimationFrame(animationFrame);
   }

   animationFrame = requestAnimationFrame(() => {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const deltaX = (e.clientX - centerX) * 0.05; // Further reduced intensity
    const deltaY = (e.clientY - centerY) * 0.05;

    gsap.to(element, {
     x: deltaX,
     y: deltaY,
     duration: 0.15, // Faster duration
     ease: "power1.out",
     overwrite: true
    });
   });
  };

  const handleMouseEnter = () => {
   isHovering = true;
  };

  const handleMouseLeave = () => {
   isHovering = false;
   if (animationFrame) {
    cancelAnimationFrame(animationFrame);
   }

   gsap.to(element, {
    x: 0,
    y: 0,
    duration: 0.4,
    ease: premiumEases().elastic,
    overwrite: true
   });
  };

  element.addEventListener('mouseenter', handleMouseEnter, { passive: true });
  element.addEventListener('mousemove', handleMouseMove, { passive: true });
  element.addEventListener('mouseleave', handleMouseLeave, { passive: true });

  const cleanup = () => {
   if (animationFrame) {
    cancelAnimationFrame(animationFrame);
   }
   element.removeEventListener('mouseenter', handleMouseEnter);
   element.removeEventListener('mousemove', handleMouseMove);
   element.removeEventListener('mouseleave', handleMouseLeave);
  };

  cleanupFunctionsRef.current.push(cleanup);
  return cleanup;
 }, []);

 // Optimized Text Animation with performance checks
 const createAdvancedTextAnimation = useCallback(() => {
  if (!mainTextRef.current || !marketplaceTextRef.current) return;
  if (isAnimatingRef.current) return; // Prevent duplicate animations

  isAnimatingRef.current = true;
  const shouldUseFullAnimations = responsiveAutoscaling.shouldUseFullAnimations();

  // Always show marketplace text immediately without delay
  gsap.set(marketplaceTextRef.current, { opacity: 1 });

  if (!shouldUseFullAnimations) {
   // Fallback to simple fade-in for low performance devices
   gsap.set(mainTextRef.current, { opacity: 1 });
   isAnimatingRef.current = false;
   return;
  }

  // Split text for character-by-character animation (only for main text)
  const mainSplit = new SplitText(mainTextRef.current, { type: "chars" });

  // Store splits for cleanup
  const cleanup = () => {
   mainSplit.revert();
   isAnimatingRef.current = false;
  };
  cleanupFunctionsRef.current.push(cleanup);

  // Set initial states with optimized transforms (only for main text)
  gsap.set(mainSplit.chars, {
   opacity: 0,
   y: 50, // Reduced from 100 for smoother animation
   rotationX: -45, // Reduced rotation for better performance
   transformOrigin: "50% 50%", // Simplified transform origin
   force3D: true // Enable hardware acceleration
  });

  // Create optimized timeline
  const masterTL = gsap.timeline({
   onComplete: () => {
    isAnimatingRef.current = false;
   }
  });

  // Get optimized settings
  const settings = responsiveAutoscaling.getOptimizedGSAPSettings();

  // Animate main text with optimized settings
  masterTL.to(mainSplit.chars, {
   opacity: 1,
   y: 0,
   rotationX: 0,
   duration: settings.duration * 1.2,
   stagger: {
    amount: settings.stagger * 8,
    from: "start"
   },
   ease: settings.ease,
   force3D: true
  });

  animationTimelineRef.current = masterTL;
  return masterTL;
 }, [t]);

 // Three.js Hyperspeed Effect will be handled by the HyperspeedBackground component

 // Motion Path Animation for Floating Elements
 const createMotionPathAnimation = () => {
  if (!floatingElementsRef.current) return;

  const elements = Array.from(floatingElementsRef.current.children);
  
  elements.forEach((element, index) => {
   // Create complex motion path
   const path = `M${Math.random() * 100},${Math.random() * 100} 
          Q${Math.random() * 200},${Math.random() * 200} 
          ${Math.random() * 300},${Math.random() * 300}
          T${Math.random() * 400},${Math.random() * 400}`;
   
   gsap.to(element, {
    motionPath: {
     path: path,
     autoRotate: true,
     alignOrigin: [0.5, 0.5]
    },
    duration: 15 + index * 5,
    repeat: -1,
    ease: "none"
   });
   
   // Add scale animation
   gsap.to(element, {
    scale: 1.3,
    duration: 3 + Math.random() * 2,
    repeat: -1,
    yoyo: true,
    ease: premiumEases.liquid
   });
  });
 };

 // Advanced Typing Effect with Scramble
 const createAdvancedTypingEffect = () => {
  if (!dynamicTextRef.current) return;

  let currentIndex = 0;
  
  const animateText = () => {
   const currentText = dynamicTexts[currentIndex];
   
   // Scramble out current text
   gsap.to(dynamicTextRef.current, {
    scrambleText: {
     text: "",
     chars: "XO01",
     speed: 0.5
    },
    duration: 0.5,
    onComplete: () => {
     // Scramble in new text
     gsap.to(dynamicTextRef.current, {
      scrambleText: {
       text: currentText,
       chars: "XO01",
       speed: 0.3
      },
      duration: 1.5,
      onComplete: () => {
       setTimeout(() => {
        currentIndex = (currentIndex + 1) % dynamicTexts.length;
        animateText();
       }, 2000);
      }
     });
    }
   });
  };
  
  setTimeout(animateText, 1500);
 };

 // Multi-layer Parallax with Depth
 const createAdvancedParallax = () => {
  parallaxLayersRef.current.forEach((layer, index) => {
   if (!layer) return;
   
   const depth = (index + 1) * 0.1;
   const scale = 1 + depth;
   
   gsap.to(layer, {
    yPercent: -50 * depth,
    scale: scale,
    rotation: depth * 5,
    scrollTrigger: {
     trigger: heroRef.current,
     start: "top bottom",
     end: "bottom top",
     scrub: true,
     markers: false
    }
   });
  });
 };

 // Optimized animation initialization with performance monitoring
 useEffect(() => {
  const ctx = gsap.context(() => {
   // Performance check before initializing animations
   const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
   const shouldUseFullAnimations = responsiveAutoscaling.shouldUseFullAnimations();

   // Priority-based animation loading - removed unused variable

   // Always load essential animations
   const textTL = createAdvancedTextAnimation();

   // Load enhanced animations only on medium+ performance
   if (performanceLevel !== 'minimal' && performanceLevel !== 'low') {
    // Delay parallax to prevent initial lag
    setTimeout(() => {
     createAdvancedParallax();
    }, 1000);

    // Add magnetic effects with reduced intensity
    if (shouldUseFullAnimations) {
     magneticElementsRef.current.slice(0, 2).forEach(element => { // Limit to 2 elements
      createMagneticEffect(element);
     });
    }
   }

   // Load premium animations only on high performance with delay
   if (performanceLevel === 'high') {
    setTimeout(() => {
     createMotionPathAnimation();
    }, 2000);

    setTimeout(() => {
     createAdvancedTypingEffect();
    }, 3000);
   }

   // Get optimized settings for master timeline
   const settings = responsiveAutoscaling.getOptimizedGSAPSettings();

   // Master entrance animation with performance optimization
   const masterTL = gsap.timeline({
    onComplete: () => {
     // Cleanup completed animations to free memory
     if (animationTimelineRef.current && animationTimelineRef.current.progress() === 1) {
      animationTimelineRef.current.kill();
     }
    }
   });

   // Set initial states to prevent flicker

   // Single unified animation timeline to prevent conflicts
   masterTL
    .from(heroRef.current, {
     opacity: 0,
     duration: 0.1
    });

   // Add text animation if available
   if (textTL) {
    masterTL.add(textTL, 0.3);
   }

   // Subtitle animation
   masterTL.from(subtitleRef.current, {
    opacity: 0,
    y: shouldUseFullAnimations ? 20 : 10,
    duration: shouldUseFullAnimations ? settings.duration * 1.2 : 0.5,
    ease: settings.ease
   }, "-=0.3");

   // CTA buttons animation
   masterTL.from(ctaRef.current?.children || [], {
    opacity: 0,
    y: shouldUseFullAnimations ? 30 : 15,
    scale: shouldUseFullAnimations ? 0.9 : 0.95,
    duration: shouldUseFullAnimations ? settings.duration : 0.4,
    stagger: shouldUseFullAnimations ? settings.stagger * 2 : 0.1,
    ease: settings.ease
   }, "-=0.2");



   animationTimelineRef.current = masterTL;

  }, heroRef);

  // Enhanced cleanup function
  return () => {
   ctx.revert();

   // Clean up all stored cleanup functions
   cleanupFunctionsRef.current.forEach(cleanup => {
    if (typeof cleanup === 'function') {
     cleanup();
    }
   });
   cleanupFunctionsRef.current = [];

   // Kill any remaining timelines
   if (animationTimelineRef.current) {
    animationTimelineRef.current.kill();
    animationTimelineRef.current = null;
   }

   isAnimatingRef.current = false;
  };
 // eslint-disable-next-line react-hooks/exhaustive-deps
 }, []);



 return (
  <>
   {/* Enhanced CSS with 3D transforms */}
   <style>{`
    @keyframes liquid-gradient {
     0%, 100% {
      background-position: 0% 50%;
     }
     50% {
      background-position: 100% 50%;
     }
    }
    
    .liquid-bg {
     background: linear-gradient(-45deg, #1E40AF, #3B82F6, #60A5FA, #93C5FD);
     background-size: 400% 400%;
     animation: liquid-gradient 8s ease infinite;
    }
    
    .text-3d {
     transform-style: preserve-3d;
     perspective: 1000px;
    }
    
    .magnetic-element {
     transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .floating-particle {
     will-change: transform;
     animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
     0%, 100% {
      transform: translateY(0px) rotate(0deg) scale(1);
      opacity: 0.6;
     }
     33% {
      transform: translateY(-15px) rotate(120deg) scale(1.1);
      opacity: 0.8;
     }
     66% {
      transform: translateY(-10px) rotate(240deg) scale(0.9);
      opacity: 0.7;
     }
    }

    .neon-blue {
     text-shadow:
      0 0 5px #3B82F6,
      0 0 10px #3B82F6,
      0 0 15px #3B82F6,
      0 0 20px #3B82F6,
      0 0 35px #3B82F6,
      0 0 40px #3B82F6;
     animation: neon-glow 2s ease-in-out infinite alternate;
    }

    @keyframes neon-glow {
     from {
      text-shadow:
       0 0 5px #3B82F6,
       0 0 10px #3B82F6,
       0 0 15px #3B82F6,
       0 0 20px #3B82F6,
       0 0 35px #3B82F6,
       0 0 40px #3B82F6;
     }
     to {
      text-shadow:
       0 0 2px #3B82F6,
       0 0 5px #3B82F6,
       0 0 8px #3B82F6,
       0 0 12px #3B82F6,
       0 0 18px #3B82F6,
       0 0 25px #3B82F6;
     }
    }

    /* Custom gradient utilities */
    .bg-gradient-radial {
     background: radial-gradient(circle, var(--tw-gradient-stops));
    }
   `}</style>

   <section
    ref={heroRef}
    className='relative pt-20 pb-16 sm:pt-24 sm:pb-20 lg:pt-32 lg:pb-28 overflow-hidden min-h-screen flex items-center'
   >
    {/* Advanced Multi-layer Background */}
    <div className='absolute inset-0'>
     <div
      ref={el => parallaxLayersRef.current[0] = el}
      className='absolute inset-0 liquid-bg opacity-5'
     />
     <div
      ref={el => parallaxLayersRef.current[1] = el}
      className='absolute inset-0 bg-gradient-radial from-blue-500/10 via-transparent to-blue-500/10'
     />
     <div
      ref={el => parallaxLayersRef.current[2] = el}
      className='absolute inset-0 bg-gradient-conic from-blue-400/5 via-blue-500/5 to-blue-600/5'
     />
    </div>

    {/* Simple Animated Background - Replaced HyperspeedBackground */}
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
     {/* Animated gradient background */}
     <div className="absolute inset-0 bg-gradient-to-br from-blue-900/5 via-blue-800/10 to-blue-700/5 animate-pulse" />

     {/* Moving gradient orbs */}
     <div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-radial from-blue-500/20 to-transparent rounded-full animate-float" />
     <div className="absolute -top-20 -right-20 w-60 h-60 bg-gradient-radial from-blue-400/15 to-transparent rounded-full animate-float" style={{ animationDelay: '2s', animationDuration: '8s' }} />
     <div className="absolute -bottom-40 -left-20 w-96 h-96 bg-gradient-radial from-blue-600/10 to-transparent rounded-full animate-float" style={{ animationDelay: '4s', animationDuration: '12s' }} />
     <div className="absolute -bottom-20 -right-40 w-72 h-72 bg-gradient-radial from-blue-300/20 to-transparent rounded-full animate-float" style={{ animationDelay: '6s', animationDuration: '10s' }} />

     {/* Floating geometric shapes */}
     <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-500/10 rounded-full animate-float" />
     <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-blue-400/15 rounded-lg rotate-45 animate-float" style={{ animationDelay: '1s' }} />
     <div className="absolute bottom-1/3 left-1/3 w-20 h-20 bg-blue-600/10 rounded-full animate-float" style={{ animationDelay: '2s' }} />
     <div className="absolute bottom-1/4 right-1/3 w-28 h-28 bg-blue-300/10 rounded-lg rotate-12 animate-float" style={{ animationDelay: '3s' }} />

     {/* Subtle grid pattern */}
     <div className="absolute inset-0 opacity-5">
      <div className="absolute inset-0" style={{
       backgroundImage: `
        linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
       `,
       backgroundSize: '50px 50px'
      }} />
     </div>
    </div>

    {/* Advanced Particle Canvas */}
    <div
     ref={particleCanvasRef}
     className='absolute inset-0 pointer-events-none overflow-hidden'
    />

    {/* Enhanced Floating Elements with Physics */}
    <div
     ref={floatingElementsRef}
     className='absolute inset-0 pointer-events-none'
    >
     <div className='absolute top-20 left-10 w-12 h-12 bg-gradient-to-br from-blue-400/20 to-blue-500/20 rounded-full backdrop-blur-sm border border-blue-400/30 floating-particle'>
      <div className='w-full h-full bg-gradient-to-br from-blue-500/30 to-blue-600/30 rounded-full animate-pulse' />
     </div>
     <div className='absolute top-40 right-20 w-8 h-8 bg-gradient-to-br from-blue-400/20 to-blue-500/20 rounded-lg backdrop-blur-sm border border-blue-400/30 floating-particle'>
      <div className='w-full h-full bg-gradient-to-br from-blue-500/30 to-blue-600/30 rounded-lg animate-pulse' />
     </div>
     <div className='absolute bottom-40 left-20 w-16 h-16 bg-gradient-to-br from-blue-400/20 to-blue-500/20 rounded-xl backdrop-blur-sm border border-blue-400/30 floating-particle'>
      <div className='w-full h-full bg-gradient-to-br from-blue-500/30 to-blue-600/30 rounded-xl animate-pulse' />
     </div>
    </div>

    <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
     <div className='text-center'>
      {/* Advanced 3D Text Animation */}
      <div ref={titleRef} className='mb-8 text-3d'>
       <h1 className='text-4xl sm:text-5xl lg:text-7xl font-bold tracking-tight mb-4'>
        <div className='flex flex-wrap items-center justify-center gap-x-4 gap-y-2'>
         <span
          ref={mainTextRef}
          className='text-gray-900 text-3d transition-colors duration-300'
         >
          {t('worldsWork')}
         </span>
         <span
          ref={marketplaceTextRef}
          className='liquid-bg bg-clip-text text-transparent font-extrabold text-3d relative z-10 text-[110%]'
          style={{
           background: 'linear-gradient(-45deg, #1E40AF, #3B82F6, #60A5FA, #93C5FD)',
           backgroundSize: '400% 400%',
           backgroundClip: 'text',
           WebkitBackgroundClip: 'text',
           color: 'transparent',
           animation: 'liquid-gradient 8s ease infinite'
          }}
         >
          {t('marketplace')}
         </span>
        </div>
       </h1>
       <div className='flex flex-col items-center justify-center gap-4'>
        <div className='text-5xl sm:text-6xl lg:text-8xl font-extrabold neon-blue text-blue-400 mb-6 drop-shadow-[0_0_30px_rgba(59,130,246,0.8)]'>
         NERAFUS
        </div>

        <div className='text-2xl sm:text-3xl lg:text-5xl font-semibold text-gray-700 h-20 flex items-center justify-center transition-colors duration-300'>
         <span
          ref={dynamicTextRef}
          className='bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 bg-clip-text text-transparent font-bold tracking-wide text-3d'
         >
          {dynamicTexts[0]}
         </span>
         <span className='animate-pulse text-blue-600 ml-2 text-4xl'>
          |
         </span>
        </div>
       </div>
      </div>



      {/* Magnetic CTA Buttons */}
      <div ref={ctaRef} className='mb-20'>
       <div className='flex flex-col sm:flex-row gap-6 justify-center items-center'>
        {!isAuthenticated ? (
         <>
          <Link
           ref={el => magneticElementsRef.current[0] = el}
           to='/auth?mode=signup'
           className='group magnetic-element btn-auto-scale inline-flex items-center px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'
          >
           <span>{t('getStarted')}</span>
           <ArrowRightIcon className='ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform duration-300' />
          </Link>

          <button 
           ref={el => magneticElementsRef.current[1] = el}
           className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-semibold text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white rounded-2xl border-2 border-gray-200 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105'
          >
           <PlayIcon className='mr-3 h-6 w-6' />
           <span>{t('watchDemo')}</span>
          </button>
         </>
        ) : (
         <>
          <Link
           ref={el => magneticElementsRef.current[0] = el}
           to='/projects'
           className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'
          >
           <span>{t('findWork')}</span>
           <ArrowRightIcon className='ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform duration-300' />
          </Link>

          <Link
           ref={el => magneticElementsRef.current[1] = el}
           to='/freelancers'
           className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-semibold text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white rounded-2xl border-2 border-gray-200 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105'
          >
           <UserGroupIcon className='mr-3 h-6 w-6' />
           <span>{t('findTalent')}</span>
          </Link>

          <Link
           ref={el => magneticElementsRef.current[2] = el}
           to='/jobs/create'
           className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'
          >
           <BriefcaseIcon className='mr-3 h-6 w-6' />
           <span>{t('postJob')}</span>
          </Link>
         </>
        )}
       </div>
      </div>


     </div>
    </div>
   </section>
  </>
 );
};

export default AppleHeroSection;

