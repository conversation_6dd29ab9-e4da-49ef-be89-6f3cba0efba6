/**
 * Lazy Animation Loader
 * Loads complex animations only when needed and performance allows
 */

// import { gsap } from 'gsap';
import responsiveAutoscaling from '../services/responsiveAutoscaling';

class LazyAnimationLoader {
 constructor() {
  this.loadedAnimations = new Set();
  this.pendingLoads = new Map();
  this.performanceThreshold = {
   high: 3000, // 3 seconds delay for high performance
   medium: 5000, // 5 seconds delay for medium performance
   low: 10000, // 10 seconds delay for low performance
   minimal: null // Never load on minimal performance
  };
 }

 /**
  * Load animation with performance-based delay
  */
 async loadAnimation(animationName, animationFunction, priority = 'normal') {
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  
  // Skip loading on minimal performance
  if (performanceLevel === 'minimal') {
   console.log(`🚫 Skipping ${animationName} on minimal performance`);
   return null;
  }

  // Check if already loaded or loading
  if (this.loadedAnimations.has(animationName)) {
   console.log(`✅ ${animationName} already loaded`);
   return;
  }

  if (this.pendingLoads.has(animationName)) {
   console.log(`⏳ ${animationName} already loading`);
   return this.pendingLoads.get(animationName);
  }

  // Calculate delay based on performance and priority
  let delay = this.performanceThreshold[performanceLevel] || 5000;
  
  if (priority === 'high') {
   delay = Math.max(delay * 0.5, 1000); // Reduce delay for high priority
  } else if (priority === 'low') {
   delay = delay * 1.5; // Increase delay for low priority
  }

  console.log(`⏰ Scheduling ${animationName} to load in ${delay}ms`);

  // Create loading promise
  const loadPromise = new Promise((resolve) => {
   setTimeout(() => {
    try {
     console.log(`🎬 Loading ${animationName}...`);
     const result = animationFunction();
     this.loadedAnimations.add(animationName);
     this.pendingLoads.delete(animationName);
     console.log(`✅ ${animationName} loaded successfully`);
     resolve(result);
    } catch (error) {
     console.error(`❌ Failed to load ${animationName}:`, error);
     this.pendingLoads.delete(animationName);
     resolve(null);
    }
   }, delay);
  });

  this.pendingLoads.set(animationName, loadPromise);
  return loadPromise;
 }

 /**
  * Load animation when element becomes visible
  */
 loadOnVisible(element, animationName, animationFunction, options = {}) {
  if (!element || this.loadedAnimations.has(animationName)) return;

  const observer = new IntersectionObserver(
   (entries) => {
    entries.forEach((entry) => {
     if (entry.isIntersecting) {
      this.loadAnimation(animationName, animationFunction, options.priority);
      observer.unobserve(element);
     }
    });
   },
   {
    threshold: options.threshold || 0.1,
    rootMargin: options.rootMargin || '50px'
   }
  );

  observer.observe(element);
 }

 /**
  * Preload critical animations
  */
 preloadCritical(animations) {
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  
  if (performanceLevel === 'minimal') return;

  animations.forEach(({ name, function: animationFunction, delay = 0 }) => {
   setTimeout(() => {
    this.loadAnimation(name, animationFunction, 'high');
   }, delay);
  });
 }

 /**
  * Unload animation to free memory
  */
 unloadAnimation(animationName) {
  if (this.loadedAnimations.has(animationName)) {
   this.loadedAnimations.delete(animationName);
   console.log(`🗑️ Unloaded ${animationName}`);
  }
 }

 /**
  * Get loading status
  */
 getStatus() {
  return {
   loaded: Array.from(this.loadedAnimations),
   pending: Array.from(this.pendingLoads.keys()),
   performanceLevel: responsiveAutoscaling.currentPerformanceLevel
  };
 }

 /**
  * Clear all animations
  */
 clearAll() {
  this.loadedAnimations.clear();
  this.pendingLoads.clear();
  console.log('🧹 Cleared all lazy animations');
 }
}

// Export singleton instance
export const lazyAnimationLoader = new LazyAnimationLoader();

// Convenience functions
export const loadAnimationLazy = (name, fn, priority) => 
 lazyAnimationLoader.loadAnimation(name, fn, priority);

export const loadOnVisible = (element, name, fn, options) => 
 lazyAnimationLoader.loadOnVisible(element, name, fn, options);

export const preloadCritical = (animations) => 
 lazyAnimationLoader.preloadCritical(animations);

export default lazyAnimationLoader;
