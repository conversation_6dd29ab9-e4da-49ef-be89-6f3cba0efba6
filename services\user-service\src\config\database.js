/**
 * PostgreSQL Database Configuration for User Service
 * Handles database connection, pooling, and operations
 */

const { Pool } = require('pg');
const config = require('./config');

class DatabaseConfig {
  constructor() {
    this.pool = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000; // 5 seconds
  }

  /**
   * Initialize database connection
   */
  async initialize() {
    try {
      console.log('🔄 Initializing User Service database connection...');
      
      // Database configuration - support both DATABASE_URL and individual env vars
      let dbConfig;
      
      if (config.DATABASE_URL) {
        // Use DATABASE_URL if provided (common for cloud deployments)
        dbConfig = {
          connectionString: config.DATABASE_URL,
          max: config.DB_POOL_MAX,
          min: config.DB_POOL_MIN,
          idleTimeoutMillis: config.DB_IDLE_TIMEOUT,
          connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT,
          ssl: config.isProduction ? { rejectUnauthorized: false } : false,
          application_name: 'vwork_user_service'
        };
      } else {
        // Use individual environment variables
        dbConfig = {
          host: config.DB_HOST,
          port: config.DB_PORT,
          database: config.DB_NAME,
          user: config.DB_USER,
          password: config.DB_PASSWORD || undefined,
          max: config.DB_POOL_MAX,
          min: config.DB_POOL_MIN,
          idleTimeoutMillis: config.DB_IDLE_TIMEOUT,
          connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT,
          ssl: config.isProduction ? { rejectUnauthorized: false } : false,
          application_name: 'vwork_user_service'
        };
      }

      console.log('📊 Database config:', {
        host: dbConfig.host || 'Using DATABASE_URL',
        database: dbConfig.database || 'From CONNECTION_STRING',
        max_connections: dbConfig.max,
        ssl: !!dbConfig.ssl
      });

      // Create connection pool
      this.pool = new Pool(dbConfig);

      // Set up error handlers
      this.pool.on('error', (err) => {
        console.error('❌ Unexpected database error:', err);
        this.isConnected = false;
      });

      this.pool.on('connect', () => {
        console.log('✅ New database connection established');
      });

      this.pool.on('remove', () => {
        console.log('🔌 Database connection removed from pool');
      });

      // Test the connection
      await this.testConnection();
      
      console.log('🎉 User Service database initialized successfully');
      return this.pool;

    } catch (error) {
      console.error('❌ Failed to initialize database:', error);
      await this.handleConnectionError(error);
      throw error;
    }
  }

  /**
   * Test database connection
   */
  async testConnection() {
    try {
      const client = await this.pool.connect();
      const result = await client.query('SELECT NOW(), current_database(), version()');
      
      console.log('🏥 Database connection test:', {
        timestamp: result.rows[0].now,
        database: result.rows[0].current_database,
        version: result.rows[0].version.split(' ')[0]
      });

      client.release();
      this.isConnected = true;
      this.connectionAttempts = 0;
      
    } catch (error) {
      this.isConnected = false;
      console.error('❌ Database connection test failed:', error.message);
      throw error;
    }
  }

  /**
   * Handle connection errors with retry logic
   */
  async handleConnectionError(error) {
    this.connectionAttempts++;
    
    if (this.connectionAttempts < this.maxRetries) {
      console.log(`🔄 Retrying database connection (${this.connectionAttempts}/${this.maxRetries}) in ${this.retryDelay/1000}s...`);
      
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      
      try {
        await this.initialize();
      } catch (retryError) {
        console.error(`❌ Retry ${this.connectionAttempts} failed:`, retryError.message);
      }
    } else {
      console.error('💥 Max database connection retries reached');
      throw new Error(`Database connection failed after ${this.maxRetries} attempts: ${error.message}`);
    }
  }

  /**
   * Execute a query
   */
  async query(text, params = []) {
    if (!this.pool) {
      throw new Error('Database not initialized');
    }

    try {
      const start = Date.now();
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;

      if (config.isDevelopment && duration > 1000) {
        console.warn(`⚠️ Slow query detected (${duration}ms):`, text.substring(0, 100));
      }

      return result;
    } catch (error) {
      console.error('❌ Database query error:', {
        error: error.message,
        query: text.substring(0, 100),
        params: params.length > 0 ? '(params provided)' : '(no params)'
      });
      throw error;
    }
  }

  /**
   * Get a database client for transactions
   */
  async getClient() {
    if (!this.pool) {
      throw new Error('Database not initialized');
    }
    return await this.pool.connect();
  }

  /**
   * Execute a transaction
   */
  async transaction(callback) {
    const client = await this.getClient();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Health check for monitoring
   */
  async healthCheck() {
    try {
      if (!this.pool) {
        return {
          status: 'error',
          message: 'Database not initialized',
          timestamp: new Date().toISOString()
        };
      }

      const start = Date.now();
      const result = await this.pool.query('SELECT 1 as health_check');
      const responseTime = Date.now() - start;

      const poolStats = {
        total: this.pool.totalCount,
        idle: this.pool.idleCount,
        waiting: this.pool.waitingCount
      };

      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
        connection: 'active',
        pool: poolStats,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Run database migrations
   */
  async runMigrations() {
    try {
      console.log('🔄 Running User Service database migrations...');
      
      // Initialize database if not already initialized
      if (!this.pool) {
        console.log('🔄 Initializing database connection for migrations...');
        await this.initialize();
      }
      
      const fs = require('fs');
      const path = require('path');
      
      const migrationsPath = path.join(__dirname, '../../migrations');
      
      if (!fs.existsSync(migrationsPath)) {
        console.log('📁 Creating migrations directory...');
        fs.mkdirSync(migrationsPath, { recursive: true });
      }

      const migrationFile = path.join(migrationsPath, 'user_schema.sql');
      
      if (fs.existsSync(migrationFile)) {
        const migrationSQL = fs.readFileSync(migrationFile, 'utf8');
        
        console.log('📊 Executing migration...');
        await this.query(migrationSQL);
        
        console.log('✅ User Service migrations completed');
      } else {
        console.log('⚠️ No migration file found, skipping...');
      }

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Close database connection
   */
  async close() {
    if (this.pool) {
      try {
        await this.pool.end();
        console.log('🔌 Database connection closed');
      } catch (error) {
        console.error('❌ Error closing database:', error);
      }
    }
  }

  /**
   * Get database statistics
   */
  getStats() {
    if (!this.pool) {
      return { status: 'not_initialized' };
    }

    return {
      totalConnections: this.pool.totalCount,
      idleConnections: this.pool.idleCount,
      waitingRequests: this.pool.waitingCount,
      isConnected: this.isConnected
    };
  }

  /**
   * Firestore-like interface for compatibility
   */
  collection(tableName) {
    return {
      // Add document (insert)
      async add(data) {
        const columns = Object.keys(data);
        const values = Object.values(data);
        const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
        
        const query = `
          INSERT INTO ${tableName} (${columns.join(', ')})
          VALUES (${placeholders})
          RETURNING *
        `;
        
        const result = await this.query(query, values);
        return { id: result.rows[0].id, data: result.rows[0] };
      },

      // Get document by ID
      doc(id) {
        return {
          async get() {
            const result = await this.query(`SELECT * FROM ${tableName} WHERE id = $1`, [id]);
            return result.rows[0] || null;
          },

          async set(data) {
            const columns = Object.keys(data);
            const values = Object.values(data);
            const setClause = columns.map((col, index) => `${col} = $${index + 2}`).join(', ');
            
            const query = `
              INSERT INTO ${tableName} (id, ${columns.join(', ')})
              VALUES ($1, ${values.map((_, index) => `$${index + 2}`).join(', ')})
              ON CONFLICT (id) DO UPDATE SET ${setClause}
              RETURNING *
            `;
            
            const result = await this.query(query, [id, ...values]);
            return result.rows[0];
          },

          async update(data) {
            const columns = Object.keys(data);
            const values = Object.values(data);
            const setClause = columns.map((col, index) => `${col} = $${index + 2}`).join(', ');
            
            const query = `
              UPDATE ${tableName}
              SET ${setClause}, updated_at = CURRENT_TIMESTAMP
              WHERE id = $1
              RETURNING *
            `;
            
            const result = await this.query(query, [id, ...values]);
            return result.rows[0];
          },

          async delete() {
            await this.query(`DELETE FROM ${tableName} WHERE id = $1`, [id]);
          }
        };
      },

      // Query with conditions
      where(field, operator, value) {
        return {
          async get() {
            const query = `SELECT * FROM ${tableName} WHERE ${field} ${operator} $1`;
            const result = await this.query(query, [value]);
            return result.rows;
          }
        };
      },

      // Get all documents
      async get() {
        const result = await this.query(`SELECT * FROM ${tableName} ORDER BY created_at DESC`);
        return result.rows;
      }
    };
  }
}

// Create singleton instance
const databaseConfig = new DatabaseConfig();

module.exports = databaseConfig; 