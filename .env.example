# ==================== NERAFUS PLATFORM CONFIGURATION ====================
# Copy this file to .env and update the values for your environment
# This file serves as a template for all required environment variables

# ==================== BASIC SETTINGS ====================
NODE_ENV=development
API_VERSION=v1
LOG_LEVEL=info
LOG_FORMAT=json

# ==================== FIREBASE CONFIGURATION ====================
# Firebase project settings (REQUIRED)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=your-firebase-client-email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com

# ==================== SECURITY CONFIGURATION ====================
# JWT Configuration (REQUIRED)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_CREDENTIALS=true

# Security Features
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=false
FORCE_HTTPS=false

# ==================== DATABASE CONFIGURATION ====================
# Database type: 'firebase' or 'postgresql'
DB_TYPE=firebase

# PostgreSQL Configuration (if using PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=your-db-name
DB_POOL_MAX=20
DB_POOL_MIN=2
DB_CONNECTION_TIMEOUT=5000

# ==================== SERVICE URLS ====================
# Development URLs (update for production)
API_GATEWAY_URL=http://localhost:8080
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
JOB_SERVICE_URL=http://localhost:3004
CHAT_SERVICE_URL=http://localhost:3005
COMMUNITY_SERVICE_URL=http://localhost:3006
PAYMENT_SERVICE_URL=http://localhost:3007
CLIENT_URL=http://localhost:3000

# ==================== EXTERNAL SERVICES ====================
# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
CACHE_TTL=3600

# Email Service Configuration (optional)
EMAIL_PROVIDER=console
EMAIL_API_KEY=your-email-api-key
EMAIL_FROM=<EMAIL>

# File Storage Configuration (optional)
STORAGE_PROVIDER=local
STORAGE_PATH=./uploads
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket
AWS_REGION=us-east-1

# ==================== RATE LIMITING ====================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==================== SESSION CONFIGURATION ====================
SESSION_SECRET=your-session-secret-change-this
SESSION_TIMEOUT=3600000
SESSION_STORE=memory

# ==================== MONITORING & HEALTH CHECKS ====================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=false
METRICS_PORT=9090

# ==================== DEVELOPMENT SETTINGS ====================
# Enable development features (set to false in production)
ENABLE_DEBUG_ROUTES=true
ENABLE_MOCK_DATA=false
DB_AUTO_MIGRATE=true
DB_AUTO_SEED=false
DB_DEBUG_QUERIES=false

# ==================== LOGGING CONFIGURATION ====================
LOG_FILE_PATH=./logs
LOG_ROTATION_ENABLED=true
DB_LOGGING=false
DB_SLOW_QUERY_THRESHOLD=1000
