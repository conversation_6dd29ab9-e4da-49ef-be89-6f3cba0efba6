services:
  - type: web
    runtime: node
    name: nerafus-community-service
    region: oregon
    branch: main
    rootDir: services/community-service
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: FIREBASE_CLIENT_EMAIL
        sync: false
      - key: FIREBASE_PRIVATE_KEY
        sync: false
      - key: DATABASE_URL
        sync: false
      - key: ALLOWED_ORIGINS
        value: https://nerafus-client.onrender.com,https://vwork-api-gateway.onrender.com,https://nerafus.com,https://www.nerafus.com
      - key: CORS_CREDENTIALS
        value: "true"
      - key: LOG_LEVEL
        value: info
      - key: LOG_FORMAT
        value: json
      - key: ENABLE_COMPRESSION
        value: "true"
      - key: ENABLE_HELMET
        value: "true"
      - key: TRUST_PROXY
        value: "true"
      - key: DB_POOL_MAX
        value: "10"
      - key: DB_POOL_MIN
        value: "2"
      - key: DB_CONNECTION_TIMEOUT
        value: "5000"
      - key: DB_IDLE_TIMEOUT
        value: "30000"
    plan: free
