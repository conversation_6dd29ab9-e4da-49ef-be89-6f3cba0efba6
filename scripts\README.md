# NERAFUS Development Scripts

## Quick Start

### 1. Install Dependencies

```bash
npm run deps
```

- Downloads và cài đặt **toàn bộ dependencies** cho tất cả services (root + client + server)
- Tương đương với việc chạy `npm install` trong root, client và server directories

### 2. Start Development Server

```bash
npm start
```

- Start **cả frontend client và backend server** đồng thời
- Frontend: <http://localhost:3000>
- Backend: <http://localhost:8080>
- Tự động setup proxy và kết nối giữa client-server

### 3. Stop All Processes

```bash
npm stop
```

- **Kill toàn bộ processes** được start từ npm start
- Kill processes trên ports: 3000, 3001, 3002, 8000, 8080, 5000
- Kill node.exe, npm.exe, npx.exe processes

## Additional Commands

### Dependencies Management

```bash
npm run clean               # Clean và reinstall tất cả dependencies
npm run setup               # Setup environment và install dependencies
```

### Development

```bash
npm run build               # Build production client
npm run lint                # Run ESLint on client code
npm run type-check          # Run TypeScript type checking
```

## ✅ Requirements Status

| Command | Status | Description |
|---------|--------|-------------|
| `npm run deps` | ✅ **ĐẠT** | Download toàn bộ dependencies cho root, client, server |
| `npm start` | ✅ **ĐẠT** | Start cả backend server (8080) và frontend client (3000) |
| `npm stop` | ✅ **ĐẠT** | Kill toàn bộ process được start từ npm start |

## Script Files

- `install.js` - Quản lý cài đặt dependencies cho tất cả services
- `start-all.js` - Start cả backend server và frontend client đồng thời
- `stop.js` - Kill development processes và free up ports
- `setup-env.js` - Setup environment variables