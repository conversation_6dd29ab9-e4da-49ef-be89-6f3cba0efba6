/**
 * PostgreSQL Configuration for Team Service
 */

const { Pool } = require('pg');
const config = require('./config');

let pool = null;

// Initialize PostgreSQL connection pool
function initializePostgreSQL() {
  try {
    const connectionConfig = {
      host: config.DB_HOST,
      port: config.DB_PORT,
      database: config.DB_NAME,
      user: config.DB_USER,
      password: config.DB_PASSWORD,
      max: config.DB_POOL_MAX,
      min: config.DB_POOL_MIN,
      connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT,
      idleTimeoutMillis: config.DB_IDLE_TIMEOUT,
      ssl: config.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
    };

    // Use DATABASE_URL if provided (for production)
    if (config.DATABASE_URL) {
      pool = new Pool({
        connectionString: config.DATABASE_URL,
        ssl: config.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        max: config.DB_POOL_MAX,
        min: config.DB_POOL_MIN,
        connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT,
        idleTimeoutMillis: config.DB_IDLE_TIMEOUT
      });
    } else {
      pool = new Pool(connectionConfig);
    }

    // Test connection
    pool.on('connect', () => {
      console.log('✅ PostgreSQL connected for Team Service');
    });

    pool.on('error', (err) => {
      console.error('❌ PostgreSQL pool error:', err);
    });

    console.log('✅ PostgreSQL pool initialized for Team Service');
    return pool;
  } catch (error) {
    console.error('❌ PostgreSQL initialization failed:', error.message);
    throw error;
  }
}

// Get database connection
function getPool() {
  if (!pool) {
    throw new Error('PostgreSQL pool not initialized. Call initializePostgreSQL() first.');
  }
  return pool;
}

// Execute query
async function query(text, params) {
  const client = await getPool().connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

// Execute transaction
async function transaction(callback) {
  const client = await getPool().connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Health check
async function healthCheck() {
  try {
    const result = await query('SELECT NOW() as current_time');
    return {
      status: 'healthy',
      timestamp: result.rows[0].current_time,
      service: 'Team Service PostgreSQL'
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      service: 'Team Service PostgreSQL'
    };
  }
}

// Close pool
async function closePool() {
  if (pool) {
    await pool.end();
    console.log('✅ PostgreSQL pool closed for Team Service');
  }
}

module.exports = {
  initializePostgreSQL,
  getPool,
  query,
  transaction,
  healthCheck,
  closePool
}; 