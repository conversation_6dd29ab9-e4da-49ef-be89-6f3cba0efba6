@echo off
echo ========================================
echo Installing PostgreSQL for Team Service
echo ========================================

echo.
echo Step 1: Downloading PostgreSQL...
echo Downloading PostgreSQL 15.5 for Windows...

:: Download PostgreSQL installer
powershell -Command "& {Invoke-WebRequest -Uri 'https://get.enterprisedb.com/postgresql/postgresql-15.5-1-windows-x64.exe' -OutFile 'postgresql-installer.exe'}"

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to download PostgreSQL installer
    echo Please download manually from: https://www.postgresql.org/download/windows/
    pause
    exit /b 1
)

echo ✅ PostgreSQL installer downloaded successfully

echo.
echo Step 2: Installing PostgreSQL...
echo Please follow the installation wizard:
echo - Use default port: 5432
echo - Set password: postgres
echo - Keep default installation directory
echo - Install all components

postgresql-installer.exe

echo.
echo Step 3: Setting up environment variables...
setx PATH "%PATH%;C:\Program Files\PostgreSQL\15\bin" /M

echo.
echo Step 4: Starting PostgreSQL service...
net start postgresql-x64-15

echo.
echo Step 5: Setting up Team Service database...
node setup-database.js

echo.
echo ========================================
echo PostgreSQL installation completed!
echo ========================================
echo.
echo Next steps:
echo 1. Restart your terminal/command prompt
echo 2. Navigate to team-service directory
echo 3. Run: npm start
echo.
pause 