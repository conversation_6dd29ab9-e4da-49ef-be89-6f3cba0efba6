import { ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';
import { getErrorSuggestions, classifyError } from '../../utils/authErrorLogger';

// Component for displaying authentication errors with proper styling and suggestions
const AuthErrorDisplay = ({ error = null, errorCode = null, className = '' }) => {
 const { t } = useLanguage();

 if (!error) return null;

 // Use error utility if we have an error code
 let errorInfo;
 if (errorCode) {
  const classification = classifyError(errorCode);
  const suggestions = getErrorSuggestions(errorCode);
  
  errorInfo = {
   type: classification.isUserError ? 'user-error' : 'system-error',
   icon: classification.severity === 'high' ? ExclamationTriangleIcon : InformationCircleIcon,
   tips: suggestions,
   isRetryable: classification.isRetryable,
   severity: classification.severity
  };
 } else {
  // Fallback to legacy logic for non-Firebase errors
  errorInfo = getLegacyErrorInfo(error);
 }

 const IconComponent = errorInfo.icon;

 return (
  <div className={`${getBgClass(errorInfo)} border ${getBorderClass(errorInfo)} rounded-lg p-4 ${className}`}>
   <div className="flex items-start space-x-3">
    <IconComponent className={`w-5 h-5 ${getIconClass(errorInfo)} flex-shrink-0 mt-0.5`} />
    <div className="flex-1">
     <p className={`${getTextClass(errorInfo)} font-medium text-sm leading-relaxed`}>
      {error}
     </p>
     
     {errorInfo.tips.length > 0 && (
      <div className={`mt-2 text-xs ${getHintClass(errorInfo)}`}>
       <p className="font-medium">💡 {t('authErrorTips')}</p>
       <ul className="list-disc list-inside mt-1 space-y-1">
        {errorInfo.tips.map((tip, index) => (
         <li key={`tip-${tip.substring(0, 10)}-${index}`}>{tip}</li>
        ))}
       </ul>
      </div>
     )}
     
     {/* Show retry suggestion for rate limiting */}
     {errorCode === 'auth/too-many-requests' && (
      <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
       <p className="text-xs text-yellow-700">
        ⏰ Firebase đã tạm khóa do quá nhiều lần thử. Hãy đợi một chút rồi thử lại.
       </p>
      </div>
     )}
     
     {/* Show account recovery for credential errors */}
     {(errorCode === 'auth/invalid-credential' || errorCode === 'auth/invalid-login-credentials') && (
      <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
       <p className="text-xs text-blue-700">
        🔐 Nếu bạn quên mật khẩu, hãy sử dụng chức năng "Quên mật khẩu" bên dưới.
       </p>
      </div>
     )}
    </div>
   </div>
  </div>
 );
};

// Helper functions for styling based on error type
const getBgClass = (errorInfo) => {
 if (errorInfo.severity === 'high') return 'bg-red-50';
 return 'bg-orange-50';
};

const getBorderClass = (errorInfo) => {
 if (errorInfo.severity === 'high') return 'border-red-200';
 return 'border-orange-200';
};

const getIconClass = (errorInfo) => {
 if (errorInfo.severity === 'high') return 'text-red-600';
 return 'text-orange-600';
};

const getTextClass = (errorInfo) => {
 if (errorInfo.severity === 'high') return 'text-red-600';
 return 'text-orange-600';
};

const getHintClass = (errorInfo) => {
 if (errorInfo.severity === 'high') return 'text-red-500';
 return 'text-orange-500';
};

// Legacy error detection for non-Firebase errors
const getLegacyErrorInfo = (errorMessage) => {
 const errorLower = errorMessage.toLowerCase();
 
 if (errorLower.includes('invalid-credential') || errorLower.includes('không hợp lệ')) {
  return {
   type: 'credential',
   icon: ExclamationTriangleIcon,
   tips: [
    'Kiểm tra lại email và mật khẩu',
    'Đảm bảo không có khoảng trắng thừa',
    'Thử đặt lại mật khẩu nếu cần'
   ],
   severity: 'high'
  };
 }
 
 if (errorLower.includes('too-many-requests') || errorLower.includes('quá nhiều')) {
  return {
   type: 'rate-limit',
   icon: InformationCircleIcon,
   tips: ['Vui lòng đợi 1-2 phút trước khi thử lại'],
   severity: 'low'
  };
 }
 
 if (errorLower.includes('network') || errorLower.includes('mạng')) {
  return {
   type: 'network',
   icon: ExclamationTriangleIcon,
   tips: ['Kiểm tra kết nối internet của bạn'],
   severity: 'high'
  };
 }
 
 if (errorLower.includes('email-already-in-use') || errorLower.includes('đã được sử dụng')) {
  return {
   type: 'email-exists',
   icon: InformationCircleIcon,
   tips: ['Thử đăng nhập thay vì đăng ký, hoặc đặt lại mật khẩu'],
   severity: 'low'
  };
 }
 
 return {
  type: 'generic',
  icon: ExclamationTriangleIcon,
  tips: [],
  severity: 'high'
 };
};

export default AuthErrorDisplay;
