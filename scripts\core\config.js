#!/usr/bin/env node

/**
 * Configuration management script for NERAFUS platform
 * Handles environment setup, validation, and configuration generation
 */

const fs = require('fs');
const path = require('path');

// Simplified ConfigManager for scripts (no shared dependencies)
class ConfigManager {
  constructor(serviceName = 'unknown') {
    this.serviceName = serviceName;
    this.config = {};
    this.errors = [];
    this.loadConfiguration();
  }

  loadConfiguration() {
    // Load from .env files
    this.loadFromEnvFile();
    // Load from process environment
    Object.keys(process.env).forEach(key => {
      this.config[key] = process.env[key];
    });
    // Set defaults
    this.setDefaults();
  }

  loadFromEnvFile() {
    const envPaths = [
      path.join(process.cwd(), '.env'),
      path.join(process.cwd(), '.env.local'),
      path.join(__dirname, '../../.env')
    ];

    for (const envPath of envPaths) {
      if (fs.existsSync(envPath)) {
        require('dotenv').config({ path: envPath });
        break;
      }
    }
  }

  setDefaults() {
    const defaults = {
      NODE_ENV: 'development',
      LOG_LEVEL: 'info',
      CORS_ORIGINS: 'http://localhost:3000'
    };

    Object.entries(defaults).forEach(([key, value]) => {
      if (!this.config[key]) {
        this.config[key] = value;
      }
    });
  }

  get(key, defaultValue = undefined) {
    return this.config[key] !== undefined ? this.config[key] : defaultValue;
  }

  getAll() {
    return { ...this.config };
  }

  isValid() {
    return this.errors.length === 0;
  }
}

const ROOT_DIR = path.join(__dirname, '..', '..');
const CONFIG_DIR = path.join(ROOT_DIR, 'config');
const SERVICES_DIR = path.join(ROOT_DIR, 'services');

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Available services
 */
const SERVICES = [
  'api-gateway',
  'auth-service',
  'user-service',
  'project-service',
  'job-service',
  'chat-service',
  'community-service',
  'payment-service'
];

/**
 * Load environment configuration
 */
function loadEnvironmentConfig(environment) {
  const envFile = path.join(CONFIG_DIR, 'environments', `${environment}.env`);
  
  if (!fs.existsSync(envFile)) {
    log.error(`Environment configuration not found: ${envFile}`);
    return null;
  }

  const envContent = fs.readFileSync(envFile, 'utf8');
  const envVars = {};

  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  return envVars;
}

/**
 * Validate configuration for all services
 */
function validateAllConfigurations(environment = 'development') {
  log.info(`Validating configurations for ${environment} environment...`);

  const envConfig = loadEnvironmentConfig(environment);
  if (!envConfig) {
    return false;
  }

  // Set environment variables temporarily for validation
  const originalEnv = { ...process.env };
  Object.assign(process.env, envConfig);

  let allValid = true;
  const validationResults = [];

  // Validate each service
  SERVICES.forEach(serviceName => {
    try {
      const config = new ConfigManager(serviceName);
      const isValid = config.isValid();
      const errors = config.errors; // Use config.errors directly

      validationResults.push({
        service: serviceName,
        valid: isValid,
        errors: errors
      });

      if (isValid) {
        log.success(`${serviceName}: Configuration valid`);
      } else {
        log.error(`${serviceName}: Configuration invalid`);
        errors.forEach(error => log.error(`  - ${error}`));
        allValid = false;
      }
    } catch (error) {
      log.error(`${serviceName}: Validation failed - ${error.message}`);
      allValid = false;
    }
  });

  // Restore original environment
  process.env = originalEnv;

  return { valid: allValid, results: validationResults };
}

/**
 * Generate service-specific .env files
 */
function generateServiceEnvFiles(environment = 'development') {
  log.info(`Generating service .env files for ${environment} environment...`);

  const envConfig = loadEnvironmentConfig(environment);
  if (!envConfig) {
    return false;
  }

  SERVICES.forEach(serviceName => {
    const servicePath = path.join(SERVICES_DIR, serviceName);
    
    if (!fs.existsSync(servicePath)) {
      log.warn(`Service directory not found: ${servicePath}`);
      return;
    }

    // Create service-specific configuration
    const serviceConfig = new ConfigManager(serviceName);
    Object.assign(process.env, envConfig);
    
    const serviceEnvContent = generateServiceEnvContent(serviceName, envConfig);
    const envFilePath = path.join(servicePath, '.env');
    
    // Backup existing .env file
    if (fs.existsSync(envFilePath)) {
      const backupPath = path.join(servicePath, '.env.backup');
      fs.copyFileSync(envFilePath, backupPath);
      log.info(`Backed up existing .env for ${serviceName}`);
    }

    fs.writeFileSync(envFilePath, serviceEnvContent);
    log.success(`Generated .env for ${serviceName}`);
  });

  return true;
}

/**
 * Generate service-specific environment content
 */
function generateServiceEnvContent(serviceName, baseConfig) {
  const serviceConfig = new ConfigManager(serviceName);
  
  let content = `# ==================== ${serviceName.toUpperCase()} CONFIGURATION ====================\n`;
  content += `# Generated automatically - do not edit manually\n`;
  content += `# Environment: ${baseConfig.NODE_ENV || 'development'}\n`;
  content += `# Generated: ${new Date().toISOString()}\n\n`;

  // Service-specific settings
  content += `# Service Configuration\n`;
  content += `SERVICE_NAME=${serviceName}\n`;
  content += `NODE_ENV=${baseConfig.NODE_ENV || 'development'}\n`;
  
  // Set service-specific port
  const servicePorts = {
    'api-gateway': 8080,
    'auth-service': 3001,
    'user-service': 3002,
    'project-service': 3003,
    'job-service': 3004,
    'chat-service': 3005,
    'community-service': 3006,
    'payment-service': 3007
  };
  
  content += `PORT=${servicePorts[serviceName] || 3000}\n\n`;

  // Add relevant configuration sections
  const relevantKeys = [
    'API_VERSION', 'LOG_LEVEL', 'LOG_FORMAT',
    'FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY', 'FIREBASE_DATABASE_URL',
    'JWT_SECRET', 'JWT_EXPIRES_IN',
    'CORS_ORIGINS', 'CORS_CREDENTIALS',
    'ENABLE_HELMET', 'ENABLE_RATE_LIMITING',
    'DB_TYPE', 'DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
    'REDIS_URL', 'REDIS_PASSWORD',
    'RATE_LIMIT_WINDOW_MS', 'RATE_LIMIT_MAX_REQUESTS'
  ];

  // Add service URLs
  const serviceUrls = [
    'API_GATEWAY_URL', 'AUTH_SERVICE_URL', 'USER_SERVICE_URL',
    'PROJECT_SERVICE_URL', 'JOB_SERVICE_URL', 'CHAT_SERVICE_URL',
    'COMMUNITY_SERVICE_URL', 'PAYMENT_SERVICE_URL', 'CLIENT_URL'
  ];

  relevantKeys.concat(serviceUrls).forEach(key => {
    if (baseConfig[key] !== undefined) {
      content += `${key}=${baseConfig[key]}\n`;
    }
  });

  return content;
}

/**
 * Check configuration differences between environments
 */
function compareEnvironments(env1 = 'development', env2 = 'production') {
  log.info(`Comparing ${env1} and ${env2} configurations...`);

  const config1 = loadEnvironmentConfig(env1);
  const config2 = loadEnvironmentConfig(env2);

  if (!config1 || !config2) {
    return false;
  }

  const allKeys = new Set([...Object.keys(config1), ...Object.keys(config2)]);
  const differences = [];

  allKeys.forEach(key => {
    const val1 = config1[key];
    const val2 = config2[key];

    if (val1 !== val2) {
      differences.push({
        key,
        [env1]: val1 || 'undefined',
        [env2]: val2 || 'undefined'
      });
    }
  });

  if (differences.length === 0) {
    log.success('No differences found between environments');
  } else {
    log.info(`Found ${differences.length} differences:`);
    differences.forEach(diff => {
      console.log(`  ${diff.key}:`);
      console.log(`    ${env1}: ${diff[env1]}`);
      console.log(`    ${env2}: ${diff[env2]}`);
    });
  }

  return differences;
}

/**
 * Create configuration template
 */
function createTemplate(serviceName) {
  const templateContent = `# ==================== ${serviceName.toUpperCase()} CONFIGURATION ====================
# Service-specific environment variables

# Basic Configuration
SERVICE_NAME=${serviceName}
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_PRIVATE_KEY=your-private-key
FIREBASE_DATABASE_URL=your-database-url

# Security
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h
CORS_ORIGINS=http://localhost:3000

# Database (if using PostgreSQL)
DB_TYPE=firebase
DB_HOST=localhost
DB_PORT=5432
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=your-db-name

# External Services
API_GATEWAY_URL=http://localhost:8080
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
JOB_SERVICE_URL=http://localhost:3004
CHAT_SERVICE_URL=http://localhost:3005
COMMUNITY_SERVICE_URL=http://localhost:3006
PAYMENT_SERVICE_URL=http://localhost:3007
`;

  const templatePath = path.join(ROOT_DIR, `${serviceName}.env.template`);
  fs.writeFileSync(templatePath, templateContent);
  log.success(`Created configuration template: ${templatePath}`);
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const target = args[1];
  const environment = args[2] || 'development';

  switch (command) {
    case 'validate':
      const validation = validateAllConfigurations(environment);
      if (!validation.valid) {
        process.exit(1);
      }
      break;

    case 'generate':
      if (!generateServiceEnvFiles(environment)) {
        process.exit(1);
      }
      break;

    case 'compare':
      const env2 = target || 'production';
      compareEnvironments(environment, env2);
      break;

    case 'template':
      if (!target) {
        log.error('Please specify a service name');
        process.exit(1);
      }
      createTemplate(target);
      break;

    default:
      console.log(`
Usage: node config.js <command> [options]

Commands:
  validate [env]     Validate configuration for environment (default: development)
  generate [env]     Generate service .env files for environment
  compare [env1] [env2]  Compare configurations between environments
  template <service> Create configuration template for service

Examples:
  node config.js validate production
  node config.js generate development
  node config.js compare development production
  node config.js template auth-service
      `);
      process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  validateAllConfigurations,
  generateServiceEnvFiles,
  compareEnvironments,
  createTemplate
};
