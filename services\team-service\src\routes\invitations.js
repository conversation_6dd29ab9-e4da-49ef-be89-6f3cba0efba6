/**
 * Team Invitations Routes
 * Handle team invitation system
 */

const express = require('express');
const router = express.Router();

// Import middleware
const { verifyFirebaseToken, requireTeamOwnership } = require('../middleware/auth');
const { validateBody, schemas } = require('../middleware/validation');
const { responseMiddleware } = require('../utils/response');
const { query, transaction } = require('../config/postgresql');
const logger = require('../utils/logger');

// Apply response middleware
router.use(responseMiddleware);

// Get user's pending invitations
router.get('/my-invitations', verifyFirebaseToken, async (req, res) => {
  try {
    const userId = req.user.uid;

    const invitationsQuery = `
      SELECT 
        ti.*,
        t.name as team_name,
        t.slogan as team_slogan,
        t.description as team_description,
        t.logo_url as team_logo,
        u.email as inviter_email
      FROM team_invitations ti
      JOIN teams t ON ti.team_id = t.id
      LEFT JOIN users u ON ti.inviter_id = u.id
      WHERE ti.invitee_id = $1 AND ti.status = 'pending' AND ti.expires_at > CURRENT_TIMESTAMP
      ORDER BY ti.created_at DESC
    `;

    const result = await query(invitationsQuery, [userId]);
    const invitations = result.rows;

    logger.info(`Retrieved ${invitations.length} pending invitations for user: ${userId}`);
    res.apiSuccess(invitations, 'Invitations retrieved successfully');

  } catch (error) {
    logger.error('Get invitations failed:', error.message);
    res.apiError('Failed to get invitations', 'GET_INVITATIONS_ERROR', 500);
  }
});

// Get team invitations (leader only)
router.get('/team/:teamId', verifyFirebaseToken, requireTeamOwnership, async (req, res) => {
  try {
    const { teamId } = req.params;

    const invitationsQuery = `
      SELECT 
        ti.*,
        u.email as invitee_email
      FROM team_invitations ti
      LEFT JOIN users u ON ti.invitee_id = u.id
      WHERE ti.team_id = $1
      ORDER BY ti.created_at DESC
    `;

    const result = await query(invitationsQuery, [teamId]);
    const invitations = result.rows;

    logger.info(`Retrieved ${invitations.length} invitations for team: ${teamId}`);
    res.apiSuccess(invitations, 'Team invitations retrieved successfully');

  } catch (error) {
    logger.error('Get team invitations failed:', error.message);
    res.apiError('Failed to get team invitations', 'GET_TEAM_INVITATIONS_ERROR', 500);
  }
});

// Send invitation to user
router.post('/team/:teamId', verifyFirebaseToken, requireTeamOwnership, validateBody(schemas.teamInvitation), async (req, res) => {
  try {
    const { teamId } = req.params;
    const { invitee_id, message } = req.body;
    const inviterId = req.user.uid;

    // Check if user is already a member
    const existingMemberQuery = `
      SELECT id FROM team_members 
      WHERE team_id = $1 AND user_id = $2
    `;
    const existingMemberResult = await query(existingMemberQuery, [teamId, invitee_id]);
    
    if (existingMemberResult.rows.length > 0) {
      return res.apiBadRequest('User is already a team member');
    }

    // Check if invitation already exists
    const existingInvitationQuery = `
      SELECT id FROM team_invitations 
      WHERE team_id = $1 AND invitee_id = $2 AND status = 'pending'
    `;
    const existingInvitationResult = await query(existingInvitationQuery, [teamId, invitee_id]);
    
    if (existingInvitationResult.rows.length > 0) {
      return res.apiBadRequest('Invitation already sent to this user');
    }

    // Create invitation
    const invitationQuery = `
      INSERT INTO team_invitations (team_id, inviter_id, invitee_id, message)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await query(invitationQuery, [teamId, inviterId, invitee_id, message]);
    const invitation = result.rows[0];

    logger.info(`Invitation sent: team ${teamId} to user ${invitee_id} by ${inviterId}`);
    res.apiSuccess(invitation, 'Invitation sent successfully');

  } catch (error) {
    logger.error('Send invitation failed:', error.message);
    res.apiError('Failed to send invitation', 'SEND_INVITATION_ERROR', 500);
  }
});

// Accept invitation
router.put('/:invitationId/accept', verifyFirebaseToken, async (req, res) => {
  try {
    const { invitationId } = req.params;
    const userId = req.user.uid;

    // Get invitation details
    const invitationQuery = `
      SELECT * FROM team_invitations 
      WHERE id = $1 AND invitee_id = $2 AND status = 'pending' AND expires_at > CURRENT_TIMESTAMP
    `;
    const invitationResult = await query(invitationQuery, [invitationId, userId]);

    if (invitationResult.rows.length === 0) {
      return res.apiNotFound('Invitation not found or expired');
    }

    const invitation = invitationResult.rows[0];

    // Accept invitation in transaction
    await transaction(async (client) => {
      // Update invitation status
      await client.query(
        'UPDATE team_invitations SET status = $1 WHERE id = $2',
        ['accepted', invitationId]
      );

      // Update team member status
      await client.query(
        'UPDATE team_members SET status = $1 WHERE team_id = $2 AND user_id = $3',
        ['active', invitation.team_id, userId]
      );

      // Check if all members have accepted (activate team)
      const pendingMembersQuery = `
        SELECT COUNT(*) as count FROM team_members 
        WHERE team_id = $1 AND status = 'pending'
      `;
      const pendingMembersResult = await client.query(pendingMembersQuery, [invitation.team_id]);
      
      if (parseInt(pendingMembersResult.rows[0].count) === 0) {
        await client.query(
          'UPDATE teams SET status = $1 WHERE id = $2',
          ['active', invitation.team_id]
        );
      }
    });

    logger.info(`Invitation accepted: ${invitationId} by user: ${userId}`);
    res.apiSuccess(null, 'Invitation accepted successfully');

  } catch (error) {
    logger.error('Accept invitation failed:', error.message);
    res.apiError('Failed to accept invitation', 'ACCEPT_INVITATION_ERROR', 500);
  }
});

// Reject invitation
router.put('/:invitationId/reject', verifyFirebaseToken, async (req, res) => {
  try {
    const { invitationId } = req.params;
    const userId = req.user.uid;

    // Get invitation details
    const invitationQuery = `
      SELECT * FROM team_invitations 
      WHERE id = $1 AND invitee_id = $2 AND status = 'pending'
    `;
    const invitationResult = await query(invitationQuery, [invitationId, userId]);

    if (invitationResult.rows.length === 0) {
      return res.apiNotFound('Invitation not found');
    }

    const invitation = invitationResult.rows[0];

    // Reject invitation in transaction
    await transaction(async (client) => {
      // Update invitation status
      await client.query(
        'UPDATE team_invitations SET status = $1 WHERE id = $2',
        ['rejected', invitationId]
      );

      // Remove team member
      await client.query(
        'DELETE FROM team_members WHERE team_id = $1 AND user_id = $2',
        [invitation.team_id, userId]
      );
    });

    logger.info(`Invitation rejected: ${invitationId} by user: ${userId}`);
    res.apiSuccess(null, 'Invitation rejected successfully');

  } catch (error) {
    logger.error('Reject invitation failed:', error.message);
    res.apiError('Failed to reject invitation', 'REJECT_INVITATION_ERROR', 500);
  }
});

// Cancel invitation (leader only)
router.delete('/:invitationId', verifyFirebaseToken, async (req, res) => {
  try {
    const { invitationId } = req.params;
    const userId = req.user.uid;

    // Get invitation and check ownership
    const invitationQuery = `
      SELECT ti.*, t.leader_id 
      FROM team_invitations ti
      JOIN teams t ON ti.team_id = t.id
      WHERE ti.id = $1 AND ti.status = 'pending'
    `;
    const invitationResult = await query(invitationQuery, [invitationId]);

    if (invitationResult.rows.length === 0) {
      return res.apiNotFound('Invitation not found');
    }

    const invitation = invitationResult.rows[0];

    // Check if user is team leader or inviter
    if (invitation.leader_id !== userId && invitation.inviter_id !== userId) {
      return res.apiForbidden('Only team leader or inviter can cancel invitation');
    }

    // Cancel invitation in transaction
    await transaction(async (client) => {
      // Update invitation status
      await client.query(
        'UPDATE team_invitations SET status = $1 WHERE id = $2',
        ['cancelled', invitationId]
      );

      // Remove team member if exists
      await client.query(
        'DELETE FROM team_members WHERE team_id = $1 AND user_id = $2 AND status = $3',
        [invitation.team_id, invitation.invitee_id, 'pending']
      );
    });

    logger.info(`Invitation cancelled: ${invitationId} by user: ${userId}`);
    res.apiSuccess(null, 'Invitation cancelled successfully');

  } catch (error) {
    logger.error('Cancel invitation failed:', error.message);
    res.apiError('Failed to cancel invitation', 'CANCEL_INVITATION_ERROR', 500);
  }
});

// Resend invitation (leader only)
router.post('/:invitationId/resend', verifyFirebaseToken, async (req, res) => {
  try {
    const { invitationId } = req.params;
    const userId = req.user.uid;

    // Get invitation and check ownership
    const invitationQuery = `
      SELECT ti.*, t.leader_id 
      FROM team_invitations ti
      JOIN teams t ON ti.team_id = t.id
      WHERE ti.id = $1 AND ti.status = 'pending'
    `;
    const invitationResult = await query(invitationQuery, [invitationId]);

    if (invitationResult.rows.length === 0) {
      return res.apiNotFound('Invitation not found');
    }

    const invitation = invitationResult.rows[0];

    // Check if user is team leader
    if (invitation.leader_id !== userId) {
      return res.apiForbidden('Only team leader can resend invitation');
    }

    // Update invitation expiry
    const updateQuery = `
      UPDATE team_invitations 
      SET expires_at = CURRENT_TIMESTAMP + INTERVAL '7 days'
      WHERE id = $1
      RETURNING *
    `;

    const result = await query(updateQuery, [invitationId]);
    const updatedInvitation = result.rows[0];

    logger.info(`Invitation resent: ${invitationId} by user: ${userId}`);
    res.apiSuccess(updatedInvitation, 'Invitation resent successfully');

  } catch (error) {
    logger.error('Resend invitation failed:', error.message);
    res.apiError('Failed to resend invitation', 'RESEND_INVITATION_ERROR', 500);
  }
});

module.exports = router; 