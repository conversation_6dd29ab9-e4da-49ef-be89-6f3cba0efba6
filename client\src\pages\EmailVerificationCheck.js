import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { ApplePageWrapper } from '../components/apple';

const EmailVerificationCheck = () => {
 const { user } = useAuth();

 if (!user) {
  return (
   <ApplePageWrapper>
    <div className="text-center">
     <h1 className="font-bold text-2xl font-bold text-blue-600 mb-4">
      📧 Email Verification Check
     </h1>
     <p className="text-gray-600">No user logged in</p>
    </div>
   </ApplePageWrapper>
  );
 }

 return (
  <ApplePageWrapper>
   <div className="max-w-2xl mx-auto">
    <h1 className="font-bold text-2xl font-bold text-blue-600 mb-6">
     📧 Email Verification Status
    </h1>
    
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="bg-gray-50 p-4 rounded">
       <h3 className="font-semibold mb-2">Firebase Auth</h3>
       <div className="space-y-1 text-sm">
        <div><strong>Email:</strong> {user.email}</div>
        <div><strong>Email Verified:</strong> {user.emailVerified ? '✅ Yes' : '❌ No'}</div>
        <div><strong>Display Name:</strong> {user.displayName || 'Not set'}</div>
        <div><strong>UID:</strong> {user.uid}</div>
       </div>
      </div>

      <div className="bg-gray-50 p-4 rounded">
       <h3 className="font-semibold mb-2">User Data</h3>
       <div className="space-y-1 text-sm">
        <div><strong>Name:</strong> {user.name || 'Not set'}</div>
        <div><strong>User Type:</strong> {user.userType || 'Not set'}</div>
        <div><strong>Is Verified:</strong> {user.isVerified ? '✅ Yes' : '❌ No'}</div>
        <div><strong>Email Verification Sent:</strong> {user.emailVerificationSent ? '✅ Yes' : '❌ No'}</div>
       </div>
      </div>
     </div>
    </div>

    <div className="bg-white rounded-lg border border-gray-200 p-6">
     <h3 className="font-semibold mb-4">Actions</h3>
     <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
      <button
       onClick={() => window.location.href = '/verify-email'}
       className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
       Go to Email Verification
      </button>
      <button
       onClick={() => window.location.href = '/onboarding'}
       className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
       Go to Onboarding
      </button>
     </div>
    </div>

    <div className="mt-6 bg-gray-50 rounded-lg border border-gray-200 p-6">
     <h3 className="font-semibold mb-4">Raw User Object</h3>
     <pre className="text-xs overflow-auto max-h-96 bg-white p-4 rounded border">
      {JSON.stringify(user, null, 2)}
     </pre>
    </div>
   </div>
  </ApplePageWrapper>
 );
};

export default EmailVerificationCheck;
