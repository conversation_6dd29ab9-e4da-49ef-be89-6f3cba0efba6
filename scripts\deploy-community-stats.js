#!/usr/bin/env node

/**
 * Deploy Community Service with Stats Endpoint
 * Adds missing /api/stats endpoint to Community Service
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 Deploying Community Service with Stats Endpoint...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

// Check if we're in the right directory
const projectRoot = path.resolve(__dirname, '..');
if (!fs.existsSync(path.join(projectRoot, 'package.json'))) {
  logError('Script must be run from the project root directory');
  process.exit(1);
}

// Check if render CLI is installed
try {
  execSync('render --version', { stdio: 'ignore' });
} catch (error) {
  logError('Render CLI not found. Please install it first:');
  log('npm install -g @render/cli', 'yellow');
  process.exit(1);
}

// Check if user is logged in to Render
try {
  execSync('render whoami', { stdio: 'ignore' });
} catch (error) {
  logError('Not logged in to Render. Please login first:');
  log('render login', 'yellow');
  process.exit(1);
}

async function deployCommunityService() {
  try {
    logStep('1', 'Deploying Community Service with Stats Endpoint...');
    
    // Change to service directory
    const serviceDir = path.join(projectRoot, 'services/community-service');
    if (!fs.existsSync(serviceDir)) {
      throw new Error(`Service directory not found: ${serviceDir}`);
    }
    
    process.chdir(serviceDir);
    
    // Deploy to Render
    logInfo(`Deploying Community Service from ${serviceDir}`);
    execSync('render deploy', { 
      stdio: 'inherit',
      env: { ...process.env, FORCE_COLOR: '1' }
    });
    
    logSuccess('Community Service deployed successfully');
    return true;
    
  } catch (error) {
    logError(`Failed to deploy Community Service: ${error.message}`);
    return false;
  }
}

async function main() {
  log('🚀 VWork Community Service Stats Deployment', 'bright');
  log('This script will deploy the Community Service with the new /api/stats endpoint', 'blue');
  
  // Deploy Community Service
  const communityServiceSuccess = await deployCommunityService();
  
  if (!communityServiceSuccess) {
    logError('Community Service deployment failed.');
    process.exit(1);
  }
  
  log('\n🎉 Community Service Stats Deployment Complete!', 'bright');
  log('\nChanges deployed:', 'cyan');
  log('• Added /api/stats endpoint for community statistics', 'green');
  log('• Added /api/stats/user/:userId endpoint for user statistics', 'green');
  log('• Updated API documentation to include stats endpoints', 'green');
  
  log('\n📋 New Endpoints Available:', 'cyan');
  log('• GET /api/v1/community/stats - Community overview statistics', 'yellow');
  log('• GET /api/v1/community/stats/user/:userId - User-specific statistics', 'yellow');
  
  log('\n📊 Statistics Include:', 'cyan');
  log('• Total posts, comments, users, and likes', 'yellow');
  log('• Engagement metrics (avg upvotes, views, comments)', 'yellow');
  log('• Recent activity from last 7 days', 'yellow');
  log('• Top categories and top users', 'yellow');
  log('• User-specific stats and recent activity', 'yellow');
  
  log('\n📋 Next steps:', 'cyan');
  log('1. Wait 2-3 minutes for service to fully restart', 'yellow');
  log('2. Test the community page at https://nerafus.com/#/community', 'yellow');
  log('3. Check that stats are loading correctly', 'yellow');
  
  log('\n🔍 To monitor deployment status:', 'cyan');
  log('render ps', 'yellow');
  
  log('\n📊 To view logs:', 'cyan');
  log('render logs nerafus-community-service', 'yellow');
  
  log('\n🧪 To test the new endpoint:', 'cyan');
  log('curl https://nerafus-community-service.onrender.com/api/stats', 'yellow');
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:');
  logError(`Promise: ${promise}`);
  logError(`Reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError('Uncaught Exception:');
  logError(error.message);
  logError(error.stack);
  process.exit(1);
});

// Run the deployment
main().catch(error => {
  logError('Deployment failed:');
  logError(error.message);
  process.exit(1);
}); 