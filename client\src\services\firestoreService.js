import {
 collection,
 doc,
 getDocs,
 getDoc,
 addDoc,
 updateDoc,
 deleteDoc,
 query,
 where,
 orderBy,
 limit,
 startAfter,
 arrayUnion,
 arrayRemove,
 increment,
 serverTimestamp,
 onSnapshot,
} from 'firebase/firestore';
import { db } from '../config/firebase';

// Helper function to handle Firestore errors
const handleFirestoreError = (error, operation) => {
 // console.(`❌ Firestore ${operation} error:`, error);

 // Handle network blocking errors
 if (error.message?.includes('ERR_BLOCKED_BY_CLIENT') || 
   error.message?.includes('net::ERR_BLOCKED_BY_CLIENT')) {
  throw new Error('Kết nối Firestore bị chặn. Vui lòng tắt ad blocker hoặc kiểm tra cấu hình mạng.');
 }

 // Handle network errors
 if (error.code === 'network-request-failed' || 
   error.message?.includes('network') ||
   error.message?.includes('fetch')) {
  throw new Error('Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.');
 }

 if (error.code === 'failed-precondition') {
  throw new Error(
   'Firestore database not initialized. Please follow setup instructions.'
  );
 }

 if (error.code === 'permission-denied') {
  throw new Error(
   'Permission denied. Please check Firestore security rules.'
  );
 }

 throw error;
};

// ===== USER SERVICES =====
export const userService = {
 // Get user by ID
 async getUser(userId) {
  try {
   const userDoc = await getDoc(doc(db, 'users', userId));
   if (userDoc.exists()) {
    return { id: userDoc.id, ...userDoc.data() };
   }
   return null;
  } catch (error) {
   handleFirestoreError(error, 'get user');
  }
 },

 // Update user profile
 async updateUser(userId, userData) {
  try {
   const userRef = doc(db, 'users', userId);
   await updateDoc(userRef, {
    ...userData,
    updatedAt: serverTimestamp(),
   });
   return { success: true };
  } catch (error) {
   handleFirestoreError(error, 'update user');
  }
 },

 // Get guild members (freelancers)
 async getGuildMembers(filters = {}) {
  try {
   const q = query(
    collection(db, 'users'),
    where('userType', '==', 'freelancer'),
    where('isActive', '==', true),
    orderBy('stats.averageRating', 'desc'),
    limit(filters.limit || 20)
   );

   const snapshot = await getDocs(q);
   return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
   handleFirestoreError(error, 'get guild members');
  }
 },

 // Search freelancers
 async searchFreelancers(searchTerm, filters = {}) {
  try {
   // Firestore doesn't support full-text search natively
   // This is a basic implementation - for production, use Algolia or similar
   let q = query(
    collection(db, 'users'),
    where('userType', '==', 'freelancer'),
    where('isActive', '==', true)
   );

   if (filters.category) {
    q = query(q, where('guildSpecialization', '==', filters.category));
   }

   const snapshot = await getDocs(q);
   const users = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

   // Client-side filtering for search term
   if (searchTerm) {
    return users.filter(
     user =>
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.profile?.bio
       ?.toLowerCase()
       .includes(searchTerm.toLowerCase()) ||
      user.guildSpecialization
       ?.toLowerCase()
       .includes(searchTerm.toLowerCase())
    );
   }

   return users;
  } catch (error) {
   handleFirestoreError(error, 'search freelancers');
  }
 },
};

// ===== PROJECT SERVICES =====
export const projectService = {
 // Get all projects
 async getProjects(filters = {}) {
  try {
   const q = query(
    collection(db, 'projects'),
    where('status', '==', 'open'),
    orderBy('createdAt', 'desc'),
    limit(filters.limit || 20)
   );

   const snapshot = await getDocs(q);
   return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
   handleFirestoreError(error, 'get projects');
  }
 },

 // Get project by ID
 async getProject(projectId) {
  try {
   const projectDoc = await getDoc(doc(db, 'projects', projectId));
   if (projectDoc.exists()) {
    return { id: projectDoc.id, ...projectDoc.data() };
   }
   return null;
  } catch (error) {
   handleFirestoreError(error, 'get project');
  }
 },

 // Create new project
 async createProject(projectData) {
  try {
   const docRef = await addDoc(collection(db, 'projects'), {
    ...projectData,
    status: 'open',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
    stats: {
     views: 0,
     totalBids: 0,
     averageBid: 0,
    },
   });
   return { id: docRef.id, success: true };
  } catch (error) {
   handleFirestoreError(error, 'create project');
  }
 },

 // Update project
 async updateProject(projectId, projectData) {
  try {
   const projectRef = doc(db, 'projects', projectId);
   await updateDoc(projectRef, {
    ...projectData,
    updatedAt: serverTimestamp(),
   });
   return { success: true };
  } catch (error) {
   handleFirestoreError(error, 'update project');
  }
 },

 // Submit bid
 async submitBid(projectId, bidData) {
  try {
   // Add bid to project's bids subcollection
   const bidRef = await addDoc(
    collection(db, 'projects', projectId, 'bids'),
    {
     ...bidData,
     status: 'pending',
     createdAt: serverTimestamp(),
    }
   );

   // Update project stats
   const projectRef = doc(db, 'projects', projectId);
   await updateDoc(projectRef, {
    'stats.totalBids': increment(1),
    updatedAt: serverTimestamp(),
   });

   return { id: bidRef.id, success: true };
  } catch (error) {
   handleFirestoreError(error, 'submit bid');
  }
 },

 // Get project bids
 async getProjectBids(projectId) {
  try {
   const q = query(
    collection(db, 'projects', projectId, 'bids'),
    orderBy('createdAt', 'desc')
   );
   const snapshot = await getDocs(q);
   return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
   handleFirestoreError(error, 'get project bids');
  }
 },

 // Accept bid
 async acceptBid(projectId, bidId) {
  try {
   // Update bid status
   const bidRef = doc(db, 'projects', projectId, 'bids', bidId);
   await updateDoc(bidRef, {
    status: 'accepted',
    acceptedAt: serverTimestamp(),
   });

   // Update project status
   const projectRef = doc(db, 'projects', projectId);
   await updateDoc(projectRef, {
    status: 'in_progress',
    selectedBidId: bidId,
    updatedAt: serverTimestamp(),
   });

   return { success: true };
  } catch (error) {
   handleFirestoreError(error, 'accept bid');
  }
 },

 // Search projects
 async searchProjects(searchTerm, filters = {}) {
  try {
   let q = query(collection(db, 'projects'), where('status', '==', 'open'));

   if (filters.category) {
    q = query(q, where('category', '==', filters.category));
   }

   if (filters.budgetMin) {
    q = query(q, where('budget.amount', '>=', filters.budgetMin));
   }

   q = query(q, orderBy('createdAt', 'desc'));

   const snapshot = await getDocs(q);
   const projects = snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
   }));

   // Client-side filtering for search term
   if (searchTerm) {
    return projects.filter(
     project =>
      project.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description
       ?.toLowerCase()
       .includes(searchTerm.toLowerCase()) ||
      project.skills?.some(skill =>
       skill.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
   }

   return projects;
  } catch (error) {
   handleFirestoreError(error, 'search projects');
  }
 },
};

// ===== MESSAGE SERVICES =====
export const messageService = {
 // Get conversations for user
 async getConversations(userId) {
  try {
   const q = query(
    collection(db, 'conversations'),
    where('participants', 'array-contains', userId),
    orderBy('lastMessageAt', 'desc')
   );
   const snapshot = await getDocs(q);
   return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
   handleFirestoreError(error, 'get conversations');
  }
 },

 // Create conversation
 async createConversation(participants) {
  try {
   const docRef = await addDoc(collection(db, 'conversations'), {
    participants,
    createdAt: serverTimestamp(),
    lastMessageAt: serverTimestamp(),
    lastMessage: '',
    unreadCount: {},
   });
   return { id: docRef.id, success: true };
  } catch (error) {
   handleFirestoreError(error, 'create conversation');
  }
 },

 // Send message
 async sendMessage(conversationId, messageData) {
  try {
   await addDoc(
    collection(db, 'conversations', conversationId, 'messages'),
    {
     ...messageData,
     createdAt: serverTimestamp(),
    }
   );

   const conversationRef = doc(db, 'conversations', conversationId);
   await updateDoc(conversationRef, {
    lastMessage: messageData.content,
    lastMessageAt: serverTimestamp(),
   });

   return { success: true };
  } catch (error) {
   handleFirestoreError(error, 'send message');
  }
 },

 // Get messages for conversation
 async getMessages(conversationId, limitCount = 50) {
  try {
   const q = query(
    collection(db, 'conversations', conversationId, 'messages'),
    orderBy('createdAt', 'desc'),
    limit(limitCount)
   );
   const snapshot = await getDocs(q);
   return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
   handleFirestoreError(error, 'get messages');
  }
 },

 // Listen to messages in real-time
 listenToMessages(conversationId, callback) {
  try {
   const q = query(
    collection(db, 'conversations', conversationId, 'messages'),
    orderBy('createdAt', 'desc'),
    limit(50)
   );

   return onSnapshot(q, snapshot => {
    const messages = snapshot.docs.map(doc => ({
     id: doc.id,
     ...doc.data(),
    }));
    callback(messages);
   });
  } catch (error) {
   handleFirestoreError(error, 'listen to messages');
  }
 },
};

// ===== COMMUNITY SERVICES =====
export const communityService = {
 // Get community posts
 async getPosts(filters = {}) {
  try {
   let q = query(
    collection(db, 'community'),
    orderBy('createdAt', 'desc'),
    limit(filters.limit || 20)
   );

   if (filters.category) {
    q = query(
     collection(db, 'community'),
     where('category', '==', filters.category),
     orderBy('createdAt', 'desc'),
     limit(filters.limit || 20)
    );
   }

   const snapshot = await getDocs(q);
   return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
   handleFirestoreError(error, 'get community posts');
  }
 },

 // Create post
 async createPost(postData) {
  try {
   const docRef = await addDoc(collection(db, 'community'), {
    ...postData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
    likes: 0,
    comments: 0,
   });
   return { id: docRef.id, success: true };
  } catch (error) {
   handleFirestoreError(error, 'create community post');
  }
 },

 // Like post
 async likePost(postId, userId) {
  try {
   const postRef = doc(db, 'community', postId);
   await updateDoc(postRef, {
    likes: increment(1),
    likedBy: arrayUnion(userId),
   });
   return { success: true };
  } catch (error) {
   handleFirestoreError(error, 'like post');
  }
 },
};

// ===== NOTIFICATION SERVICES =====
export const notificationService = {
 // Get user notifications
 async getNotifications(userId) {
  try {
   const q = query(
    collection(db, 'notifications'),
    where('userId', '==', userId),
    orderBy('createdAt', 'desc'),
    limit(50)
   );
   const snapshot = await getDocs(q);
   return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  } catch (error) {
   handleFirestoreError(error, 'get notifications');
  }
 },

 // Create notification
 async createNotification(notificationData) {
  try {
   const docRef = await addDoc(collection(db, 'notifications'), {
    ...notificationData,
    read: false,
    createdAt: serverTimestamp(),
   });
   return { id: docRef.id, success: true };
  } catch (error) {
   handleFirestoreError(error, 'create notification');
  }
 },

 // Mark notification as read
 async markAsRead(notificationId) {
  try {
   const notificationRef = doc(db, 'notifications', notificationId);
   await updateDoc(notificationRef, {
    read: true,
    readAt: serverTimestamp(),
   });
   return { success: true };
  } catch (error) {
   handleFirestoreError(error, 'mark notification as read');
  }
 },
};

export default {
 userService,
 projectService,
 messageService,
 communityService,
 notificationService,
};
