/**
 * Client-side test setup
 * Configures testing environment for React components
 */

import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';
import { TextEncoder, TextDecoder } from 'util';

// Configure testing library
configure({
 testIdAttribute: 'data-testid',
});

// Mock global objects
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
 constructor() {}
 disconnect() {}
 observe() {}
 unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
 constructor() {}
 disconnect() {}
 observe() {}
 unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
 writable: true,
 value: jest.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(), // deprecated
  removeListener: jest.fn(), // deprecated
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
 })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
 writable: true,
 value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
 getItem: jest.fn(),
 setItem: jest.fn(),
 removeItem: jest.fn(),
 clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
 getItem: jest.fn(),
 setItem: jest.fn(),
 removeItem: jest.fn(),
 clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock Firebase
jest.mock('firebase/app', () => ({
 initializeApp: jest.fn(),
 getApps: jest.fn(() => []),
 getApp: jest.fn(),
}));

jest.mock('firebase/auth', () => ({
 getAuth: jest.fn(() => ({
  currentUser: null,
  onAuthStateChanged: jest.fn(),
  signInWithEmailAndPassword: jest.fn(),
  signOut: jest.fn(),
 })),
 signInWithEmailAndPassword: jest.fn(),
 signOut: jest.fn(),
 onAuthStateChanged: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
 getFirestore: jest.fn(),
 collection: jest.fn(),
 doc: jest.fn(),
 getDocs: jest.fn(),
 getDoc: jest.fn(),
 addDoc: jest.fn(),
 updateDoc: jest.fn(),
 deleteDoc: jest.fn(),
 query: jest.fn(),
 where: jest.fn(),
 orderBy: jest.fn(),
 limit: jest.fn(),
}));

// Mock GSAP
jest.mock('gsap', () => ({
 to: jest.fn(),
 from: jest.fn(),
 fromTo: jest.fn(),
 set: jest.fn(),
 timeline: jest.fn(() => ({
  to: jest.fn(),
  from: jest.fn(),
  fromTo: jest.fn(),
  set: jest.fn(),
  play: jest.fn(),
  pause: jest.fn(),
  reverse: jest.fn(),
  restart: jest.fn(),
 })),
 registerPlugin: jest.fn(),
}));

// Mock Framer Motion
jest.mock('framer-motion', () => ({
 motion: {
  div: 'div',
  span: 'span',
  button: 'button',
  img: 'img',
  section: 'section',
  article: 'article',
  header: 'header',
  footer: 'footer',
  nav: 'nav',
  aside: 'aside',
  main: 'main',
 },
 AnimatePresence: ({ children }) => children,
 useAnimation: () => ({
  start: jest.fn(),
  stop: jest.fn(),
  set: jest.fn(),
 }),
 useMotionValue: () => ({
  get: jest.fn(),
  set: jest.fn(),
 }),
}));

// Mock React Router
jest.mock('react-router-dom', () => ({
 ...jest.requireActual('react-router-dom'),
 useNavigate: () => jest.fn(),
 useLocation: () => ({
  pathname: '/',
  search: '',
  hash: '',
  state: null,
 }),
 useParams: () => ({}),
}));

// Mock performance API
global.performance = {
 ...global.performance,
 mark: jest.fn(),
 measure: jest.fn(),
 getEntriesByName: jest.fn(() => []),
 getEntriesByType: jest.fn(() => []),
 clearMarks: jest.fn(),
 clearMeasures: jest.fn(),
 now: jest.fn(() => Date.now()),
 memory: {
  usedJSHeapSize: 1000000,
  totalJSHeapSize: 2000000,
  jsHeapSizeLimit: 4000000,
 },
};

// Mock console methods for cleaner test output
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
 console.error = (...args) => {
  if (
   typeof args[0] === 'string' &&
   args[0].includes('Warning: ReactDOM.render is no longer supported')
  ) {
   return;
  }
  originalError.call(console, ...args);
 };

 console.warn = (...args) => {
  if (
   typeof args[0] === 'string' &&
   (args[0].includes('componentWillReceiveProps') ||
    args[0].includes('componentWillUpdate'))
  ) {
   return;
  }
  originalWarn.call(console, ...args);
 };
});

afterAll(() => {
 console.error = originalError;
 console.warn = originalWarn;
});

// Clean up after each test
afterEach(() => {
 jest.clearAllMocks();
 localStorageMock.clear();
 sessionStorageMock.clear();
});

// Global test utilities
global.testUtils = {
 // Wait for async operations
 waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),
 
 // Create mock user
 createMockUser: (overrides = {}) => ({
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User',
  photoURL: null,
  emailVerified: true,
  ...overrides,
 }),
 
 // Create mock API response
 createMockApiResponse: (data, success = true) => ({
  success,
  data,
  message: success ? 'Success' : 'Error',
  timestamp: new Date().toISOString(),
 }),
};
