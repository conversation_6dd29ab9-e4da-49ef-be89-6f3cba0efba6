/**
 * Optimized state management system
 * Lightweight alternative to Redux with performance optimizations
 */

import { createContext, useContext, useReducer, useMemo, useCallback, useRef, useEffect, useState } from 'react';

/**
 * Create optimized store with selectors and middleware
 */
export const createStore = (initialState, reducer, middleware = []) => {
 const StoreContext = createContext();
 
 const StoreProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const listenersRef = useRef(new Set());
  const selectorCacheRef = useRef(new Map());
  
  // Enhanced dispatch with middleware support
  const enhancedDispatch = useCallback((action) => {
   // Apply middleware
   let finalAction = action;
   for (const middlewareFn of middleware) {
    finalAction = middlewareFn(state, finalAction, dispatch);
   }
   
   // Clear selector cache on state change
   selectorCacheRef.current.clear();
   
   // Notify listeners
   listenersRef.current.forEach(listener => listener(state, finalAction));
   
   return dispatch(finalAction);
  }, [state]);
  
  // Subscribe to state changes
  const subscribe = useCallback((listener) => {
   listenersRef.current.add(listener);
   return () => listenersRef.current.delete(listener);
  }, []);
  
  // Memoized selector with caching
  const select = useCallback((selector, deps = []) => {
   const cacheKey = selector.toString() + JSON.stringify(deps);
   
   if (selectorCacheRef.current.has(cacheKey)) {
    return selectorCacheRef.current.get(cacheKey);
   }
   
   const result = selector(state);
   selectorCacheRef.current.set(cacheKey, result);
   return result;
  }, [state]);
  
  const value = useMemo(() => ({
   state,
   dispatch: enhancedDispatch,
   subscribe,
   select
  }), [state, enhancedDispatch, subscribe, select]);
  
  return (
   <StoreContext.Provider value={value}>
    {children}
   </StoreContext.Provider>
  );
 };
 
 const useStore = () => {
  const context = useContext(StoreContext);
  if (!context) {
   throw new Error('useStore must be used within a StoreProvider');
  }
  return context;
 };
 
 // Optimized selector hook
 const useSelector = (selector, deps = []) => {
  const { state, subscribe } = useStore();
  const [selectedState, setSelectedState] = useState(() => selector(state));
  const selectorRef = useRef(selector);
  const depsRef = useRef(deps);
  
  // Update refs
  selectorRef.current = selector;
  depsRef.current = deps;
  
  useEffect(() => {
   const unsubscribe = subscribe((newState) => {
    const newSelectedState = selectorRef.current(newState);
    setSelectedState(prevState => {
     // Shallow comparison to prevent unnecessary re-renders
     if (JSON.stringify(prevState) !== JSON.stringify(newSelectedState)) {
      return newSelectedState;
     }
     return prevState;
    });
   });
   
   return unsubscribe;
  }, [subscribe, ...depsRef.current]);
  
  return selectedState;
 };
 
 // Action creator helper
 const createActions = (actionCreators) => {
  const { dispatch } = useStore();
  
  return useMemo(() => {
   const boundActions = {};
   Object.keys(actionCreators).forEach(key => {
    boundActions[key] = (...args) => dispatch(actionCreators[key](...args));
   });
   return boundActions;
  }, [dispatch]);
 };
 
 return {
  StoreProvider,
  useStore,
  useSelector,
  createActions
 };
};

/**
 * Middleware for logging actions (development only)
 */
export const loggerMiddleware = (state, action, dispatch) => {
 if (process.env.NODE_ENV === 'development') {
  console.group(`Action: ${action.type}`);
  console.log('Previous State:', state);
  console.log('Action:', action);
  console.log('Next State:', state); // This would be the next state after reducer
  console.groupEnd();
 }
 return action;
};

/**
 * Middleware for async actions
 */
export const thunkMiddleware = (state, action, dispatch) => {
 if (typeof action === 'function') {
  return action(dispatch, () => state);
 }
 return action;
};

/**
 * Middleware for action validation
 */
export const validationMiddleware = (state, action, dispatch) => {
 if (!action || typeof action.type !== 'string') {
  console.error('Invalid action:', action);
  return { type: 'INVALID_ACTION', payload: action };
 }
 return action;
};

/**
 * Performance monitoring middleware
 */
export const performanceMiddleware = (state, action, dispatch) => {
 const start = performance.now();
 
 // Execute action
 const result = action;
 
 const end = performance.now();
 const duration = end - start;
 
 if (duration > 16) { // Longer than one frame (60fps)
  console.warn(`Slow action detected: ${action.type} took ${duration.toFixed(2)}ms`);
 }
 
 return result;
};

/**
 * Local storage persistence middleware
 */
export const persistenceMiddleware = (key) => (state, action, dispatch) => {
 // Save state to localStorage after each action
 try {
  localStorage.setItem(key, JSON.stringify(state));
 } catch (error) {
  console.warn('Failed to persist state:', error);
 }
 
 return action;
};

/**
 * Load persisted state from localStorage
 */
export const loadPersistedState = (key, defaultState) => {
 try {
  const persistedState = localStorage.getItem(key);
  if (persistedState) {
   return { ...defaultState, ...JSON.parse(persistedState) };
  }
 } catch (error) {
  console.warn('Failed to load persisted state:', error);
 }
 return defaultState;
};

/**
 * Optimized context for frequently changing data
 */
export const createOptimizedContext = (initialValue) => {
 const Context = createContext();
 const UpdateContext = createContext();
 
 const Provider = ({ children }) => {
  const [value, setValue] = useState(initialValue);
  
  return (
   <Context.Provider value={value}>
    <UpdateContext.Provider value={setValue}>
     {children}
    </UpdateContext.Provider>
   </Context.Provider>
  );
 };
 
 const useValue = () => {
  const context = useContext(Context);
  if (context === undefined) {
   throw new Error('useValue must be used within Provider');
  }
  return context;
 };
 
 const useUpdate = () => {
  const context = useContext(UpdateContext);
  if (context === undefined) {
   throw new Error('useUpdate must be used within Provider');
  }
  return context;
 };
 
 return { Provider, useValue, useUpdate };
};

/**
 * Batch updates to prevent excessive re-renders
 */
export const useBatchedUpdates = () => {
 const batchRef = useRef([]);
 const timeoutRef = useRef(null);
 
 const batchUpdate = useCallback((updateFn) => {
  batchRef.current.push(updateFn);
  
  if (timeoutRef.current) {
   clearTimeout(timeoutRef.current);
  }
  
  timeoutRef.current = setTimeout(() => {
   const updates = batchRef.current;
   batchRef.current = [];
   
   // Apply all updates in a single batch
   updates.forEach(update => update());
  }, 0);
 }, []);
 
 return batchUpdate;
};

/**
 * Optimized list state management
 */
export const useOptimizedList = (initialItems = []) => {
 const [items, setItems] = useState(initialItems);
 const itemsRef = useRef(items);
 
 // Keep ref in sync
 useEffect(() => {
  itemsRef.current = items;
 }, [items]);
 
 const addItem = useCallback((item) => {
  setItems(prev => [...prev, item]);
 }, []);
 
 const removeItem = useCallback((id) => {
  setItems(prev => prev.filter(item => item.id !== id));
 }, []);
 
 const updateItem = useCallback((id, updates) => {
  setItems(prev => prev.map(item => 
   item.id === id ? { ...item, ...updates } : item
  ));
 }, []);
 
 const moveItem = useCallback((fromIndex, toIndex) => {
  setItems(prev => {
   const newItems = [...prev];
   const [movedItem] = newItems.splice(fromIndex, 1);
   newItems.splice(toIndex, 0, movedItem);
   return newItems;
  });
 }, []);
 
 const clearItems = useCallback(() => {
  setItems([]);
 }, []);
 
 return {
  items,
  addItem,
  removeItem,
  updateItem,
  moveItem,
  clearItems,
  itemsRef
 };
};
