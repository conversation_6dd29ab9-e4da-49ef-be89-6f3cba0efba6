/**
 * Configuration for User Service
 * Centralized configuration management with environment variable support
 */

require('dotenv').config();

class Config {
  constructor() {
    this.validateRequiredEnvVars();
  }

  // Environment
  get NODE_ENV() {
    return process.env.NODE_ENV || 'development';
  }

  get isDevelopment() {
    return this.NODE_ENV === 'development';
  }

  get isProduction() {
    return this.NODE_ENV === 'production';
  }

  get isTest() {
    return this.NODE_ENV === 'test';
  }

  // Server Configuration
  get PORT() {
    return parseInt(process.env.PORT) || 3001;
  }

  get SERVICE_NAME() {
    return 'user-service';
  }

  get SERVICE_VERSION() {
    return process.env.npm_package_version || '1.0.0';
  }

  // Database Configuration
  get DATABASE_URL() {
    return process.env.DATABASE_URL;
  }

  get DB_HOST() {
    return process.env.DB_HOST || 'localhost';
  }

  get DB_PORT() {
    return parseInt(process.env.DB_PORT) || 5432;
  }

  get DB_NAME() {
    return process.env.DB_NAME || 'vwork_user_service';
  }

  get DB_USER() {
    return process.env.DB_USER || 'postgres';
  }

  get DB_PASSWORD() {
    return process.env.DB_PASSWORD || undefined;
  }

  get DB_POOL_MAX() {
    return parseInt(process.env.DB_POOL_MAX) || 20;
  }

  get DB_POOL_MIN() {
    return parseInt(process.env.DB_POOL_MIN) || 2;
  }

  get DB_CONNECTION_TIMEOUT() {
    return parseInt(process.env.DB_CONNECTION_TIMEOUT) || 60000;
  }

  get DB_IDLE_TIMEOUT() {
    return parseInt(process.env.DB_IDLE_TIMEOUT) || 30000;
  }

  // Firebase Configuration
  get FIREBASE_PROJECT_ID() {
    return process.env.FIREBASE_PROJECT_ID;
  }

  get FIREBASE_CLIENT_EMAIL() {
    return process.env.FIREBASE_CLIENT_EMAIL;
  }

  get FIREBASE_PRIVATE_KEY() {
    return process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n');
  }

  // CORS Configuration
  get ALLOWED_ORIGINS() {
    const defaultOrigins = [
      'http://localhost:3000',
      'http://localhost:3006',
      'http://localhost:8080',
      'https://vwork-platform.netlify.app',
      'https://vwork.com'
    ];

    if (process.env.ALLOWED_ORIGINS) {
      return process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
    }

    return defaultOrigins;
  }

  get CORS_CREDENTIALS() {
    return process.env.CORS_CREDENTIALS === 'true';
  }

  // Service URLs
  get API_GATEWAY_URL() {
    return process.env.API_GATEWAY_URL || 'http://localhost:8080';
  }

  get AUTH_SERVICE_URL() {
    return process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
  }

  get COMMUNITY_SERVICE_URL() {
    return process.env.COMMUNITY_SERVICE_URL || 'http://localhost:3006';
  }

  get TEAM_SERVICE_URL() {
    return process.env.TEAM_SERVICE_URL || 'http://localhost:3007';
  }

  get PROJECT_SERVICE_URL() {
    return process.env.PROJECT_SERVICE_URL || 'http://localhost:3003';
  }

  get JOB_SERVICE_URL() {
    return process.env.JOB_SERVICE_URL || 'http://localhost:3004';
  }

  // Security Configuration
  get JWT_SECRET() {
    return process.env.JWT_SECRET || 'your-secret-key-change-in-production';
  }

  get JWT_EXPIRES_IN() {
    return process.env.JWT_EXPIRES_IN || '7d';
  }

  get SESSION_SECRET() {
    return process.env.SESSION_SECRET || 'your-session-secret-change-in-production';
  }

  // Validation Limits
  get MAX_BIO_LENGTH() {
    return parseInt(process.env.MAX_BIO_LENGTH) || 1000;
  }

  get MAX_SKILLS_COUNT() {
    return parseInt(process.env.MAX_SKILLS_COUNT) || 50;
  }

  get MAX_WORK_HISTORY_COUNT() {
    return parseInt(process.env.MAX_WORK_HISTORY_COUNT) || 20;
  }

  // Logging Configuration
  get LOG_LEVEL() {
    return process.env.LOG_LEVEL || (this.isDevelopment ? 'debug' : 'info');
  }

  get LOG_FORMAT() {
    return process.env.LOG_FORMAT || (this.isDevelopment ? 'dev' : 'combined');
  }

  // Security Settings
  get ENABLE_HELMET() {
    return process.env.ENABLE_HELMET !== 'false';
  }

  get ENABLE_COMPRESSION() {
    return process.env.ENABLE_COMPRESSION !== 'false';
  }

  get TRUST_PROXY() {
    return process.env.TRUST_PROXY === 'true';
  }

  // Health Check
  get HEALTH_CHECK_INTERVAL() {
    return parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000;
  }

  get HEALTH_CHECK_TIMEOUT() {
    return parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000;
  }

  // File Upload
  get MAX_FILE_SIZE() {
    return parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024; // 10MB
  }

  get UPLOAD_PATH() {
    return process.env.UPLOAD_PATH || './uploads';
  }

  // Cache Configuration
  get CACHE_TTL() {
    return parseInt(process.env.CACHE_TTL) || 300; // 5 minutes
  }

  get ENABLE_CACHE() {
    return process.env.ENABLE_CACHE !== 'false';
  }

  // Rate Limiting
  get RATE_LIMIT_WINDOW() {
    return parseInt(process.env.RATE_LIMIT_WINDOW) || 900000; // 15 minutes
  }

  get RATE_LIMIT_MAX_REQUESTS() {
    return parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100;
  }

  // Helper method to get service URL
  getServiceUrl(serviceName, defaultPort, protocol = 'http') {
    const envKey = `${serviceName.toUpperCase()}_SERVICE_URL`;
    return process.env[envKey] || `${protocol}://localhost:${defaultPort}`;
  }

  // Get service URL for external calls
  get SERVICE_URL() {
    if (this.isProduction) {
      return process.env.SERVICE_URL || `https://vwork-user-service.onrender.com`;
    }
    return `http://localhost:${this.PORT}`;
  }

  get HEALTH_CHECK_URL() {
    return `${this.SERVICE_URL}/health`;
  }

  // Validate required environment variables
  validateRequiredEnvVars() {
    const required = [];

    if (this.isProduction) {
      required.push(
        'FIREBASE_PROJECT_ID',
        'FIREBASE_CLIENT_EMAIL', 
        'FIREBASE_PRIVATE_KEY'
      );

      if (!this.DATABASE_URL) {
        required.push('DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD');
      }
    }

    const missing = required.filter(envVar => !process.env[envVar]);

    if (missing.length > 0) {
      console.warn(`⚠️  Missing environment variables: ${missing.join(', ')}`);
      if (this.isProduction) {
        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
      }
    }
  }

  // Get all configuration for debugging
  getAll() {
    return {
      NODE_ENV: this.NODE_ENV,
      PORT: this.PORT,
      SERVICE_NAME: this.SERVICE_NAME,
      SERVICE_VERSION: this.SERVICE_VERSION,
      DB_HOST: this.DB_HOST,
      DB_PORT: this.DB_PORT,
      DB_NAME: this.DB_NAME,
      FIREBASE_PROJECT_ID: this.FIREBASE_PROJECT_ID,
      ALLOWED_ORIGINS: this.ALLOWED_ORIGINS,
      LOG_LEVEL: this.LOG_LEVEL
    };
  }
}

module.exports = new Config(); 