import { createContext, useContext, useEffect, useState, useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import locationService from '../services/locationService';

const LanguageContext = createContext();

export const useLanguage = () => {
 const context = useContext(LanguageContext);
 if (!context) {
  throw new Error('useLanguage must be used within a LanguageProvider');
 }
 return context;
};

// Translations
const translations = {
 en: {
  // Navigation
  home: 'Home',
  projects: 'Projects',
  freelancers: 'Find Freelancers',
  jobs: 'Jobs',
  contests: 'Contests',
  dashboard: 'Dashboard',
  messages: 'Messages',
  support: 'Support',
  community: 'Community',
  settings: 'Settings',
  signIn: 'Sign In',
  signUp: 'Sign Up',
  signOut: 'Sign Out',
  vwork: 'NERAFUS',
  vworkAssistant: 'NERAFUS Assistant',

  // Homepage
  heroTitle: 'Find the Perfect Freelancer for Your Project',
  heroSubtitle: '',
  searchPlaceholder: 'What service are you looking for?',
  getStarted: 'Get Started',
  learnMore: 'Learn More',
  add: 'Add',
  createProject: 'Create Project',
  postJob: 'Post Job',
  createContest: 'Create Contest',
  chatAI: 'Chat AI',

  // Categories
  webDevelopment: 'Web Development',
  mobileApps: 'Mobile Apps',
  design: 'Design & Creative',
  writing: 'Writing & Translation',
  marketing: 'Digital Marketing',
  business: 'Business & Consulting',

  // Features
  securePayments: 'Secure Payments',
  qualityWork: 'Quality Guaranteed',
  support24_7: '24/7 Support',
  easyToUse: 'Easy to Use',

  // Projects
  postProject: 'Post a Project',
  viewDetails: 'View Details',
  placeBid: 'Place Bid',
  budget: 'Budget',
  deadline: 'Deadline',
  proposals: 'Proposals',
  viewAllJobs: 'View All Jobs',

  // Common
  loading: 'Loading...',
  search: 'Search',
  filter: 'Filter',
  sort: 'Sort',
  save: 'Save',
  cancel: 'Cancel',
  delete: 'Delete',
  edit: 'Edit',
  submit: 'Submit',
  next: 'Next',
  previous: 'Previous',
  more: 'More',
  user: 'User',
  searchProjectsFreelancers: 'Search projects, freelancers...',

  // Auth
  welcomeBack: 'Welcome Back',
  createYourAccount: 'Create Your Account',
  signInToAccount: 'Sign in to your account',
  joinWorldsLargest: 'Join NERAFUS - New Era For Us freelance marketplace',
  iWantTo: 'I want to...',
  findWork: 'Find Work',
  findTalent: 'Find Talent',
  hireTalent: 'Hire Talent',
  firstName: 'First Name',
  lastName: 'Last Name',
  emailAddress: 'Email Address',
  password: 'Password',
  confirmPassword: 'Confirm Password',
  createAccount: 'Create Account',
  dontHaveAccount: "Don't have an account?",
  alreadyHaveAccount: 'Already have an account?',
  forgotPassword: 'Forgot Password?',
  emailRequired: 'Email is required',
  emailInvalid: 'Please enter a valid email',
  passwordRequired: 'Password is required',
  passwordMinLength: 'Password must be at least 6 characters',
  firstNameRequired: 'First name is required',
  lastNameRequired: 'Last name is required',
  passwordsNotMatch: 'Passwords do not match',
  
  // Additional missing keys
  watchDemo: 'Watch Demo',
  showPassword: 'Show Password',
  hidePassword: 'Hide Password',
  loadingDashboard: 'Loading Dashboard...',
  gatheringData: 'Gathering Data...',
  webDevelopmentCategory: 'Web Development',
  mobileDevelopmentCategory: 'Mobile Development',
  designCategory: 'Design',
  writingCategory: 'Writing',
  marketingCategory: 'Marketing',
  dataScience: 'Data Science',
  budgetRange: 'Budget Range',
  under500: 'Under $500',
  budget500to1000: '$500 - $1,000',
  budget1000to5000: '$1,000 - $5,000',
  over5000: 'Over $5,000',
  projectDuration: 'Project Duration',
  lessThanWeek: 'Less than a week',
  oneToFourWeeks: '1-4 weeks',
  oneToThreeMonths: '1-3 months',
  moreThanThreeMonths: 'More than 3 months',
  category: 'Category',
  allContests: 'All Contests',
  filterContests: 'Filter Contests',
  readyToShowcaseTalent: 'Ready to showcase your talent?',
  joinThousandsDesigners: 'Join thousands of designers and developers',
  browseActiveContests: 'Browse Active Contests',
  startYourOwnContest: 'Start Your Own Contest',
  
  // Additional missing keys for pages
  findTalentedFreelancers: 'Find Talented Freelancers',
  connectWithProfessionals: 'Connect with Professionals',
  searchFreelancersPlaceholder: 'Search for freelancers...',
  filters: 'Filters',
  freelancersFound: 'freelancers found',
  highestRated: 'Highest Rated',
  projectsCount: 'Projects',
  loadMoreFreelancers: 'Load More Freelancers',
  allCategories: 'All Categories',
  enterLocation: 'Enter location',
  allLevels: 'All Levels',
  clearFilters: 'Clear Filters',
  designContests: 'Design Contests',
  competeWinPrizes: 'Compete & Win Prizes',
  activeContests: 'Active Contests',
  totalPrizes: 'Total Prizes',
  participants: 'Participants',
  winners: 'Winners',
  communityHubTitle: 'Team Hub',
  connectLearnGrow: 'Connect, Learn & Grow',
  membersCount: 'Members',
  postsCount: 'Posts',
  commentsCount: 'Comments',
  onlineNow: 'Online Now',
  newPost: 'New Post',
  sortBy: 'Sort by',
  mostRecent: 'Most Recent',
  mostDiscussed: 'Most Discussed',
  allPosts: 'All Posts',
  trending: 'Trending',
  timeAgo: 'time ago',
  hoursAgo: 'hours ago',
  daysAgo: 'days ago',
  loadMorePosts: 'Load More Posts',


  // Homepage sections
  worldsWork: 'NERAFUS',
  marketplace: 'New Era For Us',
  findYourDreamJob: 'Find Your Dream Job',
  hireTopTalent: 'Hire Top Talent', 
  buildYourCareer: 'Build Your Career',
  growYourBusiness: 'Grow Your Business',
  perfectMatches: 'Perfect Matches',
  creativeSolutions: 'Creative Solutions',
  cursor: '|',
  connectSubtitle: 'From ideas to reality with NERAFUS',
  findJobs: 'Find Jobs',
  jobsDescription: 'Discover thousands of job opportunities from top companies',
  
  // Search section
  findPerfectMatch: 'Find Perfect Match',
  searchThousandsOfTalented: 'Search thousands of talented professionals',
  searchSkillsOrJobs: 'Search skills or jobs',
  location: 'Location',
  hourlyRate: 'Hourly Rate',
  experienceLevel: 'Experience Level',
  
  // Location dropdown translations
  selectCountry: 'Select Country',
  selectCity: 'Select City',
  searchCountries: 'Search countries...',
  searchCities: 'Search cities...',
  loadingCountries: 'Loading countries...',
  loadingCities: 'Loading cities...',
  noCountriesFound: 'No countries found',
  noCitiesFound: 'No cities available',
  availability: 'Availability',
  moreFilters: 'More Filters',
  searchNow: 'Search Now',
  
  // Popular searches
  popularSearches: 'Popular Searches',
  reactDeveloper: 'React Developer',
  uiUxDesigner: 'UI/UX Designer',
  contentWriter: 'Content Writer',
  digitalMarketing: 'Digital Marketing',
  mobileAppDeveloper: 'Mobile App Developer',
  dataScientist: 'Data Scientist',
  
  // Popular locations
  popularLocations: 'Popular Locations',

  

  
  // Browse categories
  browseByCategory: 'Browse by Category',
  findPerfectFreelancer: 'Find the perfect freelancer',
  developmentIT: 'Development & IT',
  developmentITDesc: 'Software development and IT services',
  designCreative: 'Design & Creative',
  designCreativeDesc: 'Graphic design and creative services',
  salesMarketing: 'Sales & Marketing',
  salesMarketingDesc: 'Marketing and sales expertise',
  writingTranslation: 'Writing & Translation',
  writingTranslationDesc: 'Content writing and translation',
  videoPhotography: 'Video & Photography',
  videoPhotographyDesc: 'Video production and photography',
  dataAnalytics: 'Data & Analytics',
  dataAnalyticsDesc: 'Data analysis and business intelligence',
  musicAudio: 'Music & Audio',
  musicAudioDesc: 'Audio production and music services',
  customerService: 'Customer Service',
  customerServiceDesc: 'Customer support and service',
  viewAllCategories: 'View All Categories',
  
  // Featured jobs
  featuredJobs: 'Featured Jobs',
  discoverTopOpportunities: 'Discover top opportunities',
  urgent: 'Urgent',
  applyNow: 'Apply Now',
  
  // Top freelancers
  topFreelancers: 'Top Freelancers',
  workWithBestTalent: 'Work with the best talent',
  topRated: 'Top Rated',
  available: 'Available',
  response: 'Response',
  hire: 'Hire',
  message: 'Message',
  viewAllFreelancers: 'View All Freelancers',
  
  // Featured projects
  featuredProjects: 'Featured Projects',
  projectsText: 'Projects',
  exploreOutstandingWork: 'Explore outstanding work',
  allProjects: 'All Projects',
  webDesign: 'Web Design',
  branding: 'Branding',
  illustration: 'Illustration',
  byFreelancer: 'by freelancer',
  viewAllProjects: 'View All Projects',
  
  // How it works
  howItWorks: 'How It Works',
  simpleStepsToSuccess: 'Simple steps to success',
  postYourProject: 'Post Your Project',
  postProjectDesc: 'Describe your project requirements',
  describeProjectReq: 'Describe project requirements',
  setBudgetTimeline: 'Set budget and timeline',
  choosePreferredSkills: 'Choose preferred skills',
  reviewProposals: 'Review Proposals',
  reviewProposalsDesc: 'Review and select the best proposals',
  receiveProposalsFromQualified: 'Receive proposals from qualified freelancers',
  reviewPortfoliosPastWork: 'Review portfolios and past work',
  interviewTopCandidates: 'Interview top candidates',
  workPaySafely: 'Work & Pay Safely',
  workPaySafelyDesc: 'Secure payment and project management',
  useEscrowProtection: 'Use escrow protection',
  trackProgressMilestones: 'Track progress with milestones',
  payOnlyWhenSatisfied: 'Pay only when satisfied',
  leaveReview: 'Leave Review',
  leaveReviewDesc: 'Rate and review completed work',
  rateFreelancerWork: 'Rate freelancer work',
  provideFeedback: 'Provide feedback',
  buildLongTermRelationships: 'Build long-term relationships',
  
  // Recent activity
  recentActivity: 'Recent Activity',
  stayUpdatedWithLatest: 'Stay updated with the latest activity',
  joinedAs: 'joined as',
  newJobPosted: 'New job posted',
  completedProject: 'completed project',
  leftReview: 'left review',
  loadMoreActivity: 'Load More Activity',
  
  // Testimonials
  whatOurClients: 'What Our Clients',
  areSaying: 'Are Saying',
  dontJustTakeWord: 'Don\'t just take our word for it',
  testimonial1: 'NERAFUS helped us find the perfect development team. The quality of work exceeded our expectations and the project timeline was met.',
  testimonial2: 'Excellent platform for finding high-quality freelancers. We\'ve completed many successful projects through NERAFUS.',
  testimonial3: 'User-friendly interface and professional workflow. NERAFUS is our top choice for all our projects.',
  testimonial4: 'Dedicated support team and skilled freelancers. We\'re very satisfied with NERAFUS\'s service.',
  testimonial5: 'NERAFUS connected us with talented designers. The results exceeded expectations and were delivered on time.',

  // Featured Jobs
  jobTitle1: 'Senior React Developer',
  jobTitle2: 'UI/UX Designer',
  jobTitle3: 'Full Stack Engineer',
  jobTitle4: 'Mobile App Developer',
  jobTitle5: 'DevOps Engineer',
  jobTitle6: 'Data Scientist',

  jobCompany1: 'TechCorp Inc.',
  jobCompany2: 'Design Studio',
  jobCompany3: 'StartupXYZ',
  jobCompany4: 'InnovateTech',
  jobCompany5: 'CloudSystems',
  jobCompany6: 'DataLab',

  jobDescription1: 'We are looking for an experienced React developer to join our dynamic team...',
  jobDescription2: 'Create beautiful and intuitive user interfaces for our mobile applications...',
  jobDescription3: 'Join our fast-growing startup and help build the next generation platform...',
  jobDescription4: 'Develop cross-platform mobile applications using the latest technologies...',
  jobDescription5: 'Manage cloud infrastructure and optimize CI/CD processes for development teams...',
  jobDescription6: 'Analyze big data and build machine learning models to improve our products...',

  jobTypeFullTime: 'Full-time',
  jobTypeContract: 'Contract',
  jobTypePartTime: 'Part-time',
  jobLocationRemote: 'Remote',
  jobLocationNewYork: 'New York, NY',
  jobLocationSanFrancisco: 'San Francisco, CA',
  jobLocationLosAngeles: 'Los Angeles, CA',
  jobLocationChicago: 'Chicago, IL',

  // Featured Freelancers
  freelancerName1: 'Sarah Chen',
  freelancerName2: 'Marcus Johnson',
  freelancerName3: 'Elena Rodriguez',
  freelancerName4: 'David Kim',
  freelancerName5: 'Lisa Wang',
  freelancerName6: 'Alex Thompson',

  freelancerTitle1: 'Senior UI/UX Designer',
  freelancerTitle2: 'Full Stack Developer',
  freelancerTitle3: 'Digital Marketing Specialist',
  freelancerTitle4: 'Mobile App Developer',
  freelancerTitle5: 'Data Scientist',
  freelancerTitle6: 'DevOps Engineer',

  freelancerDescription1: 'Passionate designer with 8+ years of experience creating beautiful and functional user interfaces.',
  freelancerDescription2: 'Expert developer specializing in modern web applications and cloud architecture.',
  freelancerDescription3: 'Creative marketer with proven track record in digital campaigns and brand growth.',
  freelancerDescription4: 'Experienced mobile developer creating innovative apps for iOS and Android platforms.',
  freelancerDescription5: 'Data expert with expertise in machine learning and business intelligence solutions.',
  freelancerDescription6: 'Infrastructure specialist focused on scalable cloud solutions and automation.',

  project: 'Project',
  duration: 'Duration',
  months: 'months',
  mobileAppDevelopment: 'Mobile App Development',
  digitalMarketingCampaign: 'Digital Marketing Campaign',
  uiUxDesign: 'UI/UX Design',
  fullStackDevelopment: 'Full Stack Development',
  brandIdentityDesign: 'Brand Identity Design',
  happyClients: 'Happy Clients',
  projectSuccessRate: 'Project Success Rate',
  
  // CTA section
  readyToGetStarted2: 'Ready to Get Started?',
  started: 'Get Started',
  joinMillionsFreelancers: 'Join millions of freelancers and clients',
  joinAsFreelancer: 'Join as Freelancer',
  hireFreelancers: 'Hire Freelancers',
  instantMatching: 'Instant Matching',
  instantMatchingDesc: 'Find the right talent instantly',
  fastPayments: 'Fast Payments',
  fastPaymentsDesc: 'Secure and fast payment processing',
  qualityWorkDesc: 'High-quality work guaranteed',
  
  // Footer
  footerDescription: 'The leading platform connecting freelancers and clients',
  platform: 'Platform',
  communityHub: 'Team Hub',
  successStories: 'Success Stories',
  blog: 'Blog',
  events: 'Events',
  helpCenter: 'Help Center',
  contact: 'Contact',
  trustSafety: 'Trust & Safety',
  apiDocs: 'API Docs',
  company: 'Company',
  aboutUs: 'About Us',
  careers: 'Careers',
  press: 'Press',
  investors: 'Investors',
  allRightsReserved: 'All rights reserved',
  madeWithLove: 'Made with love',
  privacy: 'Privacy',
  terms: 'Terms',
  cookies: 'Cookies',
  global: 'Global',
  verifiedProfiles: 'Verified Profiles',
  support247: '24/7 Support',
  
  // Error messages
  authInvalidCredential: 'Invalid login credentials. Please check your email and password.',
  authUserNotFound: 'No account found with this email address.',
  authWrongPassword: 'Incorrect password.',
  authEmailAlreadyInUse: 'This email is already registered.',
  authWeakPassword: 'Password is too weak. Please choose a stronger password.',
  authInvalidEmail: 'Invalid email address.',
  authTooManyRequests: 'Too many attempts. Please try again later.',
  authNetworkError: 'Network error. Please check your internet connection.',
  authUserDisabled: 'This account has been disabled.',
  authGenericError: 'An error occurred. Please try again.',
  processing: 'Processing...',
  authErrorTips: 'Tips:',
  authCheckCredentials: 'Check your email and password',
  authRemoveSpaces: 'Remove extra spaces',
  authTryPasswordReset: 'Try password reset if needed',
  authWaitBeforeRetry: 'Please wait 1-2 minutes before trying again',
  authCheckInternet: 'Check your internet connection',
  authTryLoginInstead: 'Try logging in instead of signing up, or reset password',
  
  // Additional homepage keys
  trustedByIndustryLeaders: 'Trusted by industry leaders',
  joinThousandsOfCompanies: 'Join thousands of companies that rely on NERAFUS',
  fortuneCompanies: 'Fortune 500 Companies',
  countriesServed: 'Countries Served',
  uptimeGuarantee: 'Uptime Guarantee',
  readyToJoinThem: 'Ready to join them?',
  startYourProject: 'Start your project today',
  whyLeadingCompanies: 'and see why leading companies choose NERAFUS',
  getStartedNow: 'Get Started Now',

  // Why Choose NERAFUS section
  whyChooseVWork: 'Why Choose NERAFUS',
  trustedPlatformDesc: 'The trusted platform connecting businesses with top talent worldwide',
  secureAndTrusted: 'Secure & Trusted',
  secureDesc: 'Advanced security measures and verified professionals ensure your projects are safe',
  fairPricing: 'Fair Pricing',
  fairPricingDesc: 'Transparent pricing with no hidden fees. Pay only for quality work delivered',
  fastDelivery: 'Fast Delivery',
  fastDeliveryDesc: 'Quick turnaround times with milestone-based project management',
  expertNetwork: 'Expert Network',
  expertNetworkDesc: 'Access to thousands of pre-vetted professionals across all industries',
  qualityGuaranteed: 'Quality Guaranteed',
  qualityGuaranteedDesc: 'Money-back guarantee and dispute resolution for complete peace of mind',
  globalReach: 'Global Reach',
  globalReachDesc: 'Connect with talent from around the world, available 24/7 in all time zones',
  technology: 'Technology',
  ecommerce: 'E-commerce',
  entertainment: 'Entertainment',
  music: 'Music',
  travel: 'Travel',
  transportation: 'Transportation',
  automotive: 'Automotive',
  socialMedia: 'Social Media',
  software: 'Software',
  crm: 'CRM',

  // Onboarding translations (moved from duplicate section)
  onboardingProgress: 'Setup Progress',
  onboardingFooterText: 'Complete your profile to get the best experience on NERAFUS',
  welcomeToVWork: 'Welcome to NERAFUS',
  friend: 'Friend',
  onboardingWelcomeFreelancer: 'Let\'s set up your freelancer profile to help you find amazing projects and connect with clients.',
  onboardingWelcomeClient: 'Let\'s set up your client profile to help you find the perfect freelancers for your projects.',
  onboardingFeature1Title: 'Find Great Projects',
  onboardingFeature1Desc: 'Access thousands of projects from verified clients',
  onboardingFeature2Title: 'Connect with Talent',
  onboardingFeature2Desc: 'Build relationships with top professionals',
  onboardingFeature3Title: 'Secure Payments',
  onboardingFeature3Desc: 'Protected transactions and timely payments',
  onboardingFeature4Title: 'Quality Assurance',
  onboardingFeature4Desc: 'Rating system ensures high-quality work',
  freelancerAccount: 'Freelancer Account',
  clientAccount: 'Client Account',
  skipForNow: 'Skip for Now',
  onboardingTimeEstimate: 'This will take about 3-5 minutes to complete',
  basicInformation: 'Basic Information',
  basicInfoDescription: 'Tell us a bit about yourself to create your professional profile',
  fullName: 'Full Name',
  enterFullName: 'Enter your full name',
  bio: 'Professional Bio',
  bioPlaceholder: 'Write a brief description about yourself, your experience, and what makes you unique...',
  bioRequired: 'Bio is required',
  bioTooShort: 'Bio must be at least 50 characters',
  nameRequired: 'Name is required',
  countryRequired: 'Country is required',
  cityRequired: 'City is required',
  country: 'Country',
  city: 'City',
  enterCountry: 'Enter your country',
  enterCity: 'Enter your city',
  searchCountries: 'Search countries...',
  searchCities: 'Search cities...',
  noCountriesFound: 'No countries found',
  noCitiesFound: 'No cities found',
  loadingCountries: 'Loading countries',
  phoneNumber: 'Phone Number',
  enterPhoneNumber: 'Enter your phone number',
  website: 'Website',
  optional: 'Optional',

  // Profile Details
  profileDetails: 'Profile Details',
  profileDetailsFreelancerDesc: 'Set up your professional details to attract clients',
  profileDetailsClientDesc: 'Complete your profile to find the best freelancers',
  profilePhoto: 'Profile Photo',
  uploadPhoto: 'Upload Photo',
  profileHourlyRate: 'Hourly Rate',
  profileHourlyRateRequired: 'Hourly rate is required and must be at least $5',
  profileHourlyRateHint: 'Set a competitive rate based on your experience',
  profileAvailability: 'Availability',
  profileAvailabilityRequired: 'Please select your availability',
  profileAvailable: 'Available',
  profileBusy: 'Busy',
  profileUnavailable: 'Unavailable',
  experience: 'Experience',
  experienceRequired: 'Please describe your experience',
  experiencePlaceholder: 'Describe your professional experience, key projects, and achievements...',
  education: 'Education',
  educationPlaceholder: 'List your educational background, certifications, and relevant courses...',
  languages: 'Languages',
  languagesHint: 'Languages you can work in',

  // Skills Setup
  skillsSetup: 'Skills & Expertise',
  skillsSetupDescription: 'Select your skills to help clients find you for relevant projects',
  selectedSkills: 'Selected Skills',
  addCustomSkill: 'Add Custom Skill',
  customSkillPlaceholder: 'Enter a skill not listed above',
  maxSkillsError: 'You can select up to 10 skills',
  minSkillsError: 'Please select at least 3 skills',
  selectAtLeastOneSkill: 'Please select at least one skill',

  // Skill level and experience
  skillLevel: 'Skill Level',
  selectSkillLevel: 'Select your skill level',
  beginner: 'Beginner',
  intermediate: 'Intermediate',
  advanced: 'Advanced',
  expert: 'Expert',
  workExperience: 'Work Experience',
  selectWorkExperience: 'Select your experience',
  lessThanOneYear: 'Less than 1 year',
  oneToThreeYears: '1-3 years',
  threeToFiveYears: '3-5 years',
  fiveToTenYears: '5-10 years',
  moreThanTenYears: 'More than 10 years',
  specialization: 'Specialization',
  selectSpecialization: 'Select your specialization',
  mobileDevelopment: 'Mobile Development',
  businessFinance: 'Business & Finance',
  otherSpecialization: 'Other',

  // Profile setup page
  completeYourProfile: 'Complete Your Profile',
  setUpYourProfessionalProfile: 'Set up your professional profile to start working',
  expertiseLevel: 'Experience Level',
  whatLevelOfExpertise: 'What level of expertise do you have?',
  pleaseSelectSkillLevel: 'Please select your skill level',
  pleaseSelectWorkExperience: 'Please select your work experience',
  pleaseSelectMajor: 'Please select your specialization',

  // Preferences
  preferences: 'Preferences',

  // Client Onboarding
  whatBringsYouHere: 'What brings you to NERAFUS?',
  purposeDescription: 'Help us understand your needs to provide the best experience',
  personalPurpose: 'Personal Projects',
  personalPurposeDesc: 'I need help with personal projects, hobbies, or individual tasks',
  personalBusinessPurpose: 'Personal Business',
  personalBusinessPurposeDesc: 'I run my own business and need freelancers for various projects',
  companyPurpose: 'Company Projects',
  companyPurposeDesc: 'I represent a company and need freelancers for business projects',
  pleaseSelectPurpose: 'Please select your purpose',

  freelancerExperience: 'Have you used freelancer platforms before?',
  experienceDescription: 'This helps us customize your experience',
  neverUsed: 'Never used before',
  neverUsedDesc: 'This is my first time using a freelancer platform',
  usedBefore: 'Used before',
  usedBeforeDesc: 'I have used freelancer platforms but never paid',
  paidBefore: 'Paid before',
  paidBeforeDesc: 'I have hired and paid freelancers before',
  pleaseSelectExperience: 'Please select your experience level',
  newcomer: 'Newcomer',
  experienced: 'Experienced',
  expert: 'Expert',

  whatTypeOfFreelancer: 'What type of freelancer do you need?',
  needsDescription: 'Select the type of work arrangement you prefer',
  projectBased: 'Project-based',
  projectBasedDesc: 'Hire freelancers for specific projects with defined deliverables',
  projectBasedFeature1: 'Fixed scope and timeline',
  projectBasedFeature2: 'Clear deliverables',
  projectBasedFeature3: 'One-time payment',
  hourlyBased: 'Hourly work',
  hourlyBasedDesc: 'Hire freelancers on an hourly basis for ongoing work',
  hourlyBasedFeature1: 'Flexible hours',
  hourlyBasedFeature2: 'Ongoing collaboration',
  hourlyBasedFeature3: 'Hourly billing',
  fixedTerm: 'Fixed-term contract',
  fixedTermDesc: 'Hire freelancers for a specific period with regular work',
  fixedTermFeature1: 'Long-term commitment',
  fixedTermFeature2: 'Regular work schedule',
  fixedTermFeature3: 'Monthly payments',
  pleaseSelectAtLeastOneNeed: 'Please select at least one need',
  selectedNeeds: 'Selected needs',



  descriptionTooShort: 'Description must be at least 50 characters',
  '1to3Days': '1-3 days',
  '1Week': '1 week',
  '2to4Weeks': '2-4 weeks',
  '1to3Months': '1-3 months',
  '3to6Months': '3-6 months',
  '6PlusMonths': '6+ months',

  preferencesDescription: 'Customize your NERAFUS experience',
  notificationSettings: 'Notification Settings',
  emailNotifications: 'Email Notifications',
  emailNotificationsDesc: 'Receive important updates via email',
  pushNotifications: 'Push Notifications',
  pushNotificationsDesc: 'Get instant notifications in your browser',
  projectUpdates: 'Project Updates',
  projectUpdatesDesc: 'Notifications about your active projects',
  messageNotifications: 'Message Notifications',
  messageNotificationsDesc: 'Alerts when you receive new messages',
  marketingEmails: 'Marketing Emails',
  marketingEmailsDesc: 'Receive tips, news, and promotional content',
  appearance: 'Appearance',
  modernTheme: 'Modern Theme',
  professionalTheme: 'Professional Theme',
  languageRegion: 'Language & Region',
  currency: 'Currency',
  completing: 'Completing...',
  completeSetup: 'Complete Setup',

  // Complete Step
  welcomeComplete: 'Welcome Complete',
  onboardingCompleteFreelancer: 'Your freelancer profile is now set up! You\'re ready to start finding amazing projects and building your career on NERAFUS.',
  onboardingCompleteClient: 'Your client profile is now ready! You can start posting projects and finding talented freelancers to bring your ideas to life.',
  nextSteps: 'What\'s Next?',
  browseProjects: 'Browse Projects',
  browseProjectsDesc: 'Find projects that match your skills',
  exploreProjects: 'Explore Projects',
  buildNetwork: 'Build Your Network',
  buildNetworkDesc: 'Connect with other professionals',
  joinCommunity: 'Join Team',
  optimizeProfile: 'Optimize Profile',
  optimizeProfileDesc: 'Enhance your profile for better visibility',
  editProfile: 'Edit Profile',
  postFirstProject: 'Post Your First Project',
  postFirstProjectDesc: 'Create a project to find freelancers',
  findFreelancers: 'Find Freelancers',
  findFreelancersDesc: 'Browse talented professionals',
  browseFreelancers: 'Browse Freelancers',
  exploreFeatures: 'Explore Features',
  exploreFeaturesDesc: 'Learn about NERAFUS\'s powerful tools',
  goToDashboard: 'Go to Dashboard',
  onboardingCompleteFooter: 'You can always update your preferences in Settings',
 },
 vi: {
  // Navigation
  home: 'Trang Chủ',
  projects: 'Dự Án',
  freelancers: 'Tìm Freelancer',
  jobs: 'Việc Làm',
  contests: 'Cuộc Thi',
  dashboard: 'Bảng Điều Khiển',
  messages: 'Tin Nhắn',
  support: 'Hỗ Trợ',
  community: 'Cộng Đồng',
  settings: 'Cài Đặt',
  signIn: 'Đăng Nhập',
  signUp: 'Đăng Ký',
  signOut: 'Đăng Xuất',
  vwork: 'NERAFUS',
  vworkAssistant: 'Trợ lý NERAFUS',

  // Homepage
  heroTitle: 'Tìm Freelancer Hoàn Hảo Cho Dự Án Của Bạn',
  heroSubtitle: '',
  searchPlaceholder: 'Bạn đang tìm kiếm dịch vụ gì?',
  getStarted: 'Bắt Đầu Ngay',
  learnMore: 'Tìm Hiểu Thêm',
  add: 'Thêm',
  chatAI: 'Chat AI',

  // Categories
  webDevelopment: 'Phát Triển Web',
  mobileApps: 'Ứng Dụng Di Động',
  design: 'Thiết Kế & Sáng Tạo',
  writing: 'Viết & Dịch Thuật',
  marketing: 'Marketing Số',
  business: 'Kinh Doanh & Tư Vấn',

  // Features
  securePayments: 'Thanh Toán An Toàn',
  qualityWork: 'Chất Lượng Đảm Bảo',
  support24_7: 'Hỗ Trợ 24/7',
  easyToUse: 'Dễ Sử Dụng',

  // Projects
  postProject: 'Đăng Dự Án',
  viewDetails: 'Xem Chi Tiết',
  placeBid: 'Đặt Giá Thầu',
  budget: 'Ngân Sách',
  deadline: 'Thời Hạn',
  proposals: 'Đề Xuất',
  viewAllJobs: 'Xem Tất Cả Việc Làm',

  // Common
  loading: 'Đang tải...',
  search: 'Tìm kiếm',
  filter: 'Lọc',
  sort: 'Sắp xếp',
  save: 'Lưu',
  cancel: 'Hủy',
  delete: 'Xóa',
  edit: 'Chỉnh sửa',
  submit: 'Gửi',
  next: 'Tiếp theo',
  previous: 'Trước đó',
  more: 'Thêm',
  user: 'Người dùng',
  searchProjectsFreelancers: 'Tìm dự án, freelancer...',

  // Homepage specific translations
  findYourDreamJob: 'Tìm Công Việc Mơ Ước',
  hireTopTalent: 'Thuê Nhân Tài Hàng Đầu',
  buildYourCareer: 'Xây Dựng Sự Nghiệp',
  growYourBusiness: 'Phát Triển Kinh Doanh',
  perfectMatches: 'Kết Nối Hoàn Hảo',
  creativeSolutions: 'Giải Pháp Sáng Tạo',
  worldsWork: 'NERAFUS',
  marketplace: 'New Era For Us',
  cursor: 'Con Trỏ',
  connectSubtitle: 'Từ ý tưởng thành hiện thực với NERAFUS',
  findJobs: 'Tìm Việc Làm',
  jobsDescription: 'Khám phá hàng nghìn cơ hội việc làm từ các công ty hàng đầu',

  // Navigation and actions
  findWork: 'Tìm Việc',
  hireTalent: 'Tìm Nhân Tài',
  postJob: '+ Đăng việc',

  // Stats
  activeUsers: 'Người dùng hoạt động',
  projectsCompleted: 'Dự án hoàn thành',
  averageRating: 'Đánh giá trung bình',
  successRate: 'Tỷ lệ thành công',

  // Search section
  findPerfectMatch: 'Tìm Kết Nối Hoàn Hảo',
  searchThousandsOfTalented: 'Tìm kiếm trong hàng nghìn tài năng',
  searchSkillsOrJobs: 'Tìm kiếm kỹ năng hoặc việc làm',
  location: 'Địa điểm',
  hourlyRate: 'Mức lương theo giờ',
  experienceLevel: 'Mức độ kinh nghiệm',
  
  // Location dropdown translations
  selectCountry: 'Chọn Quốc Gia',
  selectCity: 'Chọn Thành Phố',
  searchCountries: 'Tìm kiếm quốc gia...',
  searchCities: 'Tìm kiếm thành phố...',
  loadingCountries: 'Đang tải quốc gia...',
  loadingCities: 'Đang tải thành phố...',
  noCountriesFound: 'Không tìm thấy quốc gia',
  noCitiesFound: 'Không có thành phố nào',
  availability: 'Tình trạng',
  moreFilters: 'Bộ lọc khác',
  searchNow: 'Tìm ngay',

  // Popular searches
  popularSearches: 'Tìm kiếm phổ biến',
  reactDeveloper: 'React Developer',
  uiUxDesigner: 'UI/UX Designer',
  contentWriter: 'Content Writer',
  digitalMarketing: 'Digital Marketing',
  mobileAppDeveloper: 'Mobile App Developer',
  dataScientist: 'Data Scientist',

  // Popular locations
  popularLocations: 'Địa điểm phổ biến',





  // Browse categories
  browseByCategory: 'Duyệt theo danh mục',
  findPerfectFreelancer: 'Tìm freelancer hoàn hảo',
  developmentIT: 'Phát triển & IT',
  developmentITDesc: 'Mô tả phát triển & IT',
  designCreative: 'Thiết kế & Sáng tạo',
  designCreativeDesc: 'Mô tả thiết kế & sáng tạo',
  salesMarketing: 'Bán hàng & Marketing',
  salesMarketingDesc: 'Mô tả bán hàng & marketing',
  writingTranslation: 'Viết & Dịch thuật',
  writingTranslationDesc: 'Mô tả viết & dịch thuật',
  videoPhotography: 'Video & Nhiếp ảnh',
  videoPhotographyDesc: 'Mô tả video & nhiếp ảnh',
  dataAnalytics: 'Dữ liệu & Phân tích',
  dataAnalyticsDesc: 'Mô tả dữ liệu & phân tích',
  musicAudio: 'Âm nhạc & Âm thanh',
  musicAudioDesc: 'Mô tả âm nhạc & âm thanh',
  customerService: 'Dịch vụ khách hàng',
  customerServiceDesc: 'Mô tả dịch vụ khách hàng',
  viewAllCategories: 'Xem tất cả danh mục',

  // Featured jobs
  featuredJobs: 'Việc làm nổi bật',
  discoverTopOpportunities: 'Khám phá cơ hội hàng đầu',
  urgent: 'Gấp',
  applyNow: 'Ứng tuyển ngay',

  // Top freelancers
  topFreelancers: 'Freelancer hàng đầu',
  workWithBestTalent: 'Làm việc với tài năng tốt nhất',
  topRated: 'Đánh giá cao',
  available: 'Có sẵn',
  response: 'Phản hồi',
  hire: 'Thuê',
  message: 'Tin nhắn',
  viewAllFreelancers: 'Xem tất cả freelancer',

  // Featured projects
  featuredProjects: 'Dự án nổi bật',
  projectsText: 'Dự án',
  exploreOutstandingWork: 'Khám phá công việc xuất sắc',
  allProjects: 'Tất cả dự án',
  webDesign: 'Thiết kế Web',
  branding: 'Thương hiệu',
  illustration: 'Minh họa',
  byFreelancer: 'bởi freelancer',
  viewAllProjects: 'Xem tất cả dự án',
  
  // How it works
  howItWorks: 'Cách thức hoạt động',
  simpleStepsToSuccess: 'Các bước đơn giản để thành công',
  postYourProject: 'Đăng dự án của bạn',
  postProjectDesc: 'Mô tả đăng dự án',
  describeProjectReq: 'Mô tả yêu cầu dự án',
  setBudgetTimeline: 'Đặt ngân sách và thời gian',
  choosePreferredSkills: 'Chọn kỹ năng ưa thích',
  reviewProposals: 'Xem xét đề xuất',
  reviewProposalsDesc: 'Mô tả xem xét đề xuất',
  receiveProposalsFromQualified: 'Nhận đề xuất từ người có trình độ',
  reviewPortfoliosPastWork: 'Xem xét hồ sơ và công việc trước đây',
  interviewTopCandidates: 'Phỏng vấn ứng viên hàng đầu',
  workPaySafely: 'Làm việc và thanh toán an toàn',
  workPaySafelyDesc: 'Mô tả làm việc và thanh toán an toàn',
  useEscrowProtection: 'Sử dụng bảo vệ ký quỹ',
  trackProgressMilestones: 'Theo dõi tiến độ và mốc quan trọng',
  payOnlyWhenSatisfied: 'Chỉ thanh toán khi hài lòng',
  leaveReview: 'Để lại đánh giá',
  leaveReviewDesc: 'Mô tả để lại đánh giá',
  rateFreelancerWork: 'Đánh giá công việc của freelancer',
  provideFeedback: 'Cung cấp phản hồi',
  buildLongTermRelationships: 'Xây dựng mối quan hệ dài hạn',
  
  // Recent activity
  recentActivity: 'Hoạt động gần đây',
  stayUpdatedWithLatest: 'Cập nhật với những hoạt động mới nhất',
  joinedAs: 'đã tham gia với vai trò',
  newJobPosted: 'Công việc mới đã đăng',
  completedProject: 'đã hoàn thành dự án',
  leftReview: 'đã để lại đánh giá',
  loadMoreActivity: 'Tải thêm hoạt động',

  // Testimonials
  whatOurClients: 'Khách hàng của chúng tôi',
  areSaying: 'nói gì',
  dontJustTakeWord: 'Đừng chỉ tin lời chúng tôi',
  testimonial1: 'NERAFUS đã giúp chúng tôi tìm được đội ngũ phát triển hoàn hảo. Chất lượng công việc vượt quá mong đợi và tiến độ dự án được đảm bảo.',
  testimonial2: 'Nền tảng tuyệt vời để tìm kiếm freelancer chất lượng cao. Chúng tôi đã hoàn thành nhiều dự án thành công thông qua NERAFUS.',
  testimonial3: 'Giao diện thân thiện, quy trình làm việc chuyên nghiệp. NERAFUS là lựa chọn hàng đầu cho các dự án của chúng tôi.',
  testimonial4: 'Đội ngũ hỗ trợ tận tình, freelancer có kỹ năng cao. Chúng tôi rất hài lòng với dịch vụ của NERAFUS.',
  testimonial5: 'NERAFUS đã kết nối chúng tôi với những designer tài năng. Kết quả vượt ngoài mong đợi và đúng thời hạn.',

  // Featured Jobs
  jobTitle1: 'Senior React Developer',
  jobTitle2: 'UI/UX Designer',
  jobTitle3: 'Full Stack Engineer',
  jobTitle4: 'Mobile App Developer',
  jobTitle5: 'DevOps Engineer',
  jobTitle6: 'Data Scientist',

  jobCompany1: 'TechCorp Inc.',
  jobCompany2: 'Design Studio',
  jobCompany3: 'StartupXYZ',
  jobCompany4: 'InnovateTech',
  jobCompany5: 'CloudSystems',
  jobCompany6: 'DataLab',

  jobDescription1: 'Chúng tôi đang tìm kiếm một React developer có kinh nghiệm để tham gia đội ngũ năng động của chúng tôi...',
  jobDescription2: 'Tạo ra những giao diện người dùng đẹp mắt và trực quan cho các ứng dụng di động của chúng tôi...',
  jobDescription3: 'Tham gia startup phát triển nhanh của chúng tôi và giúp xây dựng nền tảng thế hệ tiếp theo...',
  jobDescription4: 'Phát triển ứng dụng di động đa nền tảng với công nghệ hiện đại nhất...',
  jobDescription5: 'Quản lý hạ tầng cloud và tối ưu hóa quy trình CI/CD cho đội phát triển...',
  jobDescription6: 'Phân tích dữ liệu lớn và xây dựng mô hình machine learning để cải thiện sản phẩm...',

  jobTypeFullTime: 'Toàn thời gian',
  jobTypeContract: 'Hợp đồng',
  jobTypePartTime: 'Bán thời gian',
  jobLocationRemote: 'Làm việc từ xa',
  jobLocationNewYork: 'New York, NY',
  jobLocationSanFrancisco: 'San Francisco, CA',
  jobLocationLosAngeles: 'Los Angeles, CA',
  jobLocationChicago: 'Chicago, IL',

  // Featured Freelancers
  freelancerName1: 'Sarah Chen',
  freelancerName2: 'Marcus Johnson',
  freelancerName3: 'Elena Rodriguez',
  freelancerName4: 'David Kim',
  freelancerName5: 'Lisa Wang',
  freelancerName6: 'Alex Thompson',

  freelancerTitle1: 'Senior UI/UX Designer',
  freelancerTitle2: 'Full Stack Developer',
  freelancerTitle3: 'Digital Marketing Specialist',
  freelancerTitle4: 'Mobile App Developer',
  freelancerTitle5: 'Data Scientist',
  freelancerTitle6: 'DevOps Engineer',

  freelancerDescription1: 'Designer đam mê với hơn 8 năm kinh nghiệm tạo ra những giao diện đẹp mắt và chức năng.',
  freelancerDescription2: 'Developer chuyên gia về ứng dụng web hiện đại và kiến trúc cloud.',
  freelancerDescription3: 'Marketer sáng tạo với thành tích đã được chứng minh trong các chiến dịch số và phát triển thương hiệu.',
  freelancerDescription4: 'Developer di động có kinh nghiệm tạo ra các ứng dụng sáng tạo cho iOS và Android.',
  freelancerDescription5: 'Chuyên gia dữ liệu với kiến thức về machine learning và giải pháp business intelligence.',
  freelancerDescription6: 'Chuyên gia hạ tầng tập trung vào giải pháp cloud có thể mở rộng và tự động hóa.',

  mobileAppDevelopment: 'Phát triển ứng dụng di động',
  digitalMarketingCampaign: 'Chiến dịch marketing số',
  uiUxDesign: 'Thiết kế UI/UX',
  fullStackDevelopment: 'Phát triển Full Stack',
  brandIdentityDesign: 'Thiết kế nhận diện thương hiệu',
  months: 'tháng',
  happyClients: 'Khách hàng hài lòng',
  projectSuccessRate: 'Tỷ lệ thành công dự án',

  // CTA section
  readyToGetStarted2: 'Sẵn sàng bắt đầu?',
  started: 'Bắt đầu',
  joinMillionsFreelancers: 'Tham gia cùng hàng triệu freelancer và khách hàng',
  joinAsFreelancer: 'Tham gia với vai trò Freelancer',
  hireFreelancers: 'Thuê Freelancer',

  // Features bottom
  instantMatching: 'Kết nối tức thì',
  instantMatchingDesc: 'Tìm tài năng phù hợp ngay lập tức',
  fastPayments: 'Thanh toán nhanh',
  fastPaymentsDesc: 'Xử lý thanh toán an toàn và nhanh chóng',
  qualityWorkDesc: 'Đảm bảo chất lượng công việc cao',

  // Footer
  footerDescription: 'Nền tảng kết nối freelancer và khách hàng hàng đầu',
  platform: 'Nền tảng',
  communityHub: 'Trung tâm Team',
  successStories: 'Câu chuyện thành công',
  blog: 'Blog',
  events: 'Sự kiện',
  helpCenter: 'Trung tâm trợ giúp',
  contact: 'Liên hệ',
  trustSafety: 'Tin cậy & An toàn',
  apiDocs: 'Tài liệu API',
  company: 'Công ty',
  aboutUs: 'Về chúng tôi',
  careers: 'Tuyển dụng',
  press: 'Báo chí',
  investors: 'Nhà đầu tư',
  allRightsReserved: 'Tất cả quyền được bảo lưu',
  madeWithLove: 'Được tạo ra với tình yêu',
  privacy: 'Quyền riêng tư',
  terms: 'Điều khoản',
  cookies: 'Cookie',
  global: 'Toàn cầu',
  verifiedProfiles: 'Hồ sơ đã xác minh',
  support247: 'Hỗ trợ 24/7',

  // Auth form translations
  welcomeBack: 'Chào mừng trở lại',
  createYourAccount: 'Tạo tài khoản của bạn',
  signInToAccount: 'Đăng nhập vào tài khoản',
  joinWorldsLargest: 'Tham gia thị trường freelance lớn nhất thế giới',
  iWantTo: 'Tôi muốn',
  firstName: 'Tên',
  lastName: 'Họ',
  emailAddress: 'Địa chỉ email',
  password: 'Mật khẩu',
  confirmPassword: 'Xác nhận mật khẩu',
  
  // Validation messages
  emailRequired: 'Email là bắt buộc',
  emailInvalid: 'Email không hợp lệ',
  passwordRequired: 'Mật khẩu là bắt buộc',
  passwordMinLength: 'Mật khẩu phải có ít nhất 6 ký tự',
  firstNameRequired: 'Tên là bắt buộc',
  lastNameRequired: 'Họ là bắt buộc',
  passwordsNotMatch: 'Mật khẩu không khớp',

  // Additional missing keys
  watchDemo: 'Xem Demo',
  showPassword: 'Hiện mật khẩu',
  hidePassword: 'Ẩn mật khẩu',
  loadingDashboard: 'Đang tải bảng điều khiển...',
  gatheringData: 'Đang thu thập dữ liệu...',
  webDevelopmentCategory: 'Phát triển Web',
  mobileDevelopmentCategory: 'Phát triển Mobile',
  designCategory: 'Thiết kế',
  writingCategory: 'Viết lách',
  marketingCategory: 'Marketing',
  dataScience: 'Khoa học dữ liệu',
  budgetRange: 'Khoảng ngân sách',
  under500: 'Dưới $500',
  budget500to1000: '$500 - $1,000',
  budget1000to5000: '$1,000 - $5,000',
  over5000: 'Trên $5,000',
  projectDuration: 'Thời gian dự án',
  lessThanWeek: 'Ít hơn một tuần',
  oneToFourWeeks: '1-4 tuần',
  oneToThreeMonths: '1-3 tháng',
  moreThanThreeMonths: 'Hơn 3 tháng',
  category: 'Danh mục',
  allContests: 'Tất cả cuộc thi',
  filterContests: 'Lọc cuộc thi',
  readyToShowcaseTalent: 'Sẵn sàng thể hiện tài năng?',
  joinThousandsDesigners: 'Tham gia cùng hàng nghìn nhà thiết kế và lập trình viên',
  browseActiveContests: 'Duyệt cuộc thi đang diễn ra',
  startYourOwnContest: 'Tạo cuộc thi của riêng bạn',
  
  // Additional missing keys for pages (Vietnamese)
  findTalentedFreelancers: 'Tìm Freelancer Tài Năng',
  connectWithProfessionals: 'Kết Nối Với Chuyên Gia',
  searchFreelancersPlaceholder: 'Tìm kiếm freelancer...',
  filters: 'Bộ Lọc',
  freelancersFound: 'freelancer tìm thấy',
  highestRated: 'Đánh Giá Cao Nhất',
  projectsCount: 'Dự Án',
  loadMoreFreelancers: 'Tải Thêm Freelancer',
  allCategories: 'Tất Cả Danh Mục',
  enterLocation: 'Nhập địa điểm',
  allLevels: 'Tất Cả Cấp Độ',
  clearFilters: 'Xóa Bộ Lọc',
  designContests: 'Cuộc Thi Thiết Kế',
  competeWinPrizes: 'Thi Đấu & Thắng Giải',
  activeContests: 'Cuộc Thi Đang Diễn Ra',
  totalPrizes: 'Tổng Giải Thưởng',
  participants: 'Người Tham Gia',
  winners: 'Người Thắng',
  communityHubTitle: 'Trung Tâm Team',

  // Onboarding translations (Vietnamese)
  createAccount: 'Tạo Tài Khoản',
  onboardingProgress: 'Tiến Độ Thiết Lập',
  onboardingFooterText: 'Hoàn tất hồ sơ để có trải nghiệm tốt nhất trên NERAFUS',
  welcomeToVWork: 'Chào Mừng Đến NERAFUS',
  friend: 'Bạn',
  onboardingWelcomeFreelancer: 'Hãy thiết lập hồ sơ freelancer để giúp bạn tìm được những dự án tuyệt vời và kết nối với khách hàng.',
  onboardingWelcomeClient: 'Hãy thiết lập hồ sơ khách hàng để giúp bạn tìm được những freelancer hoàn hảo cho dự án của mình.',
  onboardingFeature1Title: 'Tìm Dự Án Tuyệt Vời',
  onboardingFeature1Desc: 'Truy cập hàng nghìn dự án từ khách hàng đã xác minh',
  onboardingFeature2Title: 'Kết Nối Với Tài Năng',
  onboardingFeature2Desc: 'Xây dựng mối quan hệ với các chuyên gia hàng đầu',
  onboardingFeature3Title: 'Thanh Toán An Toàn',
  onboardingFeature3Desc: 'Giao dịch được bảo vệ và thanh toán đúng hạn',
  onboardingFeature4Title: 'Đảm Bảo Chất Lượng',
  onboardingFeature4Desc: 'Hệ thống đánh giá đảm bảo chất lượng công việc cao',
  freelancerAccount: 'Tài Khoản Freelancer',
  clientAccount: 'Tài Khoản Khách Hàng',
  skipForNow: 'Bỏ Qua Bây Giờ',
  onboardingTimeEstimate: 'Quá trình này sẽ mất khoảng 3-5 phút để hoàn tất',
  basicInformation: 'Thông Tin Cơ Bản',
  basicInfoDescription: 'Hãy cho chúng tôi biết một chút về bạn để tạo hồ sơ chuyên nghiệp',
  fullName: 'Họ Và Tên',
  enterFullName: 'Nhập họ và tên của bạn',
  bio: 'Tiểu Sử Chuyên Nghiệp',
  bioPlaceholder: 'Viết mô tả ngắn gọn về bản thân, kinh nghiệm và điều gì làm bạn trở nên độc đáo...',
  bioRequired: 'Tiểu sử là bắt buộc',
  bioTooShort: 'Tiểu sử phải có ít nhất 50 ký tự',
  nameRequired: 'Tên là bắt buộc',
  countryRequired: 'Quốc gia là bắt buộc',
  cityRequired: 'Thành phố là bắt buộc',

  // Client Onboarding (Vietnamese)
  whatBringsYouHere: 'Điều gì đưa bạn đến NERAFUS?',
  purposeDescription: 'Giúp chúng tôi hiểu nhu cầu của bạn để cung cấp trải nghiệm tốt nhất',
  personalPurpose: 'Dự Án Cá Nhân',
  personalPurposeDesc: 'Tôi cần hỗ trợ cho các dự án cá nhân, sở thích hoặc công việc riêng lẻ',
  personalBusinessPurpose: 'Công Việc Cá Nhân',
  personalBusinessPurposeDesc: 'Tôi điều hành doanh nghiệp của riêng mình và cần freelancer cho các dự án khác nhau',
  companyPurpose: 'Dự Án Công Ty',
  companyPurposeDesc: 'Tôi đại diện cho công ty và cần freelancer cho các dự án kinh doanh',
  pleaseSelectPurpose: 'Vui lòng chọn mục đích của bạn',

  freelancerExperience: 'Bạn đã từng sử dụng nền tảng freelancer chưa?',
  experienceDescription: 'Điều này giúp chúng tôi tùy chỉnh trải nghiệm của bạn',
  neverUsed: 'Chưa từng sử dụng',
  neverUsedDesc: 'Đây là lần đầu tiên tôi sử dụng nền tảng freelancer',
  usedBefore: 'Đã từng sử dụng',
  usedBeforeDesc: 'Tôi đã sử dụng nền tảng freelancer nhưng chưa từng thanh toán',
  paidBefore: 'Đã từng thanh toán',
  paidBeforeDesc: 'Tôi đã thuê và thanh toán cho freelancer trước đây',
  pleaseSelectExperience: 'Vui lòng chọn mức độ kinh nghiệm của bạn',
  newcomer: 'Người Mới',
  experienced: 'Có Kinh Nghiệm',
  expert: 'Chuyên Gia',

  whatTypeOfFreelancer: 'Bạn cần loại freelancer nào?',
  needsDescription: 'Chọn loại hình làm việc bạn ưa thích',
  projectBased: 'Theo Dự Án',
  projectBasedDesc: 'Thuê freelancer cho các dự án cụ thể với kết quả được định nghĩa rõ ràng',
  projectBasedFeature1: 'Phạm vi và thời gian cố định',
  projectBasedFeature2: 'Kết quả rõ ràng',
  projectBasedFeature3: 'Thanh toán một lần',
  hourlyBased: 'Làm Việc Theo Giờ',
  hourlyBasedDesc: 'Thuê freelancer theo giờ cho công việc liên tục',
  hourlyBasedFeature1: 'Giờ làm việc linh hoạt',
  hourlyBasedFeature2: 'Hợp tác liên tục',
  hourlyBasedFeature3: 'Tính phí theo giờ',
  fixedTerm: 'Hợp Đồng Cố Định',
  fixedTermDesc: 'Thuê freelancer trong một khoảng thời gian cụ thể với công việc thường xuyên',
  fixedTermFeature1: 'Cam kết dài hạn',
  fixedTermFeature2: 'Lịch làm việc thường xuyên',
  fixedTermFeature3: 'Thanh toán hàng tháng',
  pleaseSelectAtLeastOneNeed: 'Vui lòng chọn ít nhất một nhu cầu',
  selectedNeeds: 'Nhu cầu đã chọn',



  descriptionTooShort: 'Mô tả phải có ít nhất 50 ký tự',
  '1to3Days': '1-3 ngày',
  '1Week': '1 tuần',
  '2to4Weeks': '2-4 tuần',
  '1to3Months': '1-3 tháng',
  '3to6Months': '3-6 tháng',
  '6PlusMonths': '6+ tháng',

  country: 'Quốc Gia',
  city: 'Thành Phố',
  enterCountry: 'Nhập quốc gia của bạn',
  enterCity: 'Nhập thành phố của bạn',
  searchCountries: 'Tìm kiếm quốc gia...',
  searchCities: 'Tìm kiếm thành phố...',
  noCountriesFound: 'Không tìm thấy quốc gia',
  noCitiesFound: 'Không tìm thấy thành phố',
  loadingCountries: 'Đang tải quốc gia',
  phoneNumber: 'Số Điện Thoại',
  enterPhoneNumber: 'Nhập số điện thoại của bạn',
  website: 'Website',
  optional: 'Tùy Chọn',

  // Profile Details (Vietnamese)
  profileDetails: 'Chi Tiết Hồ Sơ',
  profileDetailsFreelancerDesc: 'Thiết lập thông tin chuyên nghiệp để thu hút khách hàng',
  profileDetailsClientDesc: 'Hoàn tất hồ sơ để tìm được những freelancer tốt nhất',
  profilePhoto: 'Ảnh Hồ Sơ',
  uploadPhoto: 'Tải Ảnh Lên',
  profileHourlyRate: 'Giá Theo Giờ',
  profileHourlyRateRequired: 'Giá theo giờ là bắt buộc và phải ít nhất $5',
  profileHourlyRateHint: 'Đặt mức giá cạnh tranh dựa trên kinh nghiệm của bạn',
  profileAvailability: 'Tình Trạng',
  profileAvailabilityRequired: 'Vui lòng chọn tình trạng của bạn',
  profileAvailable: 'Có Sẵn',
  profileBusy: 'Bận',
  profileUnavailable: 'Không Có Sẵn',
  experience: 'Kinh Nghiệm',
  experienceRequired: 'Vui lòng mô tả kinh nghiệm của bạn',
  experiencePlaceholder: 'Mô tả kinh nghiệm chuyên môn, dự án chính và thành tích của bạn...',
  education: 'Học Vấn',
  educationPlaceholder: 'Liệt kê trình độ học vấn, chứng chỉ và khóa học liên quan...',
  languages: 'Ngôn Ngữ',
  languagesHint: 'Ngôn ngữ bạn có thể làm việc',

  // Skills Setup (Vietnamese)
  skillsSetup: 'Kỹ Năng & Chuyên Môn',
  skillsSetupDescription: 'Chọn kỹ năng của bạn để giúp khách hàng tìm thấy bạn cho các dự án phù hợp',
  selectedSkills: 'Kỹ Năng Đã Chọn',
  addCustomSkill: 'Thêm Kỹ Năng Tùy Chỉnh',
  customSkillPlaceholder: 'Nhập kỹ năng không có trong danh sách',
  maxSkillsError: 'Bạn có thể chọn tối đa 10 kỹ năng',
  minSkillsError: 'Vui lòng chọn ít nhất 3 kỹ năng',
  selectAtLeastOneSkill: 'Vui lòng chọn ít nhất một kỹ năng',

  // Skill level and experience (Vietnamese)
  skillLevel: 'Trình Độ Kỹ Năng',
  selectSkillLevel: 'Chọn trình độ kỹ năng của bạn',
  beginner: 'Người Mới Bắt Đầu',
  intermediate: 'Trung Bình',
  advanced: 'Nâng Cao',
  expert: 'Chuyên Gia',
  workExperience: 'Kinh Nghiệm Làm Việc',
  selectWorkExperience: 'Chọn kinh nghiệm của bạn',
  lessThanOneYear: 'Dưới 1 năm',
  oneToThreeYears: '1-3 năm',
  threeToFiveYears: '3-5 năm',
  fiveToTenYears: '5-10 năm',
  moreThanTenYears: 'Hơn 10 năm',
  specialization: 'Chuyên Môn',
  selectSpecialization: 'Chọn chuyên môn của bạn',
  mobileDevelopment: 'Phát Triển Mobile',
  businessFinance: 'Kinh Doanh & Tài Chính',
  otherSpecialization: 'Khác',

  // Profile setup page (Vietnamese)
  completeYourProfile: 'Hoàn Tất Hồ Sơ',
  setUpYourProfessionalProfile: 'Thiết lập hồ sơ chuyên nghiệp để bắt đầu làm việc',
  expertiseLevel: 'Trình Độ Kinh Nghiệm',
  whatLevelOfExpertise: 'Bạn có trình độ chuyên môn như thế nào?',
  pleaseSelectSkillLevel: 'Vui lòng chọn trình độ kỹ năng',
  pleaseSelectWorkExperience: 'Vui lòng chọn kinh nghiệm làm việc',
  pleaseSelectMajor: 'Vui lòng chọn chuyên môn',
  // minSkillsError already defined above

  // Preferences (Vietnamese)
  preferences: 'Tùy Chọn',
  preferencesDescription: 'Tùy chỉnh trải nghiệm NERAFUS của bạn',
  notificationSettings: 'Cài Đặt Thông Báo',
  emailNotifications: 'Thông Báo Email',
  emailNotificationsDesc: 'Nhận cập nhật quan trọng qua email',
  pushNotifications: 'Thông Báo Đẩy',
  pushNotificationsDesc: 'Nhận thông báo tức thì trong trình duyệt',
  projectUpdates: 'Cập Nhật Dự Án',
  projectUpdatesDesc: 'Thông báo về các dự án đang hoạt động',
  messageNotifications: 'Thông Báo Tin Nhắn',
  messageNotificationsDesc: 'Cảnh báo khi bạn nhận tin nhắn mới',
  marketingEmails: 'Email Marketing',
  marketingEmailsDesc: 'Nhận mẹo, tin tức và nội dung khuyến mãi',
  appearance: 'Giao Diện',
  modernTheme: 'Giao Diện Hiện Đại',
  professionalTheme: 'Giao Diện Chuyên Nghiệp',
  languageRegion: 'Ngôn Ngữ & Khu Vực',
  currency: 'Tiền Tệ',
  completing: 'Đang Hoàn Tất...',
  completeSetup: 'Hoàn Tất Thiết Lập',

  // Complete Step (Vietnamese)
  welcomeComplete: 'Chào Mừng Hoàn Tất',
  onboardingCompleteFreelancer: 'Hồ sơ freelancer của bạn đã được thiết lập! Bạn đã sẵn sàng bắt đầu tìm kiếm những dự án tuyệt vời và xây dựng sự nghiệp trên NERAFUS.',
  onboardingCompleteClient: 'Hồ sơ khách hàng của bạn đã sẵn sàng! Bạn có thể bắt đầu đăng dự án và tìm kiếm những freelancer tài năng để hiện thực hóa ý tưởng của mình.',
  nextSteps: 'Bước Tiếp Theo?',
  browseProjects: 'Duyệt Dự Án',
  browseProjectsDesc: 'Tìm dự án phù hợp với kỹ năng của bạn',
  exploreProjects: 'Khám Phá Dự Án',
  buildNetwork: 'Xây Dựng Mạng Lưới',
  buildNetworkDesc: 'Kết nối với các chuyên gia khác',
  joinCommunity: 'Tham Gia Team',
  optimizeProfile: 'Tối Ưu Hồ Sơ',
  optimizeProfileDesc: 'Cải thiện hồ sơ để có độ hiển thị tốt hơn',
  editProfile: 'Chỉnh Sửa Hồ Sơ',
  postFirstProject: 'Đăng Dự Án Đầu Tiên',
  postFirstProjectDesc: 'Tạo dự án để tìm freelancer',
  findFreelancers: 'Tìm Freelancer',
  findFreelancersDesc: 'Duyệt các chuyên gia tài năng',
  browseFreelancers: 'Duyệt Freelancer',
  exploreFeatures: 'Khám Phá Tính Năng',
  exploreFeaturesDesc: 'Tìm hiểu về các công cụ mạnh mẽ của NERAFUS',
  goToDashboard: 'Đi Đến Bảng Điều Khiển',
  onboardingCompleteFooter: 'Bạn luôn có thể cập nhật tùy chọn trong Cài đặt',
  connectLearnGrow: 'Kết Nối, Học Hỏi & Phát Triển',
  membersCount: 'Thành Viên',
  postsCount: 'Bài Viết',
  commentsCount: 'Bình Luận',
  onlineNow: 'Đang Trực Tuyến',
  newPost: 'Bài Viết Mới',
  sortBy: 'Sắp xếp theo',
  mostRecent: 'Mới Nhất',
  mostPopular: 'Phổ Biến Nhất',
  mostDiscussed: 'Thảo Luận Nhiều Nhất',
  allPosts: 'Tất Cả Bài Viết',
  trending: 'Đang Thịnh Hành',
  trendingFilter: 'Đang Thịnh Hành',
  timeAgo: 'trước',
  hoursAgo: 'giờ trước',
  daysAgo: 'ngày trước',
  loadMorePosts: 'Tải Thêm Bài Viết',

  // Additional keys for homepage
  project: 'Dự án',
  duration: 'Thời gian',
  
  // Missing homepage keys
  
  // Company info
  trustedByIndustryLeaders: 'Được tin tưởng bởi các nhà lãnh đạo ngành',
  joinThousandsOfCompanies: 'Tham gia cùng hàng nghìn công ty tin tưởng NERAFUS',
  fortuneCompanies: 'Công ty Fortune 500',
  countriesServed: 'Quốc gia được phục vụ',
  uptimeGuarantee: 'Đảm bảo thời gian hoạt động',
  readyToJoinThem: 'Sẵn sàng tham gia cùng họ?',
  startYourProject: 'Bắt đầu dự án của bạn ngay hôm nay',
  whyLeadingCompanies: 'và xem tại sao các công ty hàng đầu chọn NERAFUS',
  getStartedNow: 'Bắt Đầu Ngay',

  // Why Choose NERAFUS section
  whyChooseVWork: 'Tại sao chọn NERAFUS',
  trustedPlatformDesc: 'Nền tảng đáng tin cậy kết nối doanh nghiệp với nhân tài hàng đầu toàn cầu',
  secureAndTrusted: 'An Toàn & Đáng Tin Cậy',
  secureDesc: 'Các biện pháp bảo mật tiên tiến và chuyên gia đã được xác minh đảm bảo dự án của bạn an toàn',
  fairPricing: 'Giá Cả Công Bằng',
  fairPricingDesc: 'Giá cả minh bạch không có phí ẩn. Chỉ trả tiền cho công việc chất lượng được giao',
  fastDelivery: 'Giao Hàng Nhanh',
  fastDeliveryDesc: 'Thời gian hoàn thành nhanh chóng với quản lý dự án theo từng mốc',
  expertNetwork: 'Mạng Lưới Chuyên Gia',
  expertNetworkDesc: 'Tiếp cận hàng nghìn chuyên gia đã được kiểm định trước trong mọi ngành nghề',
  qualityGuaranteed: 'Chất Lượng Đảm Bảo',
  qualityGuaranteedDesc: 'Đảm bảo hoàn tiền và giải quyết tranh chấp để hoàn toàn yên tâm',
  globalReach: 'Phạm Vi Toàn Cầu',
  globalReachDesc: 'Kết nối với nhân tài từ khắp nơi trên thế giới, có sẵn 24/7 trong mọi múi giờ',
  
  // Technology companies
  technology: 'Công nghệ',
  ecommerce: 'Thương mại điện tử',
  entertainment: 'Giải trí',
  music: 'Âm nhạc',
  travel: 'Du lịch',
  transportation: 'Vận tải',
  automotive: 'Ô tô',
  socialMedia: 'Mạng xã hội',
  software: 'Phần mềm',
  crm: 'CRM',
  
  // Error messages
  authInvalidCredential: 'Thông tin đăng nhập không hợp lệ. Vui lòng kiểm tra email và mật khẩu.',
  authUserNotFound: 'Không tìm thấy tài khoản với email này.',
  authWrongPassword: 'Mật khẩu không chính xác.',
  authEmailAlreadyInUse: 'Email này đã được đăng ký.',
  authWeakPassword: 'Mật khẩu quá yếu. Vui lòng chọn mật khẩu mạnh hơn.',
  authInvalidEmail: 'Địa chỉ email không hợp lệ.',
  authTooManyRequests: 'Quá nhiều lần thử. Vui lòng thử lại sau.',
  authNetworkError: 'Lỗi mạng. Vui lòng kiểm tra kết nối internet.',
  authUserDisabled: 'Tài khoản này đã bị vô hiệu hóa.',
  authGenericError: 'Đã xảy ra lỗi. Vui lòng thử lại.',
  processing: 'Đang xử lý...',
  authErrorTips: 'Gợi ý:',
  authCheckCredentials: 'Kiểm tra lại email và mật khẩu',
  authRemoveSpaces: 'Loại bỏ khoảng trắng thừa',
  authTryPasswordReset: 'Thử đặt lại mật khẩu nếu cần',
  authWaitBeforeRetry: 'Vui lòng đợi 1-2 phút trước khi thử lại',
  authCheckInternet: 'Kiểm tra kết nối internet của bạn',
  authTryLoginInstead: 'Thử đăng nhập thay vì đăng ký, hoặc đặt lại mật khẩu',
 },
};

export const LanguageProvider = ({ children }) => {
 const [language, setLanguage] = useState('vi'); // Default to Vietnamese
 const [isReady, setIsReady] = useState(false);
 const [locationData, setLocationData] = useState({
  countries: [],
  cities: {},
  isLoading: false,
  error: null
 });

 // Load saved language preference
 useEffect(() => {
  const savedLanguage = localStorage.getItem('language');
  console.log('🌐 Saved language from localStorage:', savedLanguage);
  
  if (savedLanguage && translations[savedLanguage]) {
   console.log('🌐 Using saved language:', savedLanguage);
   setLanguage(savedLanguage);
  } else {
   // Force set to Vietnamese if no valid saved language
   console.log('🌐 No valid saved language, forcing Vietnamese');
   setLanguage('vi');
   localStorage.setItem('language', 'vi');
  }
  
  setIsReady(true);
 }, []);

 // Load location data
 useEffect(() => {
  const loadLocationData = async () => {
   try {
    setLocationData(prev => ({ ...prev, isLoading: true, error: null }));
    
    // Load countries
    const countries = await locationService.getCountries();
    
    setLocationData(prev => ({
     ...prev,
     countries,
     isLoading: false
    }));
    
    console.log('🌍 Loaded countries:', countries.length);
   } catch (error) {
    console.error('🌍 Error loading location data:', error);
    setLocationData(prev => ({
     ...prev,
     error: error.message,
     isLoading: false
    }));
   }
  };

  loadLocationData();
 }, []);

 // Update HTML lang attribute
 useEffect(() => {
  document.documentElement.lang = language;
 }, [language]);

 const changeLanguage = useCallback((lang) => {
  if (translations[lang]) {
   setLanguage(lang);
   localStorage.setItem('language', lang);
  }
 }, []);

 const t = useCallback((key) => {
  const result = translations[language][key] || translations.en[key] || key;
  
  // Enhanced debug logging
  if (result === key && !translations[language][key] && !translations.en[key]) {
   console.warn(`🌐 Missing translation for key: "${key}" in language: ${language}`);
   console.warn(`🌐 Available keys in ${language}:`, Object.keys(translations[language] || {}).length);
   console.warn(`🌐 Available keys in en:`, Object.keys(translations.en || {}).length);
  }
  
  // Log successful fallbacks for debugging
  if (result !== key && !translations[language][key] && translations.en[key]) {
   console.log(`🌐 Fallback used for "${key}": ${language} -> en`);
  }
  
  return result;
 }, [language]);

 // Location helper functions
 const getCountries = useCallback(() => {
  return locationData.countries;
 }, [locationData.countries]);

 const getCities = useCallback(async (countryCode) => {
  try {
   // Check if cities are already cached
   if (locationData.cities[countryCode]) {
    return locationData.cities[countryCode];
   }

   // Load cities from service
   const cities = await locationService.getCities(countryCode);
   
   // Update cache
   setLocationData(prev => ({
    ...prev,
    cities: {
     ...prev.cities,
     [countryCode]: cities
    }
   }));

   return cities;
  } catch (error) {
   console.error('🌍 Error loading cities:', error);
   return [];
  }
 }, [locationData.cities]);

 const searchCountries = useCallback(async (query) => {
  try {
   return await locationService.searchCountries(query);
  } catch (error) {
   console.error('🌍 Error searching countries:', error);
   return [];
  }
 }, []);

 const searchCities = useCallback(async (countryCode, query) => {
  try {
   return await locationService.searchCities(countryCode, query);
  } catch (error) {
   console.error('🌍 Error searching cities:', error);
   return [];
  }
 }, []);

 const getCountryByCode = useCallback(async (code) => {
  try {
   return await locationService.getCountryByCode(code);
  } catch (error) {
   console.error('🌍 Error getting country by code:', error);
   return null;
  }
 }, []);

 const getCountryByName = useCallback(async (name) => {
  try {
   return await locationService.getCountryByName(name);
  } catch (error) {
   console.error('🌍 Error getting country by name:', error);
   return null;
  }
 }, []);

 const value = useMemo(() => {
  console.log('🌐 LanguageContext value updated:', {
   language,
   isReady,
   hasTranslations: !!translations[language],
   translationKeys: Object.keys(translations[language] || {}).length,
   locationDataLoaded: locationData.countries.length > 0
  });
  
  return {
   language,
   changeLanguage,
   t,
   translations: translations[language],
   availableLanguages: Object.keys(translations),
   isReady,
   // Location data and functions
   locationData,
   getCountries,
   getCities,
   searchCountries,
   searchCities,
   getCountryByCode,
   getCountryByName,
  };
 }, [language, changeLanguage, t, isReady, locationData, getCountries, getCities, searchCountries, searchCities, getCountryByCode, getCountryByName]);

 return (
  <LanguageContext.Provider value={value}>
   {children}
  </LanguageContext.Provider>
 );
};

LanguageProvider.propTypes = {
 children: PropTypes.node.isRequired,
};
