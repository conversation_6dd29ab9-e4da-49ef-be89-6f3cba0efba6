#!/usr/bin/env node

/**
 * Deploy CORS Fix Script
 * Fixes CORS configuration issues between API Gateway and Community Service
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 Deploying CORS Fix...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

// Check if we're in the right directory
const projectRoot = path.resolve(__dirname, '..');
if (!fs.existsSync(path.join(projectRoot, 'package.json'))) {
  logError('Script must be run from the project root directory');
  process.exit(1);
}

// Check if render CLI is installed
try {
  execSync('render --version', { stdio: 'ignore' });
} catch (error) {
  logError('Render CLI not found. Please install it first:');
  log('npm install -g @render/cli', 'yellow');
  process.exit(1);
}

// Check if user is logged in to Render
try {
  execSync('render whoami', { stdio: 'ignore' });
} catch (error) {
  logError('Not logged in to Render. Please login first:');
  log('render login', 'yellow');
  process.exit(1);
}

async function deployService(serviceName, servicePath) {
  try {
    logStep('1', `Deploying ${serviceName}...`);
    
    // Change to service directory
    const serviceDir = path.join(projectRoot, servicePath);
    if (!fs.existsSync(serviceDir)) {
      throw new Error(`Service directory not found: ${serviceDir}`);
    }
    
    process.chdir(serviceDir);
    
    // Deploy to Render
    logInfo(`Deploying ${serviceName} from ${serviceDir}`);
    execSync('render deploy', { 
      stdio: 'inherit',
      env: { ...process.env, FORCE_COLOR: '1' }
    });
    
    logSuccess(`${serviceName} deployed successfully`);
    return true;
    
  } catch (error) {
    logError(`Failed to deploy ${serviceName}: ${error.message}`);
    return false;
  }
}

async function main() {
  log('🚀 VWork CORS Fix Deployment', 'bright');
  log('This script will deploy the CORS configuration fixes to Render', 'blue');
  
  // Deploy API Gateway first
  const apiGatewaySuccess = await deployService(
    'API Gateway', 
    'services/api-gateway'
  );
  
  if (!apiGatewaySuccess) {
    logError('API Gateway deployment failed. Stopping deployment.');
    process.exit(1);
  }
  
  // Wait a bit before deploying Community Service
  logInfo('Waiting 30 seconds before deploying Community Service...');
  await new Promise(resolve => setTimeout(resolve, 30000));
  
  // Deploy Community Service
  const communityServiceSuccess = await deployService(
    'Community Service', 
    'services/community-service'
  );
  
  if (!communityServiceSuccess) {
    logError('Community Service deployment failed.');
    process.exit(1);
  }
  
  log('\n🎉 CORS Fix Deployment Complete!', 'bright');
  log('\nChanges deployed:', 'cyan');
  log('• Fixed CORS_ORIGINS variable name in API Gateway', 'green');
  log('• Added https://www.nerafus.com to allowed origins', 'green');
  log('• Synchronized CORS configuration between services', 'green');
  
  log('\n📋 Next steps:', 'cyan');
  log('1. Wait 2-3 minutes for services to fully restart', 'yellow');
  log('2. Test the community page at https://nerafus.com/#/community', 'yellow');
  log('3. Check browser console for CORS errors', 'yellow');
  
  log('\n🔍 To monitor deployment status:', 'cyan');
  log('render ps', 'yellow');
  
  log('\n📊 To view logs:', 'cyan');
  log('render logs vwork-api-gateway', 'yellow');
  log('render logs nerafus-community-service', 'yellow');
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:');
  logError(`Promise: ${promise}`);
  logError(`Reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError('Uncaught Exception:');
  logError(error.message);
  logError(error.stack);
  process.exit(1);
});

// Run the deployment
main().catch(error => {
  logError('Deployment failed:');
  logError(error.message);
  process.exit(1);
}); 