#!/usr/bin/env node

/**
 * Cleanup script for NERAFUS platform
 * Removes unused files after refactoring completion
 */

const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..', '..');

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Files to be removed - old scripts that are now replaced by core scripts
 */
const UNUSED_SCRIPTS = [
  // Old build scripts (replaced by scripts/core/build.js)
  'scripts/build-microservices.js',
  'scripts/build-production.bat',
  'scripts/build-production.sh',
  'scripts/test-build-simple.js',
  'scripts/test-production-build.js',
  'scripts/test-production.js',
  
  // Old start scripts (replaced by scripts/core/start.js)
  'scripts/start-all.js',
  'scripts/start-client-only.js',
  'scripts/start-test.js',
  'scripts/start-with-backend.js',
  'scripts/unified-start.js',
  'scripts/stop.js',
  'scripts/stop-new.js',
  
  // Old deployment scripts (replaced by scripts/core/deploy.js)
  'scripts/deploy-and-test.js',
  'scripts/deploy-microservice.js',
  'scripts/deploy-microservices-render.js',
  'scripts/deploy-render-manual.js',
  'scripts/deploy-to-render.js',
  'scripts/prepare-render-deployment.js',
  'scripts/setup-render-env.js',
  'scripts/setup-render-spa-routing.js',
  'scripts/validate-render-spa.js',
  'scripts/test-render-deployment.js',
  
  // Old monitoring scripts (replaced by scripts/core/monitor.js)
  'scripts/health-check.js',
  'scripts/status.js',
  'scripts/quick-status.js',
  'scripts/quick-status-check.js',
  'scripts/deployment-status.js',
  'scripts/final-deployment-status.js',
  'scripts/check-services.js',
  
  // Old testing scripts (replaced by scripts/core/test.js)
  'scripts/test-api-gateway.js',
  'scripts/test-api-gateway-build.js',
  'scripts/test-auth.js',
  'scripts/test-auth-flow.js',
  'scripts/test-cors.js',
  'scripts/test-login-flow.js',
  'scripts/test-microservices.js',
  'scripts/test-npm-start.js',
  'scripts/test-ports.js',
  'scripts/test-services-simple.js',
  'scripts/test-setup.js',
  
  // Old setup scripts (functionality moved to core scripts)
  'scripts/setup-all.js',
  'scripts/setup-env.js',
  'scripts/install.js',
  'scripts/install-dependencies.js',
  'scripts/install-services.js',
  'scripts/migrate-to-workspace.js',
  
  // Database-specific scripts (moved to dedicated database management)
  'scripts/database-backup.js',
  'scripts/database-migration.js',
  'scripts/setup-database-schemas.js',
  'scripts/install-postgresql.bat',
  'scripts/install-postgresql.sh',
  'scripts/setup-postgresql-complete.bat',
  'scripts/test-postgresql-setup.js',
  
  // Firebase-specific scripts (integrated into core scripts)
  'scripts/check-firebase.js',
  'scripts/fix-firebase-domain.js',
  'scripts/open-firebase-console.js',
  
  // Environment and configuration scripts (replaced by scripts/core/config.js)
  'scripts/generate-env-vars.js',
  'scripts/check-render-env-vars.js',
  
  // Debug and fix scripts (no longer needed)
  'scripts/debug-routes.js',
  'scripts/fix-axios-error.js',
  'scripts/fix-gsap-errors.js',
  'scripts/cleanup-ports.bat',
  
  // Audit script (replaced by scripts/core/quality.js)
  'scripts/audit-scripts.js'
];

/**
 * Root-level files to be removed
 */
const UNUSED_ROOT_FILES = [
  // Old setup scripts
  'complete-setup.bat',
  'quick-setup.js',
  'setup-community-postgresql.bat',
  'setup-postgresql.bat',
  'setup-test-environment.js',
  'setup-vwork.bat',
  'setup-vwork.sh',
  'start-vwork.bat',
  'stop-vwork.bat',
  
  // Old test scripts
  'simple-community-test.js',
  'simple-test.js',
  'start-community-service.bat',
  'start-community-test.js',
  'test-api-gateway-community.js',
  'test-authentication-flow.js',
  'test-community-api.js',
  'test-community-routing.js',
  'test-community-service.js',
  'test-frontend-backend-integration.js',
  'test-interactive-features.js',
  'test-running-services.js',
  
  // Old database scripts
  'check-postgresql.js',
  'create-sample-data.js',
  
  // Old environment templates (replaced by centralized config)
  'backend.env.template',
  'postgresql.env.template'
];

/**
 * Documentation files that are outdated (keeping the latest ones)
 */
const OUTDATED_DOCS = [
  // Deployment guides (consolidated into main guides)
  'DEPLOYMENT_CHECKLIST.md',
  'DEPLOYMENT_COMPLETE.md',
  'DEPLOYMENT_GUIDE.md',
  'DEPLOYMENT_SUMMARY.md',
  'DEPLOYMENT_SYSTEM_GUIDE.md',
  'FINAL_DEPLOYMENT_SUMMARY.md',
  'MANUAL_DEPLOYMENT_GUIDE.md',
  'RENDER_DEPLOYMENT_GUIDE.md',
  'RENDER_SPA_DEPLOYMENT_GUIDE.md',
  
  // Community-specific docs (integrated into main docs)
  'COMMUNITY_API_TEST_REPORT.md',
  'COMMUNITY_REDESIGN_SUMMARY.md',
  'COMMUNITY_SERVICE_READY.md',
  'FINAL_COMMUNITY_TEST_SUMMARY.md',
  
  // Setup and debugging docs (replaced by comprehensive guides)
  'AUTH_DEBUG_GUIDE.md',
  'BUTTON_SCALING_IMPROVEMENTS.md',
  'CLEANUP_REPORT.md',
  'MICROSERVICES_ARCHITECTURE_REVIEW.md',
  'POSTGRESQL_SETUP_GUIDE.md',
  'SETUP_INSTRUCTIONS.md',
  'SUCCESS_SUMMARY.md',
  
  // Client-specific docs (consolidated)
  'client/ANIMATION_LAG_FIXES.md',
  'client/ANIMATION_OPTIMIZATION_GUIDE.md',
  'client/ANIMATION_OPTIMIZATION_REPORT.md',
  'client/CODE_QUALITY_REPORT.md',
  'client/DARK_MODE_FIXES.md',
  'client/DEBUG_RULES.md',
  'client/FIREBASE_400_ERROR_FIX.md',
  'client/FIREBASE_AUTH_ERROR_FIXES.md',
  'client/FIREBASE_CONFIG_TEMPLATE.md',
  'client/REFACTOR_PLAN.md',
  
  // Old client files
  'client/fix-warnings.js',
  'client/start-fresh.bat'
];

/**
 * Service-level unused files
 */
const UNUSED_SERVICE_FILES = [
  'services/start-all.js',
  'services/test-api.js'
];

/**
 * Deployment environment files (replaced by centralized config)
 */
const UNUSED_DEPLOYMENT_FILES = [
  'deployment-env-vars/api-gateway.env',
  'deployment-env-vars/auth-service.env',
  'deployment-env-vars/chat-service.env',
  'deployment-env-vars/client.env',
  'deployment-env-vars/job-service.env',
  'deployment-env-vars/project-service.env',
  'deployment-env-vars/search-service.env',
  'deployment-env-vars/user-service.env'
];

/**
 * Remove a file safely
 */
function removeFile(filePath) {
  const fullPath = path.join(ROOT_DIR, filePath);
  
  try {
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      log.success(`Removed: ${filePath}`);
      return true;
    } else {
      log.warn(`File not found: ${filePath}`);
      return false;
    }
  } catch (error) {
    log.error(`Failed to remove ${filePath}: ${error.message}`);
    return false;
  }
}

/**
 * Remove a directory safely
 */
function removeDirectory(dirPath) {
  const fullPath = path.join(ROOT_DIR, dirPath);
  
  try {
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
      log.success(`Removed directory: ${dirPath}`);
      return true;
    } else {
      log.warn(`Directory not found: ${dirPath}`);
      return false;
    }
  } catch (error) {
    log.error(`Failed to remove directory ${dirPath}: ${error.message}`);
    return false;
  }
}

/**
 * Create backup of files before deletion
 */
function createBackup() {
  const backupDir = path.join(ROOT_DIR, 'backup-unused-files');
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  log.info('Creating backup of files to be removed...');
  
  const allFiles = [
    ...UNUSED_SCRIPTS,
    ...UNUSED_ROOT_FILES,
    ...OUTDATED_DOCS,
    ...UNUSED_SERVICE_FILES,
    ...UNUSED_DEPLOYMENT_FILES
  ];
  
  let backedUpCount = 0;
  
  allFiles.forEach(filePath => {
    const fullPath = path.join(ROOT_DIR, filePath);
    
    if (fs.existsSync(fullPath)) {
      const backupPath = path.join(backupDir, filePath);
      const backupDirPath = path.dirname(backupPath);
      
      // Create backup directory structure
      if (!fs.existsSync(backupDirPath)) {
        fs.mkdirSync(backupDirPath, { recursive: true });
      }
      
      // Copy file to backup
      fs.copyFileSync(fullPath, backupPath);
      backedUpCount++;
    }
  });
  
  log.success(`Backed up ${backedUpCount} files to ${backupDir}`);
  return backupDir;
}

/**
 * Main cleanup function
 */
function performCleanup(options = {}) {
  log.info('🧹 Starting NERAFUS platform cleanup...');
  
  let removedCount = 0;
  let totalCount = 0;
  
  // Create backup if requested
  if (options.backup) {
    createBackup();
  }
  
  // Remove unused scripts
  log.info('\n📜 Removing unused scripts...');
  UNUSED_SCRIPTS.forEach(script => {
    totalCount++;
    if (removeFile(script)) {
      removedCount++;
    }
  });
  
  // Remove unused root files
  log.info('\n📁 Removing unused root files...');
  UNUSED_ROOT_FILES.forEach(file => {
    totalCount++;
    if (removeFile(file)) {
      removedCount++;
    }
  });
  
  // Remove outdated documentation
  if (!options.keepDocs) {
    log.info('\n📚 Removing outdated documentation...');
    OUTDATED_DOCS.forEach(doc => {
      totalCount++;
      if (removeFile(doc)) {
        removedCount++;
      }
    });
  }
  
  // Remove unused service files
  log.info('\n🔧 Removing unused service files...');
  UNUSED_SERVICE_FILES.forEach(file => {
    totalCount++;
    if (removeFile(file)) {
      removedCount++;
    }
  });
  
  // Remove deployment environment files
  log.info('\n🚀 Removing old deployment files...');
  UNUSED_DEPLOYMENT_FILES.forEach(file => {
    totalCount++;
    if (removeFile(file)) {
      removedCount++;
    }
  });
  
  // Remove empty deployment-env-vars directory
  const deploymentEnvDir = path.join(ROOT_DIR, 'deployment-env-vars');
  if (fs.existsSync(deploymentEnvDir)) {
    const files = fs.readdirSync(deploymentEnvDir);
    if (files.length === 0) {
      removeDirectory('deployment-env-vars');
    }
  }
  
  // Summary
  console.log('\n📊 Cleanup Summary:');
  console.log('─'.repeat(40));
  console.log(`Total files processed: ${totalCount}`);
  console.log(`Files removed: ${removedCount}`);
  console.log(`Files not found: ${totalCount - removedCount}`);
  
  if (removedCount > 0) {
    log.success(`\n🎉 Cleanup completed! Removed ${removedCount} unused files.`);
    log.info('The platform is now cleaner and more maintainable.');
    
    if (options.backup) {
      log.info('💾 Backup created in case you need to restore any files.');
    }
  } else {
    log.info('\n✨ No files were removed. The platform is already clean!');
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'clean';
  
  const options = {
    backup: args.includes('--backup') || args.includes('-b'),
    keepDocs: args.includes('--keep-docs'),
    dryRun: args.includes('--dry-run'),
    force: args.includes('--force')
  };

  if (command === 'list') {
    // List files that would be removed
    console.log('📋 Files that would be removed:');
    console.log('\n📜 Scripts:');
    UNUSED_SCRIPTS.forEach(file => console.log(`  - ${file}`));
    
    console.log('\n📁 Root files:');
    UNUSED_ROOT_FILES.forEach(file => console.log(`  - ${file}`));
    
    console.log('\n📚 Documentation:');
    OUTDATED_DOCS.forEach(file => console.log(`  - ${file}`));
    
    console.log('\n🔧 Service files:');
    UNUSED_SERVICE_FILES.forEach(file => console.log(`  - ${file}`));
    
    console.log('\n🚀 Deployment files:');
    UNUSED_DEPLOYMENT_FILES.forEach(file => console.log(`  - ${file}`));
    
    return;
  }

  if (command === 'clean' || command === 'cleanup') {
    if (!options.force) {
      console.log('⚠️  This will permanently remove unused files from your project.');
      console.log('💡 Use --backup to create a backup before deletion.');
      console.log('📋 Use "list" command to see what files will be removed.');
      console.log('🔧 Use --force to skip this confirmation.');
      console.log('\nProceed? (y/N)');
      
      // In a real implementation, you'd wait for user input
      // For now, we'll require --force flag
      if (!options.force) {
        log.warn('Cleanup cancelled. Use --force to proceed without confirmation.');
        return;
      }
    }
    
    performCleanup(options);
  } else {
    console.log(`
Usage: node cleanup.js <command> [options]

Commands:
  clean, cleanup   Remove unused files (default)
  list            List files that would be removed

Options:
  --backup, -b    Create backup before deletion
  --keep-docs     Keep outdated documentation files
  --dry-run       Show what would be removed without deleting
  --force         Skip confirmation prompt

Examples:
  node cleanup.js list
  node cleanup.js clean --backup
  node cleanup.js cleanup --force --keep-docs
    `);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  performCleanup,
  UNUSED_SCRIPTS,
  UNUSED_ROOT_FILES,
  OUTDATED_DOCS
};
