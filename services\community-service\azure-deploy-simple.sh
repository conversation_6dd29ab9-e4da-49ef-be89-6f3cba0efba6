#!/bin/bash

# Simple Azure Deployment Script for Community Service
# Usage: ./azure-deploy-simple.sh [app-name] [resource-group]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
APP_NAME=${1:-"vwork-community-service"}
RESOURCE_GROUP=${2:-"vwork-rg"}

echo -e "${BLUE}🚀 Simple Azure Deployment for Community Service${NC}"
echo -e "${BLUE}===============================================${NC}"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check if logged in to Azure
if ! az account show &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Azure. Please login first.${NC}"
    az login
fi

echo -e "${GREEN}✅ Azure CLI is ready${NC}"

# Create deployment package
echo -e "${BLUE}📦 Creating deployment package${NC}"

# Create a temporary directory for deployment
DEPLOY_DIR=".azure-deploy"
rm -rf $DEPLOY_DIR
mkdir $DEPLOY_DIR

# Copy necessary files
echo -e "${BLUE}📋 Copying files...${NC}"
cp -r src $DEPLOY_DIR/
cp -r scripts $DEPLOY_DIR/
cp -r migrations $DEPLOY_DIR/
cp app.js $DEPLOY_DIR/
cp package.json $DEPLOY_DIR/
cp package-lock.json $DEPLOY_DIR/
cp web.config $DEPLOY_DIR/

# Create .deployment file for Azure
cat > $DEPLOY_DIR/.deployment << EOF
[config]
command = npm install && npm start
EOF

# Create deployment script
cat > $DEPLOY_DIR/deploy.cmd << EOF
@if "%SCM_TRACE_LEVEL%" NEQ "4" @echo off

:: ----------------------
:: KUDU Deployment Script
:: Version: 1.0.17
:: ----------------------

:: Prerequisites
:: -------------

:: Verify node.js installed
where node 2>nul >nul
IF %ERRORLEVEL% NEQ 0 (
  echo Missing node.js executable, please install node.js, if already installed make sure it can be reached from current environment.
  goto error
)

:: Setup
:: -----

setlocal enabledelayedexpansion

SET ARTIFACTS=%~dp0%..\artifacts

IF NOT DEFINED DEPLOYMENT_SOURCE (
  SET DEPLOYMENT_SOURCE=%~dp0%.
)

IF NOT DEFINED DEPLOYMENT_TARGET (
  SET DEPLOYMENT_TARGET=%ARTIFACTS%\wwwroot
)

IF NOT DEFINED NEXT_MANIFEST_PATH (
  SET NEXT_MANIFEST_PATH=%ARTIFACTS%\manifest

  IF NOT DEFINED PREVIOUS_MANIFEST_PATH (
    SET PREVIOUS_MANIFEST_PATH=%ARTIFACTS%\manifest
  )
)

IF NOT DEFINED KUDU_SYNC_CMD (
  :: Install kudu sync
  echo Installing Kudu Sync
  call npm install kudusync -g --silent
  IF !ERRORLEVEL! NEQ 0 goto error

  :: Locally just running "kuduSync" would also work
  SET KUDU_SYNC_CMD=%appdata%\npm\kuduSync.cmd
)

::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
:: Deployment
:: ----------

echo Handling node.js deployment.

:: 1. KuduSync
IF /I "%IN_PLACE_DEPLOYMENT%" NEQ "1" (
  call :ExecuteCmd !KUDU_SYNC_CMD! -v 50 -f "%DEPLOYMENT_SOURCE%" -t "%DEPLOYMENT_TARGET%" -n "%NEXT_MANIFEST_PATH%" -p "%PREVIOUS_MANIFEST_PATH%" -i ".git;.hg;.deployment;deploy.cmd"
  IF !ERRORLEVEL! NEQ 0 goto error
)

:: 2. Select node version
call :SelectNodeVersion

:: 3. Install npm packages
IF EXIST "%DEPLOYMENT_TARGET%\package.json" (
  pushd "%DEPLOYMENT_TARGET%"
  call :ExecuteCmd !NPM_CMD! install --production
  IF !ERRORLEVEL! NEQ 0 goto error
  popd
)

::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
goto end

:: Execute command routine that will echo out when error
:ExecuteCmd
setlocal
set _CMD_=%*
call %_CMD_%
if "%ERRORLEVEL%" NEQ "0" echo Failed exitCode=%ERRORLEVEL%, command=%_CMD_%
exit /b %ERRORLEVEL%

:error
endlocal
echo An error has occurred during web site deployment.
call :exitSetErrorLevel
call :exitFromFunction 2>nul

:exitSetErrorLevel
exit /b 1

:exitFromFunction
()

:end
endlocal
echo Finished successfully.

:SelectNodeVersion

IF DEFINED KUDU_SELECT_NODE_VERSION_CMD (
  call !KUDU_SELECT_NODE_VERSION_CMD! "%DEPLOYMENT_TARGET%" "%DEPLOYMENT_TARGET%\package.json"
  IF !ERRORLEVEL! NEQ 0 goto error
  
  IF EXIST "%DEPLOYMENT_TARGET%\bin\node.exe" (
    SET NODE_EXE="%DEPLOYMENT_TARGET%\bin\node.exe"
  ) ELSE (
    SET NODE_EXE=node
  )
) ELSE (
  SET NODE_EXE=node
)

SET NPM_CMD="!NODE_EXE!" "%~dp0\node_modules\npm\bin\npm-cli.js"

goto :EOF
EOF

# Create zip file
echo -e "${BLUE}🗜️  Creating zip file...${NC}"
cd $DEPLOY_DIR
zip -r ../azure-deploy.zip . -x "*.git*" "*.DS_Store*" "node_modules/*"
cd ..

# Deploy to Azure
echo -e "${BLUE}🚀 Deploying to Azure...${NC}"
az webapp deployment source config-zip \
    --resource-group $RESOURCE_GROUP \
    --name $APP_NAME \
    --src azure-deploy.zip

# Clean up
echo -e "${BLUE}🧹 Cleaning up...${NC}"
rm -rf $DEPLOY_DIR
rm -f azure-deploy.zip

# Get the app URL
APP_URL=$(az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "defaultHostName" --output tsv)

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}🌐 Your app is available at: https://$APP_URL${NC}"
echo -e "${GREEN}📊 Azure Portal: https://portal.azure.com/#@/resource/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$APP_NAME${NC}"

# Show next steps
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${BLUE}1. Configure environment variables in Azure Portal${NC}"
echo -e "${BLUE}2. Set up PostgreSQL database${NC}"
echo -e "${BLUE}3. Test the health endpoint: https://$APP_URL/health${NC}"
echo -e "${BLUE}4. Monitor logs: az webapp log tail --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"

# Show current status
echo -e "${BLUE}📊 Current App Status:${NC}"
az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "{name:name, state:state, defaultHostName:defaultHostName, resourceGroup:resourceGroup}" --output table 