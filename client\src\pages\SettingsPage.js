import { useState, useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
import {
 CogIcon,
 ShieldCheckIcon,
 UserIcon,
 CreditCardIcon,
 CheckCircleIcon,
 ExclamationTriangleIcon,
 PhotoIcon,
 PlusIcon,
 TrashIcon,
 PencilIcon,

} from '@heroicons/react/24/outline';
import { ApplePageWrapper } from '../components/apple';
import dataService from '../services/dataService';
import { useLanguage } from '../contexts/LanguageContext';


const SettingsPage = () => {
 const { t } = useLanguage();

 const [settingsData, setSettingsData] = useState(null);
 const [loading, setLoading] = useState(true);
 const [activeTab, setActiveTab] = useState('profile');
 const [saving, setSaving] = useState(false);
 const pageRef = useRef(null);
 const tabsRef = useRef(null);

 // Form states
 const [profileForm, setProfileForm] = useState({});
 const [accountForm, setAccountForm] = useState({});
 const [preferencesForm, setPreferencesForm] = useState({});
 const [newSkill, setNewSkill] = useState('');

 // Load settings data
 useEffect(() => {
  const loadSettings = async () => {
   try {
    setLoading(true);
    const data = await dataService.getSettingsData();
    setSettingsData(data);

    // Initialize forms
    setProfileForm(data.profile);
    setAccountForm(data.account);
    setPreferencesForm(data.preferences);

    // console.log('Settings loaded successfully!');
   } catch (error) {
    // console.error('Failed to load settings');
   } finally {
    setLoading(false);
   }
  };

  loadSettings();
 }, []);

 // GSAP Animations
 useEffect(() => {
  if (!settingsData) return;

  const ctx = gsap.context(() => {
   gsap.fromTo(
    tabsRef.current?.children,
    { opacity: 0, y: 30 },
    {
     opacity: 1,
     y: 0,
     duration: 0.6,
     stagger: 0.1,
     ease: 'power2.out',
     delay: 0.3,
    }
   );
  }, pageRef);

  return () => ctx.revert();
 }, [settingsData, activeTab]);

 // Helper functions
 const updateProfileForm = useCallback((field, value) => {
  setProfileForm(prev => ({ ...prev, [field]: value }));
 }, []);

 const updateAccountForm = useCallback((field, value) => {
  setAccountForm(prev => ({ ...prev, [field]: value }));
 }, []);

 const updatePreferencesForm = useCallback((section, field, value) => {
  setPreferencesForm(prev => ({
   ...prev,
   [section]: { ...prev[section], [field]: value },
  }));
 }, []);

 const handleNewSkillChange = useCallback(e => {
  setNewSkill(e.target.value);
 }, []);

 const addSkill = useCallback(() => {
  if (!newSkill.trim()) return;
  const updatedSkills = [...(profileForm.skills || []), newSkill.trim()];
  updateProfileForm('skills', updatedSkills);
  setNewSkill('');
  // console.log('Skill added to your arsenal!');
 }, [newSkill, profileForm.skills, updateProfileForm]);

 const handleNewSkillKeyPress = useCallback(
  e => {
   if (e.key === 'Enter') {
    addSkill();
   }
  },
  [addSkill]
 );

 const removeSkill = useCallback(
  skillToRemove => {
   const updatedSkills = profileForm.skills.filter(
    skill => skill !== skillToRemove
   );
   updateProfileForm('skills', updatedSkills);
   // console.log('Skill removed from your arsenal!');
  },
  [profileForm.skills, updateProfileForm]
 );

 const saveSettings = useCallback(async () => {
  try {
   setSaving(true);

   const updatedData = {
    profile: profileForm,
    account: accountForm,
    preferences: preferencesForm,
   };

   // await mockDataService.updateSettings(updatedData);
   // console.log('Settings saved successfully!');
  } catch (error) {
   // console.error('Failed to save settings');
  } finally {
   setSaving(false);
  }
 }, [profileForm, accountForm, preferencesForm]);

 const handleTabChange = useCallback(tabId => {
  setActiveTab(tabId);
 }, []);

 const handleDisplayNameChange = useCallback(
  e => {
   updateProfileForm('displayName', e.target.value);
  },
  [updateProfileForm]
 );

 const handleTitleChange = useCallback(
  e => {
   updateProfileForm('title', e.target.value);
  },
  [updateProfileForm]
 );

 const handleBioChange = useCallback(
  e => {
   updateProfileForm('bio', e.target.value);
  },
  [updateProfileForm]
 );

 const handleHourlyRateChange = useCallback(
  e => {
   updateProfileForm('hourlyRate', parseFloat(e.target.value));
  },
  [updateProfileForm]
 );

 const handleWebsiteChange = useCallback(
  e => {
   updateProfileForm('website', e.target.value);
  },
  [updateProfileForm]
 );

 const handleEmailChange = useCallback(
  e => {
   updateAccountForm('email', e.target.value);
  },
  [updateAccountForm]
 );

 const handleLanguageChange = useCallback(
  e => {
   updatePreferencesForm('language', '', e.target.value);
  },
  [updatePreferencesForm]
 );

 const handleTimezoneChange = useCallback(
  e => {
   updatePreferencesForm('timezone', '', e.target.value);
  },
  [updatePreferencesForm]
 );

 const handleNotificationChange = useCallback(
  (key, checked) => {
   updatePreferencesForm('emailNotifications', key, checked);
  },
  [updatePreferencesForm]
 );

 const handleReload = useCallback(() => {
  window.location.reload();
 }, []);

 

 // Tab configuration
 const tabs = [
  { id: 'profile', label: t('userProfile'), icon: UserIcon },
  { id: 'account', label: t('accountSecurity'), icon: ShieldCheckIcon },
  { id: 'preferences', label: t('preferences'), icon: CogIcon },
  { id: 'payment', label: t('paymentMethods'), icon: CreditCardIcon },
 ];

 if (loading) {
  return (
   <ApplePageWrapper variant='page'>
    <div className='min-h-screen flex items-center justify-center'>
     <div className='text-center'>
      <div className='w-16 h-16 flex items-center justify-center mx-auto mb-6 bg-blue-500 rounded-2xl shadow-lg'>
       <CogIcon className='h-8 w-8 text-white animate-spin' />
      </div>
      <h3 className='text-2xl font-bold text-gray-900 mb-2'>
       {t('loadingSettings')}
      </h3>
      <p className='text-lg text-gray-600'>
       {t('preparingConfiguration')}
      </p>
     </div>
    </div>
   </ApplePageWrapper>
  );
 }

 if (!settingsData) {
  return (
   <ApplePageWrapper className='py-16'>
    <div className='text-center'>
     <ExclamationTriangleIcon className='h-16 w-16 text-red-500 mx-auto mb-4' />
     <h3 className='text-2xl font-semibold text-gray-900 mb-4'>
      {t('failedToLoadSettings')}
     </h3>
    </div>
   </ApplePageWrapper>
  );
 }

 return (
  <ApplePageWrapper>
   <div
    ref={pageRef}
    className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12'
   >
    {/* Header */}
    <div className='text-center mb-12'>
     <div className='w-16 h-16 mx-auto mb-6 flex items-center justify-center bg-gray-100 rounded-full'>
      <CogIcon className='h-10 w-10 text-gray-600' />
     </div>
     <h1 className='text-4xl md:text-5xl font-semibold text-gray-900 mb-4'>
      ⚙️ {t('settings')}
     </h1>
     <p className='text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed'>
      {t('configurePreferences')}
     </p>
    </div>

    <div className='grid grid-cols-1 lg:grid-cols-4 gap-8'>
     {/* Sidebar Tabs */}
     <div className='lg:col-span-1'>
      <div ref={tabsRef} className='card p-4 sticky top-4'>
       <nav className='space-y-2'>
        {tabs.map(tab => {
         const Icon = tab.icon;
         return (
          <button
           key={tab.id}
           onClick={() => handleTabChange(tab.id)}
           className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg font-medium text-left transition-all duration-200 ${
            activeTab === tab.id
             ? 'bg-blue-100 text-gray-800 border border-blue-300'
             : 'text-gray-600 hover:bg-gray-100'
           }`}
          >
           <Icon className='h-5 w-5' />
           <span>{tab.label}</span>
          </button>
         );
        })}
       </nav>
      </div>
     </div>

     {/* Main Content */}
     <div className='lg:col-span-3'>
      <div className='card p-8'>
       {/* Profile Settings */}
       {activeTab === 'profile' && (
        <div className='space-y-8'>
         <div>
          <h2 className='font-bold text-2xl font-bold text-gray-800 mb-6'>
           {t('userProfile')}
          </h2>

          {/* Avatar */}
          <div className='flex items-center space-x-6 mb-8'>
           <div className='relative'>
            <img
             src={profileForm.avatar}
             alt={t('language') === 'vi' ? 'Ảnh đại diện' : 'Avatar'}
             className='w-24 h-24 rounded-full border-4 border-blue-300'
            />
            <button className='absolute bottom-0 right-0 p-2 bg-blue-500 rounded-full text-white hover:bg-blue-600 transition-colors'>
             <PhotoIcon className='h-4 w-4' />
            </button>
           </div>
           <div>
            <h3 className='font-medium font-bold text-gray-800'>
             {t('profilePicture')}
            </h3>
            <p className='font-medium text-sm text-gray-600 mb-2'>
             {t('chooseProfilePicture')}
            </p>
            <button className='btn-secondary text-sm px-4 py-2'>
             {t('uploadNewAvatar')}
            </button>
           </div>
          </div>

          {/* Basic Info */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
           <div>
            <label
             htmlFor='displayName'
             className='block font-medium text-sm font-medium text-gray-700 mb-2'
            >
             {t('fullName')}
            </label>
            <input
             id='displayName'
             type='text'
             value={profileForm.displayName || ''}
             onChange={handleDisplayNameChange}
             className='form-input w-full px-4 py-3 font-medium'
             placeholder={t('yourFullName')}
            />
           </div>
           <div>
            <label
             htmlFor='title'
             className='block font-medium text-sm font-medium text-gray-700 mb-2'
            >
             {t('professionalTitle')}
            </label>
            <input
             id='title'
             type='text'
             value={profileForm.title || ''}
             onChange={handleTitleChange}
             className='form-input w-full px-4 py-3 font-medium'
             placeholder={t('yourProfessionalTitle')}
            />
           </div>
          </div>

          {/* Bio */}
          <div className='mb-8'>
           <label
            htmlFor='bio'
            className='block font-medium text-sm font-medium text-gray-700 mb-2'
           >
            {t('bio')}
           </label>
           <textarea
            id='bio'
            value={profileForm.bio || ''}
            onChange={handleBioChange}
            rows={4}
            className='form-input w-full px-4 py-3 font-medium'
            placeholder={t('tellYourStory')}
           />
          </div>

          {/* Skills */}
          <div className='mb-8'>
           <label
            htmlFor='skills'
            className='block font-medium text-sm font-medium text-gray-700 mb-2'
           >
            {t('skillsExpertise')}
           </label>
           <div className='flex flex-wrap gap-2 mb-4'>
            {profileForm.skills?.map((skill, index) => (
             <span
              key={index}
              className='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-gray-800 border border-blue-300'
             >
              {skill}
              <button
               onClick={() => removeSkill(skill)}
               className='ml-2 text-blue-500 hover:text-blue-700'
               aria-label={`${t('remove')} ${skill} ${t('language') === 'vi' ? 'kỹ năng' : 'skill'}`}
              >
               <TrashIcon className='h-3 w-3' />
              </button>
             </span>
            ))}
           </div>
           <div className='flex space-x-2'>
            <input
             id='skills'
             type='text'
             value={newSkill}
             onChange={handleNewSkillChange}
             onKeyPress={handleNewSkillKeyPress}
             className='form-input flex-1 px-4 py-2 font-medium'
             placeholder={t('addSkill')}
            />
            <button
             onClick={addSkill}
             className='btn-medieval px-4 py-2'
             aria-label={t('addSkill')}
            >
             <PlusIcon className='h-4 w-4' />
            </button>
           </div>
          </div>

          {/* Hourly Rate */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
           <div>
            <label
             htmlFor='hourlyRate'
             className='block font-medium text-sm font-medium text-gray-700 mb-2'
            >
             {t('hourlyRate')} (USD)
            </label>
            <input
             id='hourlyRate'
             type='number'
             value={profileForm.hourlyRate || ''}
             onChange={handleHourlyRateChange}
             className='form-input w-full px-4 py-3 font-medium'
             placeholder='45'
            />
           </div>
           <div>
            <label
             htmlFor='website'
             className='block font-medium text-sm font-medium text-gray-700 mb-2'
            >
             {t('websiteURL')}
            </label>
            <input
             id='website'
             type='url'
             value={profileForm.website || ''}
             onChange={handleWebsiteChange}
             className='form-input w-full px-4 py-3 font-medium'
             placeholder={t('language') === 'vi' ? 'https://portfolio-cua-ban.com' : 'https://your-portfolio.com'}
            />
           </div>
          </div>
         </div>
        </div>
       )}

       {/* Account Security */}
       {activeTab === 'account' && (
        <div className='space-y-8'>
         <div>
          <h2 className='font-bold text-2xl font-bold text-gray-800 mb-6'>
           {t('accountSecurity')}
          </h2>

          {/* Email */}
          <div className='mb-6'>
           <label
            htmlFor='email'
            className='block font-medium text-sm font-medium text-gray-700 mb-2'
           >
            {t('emailAddress')}
           </label>
           <div className='flex items-center space-x-2'>
            <input
             id='email'
             type='email'
             value={accountForm.email || ''}
             onChange={handleEmailChange}
             className='form-input flex-1 px-4 py-3 font-medium'
            />
            {accountForm.emailVerified ? (
             <CheckCircleIcon className='h-6 w-6 text-green-600' />
            ) : (
             <ExclamationTriangleIcon className='h-6 w-6 text-blue-600' />
            )}
           </div>
           {!accountForm.emailVerified && (
            <p className='font-medium text-sm text-blue-600 mt-1'>
             {t('emailNotVerified')}{' '}
             <button className='underline'>{t('verifyNow')}</button>
            </p>
           )}
          </div>

          {/* Two-Factor Authentication */}
          <div className='mb-6'>
           <div className='flex items-center justify-between p-4 border border-blue-200 rounded-lg bg-gray-50'>
            <div>
             <h3 className='font-medium font-semibold text-gray-800'>
              {t('twoFactorAuth')}
             </h3>
             <p className='font-medium text-sm text-gray-600'>
              {t('twoFactorAuthDesc')}
             </p>
            </div>
            <button
             className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors ${
              accountForm.twoFactorEnabled
               ? 'bg-green-100 text-green-700 border border-green-300'
               : 'btn-secondary'
             }`}
            >
             {accountForm.twoFactorEnabled
              ? t('enabled')
              : t('enable2FA')}
            </button>
           </div>
          </div>

          {/* Account Info */}
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
           <h3 className='font-medium font-semibold text-gray-800 mb-2'>
            {t('accountInformation')}
           </h3>
           <div className='space-y-2 font-medium text-sm text-gray-600'>
            <p>
             {t('lastLogin')}: {accountForm.lastLogin?.toLocaleString()}
            </p>
            <p>
             {t('accountCreated')}:{' '}
             {accountForm.accountCreated?.toLocaleDateString()}
            </p>
           </div>
          </div>
         </div>
        </div>
       )}

       {/* Preferences */}
       {activeTab === 'preferences' && (
        <div className='space-y-8'>
         <div>
          <h2 className='font-bold text-2xl font-bold text-gray-800 mb-6'>
           {t('guildPreferences')}
          </h2>

          {/* Language & Region */}
          <div className='mb-8'>
           <h3 className='font-medium font-semibold text-gray-800 mb-4'>
            {t('languageRegion')}
           </h3>
           <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
             <label
              htmlFor='language'
              className='block font-medium text-sm font-medium text-gray-700 mb-2'
             >
              {t('language')}
             </label>
             <select
              id='language'
              value={preferencesForm.language || 'en'}
              onChange={handleLanguageChange}
              className='form-input w-full px-4 py-3 font-medium'
             >
              <option value='en'>{t('english') || 'English'}</option>
              <option value='vi'>{t('vietnamese') || 'Tiếng Việt'}</option>
             </select>
            </div>
            <div>
             <label
              htmlFor='timezone'
              className='block font-medium text-sm font-medium text-gray-700 mb-2'
             >
              {t('timezone')}
             </label>
             <select
              id='timezone'
              value={
               preferencesForm.timezone || 'Asia/Ho_Chi_Minh'
              }
              onChange={handleTimezoneChange}
              className='form-input w-full px-4 py-3 font-medium'
             >
              <option value='Asia/Ho_Chi_Minh'>
               {t('hcmCity')} (UTC+7)
              </option>
              <option value='Asia/Bangkok'>
               {t('bangkok') || 'Bangkok'} (UTC+7)
              </option>
              <option value='Asia/Singapore'>
               {t('singapore') || 'Singapore'} (UTC+8)
              </option>
             </select>
            </div>
           </div>
          </div>



          {/* Email Notifications */}
          <div className='mb-8'>
           <h3 className='font-medium font-semibold text-gray-800 mb-4'>
            {t('emailNotifications')}
           </h3>
           <div className='space-y-4'>
            {Object.entries(
             preferencesForm.emailNotifications || {}
            ).map(([key, value]) => (
             <div
              key={key}
              className='flex items-center justify-between p-4 border border-blue-200 rounded-lg bg-gray-50'
             >
              <div>
               <span className='font-medium font-medium text-gray-800 capitalize'>
                {t('language') === 'vi' ? 
                 (key === 'newProjects' ? 'Dự Án Mới' :
                  key === 'bidAccepted' ? 'Đề Xuất Được Chấp Nhận' :
                  key === 'messages' ? 'Tin Nhắn' :
                  key === 'payments' ? 'Thanh Toán' :
                  key === 'newsletter' ? 'Bản Tin' :
                  key.replace(/([A-Z])/g, ' $1')
                 ) : key.replace(/([A-Z])/g, ' $1')
                }
               </span>
               <p className='font-medium text-sm text-gray-600'>
                {key === 'newProjects' &&
                 (t('newProjectsDesc'))}
                {key === 'bidAccepted' &&
                 (t('bidAcceptedDesc'))}
                {key === 'messages' &&
                 (t('messagesNotifDesc'))}
                {key === 'payments' &&
                 (t('paymentsNotifDesc'))}
                {key === 'newsletter' &&
                 (t('newsletterDesc'))}
               </p>
              </div>
              <label
               htmlFor={`notification-${key}`}
               className='sr-only'
              >
               {t('language') === 'vi' ? 'Bật/tắt' : 'Toggle'} {key.replace(/([A-Z])/g, ' $1')}{' '}
               {t('language') === 'vi' ? 'thông báo' : 'notifications'}
              </label>
              <input
               id={`notification-${key}`}
               type='checkbox'
               checked={value}
               onChange={e =>
                handleNotificationChange(key, e.target.checked)
               }
               className='form-checkbox text-medieval-gold-600 border-blue-500 rounded focus:ring-medieval-gold-500'
              />
             </div>
            ))}
           </div>
          </div>
         </div>
        </div>
       )}

       {/* Payment Methods */}
       {activeTab === 'payment' && (
        <div className='space-y-8'>
         <div>
          <h2 className='font-bold text-2xl font-bold text-gray-800 mb-6'>
           {t('paymentMethods')}
          </h2>

          {/* Earnings Overview */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
           <div className='card p-4 text-center'>
            <h3 className='font-medium font-semibold text-gray-800 mb-2'>
             {t('totalEarnings')}
            </h3>
            <p className='font-medium text-2xl font-bold text-medieval-gold-600'>
             $
             {settingsData.payment.earnings.totalEarnings.toLocaleString()}
            </p>
           </div>
           <div className='card p-4 text-center'>
            <h3 className='font-medium font-semibold text-gray-800 mb-2'>
             {t('availableBalance')}
            </h3>
            <p className='font-medium text-2xl font-bold text-green-600'>
             $
             {settingsData.payment.earnings.availableBalance.toLocaleString()}
            </p>
           </div>
           <div className='card p-4 text-center'>
            <h3 className='font-medium font-semibold text-gray-800 mb-2'>
             {t('pendingBalance')}
            </h3>
            <p className='font-medium text-2xl font-bold text-blue-600'>
             $
             {settingsData.payment.earnings.pendingBalance.toLocaleString()}
            </p>
           </div>
          </div>

          {/* Payment Methods */}
          <div className='mb-8'>
           <div className='flex items-center justify-between mb-4'>
            <h3 className='font-medium font-semibold text-gray-800'>
             {t('payoutMethods')}
            </h3>
            <button className='btn-medieval text-sm px-4 py-2'>
             <PlusIcon className='h-4 w-4 mr-2' />
             {t('addMethod')}
            </button>
           </div>

           <div className='space-y-4'>
            {settingsData.payment.methods.map(method => (
             <div
              key={method.id}
              className='flex items-center justify-between p-4 border border-blue-200 rounded-lg bg-gray-50'
             >
              <div className='flex items-center space-x-4'>
               <div
                className={`p-2 rounded-lg ${
                 method.type === 'bank'
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-medieval-purple-100 text-medieval-purple-600'
                }`}
               >
                <CreditCardIcon className='h-6 w-6' />
               </div>
               <div>
                <h4 className='font-medium font-semibold text-gray-800'>
                 {method.name}
                </h4>
                <p className='font-medium text-sm text-gray-600'>
                 {method.type === 'bank'
                  ? `${t('accountNumber')}: ${method.accountNumber}`
                  : method.email}
                </p>
                <div className='flex items-center space-x-2 mt-1'>
                 {method.isDefault ? (
                  <span className='bg-blue-100 text-medieval-gold-700 px-2 py-1 rounded text-xs font-medium'>
                   {t('defaultMethod')}
                  </span>
                 ) : null}
                 {method.verified ? (
                  <span className='bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium'>
                   {t('verified')}
                  </span>
                 ) : null}
                </div>
               </div>
              </div>
              <div className='flex items-center space-x-2'>
               <button
                className='text-gray-600 hover:text-gray-800'
                aria-label={`${t('edit')} ${method.name}`}
               >
                <PencilIcon className='h-4 w-4' />
               </button>
               <button
                className='text-blue-600 hover:text-blue-800'
                aria-label={`${t('delete')} ${method.name}`}
               >
                <TrashIcon className='h-4 w-4' />
               </button>
              </div>
             </div>
            ))}
           </div>
          </div>

          {/* Withdraw Button */}
          <div className='text-center'>
           <button className='btn-medieval px-8 py-3 text-lg'>
            💰 {t('requestWithdrawal')}
           </button>
           <p className='font-medium text-sm text-gray-600 mt-2'>
            {t('minimumWithdrawal')}: $50
           </p>
          </div>
         </div>
        </div>
       )}

       {/* Save Button */}
       <div className='mt-8 pt-6 border-t border-blue-200'>
        <div className='flex justify-end space-x-4'>
         <button
          onClick={handleReload}
          className='btn-secondary px-6 py-3'
         >
          {t('cancel')}
         </button>
         <button
          onClick={saveSettings}
          disabled={saving}
          className='btn-medieval px-8 py-3 flex items-center'
         >
          {saving ? (
           <>
            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2' />
            {t('saving')}...
           </>
          ) : (
           <>
            <CheckCircleIcon className='h-5 w-5 mr-2' />
            {t('saveSettings')}
           </>
          )}
         </button>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </ApplePageWrapper>
 );
};

export default SettingsPage;
