// User status utility functions
export const USER_STATUS = {
 NEW_USER: 'NEW_USER',
 EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
 PROFILE_INCOMPLETE: 'PROFILE_INCOMPLETE',
 ACTIVE: 'ACTIVE'
};

export const PROFILE_STEPS = {
 STEP_1: 'basic_info',
 STEP_2: 'skills',
 STEP_3: 'experience',
 STEP_4: 'portfolio',
 STEP_5: 'preferences'
};

/**
 * Check user status and determine next action
 * @param {Object} user - User object from Firebase/Context
 * @returns {Object} - Status and next action
 */
export const checkUserStatus = (user) => {
 if (!user) {
  return {
   status: null,
   nextAction: 'login',
   message: 'Please login to continue'
  };
 }

 // Check if email is verified
 if (!user.emailVerified) {
  return {
   status: USER_STATUS.EMAIL_NOT_VERIFIED,
   nextAction: 'verify_email',
   message: 'Please verify your email address',
   redirectTo: '/verify-email'
  };
 }

 // Check if user is new (no profile data in Firestore)
 if (isNew<PERSON>ser(user)) {
  return {
   status: USER_STATUS.NEW_USER,
   nextAction: 'complete_profile',
   message: 'Welcome! Please complete your profile',
   redirectTo: '/onboarding'
  };
 }

 // Check if profile is incomplete
 const incompleteStep = getIncompleteProfileStep(user);
 if (incompleteStep) {
  return {
   status: USER_STATUS.PROFILE_INCOMPLETE,
   nextAction: 'complete_profile',
   message: 'Please complete your profile',
   redirectTo: '/onboarding'
  };
 }

 // User is active and ready
 return {
  status: USER_STATUS.ACTIVE,
  nextAction: 'dashboard',
  message: 'Welcome back!',
  redirectTo: '/dashboard'
 };
};

/**
 * Check if user is new (no profile data)
 * @param {Object} user - User object
 * @returns {boolean}
 */
export const isNewUser = (user) => {
 // Check if user has basic profile fields
 const hasBasicInfo = (user.name || user.displayName) && user.userType;
 
 // Also check if profile exists and is not complete
 const hasProfile = user.profile && user.profile.isComplete;
 
 return !hasBasicInfo || !hasProfile;
};

/**
 * Get the next incomplete profile step
 * @param {Object} user - User object
 * @returns {number|null} - Step number or null if complete
 */
export const getIncompleteProfileStep = (user) => {
 // Step 1: Basic Info
 if (!user.name && !user.displayName) {
  return 1;
 }
 
 if (!user.userType) {
  return 1;
 }

 // Step 2: Skills (for freelancers)
 if (user.userType === 'freelancer' && (!user.skills || user.skills.length === 0)) {
  return 2;
 }

 // Step 3: Experience
 if (!user.experience || user.experience.length === 0) {
  return 3;
 }

 // Step 4: Portfolio (for freelancers)
 if (user.userType === 'freelancer' && (!user.portfolio || user.portfolio.length === 0)) {
  return 4;
 }

 // Step 5: Preferences
 if (!user.preferences || !user.preferences.categories) {
  return 5;
 }

 return null; // Profile is complete
};

/**
 * Get profile completion percentage
 * @param {Object} user - User object
 * @returns {number} - Completion percentage (0-100)
 */
export const getProfileCompletionPercentage = (user) => {
 if (!user) return 0;

 let completedSteps = 0;
 const totalSteps = user.userType === 'freelancer' ? 5 : 4; // Skip portfolio for clients

 // Step 1: Basic Info
 if ((user.name || user.displayName) && user.userType) {
  completedSteps++;
 }

 // Step 2: Skills (freelancers only)
 if (user.userType === 'freelancer') {
  if (user.skills && user.skills.length > 0) {
   completedSteps++;
  }
 } else {
  completedSteps++; // Skip for clients
 }

 // Step 3: Experience
 if (user.experience && user.experience.length > 0) {
  completedSteps++;
 }

 // Step 4: Portfolio (freelancers only)
 if (user.userType === 'freelancer') {
  if (user.portfolio && user.portfolio.length > 0) {
   completedSteps++;
  }
 } else {
  completedSteps++; // Skip for clients
 }

 // Step 5: Preferences
 if (user.preferences && user.preferences.categories) {
  completedSteps++;
 }

 return Math.round((completedSteps / totalSteps) * 100);
};

/**
 * Check if user needs to complete profile
 * @param {Object} user - User object
 * @returns {boolean}
 */
export const needsProfileCompletion = (user) => {
 const status = checkUserStatus(user);
 return status.status === USER_STATUS.NEW_USER || status.status === USER_STATUS.PROFILE_INCOMPLETE;
};

/**
 * Check if user needs email verification
 * @param {Object} user - User object
 * @returns {boolean}
 */
export const needsEmailVerification = (user) => {
 const status = checkUserStatus(user);
 return status.status === USER_STATUS.EMAIL_NOT_VERIFIED;
};
