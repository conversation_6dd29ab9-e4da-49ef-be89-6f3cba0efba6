#!/usr/bin/env node

/**
 * Show VWork Platform Architecture
 * Displays current service configuration and ports
 */

const path = require('path');

// Service configurations (updated without auth-service)
const SERVICES = {
  'api-gateway': { 
    port: 8080, 
    priority: 1,
    description: 'API Gateway - Routes requests to services',
    health: '/health'
  },
  'user-service': { 
    port: 3001, 
    priority: 2,
    description: 'User Service - Authentication & user management',
    health: '/health'
  },
  'project-service': { 
    port: 3002, 
    priority: 3,
    description: 'Project Service - Project management & bidding',
    health: '/health'
  },
  'job-service': { 
    port: 3003, 
    priority: 3,
    description: 'Job Service - Job postings & applications',
    health: '/health'
  },
  'chat-service': { 
    port: 3004, 
    priority: 3,
    description: 'Chat Service - Real-time messaging & chatbot',
    health: '/health'
  },
  'community-service': { 
    port: 3005, 
    priority: 3,
    description: 'Community Service - Posts, comments & interactions',
    health: '/health'
  },
  'payment-service': { 
    port: 3006, 
    priority: 3,
    description: 'Payment Service - Payment processing',
    health: '/health'
  },
  'team-service': { 
    port: 3007, 
    priority: 3,
    description: 'Team Service - Team management & collaboration',
    health: '/health'
  }
};

const CLIENT = {
  port: 3000,
  description: 'React Client - Frontend application',
  health: 'http://localhost:3000'
};

/**
 * Display architecture information
 */
function showArchitecture() {
  console.log('🏗️  VWork Platform Architecture');
  console.log('================================\n');
  
  console.log('📋 Service Overview:');
  console.log('   Auth Service has been removed - Firebase handles authentication directly');
  console.log('   User Service now handles user registration and login');
  console.log('   All services use Firebase for authentication\n');
  
  console.log('🔧 Service Configuration:');
  console.log('   Priority 1: Core infrastructure');
  console.log('   Priority 2: Authentication & user management');
  console.log('   Priority 3: Business logic services\n');
  
  console.log('🌐 Service URLs:');
  console.log('   Frontend: http://localhost:3000');
  console.log('   API Gateway: http://localhost:8080');
  console.log('');
  
  // Sort services by priority
  const sortedServices = Object.entries(SERVICES)
    .sort(([,a], [,b]) => a.priority - b.priority);
  
  sortedServices.forEach(([name, config]) => {
    console.log(`   ${name.padEnd(20)} http://localhost:${config.port}${config.health}`);
    console.log(`   ${' '.repeat(20)} ${config.description}`);
    console.log('');
  });
  
  console.log('🔐 Authentication Flow:');
  console.log('   1. Frontend → Firebase Auth (direct)');
  console.log('   2. Frontend → API Gateway → User Service → Firebase');
  console.log('   3. Other services → Firebase (for token verification)');
  console.log('');
  
  console.log('📊 Database Architecture:');
  console.log('   - User Service: PostgreSQL (user data, profiles, skills, reputation)');
  console.log('   - Community Service: PostgreSQL (posts, comments, likes)');
  console.log('   - Team Service: PostgreSQL (teams, members, invitations)');
  console.log('   - Frontend: Firestore (real-time data, caching)');
  console.log('');
  
  console.log('🚀 Quick Start Commands:');
  console.log('   npm run setup          # Install all dependencies');
  console.log('   npm start              # Start all services');
  console.log('   npm run start:dev      # Start in development mode');
  console.log('   npm run start:services # Start only services');
  console.log('   npm stop               # Stop all services');
  console.log('');
  
  console.log('🔍 Health Check URLs:');
  console.log('   API Gateway: http://localhost:8080/health');
  sortedServices.forEach(([name, config]) => {
    console.log(`   ${name.padEnd(20)} http://localhost:${config.port}${config.health}`);
  });
  console.log('');
  
  console.log('📚 Documentation:');
  console.log('   - User Service: services/user-service/README.md');
  console.log('   - Community Service: services/community-service/README.md');
  console.log('   - Team Service: services/team-service/README.md');
  console.log('   - API Gateway: services/api-gateway/README.md');
}

/**
 * Show service details
 */
function showServiceDetails(serviceName) {
  if (!SERVICES[serviceName]) {
    console.log(`❌ Service '${serviceName}' not found`);
    console.log('Available services:', Object.keys(SERVICES).join(', '));
    return;
  }
  
  const service = SERVICES[serviceName];
  const servicePath = path.join(__dirname, '..', 'services', serviceName);
  
  console.log(`🔍 Service Details: ${serviceName}`);
  console.log('='.repeat(50));
  console.log(`Port: ${service.port}`);
  console.log(`Priority: ${service.priority}`);
  console.log(`Description: ${service.description}`);
  console.log(`Health Check: http://localhost:${service.port}${service.health}`);
  console.log(`Service Path: ${servicePath}`);
  console.log('');
  
  // Check if service exists
  const fs = require('fs');
  if (fs.existsSync(servicePath)) {
    console.log('✅ Service directory exists');
    
    // Check package.json
    const packageJsonPath = path.join(servicePath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      console.log(`📦 Package: ${packageJson.name}@${packageJson.version}`);
      console.log(`📝 Description: ${packageJson.description || 'No description'}`);
      
      if (packageJson.scripts) {
        console.log('🔧 Available Scripts:');
        Object.entries(packageJson.scripts).forEach(([name, script]) => {
          console.log(`   ${name}: ${script}`);
        });
      }
    }
  } else {
    console.log('❌ Service directory not found');
  }
}

/**
 * Show environment variables needed
 */
function showEnvironmentVariables() {
  console.log('🔧 Environment Variables:');
  console.log('========================\n');
  
  console.log('📋 Required for all services:');
  console.log('   NODE_ENV=development');
  console.log('   PORT=<service-port>');
  console.log('');
  
  console.log('🔐 Firebase Configuration:');
  console.log('   FIREBASE_PROJECT_ID=vwork-786c3');
  console.log('   FIREBASE_CLIENT_EMAIL=<EMAIL>');
  console.log('   FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n"');
  console.log('');
  
  console.log('🗄️  Database Configuration:');
  console.log('   DATABASE_URL=postgresql://user:pass@localhost:5432/database_name');
  console.log('   DB_HOST=localhost');
  console.log('   DB_PORT=5432');
  console.log('   DB_NAME=database_name');
  console.log('   DB_USER=username');
  console.log('   DB_PASSWORD=password');
  console.log('');
  
  console.log('🌐 CORS Configuration:');
  console.log('   ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080');
  console.log('   CORS_CREDENTIALS=true');
  console.log('');
  
  console.log('📊 Service URLs (for inter-service communication):');
  console.log('   USER_SERVICE_URL=http://localhost:3001');
  console.log('   PROJECT_SERVICE_URL=http://localhost:3002');
  console.log('   JOB_SERVICE_URL=http://localhost:3003');
  console.log('   CHAT_SERVICE_URL=http://localhost:3004');
  console.log('   COMMUNITY_SERVICE_URL=http://localhost:3005');
  console.log('   PAYMENT_SERVICE_URL=http://localhost:3006');
  console.log('   TEAM_SERVICE_URL=http://localhost:3007');
  console.log('');
  
  console.log('📝 Example .env file:');
  console.log('   Copy .env.example to .env in each service directory');
  console.log('   Update values according to your setup');
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'service':
      const serviceName = args[1];
      if (!serviceName) {
        console.log('Usage: node show-architecture.js service <service-name>');
        console.log('Available services:', Object.keys(SERVICES).join(', '));
        return;
      }
      showServiceDetails(serviceName);
      break;
      
    case 'env':
      showEnvironmentVariables();
      break;
      
    case 'help':
      console.log(`
Usage: node show-architecture.js [command]

Commands:
  (no command)    Show architecture overview
  service <name>  Show specific service details
  env            Show environment variables needed
  help           Show this help

Examples:
  node show-architecture.js
  node show-architecture.js service user-service
  node show-architecture.js env
      `);
      break;
      
    default:
      showArchitecture();
      break;
  }
}

if (require.main === module) {
  main();
}

module.exports = { SERVICES, showArchitecture, showServiceDetails }; 