/**
 * Deploy API Gateway with Correct Community Service URL
 * Fix the proxy configuration to point to the correct Community Service
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 Deploying API Gateway with Correct Community Service URL...\n');

// Check git status
console.log('📋 Checking git status...');
try {
  const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' });
  if (gitStatus.trim()) {
    console.log('⚠️ Uncommitted changes detected:');
    console.log(gitStatus);
  } else {
    console.log('✅ Working directory is clean');
  }
} catch (error) {
  console.log('❌ Git status check failed:', error.message);
  process.exit(1);
}

console.log('');

// Check current branch
console.log('🌿 Checking current branch...');
try {
  const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  console.log(`   Current branch: ${currentBranch}`);
  
  if (currentBranch !== 'main') {
    console.log('⚠️ Not on main branch, switching...');
    execSync('git checkout main', { stdio: 'inherit' });
  }
} catch (error) {
  console.log('❌ Branch check failed:', error.message);
  process.exit(1);
}

console.log('');

// Verify API Gateway files exist
console.log('📁 Verifying API Gateway files...');
const requiredFiles = [
  'services/api-gateway/src/index.js',
  'services/api-gateway/package.json',
  'services/api-gateway/render.yaml'
];

for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`);
  } else {
    console.log(`   ❌ ${file} - MISSING`);
    process.exit(1);
  }
}

console.log('');

// Add all changes
console.log('📦 Adding all changes to git...');
try {
  execSync('git add .', { stdio: 'inherit' });
  console.log('✅ All files added to git');
} catch (error) {
  console.log('❌ Git add failed:', error.message);
  process.exit(1);
}

console.log('');

// Commit changes
console.log('💾 Committing API Gateway Community Service URL fix...');
try {
  const commitMessage = `fix: Update API Gateway Community Service URL

- Fixed COMMUNITY_SERVICE_URL in render.yaml
- Updated from https://nerafus-community-service.onrender.com
- Updated to https://vwork-community-service.onrender.com
- This fixes the 500 error when accessing /api/v1/community/stats
- Community Service is now running at the correct URL

Service URLs:
- API Gateway: https://vwork-api-gateway.onrender.com
- Community Service: https://vwork-community-service.onrender.com
- Frontend: https://nerafus.com/#/community`;

  execSync(`git commit -m "${commitMessage}"`, { stdio: 'inherit' });
  console.log('✅ Changes committed');
} catch (error) {
  console.log('❌ Git commit failed:', error.message);
  process.exit(1);
}

console.log('');

// Push to remote
console.log('🚀 Pushing to remote repository...');
try {
  execSync('git push origin main', { stdio: 'inherit' });
  console.log('✅ Changes pushed to remote repository');
} catch (error) {
  console.log('❌ Git push failed:', error.message);
  process.exit(1);
}

console.log('');

// Summary
console.log('🎉 API Gateway Deployment Summary:');
console.log('==================================');
console.log('');
console.log('✅ Configuration Updated:');
console.log('   • COMMUNITY_SERVICE_URL: https://vwork-community-service.onrender.com');
console.log('   • API Gateway proxy configuration fixed');
console.log('   • CORS configuration maintained');
console.log('');
console.log('🔧 Issue Fixed:');
console.log('   • 500 Error on /api/v1/community/stats');
console.log('   • API Gateway was pointing to wrong Community Service URL');
console.log('   • Community Service is now accessible at correct URL');
console.log('');
console.log('⏳ Deployment Process:');
console.log('   1. Render will detect the push and start deployment');
console.log('   2. API Gateway will update with new Community Service URL');
console.log('   3. Proxy requests will be routed to correct service');
console.log('   4. Check Render dashboard for deployment status');
console.log('');
console.log('🔍 Next Steps:');
console.log('   1. Wait 3-5 minutes for deployment to complete');
console.log('   2. Test API Gateway proxy endpoints');
console.log('   3. Verify frontend functionality');
console.log('   4. Check that all community endpoints work');
console.log('');
console.log('📊 Testing URLs:');
console.log('   • API Gateway Health: https://vwork-api-gateway.onrender.com/health');
console.log('   • Community Service Direct: https://vwork-community-service.onrender.com/api/stats');
console.log('   • API Gateway Community Stats: https://vwork-api-gateway.onrender.com/api/v1/community/stats');
console.log('   • Frontend: https://nerafus.com/#/community');
console.log('');
console.log('🎯 Expected Results:');
console.log('   • ✅ No more 500 errors on /api/v1/community/stats');
console.log('   • ✅ Community statistics load correctly');
console.log('   • ✅ Categories endpoint works');
console.log('   • ✅ Posts endpoint works');
console.log('   • ✅ Frontend community page loads successfully');
console.log('');
console.log('✅ Deployment completed successfully!'); 