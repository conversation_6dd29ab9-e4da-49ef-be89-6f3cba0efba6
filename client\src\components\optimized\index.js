/**
 * Optimized component library
 * High-performance, reusable components with built-in optimizations
 */

import React, { memo, forwardRef, useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { usePerformanceMonitor, useDebouncedCallback, useThrottledCallback } from '../../utils/performance';

/**
 * Optimized Button component
 */
export const Button = memo(forwardRef(({
 children,
 onClick,
 disabled = false,
 loading = false,
 variant = 'primary',
 size = 'md',
 className = '',
 ...props
}, ref) => {
 usePerformanceMonitor('Button');
 
 const handleClick = useCallback((e) => {
  if (disabled || loading) return;
  onClick?.(e);
 }, [onClick, disabled, loading]);

 const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
 
 const variantClasses = {
  primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',
  secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',
  danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',
  ghost: 'bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-gray-500'
 };
 
 const sizeClasses = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg'
 };

 const classes = useMemo(() => 
  `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`.trim(),
  [variant, size, disabled, className]
 );

 return (
  <button
   ref={ref}
   className={classes}
   onClick={handleClick}
   disabled={disabled || loading}
   {...props}
  >
   {loading && (
    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
     <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
     <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
    </svg>
   )}
   {children}
  </button>
 );
}));

/**
 * Optimized Input component
 */
export const Input = memo(forwardRef(({
 label,
 error,
 helperText,
 onChange,
 debounceMs = 0,
 className = '',
 ...props
}, ref) => {
 usePerformanceMonitor('Input');
 
 const debouncedOnChange = useDebouncedCallback(onChange, debounceMs);
 
 const handleChange = useCallback((e) => {
  if (debounceMs > 0) {
   debouncedOnChange(e);
  } else {
   onChange?.(e);
  }
 }, [onChange, debouncedOnChange, debounceMs]);

 const inputClasses = useMemo(() => 
  `block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm ${
   error 
    ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' 
    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
  } ${className}`.trim(),
  [error, className]
 );

 return (
  <div className="space-y-1">
   {label && (
    <label className="block text-sm font-medium text-gray-700">
     {label}
    </label>
   )}
   <input
    ref={ref}
    className={inputClasses}
    onChange={handleChange}
    {...props}
   />
   {error && (
    <p className="text-sm text-red-600">{error}</p>
   )}
   {helperText && !error && (
    <p className="text-sm text-gray-500">{helperText}</p>
   )}
  </div>
 );
}));

/**
 * Optimized Modal component
 */
export const Modal = memo(({
 isOpen,
 onClose,
 title,
 children,
 size = 'md',
 closeOnOverlayClick = true,
 showCloseButton = true
}) => {
 usePerformanceMonitor('Modal');
 
 const modalRef = useRef(null);
 
 useEffect(() => {
  if (isOpen) {
   document.body.style.overflow = 'hidden';
   modalRef.current?.focus();
  } else {
   document.body.style.overflow = 'unset';
  }
  
  return () => {
   document.body.style.overflow = 'unset';
  };
 }, [isOpen]);

 const handleOverlayClick = useCallback((e) => {
  if (closeOnOverlayClick && e.target === e.currentTarget) {
   onClose();
  }
 }, [onClose, closeOnOverlayClick]);

 const handleKeyDown = useCallback((e) => {
  if (e.key === 'Escape') {
   onClose();
  }
 }, [onClose]);

 const sizeClasses = {
  sm: 'max-w-md',
  md: 'max-w-lg',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl'
 };

 if (!isOpen) return null;

 return (
  <div 
   className="fixed inset-0 z-50 overflow-y-auto"
   onKeyDown={handleKeyDown}
   tabIndex={-1}
  >
   <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <div 
     className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
     onClick={handleOverlayClick}
    />
    
    <div 
     ref={modalRef}
     className={`inline-block w-full ${sizeClasses[size]} p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg`}
    >
     {(title || showCloseButton) && (
      <div className="flex items-center justify-between mb-4">
       {title && (
        <h3 className="text-lg font-medium text-gray-900">
         {title}
        </h3>
       )}
       {showCloseButton && (
        <button
         onClick={onClose}
         className="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
        >
         <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
         </svg>
        </button>
       )}
      </div>
     )}
     
     <div className="mt-2">
      {children}
     </div>
    </div>
   </div>
  </div>
 );
});

/**
 * Optimized List component with virtualization
 */
export const VirtualizedList = memo(({
 items,
 renderItem,
 itemHeight = 50,
 containerHeight = 400,
 className = ''
}) => {
 usePerformanceMonitor('VirtualizedList');
 
 const [scrollTop, setScrollTop] = useState(0);
 const containerRef = useRef(null);

 const visibleItems = useMemo(() => {
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
   startIndex + Math.ceil(containerHeight / itemHeight) + 1,
   items.length
  );
  
  return items.slice(startIndex, endIndex).map((item, index) => ({
   ...item,
   index: startIndex + index
  }));
 }, [items, itemHeight, containerHeight, scrollTop]);

 const handleScroll = useThrottledCallback((e) => {
  setScrollTop(e.target.scrollTop);
 }, 16); // 60fps

 const totalHeight = items.length * itemHeight;
 const offsetY = Math.floor(scrollTop / itemHeight) * itemHeight;

 return (
  <div
   ref={containerRef}
   className={`overflow-auto ${className}`}
   style={{ height: containerHeight }}
   onScroll={handleScroll}
  >
   <div style={{ height: totalHeight, position: 'relative' }}>
    <div style={{ transform: `translateY(${offsetY}px)` }}>
     {visibleItems.map((item) => (
      <div
       key={item.id || item.index}
       style={{ height: itemHeight }}
       className="flex items-center"
      >
       {renderItem(item, item.index)}
      </div>
     ))}
    </div>
   </div>
  </div>
 );
});

/**
 * Optimized Card component
 */
export const Card = memo(({
 children,
 className = '',
 hover = false,
 padding = 'md',
 ...props
}) => {
 usePerformanceMonitor('Card');

 const paddingClasses = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8'
 };

 const classes = useMemo(() => 
  `bg-white rounded-lg shadow-sm border border-gray-200 ${paddingClasses[padding]} ${
   hover ? 'hover:shadow-md transition-shadow duration-200' : ''
  } ${className}`.trim(),
  [padding, hover, className]
 );

 return (
  <div className={classes} {...props}>
   {children}
  </div>
 );
});

/**
 * Optimized Loading component
 */
export const Loading = memo(({
 size = 'md',
 text = 'Loading...',
 className = ''
}) => {
 const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-8 w-8',
  lg: 'h-12 w-12'
 };

 return (
  <div className={`flex items-center justify-center space-x-2 ${className}`}>
   <svg 
    className={`animate-spin ${sizeClasses[size]} text-blue-600`} 
    fill="none" 
    viewBox="0 0 24 24"
   >
    <circle 
     className="opacity-25" 
     cx="12" 
     cy="12" 
     r="10" 
     stroke="currentColor" 
     strokeWidth="4" 
    />
    <path 
     className="opacity-75" 
     fill="currentColor" 
     d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" 
    />
   </svg>
   {text && <span className="text-gray-600">{text}</span>}
  </div>
 );
});

/**
 * Error Boundary component
 */
export class ErrorBoundary extends React.Component {
 constructor(props) {
  super(props);
  this.state = { hasError: false, error: null };
 }

 static getDerivedStateFromError(error) {
  return { hasError: true, error };
 }

 componentDidCatch(error, errorInfo) {
  console.error('Error caught by boundary:', error, errorInfo);
  
  // Report to error tracking service
  if (window.gtag) {
   window.gtag('event', 'exception', {
    description: error.toString(),
    fatal: false
   });
  }
 }

 render() {
  if (this.state.hasError) {
   return this.props.fallback || (
    <div className="flex items-center justify-center min-h-screen">
     <div className="text-center">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">
       Something went wrong
      </h2>
      <p className="text-gray-600 mb-4">
       We're sorry, but something unexpected happened.
      </p>
      <Button onClick={() => window.location.reload()}>
       Reload Page
      </Button>
     </div>
    </div>
   );
  }

  return this.props.children;
 }
}
