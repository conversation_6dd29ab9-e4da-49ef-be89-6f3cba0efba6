#!/usr/bin/env node

/**
 * Fix Production Database Schema
 * Connects to production database and creates required tables
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logSection = (title) => {
  console.log('\n' + '='.repeat(60));
  log(title, 'bright');
  console.log('='.repeat(60));
};

async function fixProductionDatabase() {
  let pool;
  
  try {
    logSection('🔧 Fixing Production Database Schema');
    
    // Get production database URL from environment variables
    // This should be the same DATABASE_URL used in production
    const databaseUrl = process.env.DATABASE_URL;
    
    if (!databaseUrl) {
      log('❌ DATABASE_URL environment variable not found', 'red');
      log('💡 Please set DATABASE_URL to the production PostgreSQL connection string', 'yellow');
      log('   Example: DATABASE_URL="postgresql://user:pass@host:port/db"', 'cyan');
      process.exit(1);
    }
    
    log(`🔗 Connecting to production database...`, 'blue');
    log(`📍 Database URL: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`, 'cyan');
    
    // Create database connection with production settings
    pool = new Pool({
      connectionString: databaseUrl,
      ssl: { rejectUnauthorized: false }, // Required for most cloud PostgreSQL
      max: 10,
      min: 2,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 5000
    });
    
    const client = await pool.connect();
    log('✅ Connected to production database successfully', 'green');
    
    // Check current database state
    logSection('📋 Checking Current Database State');
    
    const requiredTables = ['users', 'posts', 'comments', 'likes'];
    const existingTables = [];
    const missingTables = [];
    
    for (const table of requiredTables) {
      try {
        const result = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [table]);
        
        if (result.rows[0].exists) {
          existingTables.push(table);
          log(`✅ Table '${table}' exists`, 'green');
        } else {
          missingTables.push(table);
          log(`❌ Table '${table}' missing`, 'red');
        }
      } catch (error) {
        log(`❌ Error checking table '${table}': ${error.message}`, 'red');
        missingTables.push(table);
      }
    }
    
    // Create missing tables
    if (missingTables.length > 0) {
      logSection('🗄️ Creating Missing Database Tables');
      
      try {
        // Read schema file
        const schemaPath = path.join(__dirname, '..', 'migrations', 'clean_schema.sql');
        
        if (!fs.existsSync(schemaPath)) {
          log(`❌ Schema file not found: ${schemaPath}`, 'red');
          process.exit(1);
        }
        
        const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
        log('📄 Executing database migration...', 'blue');
        log(`📝 Schema file: ${schemaPath}`, 'cyan');
        
        // Execute schema migration
        await client.query(schemaSQL);
        log('✅ Database migration completed successfully!', 'green');
        
        // Verify tables were created
        logSection('✅ Verification');
        for (const table of missingTables) {
          const result = await client.query(`
            SELECT EXISTS (
              SELECT FROM information_schema.tables 
              WHERE table_schema = 'public' 
              AND table_name = $1
            );
          `, [table]);
          
          if (result.rows[0].exists) {
            log(`✅ Table '${table}' created successfully`, 'green');
          } else {
            log(`❌ Table '${table}' still missing after migration`, 'red');
          }
        }
        
      } catch (error) {
        log(`❌ Database migration failed: ${error.message}`, 'red');
        console.error('Full error:', error);
        throw error;
      }
    } else {
      log('✅ All required tables already exist', 'green');
    }
    
    // Test basic queries
    logSection('🧪 Testing Database Queries');
    
    try {
      // Test users table
      const userCount = await client.query('SELECT COUNT(*) FROM users');
      log(`👥 Users table: ${userCount.rows[0].count} records`, 'cyan');
      
      // Test posts table
      const postCount = await client.query('SELECT COUNT(*) FROM posts');
      log(`📝 Posts table: ${postCount.rows[0].count} records`, 'cyan');
      
      // Test comments table
      const commentCount = await client.query('SELECT COUNT(*) FROM comments');
      log(`💬 Comments table: ${commentCount.rows[0].count} records`, 'cyan');
      
      // Test likes table
      const likeCount = await client.query('SELECT COUNT(*) FROM likes');
      log(`❤️ Likes table: ${likeCount.rows[0].count} records`, 'cyan');
      
      log('✅ All database queries working correctly', 'green');
      
    } catch (error) {
      log(`❌ Database query test failed: ${error.message}`, 'red');
      throw error;
    }
    
    client.release();
    
    logSection('🎉 Production Database Fix Complete');
    log('✅ Database schema is now properly configured', 'green');
    log('✅ All required tables exist and are accessible', 'green');
    log('✅ Community Service should now work correctly', 'green');
    
    logSection('🚀 Next Steps');
    log('1. 🔄 Test production endpoints:', 'cyan');
    log('   npm run test:production', 'blue');
    log('2. 🌐 Test from browser:', 'cyan');
    log('   Visit your VWork application community page', 'blue');
    log('3. 📊 Monitor service health:', 'cyan');
    log('   curl https://vwork-community-service.onrender.com/health', 'blue');
    
  } catch (error) {
    log(`❌ Production database fix failed: ${error.message}`, 'red');
    console.error('Full error:', error);
    
    logSection('🔧 Troubleshooting');
    log('1. 🔑 Verify DATABASE_URL is correct', 'yellow');
    log('2. 🌐 Check database connectivity', 'yellow');
    log('3. 🗄️ Verify PostgreSQL version compatibility', 'yellow');
    log('4. 📋 Check database permissions', 'yellow');
    
    process.exit(1);
  } finally {
    if (pool) {
      await pool.end();
    }
  }
}

// Run if called directly
if (require.main === module) {
  fixProductionDatabase();
}

module.exports = { fixProductionDatabase }; 