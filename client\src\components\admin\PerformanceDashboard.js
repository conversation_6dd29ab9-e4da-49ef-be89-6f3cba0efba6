/**
 * Performance Dashboard Component
 * Displays real-time autoscaling metrics and system performance
 */

import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import responsiveAutoscaling from '../../services/responsiveAutoscaling';
import pwaService from '../../services/pwaService';

const PerformanceDashboard = () => {
 const [metrics, setMetrics] = useState(null);
 const [loading, setLoading] = useState(true);
 const [error, setError] = useState(null);
 const [autoRefresh, setAutoRefresh] = useState(true);

 // Fetch performance metrics
 const fetchMetrics = async () => {
  try {
   setError(null);
   
   // Fetch backend metrics
   const response = await fetch('/metrics');
   const backendMetrics = await response.json();
   
   // Get frontend metrics
   const frontendMetrics = responsiveAutoscaling.getDeviceInfo();
   
   // Get PWA cache stats
   const cacheStats = await pwaService.getCacheStats();
   
   // Get network status
   const networkStatus = pwaService.getNetworkStatus();
   
   setMetrics({
    backend: backendMetrics,
    frontend: frontendMetrics,
    cache: cacheStats,
    network: networkStatus,
    timestamp: new Date().toISOString()
   });
   
   setLoading(false);
  } catch (err) {
   setError(err.message);
   setLoading(false);
  }
 };

 // Auto-refresh metrics
 useEffect(() => {
  fetchMetrics();
  
  let interval;
  if (autoRefresh) {
   interval = setInterval(fetchMetrics, 5000); // Refresh every 5 seconds
  }
  
  return () => {
   if (interval) clearInterval(interval);
  };
 }, [autoRefresh]);

 // Format percentage
 const formatPercentage = (value) => {
  return `${parseFloat(value).toFixed(1)}%`;
 };

 // Format memory size
 const formatMemory = (bytes) => {
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(1)} MB`;
 };

 // Get status color
 const getStatusColor = (status) => {
  switch (status) {
   case 'healthy':
   case 'success':
    return 'text-green-600 bg-green-100';
   case 'warning':
   case 'degraded':
    return 'text-yellow-600 bg-yellow-100';
   case 'error':
   case 'critical':
    return 'text-red-600 bg-red-100';
   default:
    return 'text-gray-600 bg-gray-100';
  }
 };

 if (loading) {
  return (
   <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
   </div>
  );
 }

 if (error) {
  return (
   <div className="bg-red-50 border border-red-200 rounded-lg p-4">
    <div className="flex">
     <div className="flex-shrink-0">
      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
       <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
      </svg>
     </div>
     <div className="ml-3">
      <h3 className="text-sm font-medium text-red-800">Error loading metrics</h3>
      <p className="text-sm text-red-700 mt-1">{error}</p>
      <button
       onClick={fetchMetrics}
       className="mt-2 text-sm text-red-800 underline hover:text-red-900"
      >
       Try again
      </button>
     </div>
    </div>
   </div>
  );
 }

 return (
  <div className="space-y-6">
   {/* Header */}
   <div className="flex items-center justify-between">
    <h2 className="text-2xl font-bold text-gray-900">Performance Dashboard</h2>
    <div className="flex items-center space-x-4">
     <label className="flex items-center">
      <input
       type="checkbox"
       checked={autoRefresh}
       onChange={(e) => setAutoRefresh(e.target.checked)}
       className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
      />
      <span className="ml-2 text-sm text-gray-700">Auto-refresh</span>
     </label>
     <button
      onClick={fetchMetrics}
      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
     >
      Refresh
     </button>
    </div>
   </div>

   {/* Backend Metrics */}
   <div className="bg-white rounded-lg shadow p-6">
    <h3 className="text-lg font-semibold text-gray-900 mb-4">Backend Performance</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Requests</div>
      <div className="text-2xl font-bold text-gray-900">
       {metrics?.backend?.performance?.requests || 0}
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Avg Response Time</div>
      <div className="text-2xl font-bold text-gray-900">
       {metrics?.backend?.performance?.avgResponseTime || 0}ms
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Error Rate</div>
      <div className="text-2xl font-bold text-gray-900">
       {formatPercentage(metrics?.backend?.performance?.errorRate || 0)}
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Active Connections</div>
      <div className="text-2xl font-bold text-gray-900">
       {metrics?.backend?.performance?.activeConnections || 0}
      </div>
     </div>
    </div>
   </div>

   {/* System Resources */}
   <div className="bg-white rounded-lg shadow p-6">
    <h3 className="text-lg font-semibold text-gray-900 mb-4">System Resources</h3>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Memory Usage</div>
      <div className="text-2xl font-bold text-gray-900">
       {formatMemory(metrics?.backend?.system?.memory?.heapUsed || 0)}
      </div>
      <div className="text-sm text-gray-500">
       / {formatMemory(metrics?.backend?.system?.memory?.heapTotal || 0)}
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Uptime</div>
      <div className="text-2xl font-bold text-gray-900">
       {Math.floor((metrics?.backend?.system?.uptime || 0) / 3600)}h
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Cache Hit Rate</div>
      <div className="text-2xl font-bold text-gray-900">
       {formatPercentage(metrics?.backend?.cache?.hitRate || 0)}
      </div>
     </div>
    </div>
   </div>

   {/* Frontend Performance */}
   <div className="bg-white rounded-lg shadow p-6">
    <h3 className="text-lg font-semibold text-gray-900 mb-4">Frontend Performance</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Performance Level</div>
      <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(metrics?.frontend?.performanceLevel)}`}>
       {metrics?.frontend?.performanceLevel || 'unknown'}
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Frame Rate</div>
      <div className="text-2xl font-bold text-gray-900">
       {metrics?.frontend?.metrics?.frameRate || 0} FPS
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Device Type</div>
      <div className="text-sm text-gray-900">
       {metrics?.frontend?.capabilities?.isMobile ? 'Mobile' :
        metrics?.frontend?.capabilities?.isTablet ? 'Tablet' : 'Desktop'}
      </div>
     </div>
     <div className="bg-gray-50 rounded-lg p-4">
      <div className="text-sm font-medium text-gray-500">Connection</div>
      <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
       metrics?.network?.isOnline ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
      }`}>
       {metrics?.network?.isOnline ? 'Online' : 'Offline'}
      </div>
     </div>
    </div>
   </div>

   {/* Cache Statistics */}
   {metrics?.cache && (
    <div className="bg-white rounded-lg shadow p-6">
     <h3 className="text-lg font-semibold text-gray-900 mb-4">Cache Statistics</h3>
     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {Object.entries(metrics.cache.caches || {}).map(([cacheName, count]) => (
       <div key={cacheName} className="bg-gray-50 rounded-lg p-4">
        <div className="text-sm font-medium text-gray-500">{cacheName}</div>
        <div className="text-2xl font-bold text-gray-900">{count} items</div>
       </div>
      ))}
     </div>
    </div>
   )}

   {/* Last Updated */}
   <div className="text-sm text-gray-500 text-center">
    Last updated: {new Date(metrics?.timestamp).toLocaleString()}
   </div>
  </div>
 );
};

export default PerformanceDashboard;
