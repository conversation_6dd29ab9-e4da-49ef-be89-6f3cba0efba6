import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  CameraIcon, 
  StarIcon, 
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  PlayIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';

// Mock data cho freelancers chuy<PERSON><PERSON>nh Video & Photography
const mockCreators = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    title: 'Video Editor & Motion Graphics',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 145,
    hourlyRate: 35,
    location: '<PERSON><PERSON> Ch<PERSON> Minh',
    skills: ['After Effects', 'Premiere Pro', 'DaVinci Resolve', 'Motion Graphics'],
    description: 'Chuyên gia video editing và motion graphics với 6+ năm kinh nghiệm. <PERSON><PERSON> sản xuất 500+ video.',
    completedJobs: 189,
    responseTime: '2 giờ',
    availability: 'Sẵn sàng',
    equipment: ['Sony A7S III', 'DJI Ronin', 'Adobe Suite'],
    videoTypes: ['Commercial', 'Corporate', 'Social Media'],
    portfolio: [
      'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1492619375914-88005aa9e8fb?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=300&h=200&fit=crop'
    ],
    views: 25600,
    likes: 1890
  },
  {
    id: 2,
    name: 'Trần Thị Linh',
    title: 'Wedding Photographer',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 234,
    hourlyRate: 40,
    location: 'Hà Nội',
    skills: ['Wedding Photography', 'Portrait', 'Lightroom', 'Photoshop'],
    description: 'Nhiếp ảnh gia chuyên chụp cưới với phong cách nghệ thuật. Đã thực hiện 300+ đám cưới.',
    completedJobs: 312,
    responseTime: '1 giờ',
    availability: 'Bận',
    equipment: ['Canon 5D Mark IV', 'Sony A7R V', 'Professional Lighting'],
    videoTypes: ['Wedding', 'Portrait', 'Event'],
    portfolio: [
      'https://images.unsplash.com/photo-1519741497674-611481863552?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1606216794074-735e91aa2c92?w=300&h=200&fit=crop'
    ],
    views: 45200,
    likes: 3450
  },
  {
    id: 3,
    name: 'Lê Minh Đức',
    title: 'Drone Videographer',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 4.7,
    reviewCount: 89,
    hourlyRate: 45,
    location: 'Đà Nẵng',
    skills: ['Drone Operation', 'Aerial Photography', 'Video Production', 'Color Grading'],
    description: 'Chuyên gia quay phim drone và nhiếp ảnh flycam. Có giấy phép bay chính thức.',
    completedJobs: 156,
    responseTime: '3 giờ',
    availability: 'Sẵn sàng',
    equipment: ['DJI Mavic 3 Pro', 'DJI FPV', 'Gimbal Stabilizer'],
    videoTypes: ['Real Estate', 'Tourism', 'Commercial'],
    portfolio: [
      'https://images.unsplash.com/photo-1473773508845-188df298d2d1?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
    ],
    views: 18900,
    likes: 1234
  },
  {
    id: 4,
    name: 'Phạm Thị Mai',
    title: 'Product Photographer',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 167,
    hourlyRate: 30,
    location: 'Hồ Chí Minh',
    skills: ['Product Photography', 'E-commerce', 'Studio Lighting', 'Retouching'],
    description: 'Chuyên gia chụp ảnh sản phẩm cho e-commerce. Phong cách chuyên nghiệp và sáng tạo.',
    completedJobs: 445,
    responseTime: '1.5 giờ',
    availability: 'Sẵn sàng',
    equipment: ['Nikon D850', 'Studio Lighting Kit', 'Macro Lenses'],
    videoTypes: ['E-commerce', 'Fashion', 'Food'],
    portfolio: [
      'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=200&fit=crop'
    ],
    views: 32100,
    likes: 2567
  },
  {
    id: 5,
    name: 'Võ Văn Tùng',
    title: 'Documentary Filmmaker',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 4.6,
    reviewCount: 78,
    hourlyRate: 50,
    location: 'Hà Nội',
    skills: ['Documentary', 'Storytelling', 'Cinematography', 'Sound Design'],
    description: 'Đạo diễn phim tài liệu với nhiều giải thưởng. Chuyên kể chuyện qua hình ảnh.',
    completedJobs: 67,
    responseTime: '4 giờ',
    availability: 'Bận',
    equipment: ['RED Camera', 'Professional Audio', 'Lighting Kit'],
    videoTypes: ['Documentary', 'Corporate', 'Educational'],
    portfolio: [
      'https://images.unsplash.com/photo-1485846234645-a62644f84728?w=300&h=200&fit=crop'
    ],
    views: 15600,
    likes: 987
  },
  {
    id: 6,
    name: 'Hoàng Thị Lan',
    title: 'Social Media Content Creator',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 198,
    hourlyRate: 25,
    location: 'Hồ Chí Minh',
    skills: ['Social Media', 'Short Videos', 'Instagram Reels', 'TikTok'],
    description: 'Content creator chuyên tạo video ngắn cho social media. Hiểu rõ xu hướng và viral content.',
    completedJobs: 356,
    responseTime: '30 phút',
    availability: 'Sẵn sàng',
    equipment: ['iPhone 14 Pro', 'Ring Light', 'Wireless Mic'],
    videoTypes: ['Social Media', 'Marketing', 'Lifestyle'],
    portfolio: [
      'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300&h=200&fit=crop'
    ],
    views: 89400,
    likes: 5670
  }
];

const VideoPage = () => {
  const { t } = useLanguage();
  const [filteredCreators, setFilteredCreators] = useState(mockCreators);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSkill, setSelectedSkill] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [availability, setAvailability] = useState('');

  // Get all unique skills
  const allSkills = [...new Set(mockCreators.flatMap(creator => creator.skills))];

  // Filter function
  useEffect(() => {
    let filtered = mockCreators;

    if (searchQuery) {
      filtered = filtered.filter(creator => 
        creator.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        creator.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        creator.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedSkill) {
      filtered = filtered.filter(creator => creator.skills.includes(selectedSkill));
    }

    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filtered = filtered.filter(creator => {
        if (max) {
          return creator.hourlyRate >= min && creator.hourlyRate <= max;
        } else {
          return creator.hourlyRate >= min;
        }
      });
    }

    if (availability) {
      filtered = filtered.filter(creator => creator.availability === availability);
    }

    setFilteredCreators(filtered);
  }, [searchQuery, selectedSkill, priceRange, availability]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <div className="p-3 bg-indigo-100 rounded-lg">
              <CameraIcon className="h-8 w-8 text-indigo-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Video & Photography</h1>
              <p className="text-lg text-gray-600">Khám phá các chuyên gia video và nhiếp ảnh tài năng</p>
            </div>
          </div>
          
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input
              type="text"
              placeholder="Tìm kiếm creator..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />

            <select
              value={selectedSkill}
              onChange={(e) => setSelectedSkill(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Tất cả kỹ năng</option>
              {allSkills.map(skill => (
                <option key={skill} value={skill}>{skill}</option>
              ))}
            </select>

            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Tất cả mức giá</option>
              <option value="0-30">$0 - $30/giờ</option>
              <option value="30-40">$30 - $40/giờ</option>
              <option value="40-50">$40 - $50/giờ</option>
              <option value="50">$50+/giờ</option>
            </select>

            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Sẵn sàng">Sẵn sàng</option>
              <option value="Bận">Bận</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Tìm thấy <span className="font-semibold">{filteredCreators.length}</span> creator phù hợp
          </p>
        </div>

        {/* Creator Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCreators.map(creator => (
            <div key={creator.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
              {/* Portfolio Preview */}
              {creator.portfolio.length > 0 && (
                <div className="relative h-48 bg-gray-100">
                  <img
                    src={creator.portfolio[0]}
                    alt="Portfolio preview"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                    <PlayIcon className="h-12 w-12 text-white" />
                  </div>
                  <div className="absolute top-2 right-2 flex space-x-2">
                    <div className="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center space-x-1">
                      <PhotoIcon className="h-3 w-3" />
                      <span>{creator.views.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}

              <div className="p-6">
                {/* Creator Header */}
                <div className="flex items-start space-x-4 mb-4">
                  <img
                    src={creator.avatar}
                    alt={creator.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{creator.name}</h3>
                    <p className="text-indigo-600 font-medium">{creator.title}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <div className="flex items-center">
                        <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 ml-1">{creator.rating}</span>
                      </div>
                      <span className="text-gray-400">•</span>
                      <span className="text-sm text-gray-600">{creator.reviewCount} đánh giá</span>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{creator.description}</p>

                {/* Skills */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {creator.skills.slice(0, 3).map(skill => (
                      <span key={skill} className="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                        {skill}
                      </span>
                    ))}
                    {creator.skills.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{creator.skills.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* Equipment & Specialties */}
                <div className="mb-4 text-xs">
                  <p className="text-gray-500 mb-1">Thiết bị: {creator.equipment.slice(0, 2).join(', ')}</p>
                  <p className="text-gray-500">Chuyên môn: {creator.videoTypes.join(', ')}</p>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">{creator.location}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">{creator.responseTime}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">${creator.hourlyRate}/giờ</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`w-2 h-2 rounded-full ${creator.availability === 'Sẵn sàng' ? 'bg-green-400' : 'bg-red-400'}`}></span>
                    <span className="text-gray-600">{creator.availability}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <button className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium">
                    Liên hệ
                  </button>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
                    Portfolio
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {filteredCreators.length > 0 && (
          <div className="text-center mt-8">
            <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              Xem thêm creator
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoPage;
