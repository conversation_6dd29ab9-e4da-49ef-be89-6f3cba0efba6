#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Setting up Firebase configuration...');

// Check if .env file exists
const envPath = path.join(__dirname, '..', '.env');
const envExamplePath = path.join(__dirname, '..', 'env.example');

if (!fs.existsSync(envPath)) {
  console.log('❌ .env file not found');
  
  if (fs.existsSync(envExamplePath)) {
    console.log('📋 Creating .env from env.example...');
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created successfully');
  } else {
    console.log('❌ env.example not found either');
    process.exit(1);
  }
}

// Read and validate .env file
const envContent = fs.readFileSync(envPath, 'utf8');
const requiredVars = [
  'REACT_APP_FIREBASE_API_KEY',
  'REACT_APP_FIREBASE_AUTH_DOMAIN',
  'REACT_APP_FIREBASE_PROJECT_ID',
  'REACT_APP_FIREBASE_STORAGE_BUCKET',
  'REACT_APP_FIREBASE_MESSAGING_SENDER_ID',
  'REACT_APP_FIREBASE_APP_ID'
];

const missingVars = requiredVars.filter(varName => {
  const regex = new RegExp(`^${varName}=`, 'm');
  return !regex.test(envContent);
});

if (missingVars.length > 0) {
  console.log('❌ Missing required environment variables:', missingVars);
  console.log('📋 Please check your .env file');
  process.exit(1);
}

// Check for correct storage bucket
if (!envContent.includes('vwork-786c3.firebasestorage.app')) {
  console.log('⚠️  Incorrect storage bucket detected');
  console.log('📋 Updating storage bucket...');
  
  const updatedContent = envContent.replace(
    /REACT_APP_FIREBASE_STORAGE_BUCKET=.*/g,
    'REACT_APP_FIREBASE_STORAGE_BUCKET=vwork-786c3.firebasestorage.app'
  );
  
  fs.writeFileSync(envPath, updatedContent);
  console.log('✅ Storage bucket updated');
}

console.log('✅ Firebase configuration is ready!');
console.log('🚀 You can now start the development server with: npm start'); 