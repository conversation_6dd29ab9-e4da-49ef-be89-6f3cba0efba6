/**
 * Comment Section Component
 * Facebook-style comment system with expandable form and real-time updates
 */

import { useState, useRef, useEffect } from 'react';
import { 
 ChatBubbleLeftIcon,
 PaperAirplaneIcon,
 FaceSmileIcon,
 PhotoIcon,
 HeartIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { toast } from 'react-hot-toast';
import { commentsApi, likesApi, communityUtils } from '../../../services/communityApiService';
import useAuthGuard from '../../../hooks/useAuthGuard';
import { useNavigate } from 'react-router-dom';

const CommentSection = ({ post, authState }) => {
 const navigate = useNavigate();
 const [isExpanded, setIsExpanded] = useState(false);
 const [comments, setComments] = useState(post.existingComments || []);
 const [newComment, setNewComment] = useState('');
 const [isSubmitting, setIsSubmitting] = useState(false);
 const [likedComments, setLikedComments] = useState(new Set());
 const textareaRef = useRef(null);
 const { guards } = useAuthGuard();

 // Auto-resize textarea
 useEffect(() => {
  if (textareaRef.current) {
   textareaRef.current.style.height = 'auto';
   textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
  }
 }, [newComment]);

 // Focus textarea when expanded
 useEffect(() => {
  if (isExpanded && textareaRef.current) {
   textareaRef.current.focus();
  }
 }, [isExpanded]);

 // Load comments from API when component mounts
 useEffect(() => {
  const loadComments = async () => {
   try {
    const result = await commentsApi.getComments(post.id);
    console.log('🔍 Raw API comments response:', result);
    console.log('🔍 Current Firebase user info:', {
     user: authState.user,
     displayName: authState.user?.displayName,
     email: authState.user?.email,
     uid: authState.user?.uid
    });

    if (result.comments) {
     // Format comments to ensure author.name is available
     const formattedComments = result.comments.map(comment => {
      let authorName = 'Anonymous User';

      // Handle different name formats - prioritize firstName/lastName from backend
      if (comment.author.firstName || comment.author.lastName) {
       authorName = `${comment.author.firstName || ''} ${comment.author.lastName || ''}`.trim();
      } else if (comment.author.name) {
       authorName = comment.author.name;
      } else if (comment.author.username) {
       authorName = comment.author.username.replace('@', '');
      }

      // Fallback for empty names
      if (!authorName || authorName.trim() === '') {
       authorName = 'Anonymous User';
      }

      return {
       ...comment,
       author: {
        ...comment.author,
        name: authorName,
        avatar: comment.author.avatarUrl || comment.author.picture || comment.author.avatar || 'https://via.placeholder.com/32x32/6366f1/ffffff?text=U',
        username: comment.author.username || `@${(comment.author.firstName || 'user').toLowerCase()}`
       },
       timestamp: new Date(comment.createdAt || comment.timestamp)
      };
     });

     console.log('📝 Formatted comments:', formattedComments.map(c => ({
      id: c.id,
      content: c.content.substring(0, 50) + '...',
      author: c.author
     })));

     setComments(formattedComments);

     // Only load like status for comments if user is authenticated
     if (authState.isAuthenticated) {
      const userId = communityUtils.getCurrentUserId(authState.user);
      if (userId) {
       console.log('🔍 Loading like status for comments, userId:', userId);
       const likePromises = formattedComments.map(comment =>
        likesApi.checkLikeStatus(comment.id, 'comment', userId)
         .then(status => ({ commentId: comment.id, liked: status.liked }))
         .catch(() => ({ commentId: comment.id, liked: false }))
       );

       const likeStatuses = await Promise.all(likePromises);
       const likedSet = new Set();
       likeStatuses.forEach(status => {
        if (status.liked) {
         likedSet.add(status.commentId);
        }
       });
       setLikedComments(likedSet);
      }
     } else {
      console.log('⏭️ Skipping comment like status loading - user not authenticated');
      setLikedComments(new Set());
     }
    }
   } catch (error) {
    console.error('Error loading comments:', error);
    // Keep existing comments if API fails
   }
  };

  loadComments();
 }, [post.id, authState.isAuthenticated]);

 const handleCommentClick = guards.comment(() => {
  setIsExpanded(true);
 });

 const handleSubmitComment = guards.comment(async (e) => {
  e.preventDefault();
  
  if (!newComment.trim()) {
   toast.error('Vui lòng nhập nội dung bình luận');
   return;
  }

  setIsSubmitting(true);
  
  try {
   const userId = communityUtils.getCurrentUserId(authState.user);
   const content = newComment.trim();

   // Get user display name for Firebase user
   let displayName = 'Bạn';
   if (authState.user) {
    if (authState.user.displayName) {
     displayName = authState.user.displayName;
    } else if (authState.user.email) {
     displayName = authState.user.email.split('@')[0];
    } else {
     displayName = 'Firebase User';
    }
   }

   // Optimistic update - add comment to UI immediately
   const tempComment = {
    id: `temp-${Date.now()}`,
    content,
    author: {
     id: userId,
     name: displayName,
     avatar: authState.user?.photoURL || 'https://via.placeholder.com/32x32/6366f1/ffffff?text=U',
     username: `@${displayName.toLowerCase().replace(/\s+/g, '')}`
    },
    timestamp: new Date(),
    likes: 0,
    replies: [],
    isTemp: true
   };

   setComments(prev => [...prev, tempComment]);
   setNewComment('');

   // Call API to create comment
   console.log('🔥 Creating comment with correct params:', { postId: post.id, content, parentId: null });
   const result = await commentsApi.createComment(post.id, content, null);

   // Replace temp comment with real comment from API
   setComments(prev =>
    prev.map(comment =>
     comment.id === tempComment.id
      ? {
        id: result.comment.id,
        content: result.comment.content,
        author: {
         id: result.comment.author.id,
         name: result.comment.author.name || `${result.comment.author.firstName || ''} ${result.comment.author.lastName || ''}`.trim() || 'User',
         firstName: result.comment.author.firstName,
         lastName: result.comment.author.lastName,
         avatar: result.comment.author.avatarUrl || result.comment.author.picture || '/api/placeholder/32/32',
         username: result.comment.author.username || `@${(result.comment.author.firstName || 'user').toLowerCase()}`
        },
        timestamp: new Date(result.comment.createdAt),
        likes: result.comment.upvotes || 0,
        replies: [],
        isTemp: false
       }
      : comment
    )
   );

   // Update post comment count
   if (post.onCommentAdded) {
    post.onCommentAdded(post.id);
   }

   toast.success('💬 Đã thêm bình luận!', {
    duration: 2000,
    position: 'bottom-center'
   });

  } catch (error) {
   // Remove temp comment on error
   setComments(prev => prev.filter(comment => !comment.isTemp));
   setNewComment(newComment); // Restore comment text

   console.error('Error submitting comment:', error);
   toast.error(communityUtils.formatApiError(error) || 'Có lỗi xảy ra khi đăng bình luận');
  } finally {
   setIsSubmitting(false);
  }
 });

 const handleKeyDown = (e) => {
  // Ctrl/Cmd + Enter to submit
  if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
   e.preventDefault();
   if (newComment.trim() && !isSubmitting) {
    handleSubmitComment(e);
   }
  }
  // Regular Enter just creates new line (default behavior)
 };

 const handleLikeComment = guards.like(async (commentId) => {
  const isLiked = likedComments.has(commentId);

  // Get user from AuthContext
  const userId = communityUtils.getCurrentUserId(authState.user);

  console.log('🔍 handleLikeComment Debug:', {
   isAuthenticated: authState.isAuthenticated,
   canInteract: authState.canInteract,
   user: authState.user ? { uid: authState.user.uid, email: authState.user.email } : null,
   userId: userId
  });

  // Check if user is properly authenticated
  if (!userId || !authState.isAuthenticated) {
   toast.error('Vui lòng đăng nhập để thích bình luận', {
    duration: 3000,
    position: 'bottom-center'
   });
   return;
  }

  try {
   // Optimistic update
   setLikedComments(prev => {
    const newSet = new Set(prev);
    if (isLiked) {
     newSet.delete(commentId);
    } else {
     newSet.add(commentId);
    }
    return newSet;
   });

   setComments(prev =>
    prev.map(comment => {
     if (comment.id === commentId) {
      const newLikes = isLiked
       ? Math.max(0, comment.likes - 1)
       : comment.likes + 1;
      return { ...comment, likes: newLikes };
     }
     return comment;
    })
   );

   // Call API to persist the change
   const result = await likesApi.toggleLike(commentId, 'comment');

   console.log('✅ Comment like API result:', result);

   // Show feedback toast
   if (result.liked) {
    toast.success('👍 Đã thích bình luận!', {
     duration: 1500,
     position: 'bottom-center'
    });
   }

  } catch (error) {
   // Revert optimistic update on error
   setLikedComments(prev => {
    const newSet = new Set(prev);
    if (isLiked) {
     newSet.add(commentId);
    } else {
     newSet.delete(commentId);
    }
    return newSet;
   });

   setComments(prev =>
    prev.map(comment => {
     if (comment.id === commentId) {
      const revertedLikes = isLiked
       ? comment.likes + 1
       : Math.max(0, comment.likes - 1);
      return { ...comment, likes: revertedLikes };
     }
     return comment;
    })
   );

   console.error('Error toggling comment like:', error);
   toast.error(communityUtils.formatApiError(error), {
    duration: 3000,
    position: 'bottom-center'
   });
  }
 });

 const formatTimeAgo = (timestamp) => {
  const now = new Date();
  const diff = now - new Date(timestamp);
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'Vừa xong';
  if (minutes < 60) return `${minutes} phút`;
  if (hours < 24) return `${hours} giờ`;
  return `${days} ngày`;
 };

 return (
  <div className="border-t border-gray-200 pt-3">
   {/* Comment Button */}
   <button
    onClick={handleCommentClick}
    disabled={!authState.canInteract}
    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 ${
     !authState.canInteract
      ? 'text-gray-400 cursor-not-allowed opacity-60'
      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
    }`}
    title={!authState.canInteract ? 'Đăng nhập để bình luận' : 'Bình luận'}
   >
    <ChatBubbleLeftIcon className="h-5 w-5" />
    <span className="font-medium">{comments.length}</span>
    <span className="text-sm">Bình luận</span>
   </button>

   {/* Expanded Comment Section */}
   {isExpanded && (
    <div className="mt-4 space-y-4">
     {/* Comment Form */}
     <form onSubmit={handleSubmitComment} className="flex space-x-3">
      <img
       src={authState.user?.photoURL || 'https://via.placeholder.com/32x32/6366f1/ffffff?text=U'}
       alt="Your avatar"
       className="w-8 h-8 rounded-full object-cover"
      />
      <div className="flex-1">
       <div className="relative">
        <textarea
         ref={textareaRef}
         value={newComment}
         onChange={(e) => setNewComment(e.target.value)}
         onKeyDown={handleKeyDown}
         placeholder="Viết bình luận... (Ctrl+Enter để gửi)"
         className="w-full px-4 py-2 pr-12 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-gray-900 placeholder-gray-500 min-h-[40px] max-h-32"
         rows="1"
         disabled={isSubmitting}
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
         <button
          type="button"
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          title="Emoji"
         >
          <FaceSmileIcon className="h-4 w-4" />
         </button>
         <button
          type="button"
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          title="Ảnh"
         >
          <PhotoIcon className="h-4 w-4" />
         </button>
         <button
          type="submit"
          disabled={!newComment.trim() || isSubmitting}
          className={`p-1 transition-colors ${
           newComment.trim() && !isSubmitting
            ? 'text-blue-500 hover:text-blue-600'
            : 'text-gray-300 cursor-not-allowed'
          }`}
          title="Gửi"
         >
          <PaperAirplaneIcon className="h-4 w-4" />
         </button>
        </div>
       </div>
      </div>
     </form>

     {/* Comments List */}
     {comments.length > 0 && (
      <div className="space-y-3">
       {comments.map((comment) => (
        <div key={comment.id} className="flex space-x-3">
         <img
          src={comment.author.avatar}
          alt={comment.author.name}
          className="w-8 h-8 rounded-full object-cover"
         />
         <div className="flex-1">
          <div className="bg-gray-100 rounded-2xl px-4 py-2">
           <div className="flex items-center space-x-2 mb-1">
            <span 
             className="font-semibold text-sm text-gray-900 hover:text-blue-600 cursor-pointer transition-colors"
             onClick={() => {
              if (comment.author?.id && comment.author.id !== 'unknown') {
               navigate(`/freelancers/${comment.author.id}`);
              }
             }}
             title="Xem profile"
            >
             {comment.author.name}
            </span>
            <span className="text-xs text-gray-500">
             {formatTimeAgo(comment.timestamp)}
            </span>
           </div>
           <p className="text-sm text-gray-800">
            {comment.content}
           </p>
          </div>
          
          {/* Comment Actions */}
          <div className="flex items-center space-x-4 mt-1 ml-4">
           <button
            onClick={() => handleLikeComment(comment.id)}
            disabled={!authState.canInteract}
            className={`flex items-center space-x-1 text-xs transition-colors ${
             !authState.canInteract
              ? 'text-gray-400 cursor-not-allowed'
              : likedComments.has(comment.id)
              ? 'text-red-500'
              : 'text-gray-500 hover:text-red-500'
            }`}
           >
            {likedComments.has(comment.id) ? (
             <HeartSolidIcon className="h-3 w-3" />
            ) : (
             <HeartIcon className="h-3 w-3" />
            )}
            <span>{comment.likes > 0 ? comment.likes : ''}</span>
           </button>
           
           <button
            disabled={!authState.canInteract}
            className={`text-xs transition-colors ${
             !authState.canInteract
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-500 hover:text-blue-500'
            }`}
           >
            Trả lời
           </button>
          </div>
         </div>
        </div>
       ))}
      </div>
     )}

     {/* Collapse Button */}
     <button
      onClick={() => setIsExpanded(false)}
      className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
     >
      Ẩn bình luận
     </button>
    </div>
   )}
  </div>
 );
};

export default CommentSection;
