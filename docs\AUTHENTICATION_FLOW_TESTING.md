# VWork Authentication Flow Testing Guide

## Overview
This guide provides comprehensive testing procedures for the complete authentication and onboarding flow in VWork, covering both local development and production environments.

## 🧪 Test Scenarios

### 1. New User Registration & Onboarding Flow

#### Test Case 1.1: Freelancer Registration
**Environment**: Local Development & Production
**Steps**:
1. Navigate to `/register`
2. Fill registration form:
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
   - User Type: Select "Freelancer"
   - Name: `Test Freelancer`
3. Submit form
4. Verify email verification page appears
5. Check email for verification link
6. Click verification link
7. **Expected**: Onboarding flow should start automatically

#### Test Case 1.2: Client Registration
**Environment**: Local Development & Production
**Steps**:
1. Navigate to `/register`
2. Fill registration form:
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
   - User Type: Select "Client"
   - Name: `Test Client`
3. Submit form
4. Verify email verification page appears
5. Check email for verification link
6. Click verification link
7. **Expected**: Onboarding flow should start automatically

### 2. Onboarding Flow Testing

#### Test Case 2.1: Freelancer Onboarding
**Prerequisites**: Completed Test Case 1.1
**Steps**:
1. **Welcome Step**:
   - Verify welcome message displays correctly
   - Check freelancer-specific content
   - Test "Get Started" button
   - Test "Skip for Now" button (should complete profile with minimal data)

2. **Basic Info Step**:
   - Fill required fields: Full Name, Bio (min 20 chars), Country, City
   - Test optional fields: Phone, Website
   - Verify form validation
   - Test "Previous" and "Next" buttons

3. **Profile Details Step**:
   - Set hourly rate (min $5)
   - Select availability status
   - Fill experience description
   - Add education (optional)
   - Verify language tags display

4. **Skills Setup Step**:
   - Select skills from categories (min 3 required)
   - Test custom skill addition
   - Verify max 10 skills limit
   - Test skill removal

5. **Preferences Step**:
   - Configure notification settings
   - Select theme preference
   - Choose language (Vietnamese/English)
   - Set currency preference
   - Test "Complete Setup" button

6. **Complete Step**:
   - Verify success animation
   - Check profile summary
   - Test next steps buttons
   - Verify "Go to Dashboard" redirects correctly

#### Test Case 2.2: Client Onboarding
**Prerequisites**: Completed Test Case 1.2
**Steps**:
1. **Welcome Step**: Same as freelancer but with client-specific content
2. **Basic Info Step**: Same as freelancer
3. **Profile Details Step**: 
   - No hourly rate or availability fields
   - Focus on company/project information
4. **Preferences Step**: Same as freelancer
5. **Complete Step**: Client-specific next steps

### 3. Authentication Edge Cases

#### Test Case 3.1: Email Already Verified
**Steps**:
1. Complete registration and email verification
2. Try to access `/verify-email` directly
3. **Expected**: Should redirect to dashboard or onboarding

#### Test Case 3.2: Incomplete Onboarding
**Steps**:
1. Start onboarding flow
2. Close browser/tab during process
3. Login again
4. **Expected**: Should resume onboarding where left off

#### Test Case 3.3: Skip Onboarding
**Steps**:
1. Start onboarding flow
2. Click "Skip for Now" on welcome step
3. **Expected**: Should mark profile as complete and redirect to dashboard

### 4. GSAP Animation Testing

#### Test Case 4.1: Animation Performance
**Steps**:
1. Open browser developer tools
2. Navigate through onboarding steps
3. Monitor console for GSAP errors
4. **Expected**: No "GSAP target null not found" errors

#### Test Case 4.2: Animation Smoothness
**Steps**:
1. Complete onboarding flow
2. Observe step transitions
3. Check progress bar animations
4. **Expected**: Smooth animations without lag

### 5. Internationalization Testing

#### Test Case 5.1: Vietnamese Language
**Steps**:
1. Start onboarding in Vietnamese
2. Verify all text displays in Vietnamese
3. Check for fallback messages
4. **Expected**: No "🌐 Fallback used" messages in console

#### Test Case 5.2: Language Switching
**Steps**:
1. Start onboarding in English
2. Switch to Vietnamese in preferences step
3. Verify immediate language change
4. Complete onboarding
5. **Expected**: Interface updates immediately

### 6. API Integration Testing

#### Test Case 6.1: CORS Configuration
**Environment**: Production
**Steps**:
1. Open browser developer tools
2. Navigate to registration page
3. Submit registration form
4. Monitor network tab
5. **Expected**: No CORS errors in console

#### Test Case 6.2: API Endpoints
**Steps**:
1. Test registration API call
2. Test email verification API call
3. Test profile update API calls
4. **Expected**: All API calls succeed with proper responses

## 🌐 Environment-Specific Testing

### Local Development Testing
**URL**: `http://localhost:3000`
**API**: `http://localhost:8080`

1. Start development servers:
   ```bash
   # Terminal 1: Start API Gateway
   cd services/api-gateway
   npm run dev
   
   # Terminal 2: Start Auth Service
   cd services/auth-service
   npm run dev
   
   # Terminal 3: Start Client
   cd client
   npm start
   ```

2. Run all test cases above
3. Monitor console for errors
4. Verify Firebase integration works

### Production Testing
**URL**: `https://frontend-ce4z.onrender.com`
**API**: `https://vwork-api-gateway.onrender.com`

1. Deploy latest changes to Render
2. Wait for deployment completion
3. Run all test cases above
4. Verify CORS configuration
5. Test email delivery in production

## 🐛 Common Issues & Solutions

### Issue 1: GSAP Null Target Errors
**Symptoms**: Console shows "GSAP target null not found"
**Solution**: Check that safeGSAP utilities are being used

### Issue 2: CORS Errors
**Symptoms**: Network requests fail with CORS error
**Solution**: Verify CORS_ORIGIN environment variable includes frontend URL

### Issue 3: Missing Translations
**Symptoms**: Fallback messages in console
**Solution**: Check LanguageContext.js for missing translation keys

### Issue 4: Onboarding Not Triggering
**Symptoms**: Users go directly to dashboard after email verification
**Solution**: Verify profile.isComplete is set to false for new users

## ✅ Success Criteria

### Development Environment
- [ ] All test cases pass without errors
- [ ] No GSAP animation errors in console
- [ ] Vietnamese translations work correctly
- [ ] Onboarding flow completes successfully
- [ ] API calls succeed locally

### Production Environment
- [ ] All test cases pass without errors
- [ ] No CORS errors in production
- [ ] Email verification works
- [ ] Onboarding flow completes successfully
- [ ] API calls succeed in production
- [ ] Performance is acceptable

## 📊 Performance Benchmarks

### Page Load Times (Target)
- Registration page: < 2 seconds
- Onboarding steps: < 1 second transition
- Dashboard redirect: < 3 seconds

### Animation Performance
- Step transitions: 60 FPS
- Progress bar: Smooth animation
- No dropped frames during onboarding

## 🚀 Deployment Checklist

Before deploying to production:
- [ ] Run automated test script: `node scripts/test-auth-flow.js`
- [ ] Complete manual testing in development
- [ ] Verify environment variables are correct
- [ ] Test email delivery configuration
- [ ] Monitor deployment logs for errors
- [ ] Complete production testing within 30 minutes of deployment
