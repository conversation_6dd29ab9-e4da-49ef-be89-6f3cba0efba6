/**
 * Initialize PostgreSQL Database for Team Service
 */

const { Pool } = require('pg');
const config = require('../src/config/config');
const logger = require('../src/utils/logger');

async function initializeDatabase() {
    let pool = null;
    
    try {
        console.log('🔧 Initializing PostgreSQL database for Team Service...');
        
        // Create connection to PostgreSQL server (not specific database)
        const serverPool = new Pool({
            host: config.DB_HOST,
            port: config.DB_PORT,
            user: config.DB_USER,
            password: config.DB_PASSWORD,
            database: 'postgres', // Connect to default database first
            ssl: config.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
        });

        // Check if database exists
        const dbExistsQuery = `
            SELECT 1 FROM pg_database WHERE datname = $1
        `;
        
        const dbExistsResult = await serverPool.query(dbExistsQuery, [config.DB_NAME]);
        
        if (dbExistsResult.rows.length === 0) {
            console.log(`📦 Creating database: ${config.DB_NAME}`);
            
            // Create database
            await serverPool.query(`CREATE DATABASE ${config.DB_NAME}`);
            console.log(`✅ Database ${config.DB_NAME} created successfully`);
        } else {
            console.log(`✅ Database ${config.DB_NAME} already exists`);
        }

        await serverPool.end();

        // Connect to the specific database
        pool = new Pool({
            host: config.DB_HOST,
            port: config.DB_PORT,
            user: config.DB_USER,
            password: config.DB_PASSWORD,
            database: config.DB_NAME,
            max: config.DB_POOL_MAX,
            min: config.DB_POOL_MIN,
            connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT,
            idleTimeoutMillis: config.DB_IDLE_TIMEOUT,
            ssl: config.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
        });

        // Test connection
        const testResult = await pool.query('SELECT NOW() as current_time');
        console.log(`✅ Connected to database: ${config.DB_NAME}`);
        console.log(`⏰ Server time: ${testResult.rows[0].current_time}`);

        // Check if tables exist
        const tablesExistQuery = `
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('teams', 'team_members', 'team_invitations')
        `;
        
        const tablesResult = await pool.query(tablesExistQuery);
        const existingTables = tablesResult.rows.map(row => row.table_name);
        
        if (existingTables.length === 0) {
            console.log('📋 No team tables found, database needs migration');
            console.log('💡 Run: npm run db:migrate to create tables');
        } else {
            console.log(`✅ Found existing tables: ${existingTables.join(', ')}`);
        }

        await pool.end();
        
        console.log('🎉 PostgreSQL initialization completed successfully!');
        return true;

    } catch (error) {
        console.error('❌ PostgreSQL initialization failed:', error.message);
        
        if (pool) {
            await pool.end();
        }
        
        throw error;
    }
}

// Run if called directly
if (require.main === module) {
    initializeDatabase()
        .then(() => {
            console.log('✅ Database initialization completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Database initialization failed:', error);
            process.exit(1);
        });
}

module.exports = { initializeDatabase }; 