import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../contexts/AuthContext';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaCheckCircle, FaRocket, FaUsers, FaProjectDiagram, FaArrowRight } from 'react-icons/fa';
import confetti from 'canvas-confetti';

const CompleteStep = () => {
 const { user } = useAuth();
 const { completeOnboarding, onboardingData } = useOnboarding();
 const { t } = useLanguage();

 useEffect(() => {
  // Trigger confetti animation
  const timer = setTimeout(() => {
   confetti({
    particleCount: 100,
    spread: 70,
    origin: { y: 0.6 }
   });
  }, 500);

  return () => clearTimeout(timer);
 }, []);

 const nextSteps = user?.userType === 'freelancer' ? [
  {
   icon: FaProjectDiagram,
   title: t('browseProjects'),
   description: t('browseProjectsDesc'),
   action: t('exploreProjects'),
   link: '/projects'
  },
  {
   icon: FaUsers,
   title: t('buildNetwork'),
   description: t('buildNetworkDesc'),
   action: t('joinCommunity'),
       link: '/community'
  },
  {
   icon: FaRocket,
   title: t('optimizeProfile'),
   description: t('optimizeProfileDesc'),
   action: t('editProfile'),
   link: '/profile'
  }
 ] : [
  {
   icon: FaProjectDiagram,
   title: t('postFirstProject'),
   description: t('postFirstProjectDesc'),
   action: t('createProject'),
   link: '/create-project'
  },
  {
   icon: FaUsers,
   title: t('findFreelancers'),
   description: t('findFreelancersDesc'),
   action: t('browseFreelancers'),
   link: '/freelancers'
  },
  {
   icon: FaRocket,
   title: t('exploreFeatures'),
   description: t('exploreFeaturesDesc'),
   action: t('learnMore'),
   link: '/help'
  }
 ];

 const handleGetStarted = async () => {
  try {
   // Ensure onboarding is marked as complete
   const result = await completeOnboarding();
   if (result.success) {
    window.location.href = '/dashboard';
   }
  } catch (error) {
   console.error('Error completing onboarding:', error);
   // Still redirect to dashboard even if there's an error
   window.location.href = '/dashboard';
  }
 };

 const handleQuickAction = (link) => {
  window.location.href = link;
 };

 return (
  <div className="p-8 md:p-12 text-center">
   {/* Success Animation */}
   <motion.div
    className="mb-8"
    initial={{ scale: 0 }}
    animate={{ scale: 1 }}
    transition={{ 
     type: "spring",
     stiffness: 260,
     damping: 20,
     delay: 0.2 
    }}
   >
    <div className="w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
     <FaCheckCircle className="text-4xl text-white" />
    </div>
   </motion.div>

   {/* Header */}
   <motion.div
    className="mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.4 }}
   >
    <h1 className="text-3xl md:text-4xl font-medium font-bold text-gray-800 mb-4">
     {t('welcomeComplete')}! 🎉
    </h1>
    
    <p className="text-lg text-gray-600 font-medium max-w-2xl mx-auto">
     {user?.userType === 'freelancer' 
      ? t('onboardingCompleteFreelancer')
      : t('onboardingCompleteClient')
     }
    </p>
    

   </motion.div>

   {/* Profile Summary */}
   <motion.div
    className="bg-medieval-brown-50 rounded-xl p-6 border border-gray-200 mb-8 max-w-md mx-auto"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.6 }}
   >
    <div className="flex items-center justify-center mb-4">
     <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden">
      {user?.photoURL ? (
       <img 
        src={user.photoURL} 
        alt="Profile" 
        className="w-full h-full object-cover"
       />
      ) : (
       <span className="text-2xl font-medium font-bold text-medieval-gold-600">
        {user?.name?.charAt(0) || user?.displayName?.charAt(0) || 'U'}
       </span>
      )}
     </div>
    </div>
    
    <h3 className="font-medium font-semibold text-gray-800 mb-1">
     {user?.name || user?.displayName}
    </h3>
    
    <p className="text-gray-600 text-sm font-medium mb-2">
     {user?.email}
    </p>
    
    <div className="inline-flex items-center bg-blue-100 text-medieval-gold-800 px-3 py-1 rounded-full text-sm font-medium">
     {user?.userType === 'freelancer' ? t('freelancer') : t('client')}
    </div>
   </motion.div>

   {/* Next Steps */}
   <motion.div
    className="mb-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.8 }}
   >
    <h2 className="text-xl font-medium font-semibold text-gray-800 mb-6">
     {t('nextSteps')}
    </h2>
    
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
     {nextSteps.map((step, index) => (
      <motion.div
       key={index}
       initial={{ opacity: 0, y: 20 }}
       animate={{ opacity: 1, y: 0 }}
       transition={{ delay: 1.0 + index * 0.1 }}
       className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow"
      >
       <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
        <step.icon className="text-xl text-medieval-gold-600" />
       </div>
       
       <h3 className="font-medium font-semibold text-gray-800 mb-2">
        {step.title}
       </h3>
       
       <p className="text-gray-600 text-sm mb-4">
        {step.description}
       </p>
       
       <button
        onClick={() => handleQuickAction(step.link)}
        className="btn-secondary btn-auto-scale text-sm px-4 py-2 font-medium w-full flex items-center justify-center"
       >
        {step.action}
        <FaArrowRight className="ml-2 text-xs" />
       </button>
      </motion.div>
     ))}
    </div>
   </motion.div>

   {/* Main Action */}
   <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 1.4 }}
   >
    <button
     onClick={handleGetStarted}
     className="btn-primary btn-auto-scale px-8 py-3 text-lg font-medium font-semibold flex items-center mx-auto"
    >
     <FaRocket className="mr-2" />
     {t('goToDashboard')}
    </button>
    
    <p className="text-gray-500 text-sm font-medium mt-4">
     {t('onboardingCompleteFooter')}
    </p>
   </motion.div>
  </div>
 );
};

export default CompleteStep;
