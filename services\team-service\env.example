# Team Service Environment Variables
# Copy this file to .env and update with your values

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3008
SERVICE_HOST=localhost

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Option 1: Use DATABASE_URL (recommended for production)
# DATABASE_URL=postgresql://username:password@host:port/database

# Option 2: Use individual variables
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_team_service
DB_USER=vwork_admin
DB_PASSWORD=VWork2024!

# Database Pool Settings
DB_POOL_MAX=20
DB_POOL_MIN=2
DB_CONNECTION_TIMEOUT=5000
DB_IDLE_TIMEOUT=30000

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
CORS_CREDENTIALS=true

# =============================================================================
# SERVICE URLs (for inter-service communication)
# =============================================================================
# These will be auto-generated if not provided, using SERVICE_HOST and default ports
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
CHAT_SERVICE_URL=http://localhost:3005

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ENABLE_COMPRESSION=true
ENABLE_HELMET=true
TRUST_PROXY=true

# =============================================================================
# TEAM SERVICE SPECIFIC
# =============================================================================
# Minimum friendship duration in days before team creation
MIN_FRIENDSHIP_DAYS=5
# Minimum completed projects for team members
MIN_COMPLETED_PROJECTS=10
# Minimum rating for team members
MIN_RATING=4.8
# Maximum team members
MAX_TEAM_MEMBERS=10
# Minimum team members
MIN_TEAM_MEMBERS=3 