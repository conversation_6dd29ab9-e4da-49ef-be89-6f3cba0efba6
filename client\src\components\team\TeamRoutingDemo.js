import React from 'react';
import { Link } from 'react-router-dom';

const TeamRoutingDemo = () => {
 return (
  <div className="p-8 max-w-4xl mx-auto">
   <div className="bg-white rounded-lg shadow-lg p-6">
    <h1 className="text-2xl font-bold mb-4">Team Routing Demo</h1>
    
    <div className="space-y-4">
     <div className="p-4 bg-blue-50 rounded-lg">
      <h2 className="font-semibold text-blue-800">✅ Team Route đã được cấu hình</h2>
      <p className="text-blue-700 mt-2">
       Route <code>/team</code> đã được thêm vào hệ thống routing và sẽ hiển thị Freelancer Team mock.
      </p>
     </div>

     <div className="p-4 bg-green-50 rounded-lg">
      <h2 className="font-semibold text-green-800">🔗 Navigation Links</h2>
      <div className="mt-2 space-y-2">
       <Link 
        to="/team" 
        className="inline-block px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
       >
        Đi đến Team Page
       </Link>
       <br />
       <Link 
        to="/" 
        className="inline-block px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
       >
        Về Homepage
       </Link>
      </div>
     </div>

     <div className="p-4 bg-yellow-50 rounded-lg">
      <h2 className="font-semibold text-yellow-800">📋 Các file đã được cập nhật</h2>
      <ul className="list-disc list-inside text-yellow-700 mt-2 space-y-1">
       <li><code>App.js</code> - Thêm route và import</li>
       <li><code>AppleHeader.js</code> - Cập nhật navigation</li>
       <li><code>router/index.js</code> - Thêm route configuration</li>
       <li><code>AppleTeamPage.js</code> - Component page mới</li>
       <li><code>SPA_ROUTING_FIX_SUMMARY.md</code> - Cập nhật documentation</li>
      </ul>
     </div>

     <div className="p-4 bg-purple-50 rounded-lg">
      <h2 className="font-semibold text-purple-800">🎯 Tính năng Team</h2>
      <ul className="list-disc list-inside text-purple-700 mt-2 space-y-1">
       <li>Tạo team mới với quy trình 4 bước</li>
       <li>Khám phá danh sách teams</li>
       <li>Xem hồ sơ team chi tiết</li>
       <li>Điều kiện thành lập team</li>
       <li>Responsive design</li>
      </ul>
     </div>
    </div>
   </div>
  </div>
 );
};

export default TeamRoutingDemo; 
