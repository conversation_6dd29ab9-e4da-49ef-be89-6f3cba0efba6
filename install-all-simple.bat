@echo off
echo VWork Platform - Install All Dependencies
echo.

echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

echo.
echo Installing Root Project...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Root Project
    pause
    exit /b 1
)

echo.
echo Installing Client...
cd client
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Client
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo Installing Services...
cd services\api-gateway
npm install
cd ..\chat-service
npm install
cd ..\community-service
npm install
cd ..\job-service
npm install
cd ..\payment-service
npm install
cd ..\project-service
npm install
cd ..\team-service
npm install
cd ..\user-service
npm install
cd ..\..

echo.
echo Installation completed successfully!
echo You can now run: npm start
pause 