/**
 * User Sync Service
 * Sync Firebase user data with backend
 */

import { communityApiService } from './communityApiService';

class UserSyncService {
 /**
  * Sync current Firebase user with backend
  */
 async syncFirebaseUser(firebaseUser) {
  if (!firebaseUser || !firebaseUser.uid) {
   console.log('⚠️ No Firebase user to sync');
   return null;
  }

  try {
   console.log('🔄 Syncing Firebase user:', {
    uid: firebaseUser.uid,
    displayName: firebaseUser.displayName,
    email: firebaseUser.email,
    photoURL: firebaseUser.photoURL
   });

   // Get real name from Firebase user
   let displayName = 'User';

   if (firebaseUser.displayName && firebaseUser.displayName.trim()) {
    displayName = firebaseUser.displayName.trim();
    console.log('✅ Using Firebase displayName:', displayName);
   } else if (firebaseUser.email) {
    // Extract name from email if no displayName
    const emailName = firebaseUser.email.split('@')[0];
    displayName = emailName.charAt(0).toUpperCase() + emailName.slice(1);
    console.log('✅ Using email-based name:', displayName);
   }

   // Prepare user data for sync
   const userData = {
    firebaseUID: firebaseUser.uid,
    email: firebaseUser.email,
    displayName: displayName
   };

   // Call sync API
   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/users/sync-firebase`, {
    method: 'POST',
    headers: {
     'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData)
   });

   if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
   }

   const result = await response.json();
   console.log('✅ Firebase user synced:', result);
   return result;

  } catch (error) {
   console.error('❌ Error syncing Firebase user:', error);
   return null;
  }
 }

 /**
  * Update Firebase user profile with real name
  */
 async updateFirebaseProfile(displayName, photoURL = null) {
  try {
   // Import Firebase auth functions
   const { updateProfile } = await import('firebase/auth');
   const { auth } = await import('../config/firebase');

   if (!auth.currentUser) {
    console.log('⚠️ No current Firebase user to update');
    return null;
   }

   console.log('🔄 Updating Firebase profile:', { displayName, photoURL });

   await updateProfile(auth.currentUser, {
    displayName: displayName,
    ...(photoURL && { photoURL: photoURL })
   });

   console.log('✅ Firebase profile updated successfully');

   // Sync with backend after updating Firebase profile
   await this.syncFirebaseUser(auth.currentUser);

   return { success: true, displayName, photoURL };

  } catch (error) {
   console.error('❌ Error updating Firebase profile:', error);
   return null;
  }
 }

 /**
  * Update user profile in backend
  */
 async updateUserProfile(userId, profileData) {
  try {
   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/users/${userId}`, {
    method: 'PUT',
    headers: {
     'Content-Type': 'application/json',
    },
    body: JSON.stringify(profileData)
   });

   if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
   }

   const result = await response.json();
   console.log('✅ User profile updated:', result);
   return result;

  } catch (error) {
   console.error('❌ Error updating user profile:', error);
   return null;
  }
 }

 /**
  * Get user profile from backend
  */
 async getUserProfile(userId) {
  try {
   const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://vwork-api-gateway.onrender.com'}/api/v1/users/${userId}`);
   
   if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
   }

   const result = await response.json();
   return result.user;

  } catch (error) {
   console.error('❌ Error getting user profile:', error);
   return null;
  }
 }
}

export const userSyncService = new UserSyncService();
