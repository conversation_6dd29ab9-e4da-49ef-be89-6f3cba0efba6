/**
 * High-Performance Animation Hook
 * Provides optimized animation management with automatic performance scaling
 */

import { useRef, useEffect, useCallback, useMemo } from 'react';
import { gsap } from 'gsap';
import { animationOptimizer } from '../utils/animationOptimizer';
import responsiveAutoscaling from '../services/responsiveAutoscaling';

export const usePerformantAnimation = (options = {}) => {
 const elementRef = useRef(null);
 const timelineRef = useRef(null);
 const animationsRef = useRef(new Set());
 const cleanupFunctionsRef = useRef([]);
 const isInitializedRef = useRef(false);

 // Memoized performance settings
 const performanceSettings = useMemo(() => {
  return responsiveAutoscaling.getOptimizedGSAPSettings();
 }, [responsiveAutoscaling.currentPerformanceLevel]);

 /**
  * Create optimized entrance animation
  */
 const animateIn = useCallback((targets = elementRef.current, config = {}) => {
  if (!targets) return null;

  const defaultConfig = {
   opacity: 1,
   y: 0,
   scale: 1,
   duration: performanceSettings.duration,
   ease: performanceSettings.ease,
   from: { opacity: 0, y: 30, scale: 0.95 }
  };

  const finalConfig = { ...defaultConfig, ...config };
  
  // Set initial state
  if (finalConfig.from) {
   gsap.set(targets, finalConfig.from);
  }

  // Create optimized animation
  const animation = animationOptimizer.createAnimation(targets, {
   opacity: finalConfig.opacity,
   y: finalConfig.y,
   scale: finalConfig.scale,
   duration: finalConfig.duration,
   ease: finalConfig.ease,
   stagger: finalConfig.stagger,
   delay: finalConfig.delay,
   onComplete: finalConfig.onComplete
  });

  if (animation) {
   animationsRef.current.add(animation);
  }

  return animation;
 }, [performanceSettings]);

 /**
  * Create optimized scale animation
  */
 const animateScale = useCallback((targets = elementRef.current, config = {}) => {
  if (!targets) return null;

  const defaultConfig = {
   scale: 1,
   duration: performanceSettings.duration * 0.6,
   ease: performanceSettings.ease,
   from: { scale: 0 }
  };

  const finalConfig = { ...defaultConfig, ...config };

  if (finalConfig.from) {
   gsap.set(targets, finalConfig.from);
  }

  const animation = animationOptimizer.createAnimation(targets, {
   scale: finalConfig.scale,
   duration: finalConfig.duration,
   ease: finalConfig.ease,
   stagger: finalConfig.stagger,
   delay: finalConfig.delay,
   onComplete: finalConfig.onComplete
  });

  if (animation) {
   animationsRef.current.add(animation);
  }

  return animation;
 }, [performanceSettings]);

 /**
  * Create optimized stagger animation
  */
 const animateStagger = useCallback((targets, config = {}) => {
  if (!targets || targets.length === 0) return null;

  const defaultConfig = {
   opacity: 1,
   y: 0,
   duration: performanceSettings.duration,
   stagger: performanceSettings.stagger,
   ease: performanceSettings.ease,
   from: { opacity: 0, y: 20 }
  };

  const finalConfig = { ...defaultConfig, ...config };

  if (finalConfig.from) {
   gsap.set(targets, finalConfig.from);
  }

  const animation = animationOptimizer.createAnimation(targets, {
   opacity: finalConfig.opacity,
   y: finalConfig.y,
   x: finalConfig.x,
   scale: finalConfig.scale,
   rotation: finalConfig.rotation,
   duration: finalConfig.duration,
   stagger: finalConfig.stagger,
   ease: finalConfig.ease,
   delay: finalConfig.delay,
   onComplete: finalConfig.onComplete
  });

  if (animation) {
   animationsRef.current.add(animation);
  }

  return animation;
 }, [performanceSettings]);

 /**
  * Create optimized timeline
  */
 const createTimeline = useCallback((config = {}) => {
  const timeline = animationOptimizer.createTimeline({
   ...config,
   onComplete: () => {
    if (config.onComplete) config.onComplete();
    // Clean up completed timeline
    if (timelineRef.current === timeline) {
     timelineRef.current = null;
    }
   }
  });

  timelineRef.current = timeline;
  return timeline;
 }, []);

 /**
  * Create hover animation with performance optimization
  */
 const createHoverAnimation = useCallback((element, config = {}) => {
  if (!element || !responsiveAutoscaling.shouldUseFullAnimations()) return null;

  const defaultConfig = {
   scale: 1.05,
   duration: 0.2,
   ease: 'power2.out'
  };

  const finalConfig = { ...defaultConfig, ...config };
  let hoverAnimation = null;

  const handleMouseEnter = () => {
   if (hoverAnimation) hoverAnimation.kill();
   
   hoverAnimation = animationOptimizer.createAnimation(element, {
    scale: finalConfig.scale,
    y: finalConfig.y || -2,
    duration: finalConfig.duration,
    ease: finalConfig.ease
   });
  };

  const handleMouseLeave = () => {
   if (hoverAnimation) hoverAnimation.kill();
   
   hoverAnimation = animationOptimizer.createAnimation(element, {
    scale: 1,
    y: 0,
    duration: finalConfig.duration,
    ease: finalConfig.ease
   });
  };

  element.addEventListener('mouseenter', handleMouseEnter, { passive: true });
  element.addEventListener('mouseleave', handleMouseLeave, { passive: true });

  const cleanup = () => {
   if (hoverAnimation) hoverAnimation.kill();
   element.removeEventListener('mouseenter', handleMouseEnter);
   element.removeEventListener('mouseleave', handleMouseLeave);
  };

  cleanupFunctionsRef.current.push(cleanup);
  return cleanup;
 }, []);

 /**
  * Queue animation for later execution
  */
 const queueAnimation = useCallback((targets, config, priority = 0) => {
  animationOptimizer.queueAnimation(targets, config, priority);
 }, []);

 /**
  * Kill all animations associated with this hook
  */
 const killAnimations = useCallback(() => {
  // Kill individual animations
  animationsRef.current.forEach(animation => {
   if (animation && animation.kill) {
    animation.kill();
   }
  });
  animationsRef.current.clear();

  // Kill timeline
  if (timelineRef.current) {
   timelineRef.current.kill();
   timelineRef.current = null;
  }

  // Run cleanup functions
  cleanupFunctionsRef.current.forEach(cleanup => {
   if (typeof cleanup === 'function') {
    cleanup();
   }
  });
  cleanupFunctionsRef.current = [];
 }, []);

 /**
  * Performance level change handler
  */
 useEffect(() => {
  const handlePerformanceChange = (event) => {
   const { level } = event.detail;
   
   // If performance dropped significantly, kill complex animations
   if (level === 'minimal' || level === 'low') {
    animationsRef.current.forEach(animation => {
     if (animation && animation.vars) {
      // Kill animations with complex properties
      const hasComplexProps = animation.vars.rotationX || 
                  animation.vars.rotationY || 
                  animation.vars.morphSVG ||
                  animation.vars.motionPath;
      
      if (hasComplexProps) {
       animation.kill();
       animationsRef.current.delete(animation);
      }
     }
    });
   }
  };

  window.addEventListener('performanceLevelChange', handlePerformanceChange);
  
  return () => {
   window.removeEventListener('performanceLevelChange', handlePerformanceChange);
  };
 }, []);

 /**
  * Cleanup on unmount
  */
 useEffect(() => {
  return () => {
   killAnimations();
  };
 }, [killAnimations]);

 return {
  elementRef,
  animateIn,
  animateScale,
  animateStagger,
  createTimeline,
  createHoverAnimation,
  queueAnimation,
  killAnimations,
  performanceSettings,
  isHighPerformance: responsiveAutoscaling.currentPerformanceLevel === 'high',
  shouldUseFullAnimations: responsiveAutoscaling.shouldUseFullAnimations()
 };
};

export default usePerformantAnimation;
