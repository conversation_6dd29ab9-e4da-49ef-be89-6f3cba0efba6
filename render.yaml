services:
  # API Gateway (Main entry point)
  - type: web
    runtime: node
    name: vwork-gateway
    region: oregon
    branch: main
    rootDir: services/api-gateway
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      # Auth Service removed - using Firebase directly
      - key: USER_SERVICE_URL
        value: https://vwork-user-service.onrender.com
      - key: PROJECT_SERVICE_URL
        value: https://vwork-project-service.onrender.com
      - key: JOB_SERVICE_URL
        value: https://vwork-job-service.onrender.com
      - key: CHAT_SERVICE_URL
        value: https://vwork-chat-service.onrender.com
      - key: SEARCH_SERVICE_URL
        value: https://vwork-search-service.onrender.com
      - key: COMMUNITY_SERVICE_URL
        value: https://vwork-community-service.onrender.com
      - key: CORS_ORIGIN
        value: https://frontend-ce4z.onrender.com,https://vwork-client.onrender.com,https://vwork-api-gateway.onrender.com,https://nerafus.com
    plan: free

  # Community Service
  - type: web
    runtime: node
    name: vwork-community-service
    region: oregon
    branch: main
    rootDir: services/community-service
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: DB_TYPE
        value: postgresql
      - key: FIREBASE_PROJECT_ID
        sync: false
      - key: FIREBASE_CLIENT_EMAIL
        sync: false
      - key: FIREBASE_PRIVATE_KEY
        sync: false
      - key: DATABASE_URL
        sync: false
      - key: ALLOWED_ORIGINS
        value: https://nerafus-client.onrender.com,https://vwork-api-gateway.onrender.com,https://nerafus.com
      - key: CORS_ORIGIN
        value: https://nerafus-client.onrender.com,https://vwork-api-gateway.onrender.com,https://nerafus.com
      - key: CORS_CREDENTIALS
        value: "true"
      - key: LOG_LEVEL
        value: info
      - key: LOG_FORMAT
        value: json
      - key: ENABLE_COMPRESSION
        value: "true"
      - key: ENABLE_HELMET
        value: "true"
      - key: TRUST_PROXY
        value: "true"
      - key: DB_POOL_MAX
        value: "10"
      - key: DB_POOL_MIN
        value: "2"
      - key: DB_CONNECTION_TIMEOUT
        value: "5000"
      - key: DB_IDLE_TIMEOUT
        value: "30000"
    plan: free

  # React Client
  - type: web
    runtime: static
    name: nerafus-client
    region: oregon
    branch: main
    rootDir: client
    buildCommand: npm ci && npm run build
    staticPublishPath: build
    # SPA routing configuration - redirect all routes to index.html
    routes:
      - type: rewrite
        source: "/*"
        destination: "/index.html"
    # Security and caching headers
    headers:
      - path: "/*"
        name: "X-Content-Type-Options"
        value: "nosniff"
      - path: "/*"
        name: "X-Frame-Options"
        value: "DENY"
      - path: "/*"
        name: "X-XSS-Protection"
        value: "1; mode=block"
      - path: "/*"
        name: "Referrer-Policy"
        value: "strict-origin-when-cross-origin"
      - path: "/static/*"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
      - path: "/*.html"
        name: "Cache-Control"
        value: "no-cache, no-store, must-revalidate"
      - path: "/*.js"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
      - path: "/*.css"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
      - path: "/*.png"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
      - path: "/*.jpg"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
      - path: "/*.svg"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
      - path: "/*.ico"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
    envVars:
      - key: NODE_ENV
        value: production
      - key: GENERATE_SOURCEMAP
        value: "false"
      - key: REACT_APP_API_URL
        value: https://vwork-api-gateway.onrender.com
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: "1050922072615"
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02
    plan: free
