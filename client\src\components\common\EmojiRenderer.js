import { useEffect, useRef } from 'react';
import twemoji from 'twemoji';

const EmojiRenderer = ({
 text,
 className = '',
 size = '1.2em',
 fallback = null,
 onLoad = null,
}) => {
 const containerRef = useRef(null);

 useEffect(() => {
  if (containerRef.current && text) {
   // Parse emojis using Twemoji
   twemoji.parse(containerRef.current, {
    folder: 'svg',
    ext: '.svg',
    base: 'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/',
    callback: function (icon, options, variant) {
     // Custom callback to handle loading
     return ''.concat(options.base, options.size, '/', icon, options.ext);
    },
    attributes: function () {
     return {
      style: `width: ${size}; height: ${size}; display: inline-block; vertical-align: text-bottom; margin: 0 0.1em;`,
      loading: 'eager',
     };
    },
    onerror: function () {
     // Fallback if emoji fails to load
     if (fallback && containerRef.current) {
      containerRef.current.innerHTML = fallback;
     }
    },
   });

   // Call onLoad callback if provided
   if (onLoad) {
    setTimeout(onLoad, 100);
   }
  }
 }, [text, size, fallback, onLoad]);

 return (
  <span
   ref={containerRef}
   className={`emoji-container ${className}`}
   style={{
    fontFamily:
     '"Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif',
    lineHeight: 1,
   }}
  >
   {text}
  </span>
 );
};

export default EmojiRenderer;
