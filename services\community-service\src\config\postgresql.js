/**
 * PostgreSQL Configuration for Community Service
 * Handles database connection and operations
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');
const config = require('./config');

class PostgreSQLConfig {
  constructor() {
    this.pool = null;
    this.isConnected = false;

    // Database configuration - support both DATABASE_URL and individual env vars
    if (config.DATABASE_URL) {
      // Use DATABASE_URL if provided (common for Render, Heroku, etc.)
      this.config = {
        connectionString: config.DATABASE_URL,
        // Connection pool settings
        max: config.DB_POOL_MAX,
        min: config.DB_POOL_MIN,
        idleTimeoutMillis: config.DB_IDLE_TIMEOUT,
        connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT,

        // SSL settings for production
        ssl: config.isProduction ? {
          rejectUnauthorized: false
        } : false,

        // Application name for monitoring
        application_name: 'vwork_community_service'
      };
    } else {
      // Use individual environment variables
      this.config = {
        host: config.DB_HOST,
        port: config.DB_PORT,
        database: config.DB_NAME,
        user: config.DB_USER,
        password: String(config.DB_PASSWORD),

        // Connection pool settings
        max: config.DB_POOL_MAX,
        min: config.DB_POOL_MIN,
        idleTimeoutMillis: config.DB_IDLE_TIMEOUT,
        connectionTimeoutMillis: config.DB_CONNECTION_TIMEOUT,

        // SSL settings for production
        ssl: config.isProduction ? {
          rejectUnauthorized: false
        } : false,

        // Application name for monitoring
        application_name: 'vwork_community_service'
      };
    }
    
    // Connection statistics
    this.stats = {
      connections: 0,
      queries: 0,
      errors: 0,
      lastConnected: null,
      lastError: null
    };
  }

  /**
   * Initialize PostgreSQL connection
   */
  async initialize() {
    try {
      logger.info('Initializing PostgreSQL connection for Community Service', {
        host: this.config.host,
        port: this.config.port,
        database: this.config.database,
        user: this.config.user
      });

      // Create connection pool
      this.pool = new Pool(this.config);

      // Test connection
      const client = await this.pool.connect();
      const result = await client.query('SELECT NOW() as current_time, version() as version');
      client.release();

      this.isConnected = true;
      this.stats.lastConnected = new Date();
      this.stats.connections++;

      logger.info('✅ PostgreSQL connected successfully for Community Service', {
        version: result.rows[0].version.split(' ')[0] + ' ' + result.rows[0].version.split(' ')[1],
        currentTime: result.rows[0].current_time,
        database: this.config.database
      });

      // Setup connection event handlers
      this.pool.on('connect', (client) => {
        this.stats.connections++;
        logger.debug('New PostgreSQL connection established');
      });

      this.pool.on('error', (err) => {
        this.stats.errors++;
        this.stats.lastError = new Date();
        logger.error('PostgreSQL pool error', { error: err.message });
      });

      return true;

    } catch (error) {
      this.isConnected = false;
      this.stats.lastError = new Date();
      this.stats.errors++;
      
      logger.error('❌ PostgreSQL initialization failed for Community Service', {
        error: error.message,
        config: {
          host: this.config.host,
          port: this.config.port,
          database: this.config.database,
          user: this.config.user
        }
      });
      
      throw error; // Fail fast if database cannot be initialized
    }
  }

  /**
   * Execute query
   */
  async query(text, params = []) {
    if (!this.isConnected || !this.pool) {
      throw new Error('PostgreSQL not connected');
    }

    const start = Date.now();
    
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      
      this.stats.queries++;
      
      logger.debug('PostgreSQL query executed', {
        query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        duration: `${duration}ms`,
        rowCount: result.rowCount
      });
      
      return result;
      
    } catch (error) {
      const duration = Date.now() - start;
      this.stats.errors++;
      this.stats.lastError = new Date();
      
      logger.error('PostgreSQL query failed', {
        query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        duration: `${duration}ms`,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get client from pool
   */
  async getClient() {
    if (!this.isConnected || !this.pool) {
      throw new Error('PostgreSQL not connected');
    }
    return await this.pool.connect();
  }

  /**
   * Execute transaction
   */
  async transaction(callback) {
    if (!this.isConnected || !this.pool) {
      throw new Error('PostgreSQL not connected');
    }

    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    if (!this.isConnected || !this.pool) {
      return {
        status: 'unhealthy',
        message: 'PostgreSQL not connected',
        error: 'Database not initialized'
      };
    }

    try {
      const start = Date.now();
      const result = await this.query('SELECT NOW() as current_time, version() as version');
      const duration = Date.now() - start;

      return {
        status: 'healthy',
        message: 'PostgreSQL connection is working',
        duration: `${duration}ms`,
        version: result.rows[0].version.split(' ')[0] + ' ' + result.rows[0].version.split(' ')[1],
        currentTime: result.rows[0].current_time,
        stats: this.getStats()
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: 'PostgreSQL health check failed',
        error: error.message,
        duration: '0ms'
      };
    }
  }

  /**
   * Close connection
   */
  async close() {
    if (this.pool) {
      try {
        await this.pool.end();
        this.isConnected = false;
        logger.info('PostgreSQL connection closed');
      } catch (error) {
        logger.error('Error closing PostgreSQL connection', { error: error.message });
      }
    }
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      ...this.stats,
      isConnected: this.isConnected,
      poolSize: this.pool ? this.pool.totalCount : 0,
      idleCount: this.pool ? this.pool.idleCount : 0,
      waitingCount: this.pool ? this.pool.waitingCount : 0
    };
  }

  /**
   * Collection-like interface for compatibility
   */
  collection(tableName) {
    return {
      async add(data) {
        const columns = Object.keys(data);
        const values = Object.values(data);
        const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
        
        const query = `
          INSERT INTO ${tableName} (${columns.join(', ')})
          VALUES (${placeholders})
          RETURNING *
        `;
        
        const result = await this.query(query, values);
        return result.rows[0];
      },

      doc(id) {
        return {
          async get() {
            const query = `SELECT * FROM ${tableName} WHERE id = $1`;
            const result = await this.query(query, [id]);
            return result.rows[0] || null;
          },

          async set(data) {
            const columns = Object.keys(data);
            const values = Object.values(data);
            const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
            
            const query = `
              INSERT INTO ${tableName} (${columns.join(', ')})
              VALUES (${placeholders})
              ON CONFLICT (id) DO UPDATE SET
              ${columns.map(col => `${col} = EXCLUDED.${col}`).join(', ')}
              RETURNING *
            `;
            
            const result = await this.query(query, values);
            return result.rows[0];
          },

          async update(data) {
            const updates = Object.keys(data).map((key, index) => `${key} = $${index + 2}`).join(', ');
            const values = [id, ...Object.values(data)];
            
            const query = `
              UPDATE ${tableName}
              SET ${updates}
              WHERE id = $1
              RETURNING *
            `;
            
            const result = await this.query(query, values);
            return result.rows[0];
          },

          async delete() {
            const query = `DELETE FROM ${tableName} WHERE id = $1 RETURNING *`;
            const result = await this.query(query, [id]);
            return result.rows[0];
          }
        };
      },

      where(field, operator, value) {
        return {
          async get() {
            const query = `SELECT * FROM ${tableName} WHERE ${field} ${operator} $1`;
            const result = await this.query(query, [value]);
            return result.rows;
          },

          orderBy(field, direction = 'asc') {
            return {
              async get() {
                const query = `SELECT * FROM ${tableName} WHERE ${field} ${operator} $1 ORDER BY ${field} ${direction.toUpperCase()}`;
                const result = await this.query(query, [value]);
                return result.rows;
              },

              limit(count) {
                return {
                  async get() {
                    const query = `SELECT * FROM ${tableName} WHERE ${field} ${operator} $1 ORDER BY ${field} ${direction.toUpperCase()} LIMIT $2`;
                    const result = await this.query(query, [value, count]);
                    return result.rows;
                  }
                };
              }
            };
          },

          limit(count) {
            return {
              async get() {
                const query = `SELECT * FROM ${tableName} WHERE ${field} ${operator} $1 LIMIT $2`;
                const result = await this.query(query, [value, count]);
                return result.rows;
              }
            };
          }
        };
      },

      orderBy(field, direction = 'asc') {
        return {
          async get() {
            const query = `SELECT * FROM ${tableName} ORDER BY ${field} ${direction.toUpperCase()}`;
            const result = await this.query(query);
            return result.rows;
          },

          limit(count) {
            return {
              async get() {
                const query = `SELECT * FROM ${tableName} ORDER BY ${field} ${direction.toUpperCase()} LIMIT $1`;
                const result = await this.query(query, [count]);
                return result.rows;
              }
            };
          }
        };
      },

      limit(count) {
        return {
          async get() {
            const query = `SELECT * FROM ${tableName} LIMIT $1`;
            const result = await this.query(query, [count]);
            return result.rows;
          }
        };
      },

      async get() {
        const query = `SELECT * FROM ${tableName}`;
        const result = await this.query(query);
        return result.rows;
      }
    };
  }
}

// Create singleton instance
const postgresqlConfig = new PostgreSQLConfig();

module.exports = { postgresqlConfig };

// Test connection function
async function testConnection() {
  try {
    await postgresqlConfig.initialize();
    console.log('✅ PostgreSQL connection test successful');
    await postgresqlConfig.close();
  } catch (error) {
    console.error('❌ PostgreSQL connection test failed:', error.message);
    process.exit(1);
  }
}

// Run test if called directly
if (require.main === module) {
  testConnection();
}
