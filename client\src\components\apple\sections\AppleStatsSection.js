import { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { DrawSVGPlugin } from 'gsap/DrawSVGPlugin';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { SplitText } from 'gsap/SplitText';
import { useLanguage } from '../../../contexts/LanguageContext';
import responsiveAutoscaling from '../../../services/responsiveAutoscaling';
import {
 UserGroupIcon,
 BriefcaseIcon,
 CurrencyDollarIcon,
 StarIcon,
 TrophyIcon,
 GlobeAltIcon,
} from '@heroicons/react/24/outline';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, MorphSVGPlugin, DrawSVGPlugin, Physics2DPlugin, CustomEase, SplitText);

// Advanced custom eases for premium effects
const liquidEases = {
 blob: CustomEase.create("blob", "M0,0 C0.3,0 0.4,1.8 0.7,1 C0.9,0.2 1,1 1,1"),
 wave: CustomEase.create("wave", "M0,0 C0.2,0 0.3,1.3 0.5,1 C0.7,0.7 0.8,1.3 1,1"),
 elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
 magnetic: CustomEase.create("magnetic", "M0,0 C0.1,0 0.2,2 0.6,1 C0.8,0 0.9,1 1,1")
};

const AppleStatsSection = () => {
 const { t } = useLanguage();
 const sectionRef = useRef(null);
 const titleRef = useRef(null);
 const statsRef = useRef(null);
 const backgroundRef = useRef(null);
 const liquidBlobsRef = useRef([]);
 const svgContainerRef = useRef(null);
 const [countersStarted, setCountersStarted] = useState(false);

 // Cleanup tracking
 const cleanupFunctionsRef = useRef([]);
 const splitTextInstancesRef = useRef([]);
 const isAnimatingRef = useRef(false);

 const stats = [
  {
   icon: UserGroupIcon,
   value: 0,
   suffix: '+',
   label: t('activeFreelancers'),
   description: t('skilledProfessionals'),
   color: 'blue',
   svgPath: 'M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7C14.4 7 14 7.4 14 8V16C14 16.6 14.4 17 15 17S16 16.6 16 16V12L18 12V16C18 16.6 18.4 17 19 17S20 16.6 20 16V12L21 12V9Z',
  },
  {
   icon: BriefcaseIcon,
   value: 0,
   suffix: '+',
   label: t('projectsCompleted'),
   description: t('successfullyDelivered'),
   color: 'green',
   svgPath: 'M12 2L13.4 8.6L20 10L13.4 11.4L12 18L10.6 11.4L4 10L10.6 8.6L12 2Z',
  },
  {
   icon: CurrencyDollarIcon,
   value: 0,
   suffix: 'M+',
   label: t('totalEarnings'),
   description: t('paidToFreelancers'),
   color: 'yellow',
   svgPath: 'M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22S2 17.5 2 12S6.5 2 12 2ZM13 7H11V9H9V11H11V13H13V11H15V9H13V7Z',
  },
  {
   icon: StarIcon,
   value: 0,
   suffix: '/5',
   label: t('averageRating'),
   description: t('clientSatisfaction'),
   color: 'purple',
   svgPath: 'M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z',
  },
  {
   icon: TrophyIcon,
   value: 0,
   suffix: '%',
   label: t('successRate'),
   description: t('projectSuccess'),
   color: 'orange',
   svgPath: 'M7 4V2C7 1.4 7.4 1 8 1H16C16.6 1 17 1.4 17 2V4H20C20.6 4 21 4.4 21 5S20.6 6 20 6H19V7C19 10.9 15.9 14 12 14S5 10.9 5 7V6H4C3.4 6 3 5.6 3 5S3.4 4 4 4H7Z',
  },
  {
   icon: GlobeAltIcon,
   value: 0,
   suffix: '+',
   label: t('countries'),
   description: t('globalReach'),
   color: 'indigo',
   svgPath: 'M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22S2 17.5 2 12S6.5 2 12 2ZM12 4C9.8 4 7.8 4.9 6.3 6.3C4.9 7.8 4 9.8 4 12S4.9 16.2 6.3 17.7C7.8 19.1 9.8 20 12 20S16.2 19.1 17.7 17.7C19.1 16.2 20 14.2 20 12S19.1 7.8 17.7 6.3C16.2 4.9 14.2 4 12 4Z',
  },
 ];

 const getColorClasses = (color) => {
  const colorMap = {
   blue: {
    bg: 'from-blue-400/20 to-cyan-500/20',
    icon: 'text-blue-600 
    glow: 'shadow-blue-500/20',
    accent: '#3B82F6'
   },
   green: {
    bg: 'from-green-400/20 to-emerald-500/20',
    icon: 'text-green-600 
    glow: 'shadow-green-500/20',
    accent: '#10B981'
   },
   yellow: {
    bg: 'from-yellow-400/20 to-orange-500/20',
    icon: 'text-yellow-600 
    glow: 'shadow-yellow-500/20',
    accent: '#F59E0B'
   },
   purple: {
    bg: 'from-purple-400/20 to-pink-500/20',
    icon: 'text-purple-600 
    glow: 'shadow-purple-500/20',
    accent: '#8B5CF6'
   },
   orange: {
    bg: 'from-orange-400/20 to-red-500/20',
    icon: 'text-orange-600 
    glow: 'shadow-orange-500/20',
    accent: '#F97316'
   },
   indigo: {
    bg: 'from-indigo-400/20 to-blue-600/20',
    icon: 'text-indigo-600 
    glow: 'shadow-indigo-500/20',
    accent: '#6366F1'
   },
  };
  return colorMap[color] || colorMap.blue;
 };

 // Create liquid morphing backgrounds
 const createLiquidMorphing = () => {
  if (!backgroundRef.current) return;

  // Create liquid blob shapes
  const createBlob = (index) => {
   const blob = document.createElement('div');
   blob.className = `absolute rounded-full bg-gradient-to-br opacity-5 transition-all duration-1000`;
   blob.style.width = `${200 + Math.random() * 300}px`;
   blob.style.height = blob.style.width;
   blob.style.left = `${Math.random() * 80}%`;
   blob.style.top = `${Math.random() * 80}%`;
   
   const colors = ['from-blue-500 to-purple-500', 'from-green-500 to-blue-500', 'from-purple-500 to-pink-500', 'from-orange-500 to-red-500'];
   blob.className += ` ${colors[index % colors.length]}`;
   
   backgroundRef.current.appendChild(blob);
   liquidBlobsRef.current[index] = blob;

   // Animate blob with physics
   gsap.to(blob, {
    x: `+=${Math.random() * 200 - 100}`,
    y: `+=${Math.random() * 200 - 100}`,
    scale: 1.5 + Math.random(),
    rotation: 360,
    duration: 20 + Math.random() * 10,
    repeat: -1,
    yoyo: true,
    ease: liquidEases.blob
   });

   // Morphing effect
   gsap.to(blob, {
    borderRadius: `${Math.random() * 50 + 30}% ${Math.random() * 50 + 30}% ${Math.random() * 50 + 30}% ${Math.random() * 50 + 30}%`,
    duration: 8 + Math.random() * 4,
    repeat: -1,
    yoyo: true,
    ease: liquidEases.wave
   });
  };

  for (let i = 0; i < 6; i++) {
   createBlob(i);
  }
 };

 // Advanced magnetic hover effect
 const createMagneticHover = (element, strength = 0.3) => {
  if (!element) return;

  const handleMouseMove = (e) => {
   const rect = element.getBoundingClientRect();
   const centerX = rect.left + rect.width / 2;
   const centerY = rect.top + rect.height / 2;
   const deltaX = (e.clientX - centerX) * strength;
   const deltaY = (e.clientY - centerY) * strength;
   
   gsap.to(element, {
    x: deltaX,
    y: deltaY,
    rotationX: deltaY * 0.1,
    rotationY: deltaX * 0.1,
    duration: 0.3,
    ease: liquidEases.magnetic
   });

   // Glow effect
   gsap.to(element.querySelector('.glow-element'), {
    opacity: 1,
    scale: 1.1,
    duration: 0.3
   });
  };
  
  const handleMouseLeave = () => {
   gsap.to(element, {
    x: 0,
    y: 0,
    rotationX: 0,
    rotationY: 0,
    duration: 0.8,
    ease: liquidEases.elastic
   });

   gsap.to(element.querySelector('.glow-element'), {
    opacity: 0,
    scale: 1,
    duration: 0.5
   });
  };
  
  element.addEventListener('mousemove', handleMouseMove, { passive: true });
  element.addEventListener('mouseleave', handleMouseLeave, { passive: true });

  // Return cleanup function and track it
  const cleanup = () => {
   element.removeEventListener('mousemove', handleMouseMove);
   element.removeEventListener('mouseleave', handleMouseLeave);
  };

  cleanupFunctionsRef.current.push(cleanup);
  return cleanup;
 };

 // Optimized counter animation with performance checks
 const animateCountersWithPhysics = () => {
  console.log('🔢 Starting counter animations...', stats.length);
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;

  stats.forEach((stat, index) => {
   const element = document.querySelector(`[data-counter="${index}"]`);
   console.log(`🎯 Counter ${index}:`, element, stat.value);
   if (element) {
    // Simple counter animation without SplitText
    const obj = { value: 0 };

    // Set initial state
    element.textContent = '0';

    // Reduced duration for better performance
    const duration = performanceLevel === 'high' ? 2.5 : performanceLevel === 'medium' ? 1.8 : 1.2;
    const staggerDelay = performanceLevel === 'high' ? 0.2 : 0.1;

    gsap.to(obj, {
     value: stat.value,
     duration: duration,
     delay: index * staggerDelay,
     ease: performanceLevel === 'high' ? liquidEases.wave : 'power2.out',
     onUpdate: () => {
      if (stat.suffix === '/5') {
       element.textContent = obj.value.toFixed(1);
      } else {
       element.textContent = Math.round(obj.value).toLocaleString();
      }
     },
     onComplete: () => {
      // Only add floating animation on high performance
      if (performanceLevel === 'high') {
       gsap.to(element, {
        y: "+=1", // Reduced movement
        duration: 4, // Slower for less CPU usage
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
       });
      }
     }
    });

    // Simplified entrance animation
    gsap.fromTo(element, {
     scale: performanceLevel === 'minimal' ? 0.9 : 0.5,
     opacity: 0
    }, {
     scale: 1,
     opacity: 1,
     duration: performanceLevel === 'high' ? 0.8 : 0.5,
     delay: index * (staggerDelay * 0.5),
     ease: performanceLevel === 'high' ? liquidEases.elastic : 'power2.out'
    });
   }
  });
 };

 // Create SVG animations for each stat
 const createSVGAnimations = () => {
  if (!svgContainerRef.current) return;

  stats.forEach((stat, index) => {
   const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
   svg.setAttribute('width', '100');
   svg.setAttribute('height', '100');
   svg.setAttribute('viewBox', '0 0 24 24');
   svg.className = 'absolute inset-0 opacity-10';
   
   const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
   path.setAttribute('d', stat.svgPath);
   path.setAttribute('fill', 'none');
   path.setAttribute('stroke', getColorClasses(stat.color).accent);
   path.setAttribute('stroke-width', '2');
   path.setAttribute('stroke-linecap', 'round');
   path.setAttribute('stroke-linejoin', 'round');
   
   svg.appendChild(path);
   
   const container = document.querySelector(`[data-svg-container="${index}"]`);
   if (container) {
    container.appendChild(svg);
    
    // Animate SVG drawing
    gsap.fromTo(path, {
     drawSVG: "0%"
    }, {
     drawSVG: "100%",
     duration: 2,
     delay: index * 0.3,
     ease: liquidEases.elastic,
     scrollTrigger: {
      trigger: container,
      start: 'top 80%',
      toggleActions: 'play none none reverse'
     }
    });

    // Add rotation animation
    gsap.to(svg, {
     rotation: 360,
     duration: 20,
     repeat: -1,
     ease: "none"
    });
   }
  });
 };

 // Advanced title animation with split text
 const animateTitle = () => {
  if (!titleRef.current) return;

  const titleSplit = new SplitText(titleRef.current.querySelector('h2'), { type: "words,chars" });
  const subtitleSplit = new SplitText(titleRef.current.querySelector('p'), { type: "words" });

  // Animate title characters
  gsap.fromTo(titleSplit.chars, {
   opacity: 0,
   y: 100,
   rotationX: -90,
   transformOrigin: "center bottom"
  }, {
   opacity: 1,
   y: 0,
   rotationX: 0,
   duration: 1.2,
   stagger: 0.03,
   ease: liquidEases.elastic,
   scrollTrigger: {
    trigger: titleRef.current,
    start: 'top 80%',
    toggleActions: 'play none none reverse'
   }
  });

  // Animate subtitle words
  gsap.fromTo(subtitleSplit.words, {
   opacity: 0,
   y: 30,
   scale: 0.8
  }, {
   opacity: 1,
   y: 0,
   scale: 1,
   duration: 0.8,
   stagger: 0.1,
   ease: liquidEases.wave,
   delay: 0.5,
   scrollTrigger: {
    trigger: titleRef.current,
    start: 'top 80%',
    toggleActions: 'play none none reverse'
   }
  });
 };

 useEffect(() => {
  const ctx = gsap.context(() => {
   createLiquidMorphing();
   animateTitle();
   createSVGAnimations();

   // Single unified stats animation to prevent conflicts
   if (statsRef.current && statsRef.current.children && !isAnimatingRef.current) {
    const statElements = Array.from(statsRef.current.children);
    isAnimatingRef.current = true;

    // Create single timeline for all stats animations
    const masterTL = gsap.timeline({
     scrollTrigger: {
      trigger: statsRef.current,
      start: 'top 80%',
      end: 'bottom 20%',
      toggleActions: 'play none none reverse',
      markers: false,
      onEnter: () => {
       console.log('📊 Stats section entered, countersStarted:', countersStarted);
       if (!countersStarted) {
        setCountersStarted(true);
        console.log('🚀 Triggering counter animations...');
        // Trigger counter animation after entrance animation
        setTimeout(() => animateCountersWithPhysics(), 800);
       }
      },
     },
     onComplete: () => {
      isAnimatingRef.current = false;
     }
    });

    // Entrance animation
    masterTL.fromTo(statElements, {
     opacity: 0,
     y: 100,
     scale: 0.8,
     rotationY: 45
    }, {
     opacity: 1,
     y: 0,
     scale: 1,
     rotationY: 0,
     duration: 1.5,
     stagger: 0.15,
     ease: liquidEases.elastic
    });

    // Add magnetic effects to stat cards (with cleanup tracking)
    statElements.forEach(element => {
     createMagneticHover(element);
    });
   }
  }, sectionRef);

  // Fallback trigger for counter animation if ScrollTrigger doesn't work
  const fallbackTimer = setTimeout(() => {
   if (!countersStarted) {
    console.log('🔄 Fallback trigger for counter animations');
    setCountersStarted(true);
    animateCountersWithPhysics();
   }
  }, 3000);

  return () => {
   // Enhanced cleanup
   ctx.revert();
   clearTimeout(fallbackTimer);

   // Clean up all tracked cleanup functions
   cleanupFunctionsRef.current.forEach(cleanup => {
    if (typeof cleanup === 'function') {
     cleanup();
    }
   });
   cleanupFunctionsRef.current = [];

   // Revert all SplitText instances
   splitTextInstancesRef.current.forEach(split => {
    if (split && split.revert) {
     split.revert();
    }
   });
   splitTextInstancesRef.current = [];

   // Reset animation state
   isAnimatingRef.current = false;
  };
 // eslint-disable-next-line react-hooks/exhaustive-deps
 }, [countersStarted]);

 return (
  <>
   {/* Enhanced CSS for liquid effects */}
   <style>{`
    @keyframes liquid-float {
     0%, 100% {
      transform: translateY(0px) rotate(0deg);
     }
     33% {
      transform: translateY(-10px) rotate(120deg);
     }
     66% {
      transform: translateY(5px) rotate(240deg);
     }
    }
    
    @keyframes glow-pulse {
     0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
     }
     50% {
      box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
     }
    }
    
    .liquid-card {
     transform-style: preserve-3d;
     perspective: 1000px;
    }
    
    .glow-element {
     animation: glow-pulse 3s ease-in-out infinite;
    }
    
    .liquid-bg-animation {
     animation: liquid-float 15s ease-in-out infinite;
    }
   `}</style>

   <section
    ref={sectionRef}
    className='relative py-20 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30  transition-colors duration-300 overflow-hidden'
   >
    {/* Liquid Morphing Background */}
    <div 
     ref={backgroundRef}
     className='absolute inset-0 overflow-hidden'
    />

    {/* Enhanced Background Pattern */}
    <div className='absolute inset-0 opacity-30'>
     <div className='absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl liquid-bg-animation' />
     <div className='absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-br from-green-400/10 to-blue-400/10 rounded-full blur-3xl liquid-bg-animation' style={{ animationDelay: '5s' }} />
     <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl liquid-bg-animation' style={{ animationDelay: '10s' }} />
    </div>

    <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
     {/* Enhanced Section Header */}
     <div ref={titleRef} className='text-center mb-20'>
      <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 mb-8 transition-colors duration-300'>
       {t('numbersSpeak')}
      </h2>
      <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
       {t('trustedByThousands')}
      </p>
     </div>

     {/* Enhanced Stats Grid with Liquid Cards */}
     <div
      ref={statsRef}
      className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12'
     >
      {stats.map((stat, index) => {
       const colorConfig = getColorClasses(stat.color);
       return (
        <div
         key={stat.label}
         className='group liquid-card relative'
        >
         {/* Glow Effect */}
         <div className={`glow-element absolute inset-0 bg-gradient-to-br ${colorConfig.bg} rounded-3xl blur-xl opacity-0 transition-all duration-500`} />
         
         {/* Main Card */}
         <div className='relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-gray-200/50 hover:border-gray-300 transition-all duration-500 shadow-xl hover:shadow-2xl transform hover:scale-105'>
          
          {/* SVG Background Animation */}
          <div 
           data-svg-container={index}
           className='absolute inset-0 overflow-hidden rounded-3xl'
          />

          {/* Icon with Enhanced Styling */}
          <div className='mb-8 relative'>
           <div className={`inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br ${colorConfig.bg} rounded-2xl transition-all duration-500 shadow-lg group-hover:shadow-xl transform group-hover:scale-110 group-hover:-rotate-6`}>
            <stat.icon className={`h-10 w-10 ${colorConfig.icon} transition-all duration-500 transform group-hover:scale-110`} />
           </div>
           
           {/* Floating particles around icon */}
           <div className='absolute -top-2 -right-2 w-3 h-3 bg-blue-400/60 rounded-full animate-ping' />
           <div className='absolute -bottom-2 -left-2 w-2 h-2 bg-purple-400/60 rounded-full animate-ping' style={{ animationDelay: '1s' }} />
          </div>

          {/* Enhanced Value Display */}
          <div className='mb-6 relative'>
           <div className='flex items-baseline justify-center'>
            <span
             data-counter={index}
             className='text-5xl sm:text-6xl lg:text-7xl font-black text-gray-900 transition-colors duration-300 group-hover:text-blue-600 
            >
             0
            </span>
            <span className='text-3xl sm:text-4xl lg:text-5xl font-black text-gray-600 ml-2 transition-colors duration-300 group-hover:text-blue-500 
             {stat.suffix}
            </span>
           </div>
           
           {/* Progress line */}
           <div className='mt-4 w-full h-1 bg-gray-200 rounded-full overflow-hidden'>
            <div className={`h-full bg-gradient-to-r ${colorConfig.bg.replace('/20', '/60')} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-1000 origin-left`} />
           </div>
          </div>

          {/* Enhanced Label & Description */}
          <div className='text-center'>
           <h3 className='text-xl sm:text-2xl font-bold text-gray-900 mb-3 transition-colors duration-300 group-hover:text-blue-600 
            {stat.label}
           </h3>
           <p className='text-base sm:text-lg text-gray-600 transition-colors duration-300 group-hover:text-gray-800 leading-relaxed'>
            {stat.description}
           </p>
          </div>

          {/* Liquid corner decorations */}
          <div className='absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full animate-pulse' />
          <div className='absolute bottom-4 left-4 w-6 h-6 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full animate-pulse' style={{ animationDelay: '1s' }} />
         </div>
        </div>
       );
      })}
     </div>
    </div>
   </section>
  </>
 );
};

export default AppleStatsSection;
