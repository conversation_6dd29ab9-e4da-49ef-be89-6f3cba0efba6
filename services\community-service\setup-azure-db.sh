#!/bin/bash

# Quick Azure Database Setup Script for Community Service
# Usage: ./setup-azure-db.sh [app-name] [resource-group]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
APP_NAME=${1:-"vwork-community-service"}
RESOURCE_GROUP=${2:-"vwork-rg"}
LOCATION="eastus"
DB_SERVER_NAME="${APP_NAME}-db-server"
DB_NAME="vwork_community_service"
DB_USER="vwork_admin"

echo -e "${BLUE}🗄️  Quick Azure Database Setup for Community Service${NC}"
echo -e "${BLUE}==================================================${NC}"

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check Azure CLI
if ! command -v az &> /dev/null; then
    echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if logged in
if ! az account show &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Azure. Please login first.${NC}"
    az login
fi

# Check if openssl is available
if ! command -v openssl &> /dev/null; then
    echo -e "${YELLOW}⚠️  openssl not found. Generating simple password...${NC}"
    DB_PASSWORD="VWork2024!$(date +%s)"
else
    DB_PASSWORD=$(openssl rand -base64 32)
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Check if database server already exists
if az postgres flexible-server show --resource-group $RESOURCE_GROUP --name $DB_SERVER_NAME &> /dev/null; then
    echo -e "${YELLOW}⚠️  Database server $DB_SERVER_NAME already exists${NC}"
    echo -e "${BLUE}📋 Getting existing database information...${NC}"
    
    DB_HOST=$(az postgres flexible-server show \
        --resource-group $RESOURCE_GROUP \
        --name $DB_SERVER_NAME \
        --query "fullyQualifiedDomainName" \
        --output tsv)
    
    echo -e "${GREEN}✅ Using existing database server: $DB_HOST${NC}"
else
    # Create PostgreSQL Flexible Server
    echo -e "${BLUE}🗄️  Creating PostgreSQL Flexible Server: $DB_SERVER_NAME${NC}"
    az postgres flexible-server create \
        --name $DB_SERVER_NAME \
        --resource-group $RESOURCE_GROUP \
        --location $LOCATION \
        --admin-user $DB_USER \
        --admin-password "$DB_PASSWORD" \
        --sku-name "Standard_B1ms" \
        --tier "Burstable" \
        --storage-size 32 \
        --version 14 \
        --output none

    echo -e "${GREEN}✅ PostgreSQL server created successfully${NC}"
    
    # Get server hostname
    DB_HOST=$(az postgres flexible-server show \
        --resource-group $RESOURCE_GROUP \
        --name $DB_SERVER_NAME \
        --query "fullyQualifiedDomainName" \
        --output tsv)
fi

# Check if database exists
if az postgres flexible-server db show \
    --resource-group $RESOURCE_GROUP \
    --server-name $DB_SERVER_NAME \
    --database-name $DB_NAME &> /dev/null; then
    echo -e "${YELLOW}⚠️  Database $DB_NAME already exists${NC}"
else
    # Create database
    echo -e "${BLUE}📊 Creating database: $DB_NAME${NC}"
    az postgres flexible-server db create \
        --resource-group $RESOURCE_GROUP \
        --server-name $DB_SERVER_NAME \
        --database-name $DB_NAME \
        --output none

    echo -e "${GREEN}✅ Database created successfully${NC}"
fi

# Configure firewall rules
echo -e "${BLUE}🔥 Configuring firewall rules${NC}"
az postgres flexible-server firewall-rule create \
    --resource-group $RESOURCE_GROUP \
    --name $DB_SERVER_NAME \
    --rule-name "AllowAzureServices" \
    --start-ip-address "0.0.0.0" \
    --end-ip-address "***************" \
    --output none

echo -e "${GREEN}✅ Firewall rules configured${NC}"

# Create connection string
DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:5432/${DB_NAME}?sslmode=require"

echo -e "${GREEN}✅ Database setup completed!${NC}"
echo -e "${BLUE}📋 Database Information:${NC}"
echo -e "${BLUE}   Server: $DB_HOST${NC}"
echo -e "${BLUE}   Database: $DB_NAME${NC}"
echo -e "${BLUE}   Username: $DB_USER${NC}"
echo -e "${BLUE}   Password: $DB_PASSWORD${NC}"

echo -e "${BLUE}🔗 Connection String:${NC}"
echo -e "${YELLOW}$DATABASE_URL${NC}"

# Update App Service with database configuration
echo -e "${BLUE}⚙️  Updating App Service configuration${NC}"
az webapp config appsettings set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --settings \
    DATABASE_URL="$DATABASE_URL" \
    DB_HOST="$DB_HOST" \
    DB_NAME="$DB_NAME" \
    DB_USER="$DB_USER" \
    DB_PASSWORD="$DB_PASSWORD" \
    --output none

echo -e "${GREEN}✅ App Service configuration updated${NC}"

# Save credentials to file
CREDS_FILE="azure-db-credentials.txt"
cat > $CREDS_FILE << EOF
# Azure PostgreSQL Database Credentials
# Generated on: $(date)

Server: $DB_HOST
Database: $DB_NAME
Username: $DB_USER
Password: $DB_PASSWORD

Connection String:
$DATABASE_URL

# Important: Keep this file secure and delete after deployment
EOF

echo -e "${GREEN}✅ Credentials saved to $CREDS_FILE${NC}"

# Show next steps
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${BLUE}1. Configure remaining environment variables in Azure Portal:${NC}"
echo -e "${BLUE}   - FIREBASE_PROJECT_ID${NC}"
echo -e "${BLUE}   - FIREBASE_CLIENT_EMAIL${NC}"
echo -e "${BLUE}   - FIREBASE_PRIVATE_KEY${NC}"
echo -e "${BLUE}   - ALLOWED_ORIGINS${NC}"
echo -e "${BLUE}2. Run database migrations:${NC}"
echo -e "${BLUE}   az webapp ssh --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   npm run db:setup${NC}"
echo -e "${BLUE}3. Test the application:${NC}"
echo -e "${BLUE}   curl https://$APP_NAME.azurewebsites.net/health${NC}"

# Show database status
echo -e "${BLUE}📊 Database Status:${NC}"
az postgres flexible-server show \
    --resource-group $RESOURCE_GROUP \
    --name $DB_SERVER_NAME \
    --query "{name:name, state:state, fullyQualifiedDomainName:fullyQualifiedDomainName, version:version}" \
    --output table

echo -e "${GREEN}🎉 Database setup completed!${NC}"
echo -e "${YELLOW}⚠️  Remember to delete $CREDS_FILE after deployment${NC}" 