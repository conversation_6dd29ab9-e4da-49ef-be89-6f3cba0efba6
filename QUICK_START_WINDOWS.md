# 🚀 VWork Platform - Quick Start Guide (Windows)

## 📋 Y<PERSON>u cầu <PERSON> thống
- Windows 10/11 (64-bit)
- Node.js 18+ 
- npm 9+
- Git

## ⚡ Khởi động Nhanh

### Phương pháp 1: Sử dụng Batch Files (Khuyến nghị)

1. **Khởi động tất cả services:**
   ```bash
   # Double-click file này hoặc chạy trong CMD:
   start-vwork.bat
   ```

2. **Dừng tất cả services:**
   ```bash
   # Double-click file này hoặc chạy trong CMD:
   stop-vwork.bat
   ```

### Phương pháp 2: Sử dụng npm Commands

1. **Khởi động tất cả services:**
   ```bash
   npm start
   ```

2. **Dừng tất cả services:**
   ```bash
   npm run stop
   ```

3. **Khởi động chỉ services (không có client):**
   ```bash
   npm run start:services
   ```

4. **Khởi động chỉ client:**
   ```bash
   npm run start:client
   ```

## 🖥️ Cách hoạt động

Khi chạy `npm start`, hệ thống sẽ:

1. **Mở 9 cửa sổ CMD riêng biệt:**
   - **API Gateway** (Port 8080)
   - **User Service** (Port 3001)
   - **Project Service** (Port 3002)
   - **Job Service** (Port 3003)
   - **Chat Service** (Port 3004)
   - **Community Service** (Port 3005)
   - **Payment Service** (Port 3006)
   - **Team Service** (Port 3007)
   - **Client** (Port 3000)

2. **Mỗi cửa sổ hiển thị:**
   - Tên service và port
   - Logs real-time
   - Error messages
   - Health check status

## 🌐 Truy cập Ứng dụng

- **Frontend:** http://localhost:3000
- **API Gateway:** http://localhost:8080
- **Health Check:** http://localhost:8080/health

## 🛑 Dừng Services

### Tự động:
- Nhấn `Ctrl+C` trong cửa sổ chính
- Chạy `npm run stop`

### Thủ công:
- Đóng từng cửa sổ CMD
- Hoặc chạy `stop-vwork.bat`

## 🔧 Troubleshooting

### Port đã được sử dụng:
```bash
# Kiểm tra port nào đang được sử dụng
netstat -ano | findstr :3000
netstat -ano | findstr :8080

# Kill process theo PID
taskkill /F /PID <PID>
```

### Service không khởi động:
1. Kiểm tra Node.js và npm đã cài đặt
2. Chạy `npm run install:all` để cài dependencies
3. Kiểm tra file `.env` có tồn tại

### Logs không hiển thị:
- Kiểm tra cửa sổ CMD có mở không
- Đảm bảo không có lỗi syntax trong code

## 📊 Monitoring

### Kiểm tra trạng thái services:
```bash
npm run health
```

### Xem logs real-time:
- Mỗi service có cửa sổ CMD riêng
- Logs hiển thị real-time
- Dễ dàng debug và monitor

## 🎯 Tips

1. **Sắp xếp cửa sổ:** Có thể sắp xếp các cửa sổ CMD để dễ theo dõi
2. **Pin cửa sổ:** Pin các cửa sổ quan trọng vào taskbar
3. **Logs:** Mỗi service có log riêng, dễ debug
4. **Restart:** Có thể restart từng service bằng cách đóng và mở lại cửa sổ

## 🆘 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong các cửa sổ CMD
2. Chạy `npm run stop` và khởi động lại
3. Kiểm tra file `.env` và dependencies
4. Xem logs chi tiết trong từng cửa sổ service 