#!/usr/bin/env node

/**
 * Unified build script for NERAFUS platform
 * Replaces multiple build scripts with a single, configurable solution
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..', '..');
const SERVICES_DIR = path.join(ROOT_DIR, 'services');
const CLIENT_DIR = path.join(ROOT_DIR, 'client');

// Available services
const SERVICES = [
  'auth-service',
  'user-service', 
  'project-service',
  'job-service',
  'chat-service',
  'community-service',
  'payment-service',
  'api-gateway'
];

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Execute command with proper error handling
 */
function execCommand(command, cwd = ROOT_DIR, options = {}) {
  try {
    log.info(`Executing: ${command}`);
    const result = execSync(command, { 
      cwd, 
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8'
    });
    return result;
  } catch (error) {
    log.error(`Command failed: ${command}`);
    if (options.exitOnError !== false) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if service exists
 */
function serviceExists(serviceName) {
  const servicePath = path.join(SERVICES_DIR, serviceName);
  return fs.existsSync(servicePath) && fs.existsSync(path.join(servicePath, 'package.json'));
}

/**
 * Build a single service
 */
function buildService(serviceName, production = false) {
  if (!serviceExists(serviceName)) {
    log.warn(`Service ${serviceName} not found, skipping...`);
    return false;
  }

  const servicePath = path.join(SERVICES_DIR, serviceName);
  log.info(`Building ${serviceName}...`);

  // Install dependencies
  execCommand('npm ci', servicePath);

  // Run build script if it exists
  const packageJson = JSON.parse(fs.readFileSync(path.join(servicePath, 'package.json'), 'utf8'));
  if (packageJson.scripts && packageJson.scripts.build) {
    const buildCommand = production ? 'npm run build:production' : 'npm run build';
    execCommand(buildCommand, servicePath, { exitOnError: false });
  }

  log.success(`${serviceName} built successfully`);
  return true;
}

/**
 * Build client application
 */
function buildClient(production = false) {
  if (!fs.existsSync(CLIENT_DIR)) {
    log.warn('Client directory not found, skipping...');
    return false;
  }

  log.info('Building client application...');
  
  // Install dependencies
  execCommand('npm ci', CLIENT_DIR);
  
  // Build client
  const buildCommand = production ? 'npm run build:production' : 'npm run build';
  execCommand(buildCommand, CLIENT_DIR);
  
  log.success('Client built successfully');
  return true;
}

/**
 * Build all services
 */
function buildAllServices(production = false) {
  log.info('Building all services...');
  
  let successCount = 0;
  let failCount = 0;

  SERVICES.forEach(serviceName => {
    if (buildService(serviceName, production)) {
      successCount++;
    } else {
      failCount++;
    }
  });

  log.info(`Services build complete: ${successCount} successful, ${failCount} failed`);
  return failCount === 0;
}

/**
 * Clean build artifacts
 */
function clean(target = 'all') {
  log.info(`Cleaning ${target}...`);

  if (target === 'all' || target === 'services') {
    SERVICES.forEach(serviceName => {
      if (serviceExists(serviceName)) {
        const servicePath = path.join(SERVICES_DIR, serviceName);
        execCommand('rm -rf node_modules dist build', servicePath, { exitOnError: false });
      }
    });
  }

  if (target === 'all' || target === 'client') {
    if (fs.existsSync(CLIENT_DIR)) {
      execCommand('rm -rf node_modules build dist', CLIENT_DIR, { exitOnError: false });
    }
  }

  if (target === 'all') {
    execCommand('rm -rf node_modules', ROOT_DIR, { exitOnError: false });
  }

  log.success('Clean complete');
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const target = args[1] || 'all';
  const production = args.includes('--production') || process.env.NODE_ENV === 'production';

  switch (command) {
    case 'service':
      if (!target || target === 'all') {
        buildAllServices(production);
      } else {
        buildService(target, production);
      }
      break;

    case 'client':
      buildClient(production);
      break;

    case 'all':
      buildAllServices(production);
      buildClient(production);
      break;

    case 'clean':
      clean(target);
      break;

    default:
      console.log(`
Usage: node build.js <command> [target] [options]

Commands:
  service [name]  Build specific service or all services
  client          Build client application
  all             Build everything
  clean [target]  Clean build artifacts

Options:
  --production    Build for production

Examples:
  node build.js all
  node build.js service auth-service
  node build.js client --production
  node build.js clean services
      `);
      process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  buildService,
  buildClient,
  buildAllServices,
  clean
};
