// Location Service for managing country and city data
class LocationService {
  constructor() {
    this.countriesCache = null;
    this.citiesCache = {};
    this.baseUrl = 'https://restcountries.com/v3.1';
  }

  // Fetch all countries from REST Countries API
  async getCountries() {
    try {
      // Return cached data if available
      if (this.countriesCache) {
        return this.countriesCache;
      }

      const response = await fetch(`${this.baseUrl}/all?fields=name,cca2,flag,region,subregion,population,capital`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Transform and sort countries
      const countries = data
        .map(country => ({
          code: country.cca2,
          name: country.name.common,
          flag: country.flag,
          region: country.region,
          subregion: country.subregion,
          population: country.population,
          capital: country.capital?.[0] || ''
        }))
        .sort((a, b) => a.name.localeCompare(b.name));

      console.log('Fetched countries:', countries.length);
      console.log('Sample countries:', countries.slice(0, 5));

      // Cache the result
      this.countriesCache = countries;
      return countries;
    } catch (error) {
      console.error('Error fetching countries:', error);
      throw error;
    }
  }

  // Search countries by name
  async searchCountries(query) {
    try {
      const countries = await this.getCountries();
      return countries.filter(country =>
        country.name.toLowerCase().includes(query.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching countries:', error);
      return [];
    }
  }

  // Get cities for a specific country
  async getCities(countryCode) {
    try {
      // Return cached data if available
      if (this.citiesCache[countryCode]) {
        return this.citiesCache[countryCode];
      }

      // For now, using mock data. In production, you would use a real API
      const mockCities = this.getMockCities(countryCode);
      
      // Cache the result
      this.citiesCache[countryCode] = mockCities;
      return mockCities;
    } catch (error) {
      console.error('Error fetching cities:', error);
      return [];
    }
  }

  // Search cities by name within a country
  async searchCities(countryCode, query) {
    try {
      const cities = await this.getCities(countryCode);
      return cities.filter(city =>
        city.toLowerCase().includes(query.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching cities:', error);
      return [];
    }
  }

  // Mock cities data - replace with real API calls
  getMockCities(countryCode) {
    const citiesData = {
      'VN': [
        'Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Hai Phong', 'Can Tho',
        'Bien Hoa', 'Hue', 'Nha Trang', 'Buon Ma Thuot', 'Vung Tau',
        'Quy Nhon', 'Thai Nguyen', 'Bac Ninh', 'Hai Duong', 'Nam Dinh'
      ],
      'US': [
        'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix',
        'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose',
        'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte'
      ],
      'GB': [
        'London', 'Birmingham', 'Leeds', 'Glasgow', 'Sheffield',
        'Bradford', 'Edinburgh', 'Liverpool', 'Manchester', 'Bristol',
        'Cardiff', 'Coventry', 'Leicester', 'Nottingham', 'Newcastle'
      ],
      'CA': [
        'Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Edmonton',
        'Ottawa', 'Winnipeg', 'Quebec City', 'Hamilton', 'Kitchener'
      ],
      'AU': [
        'Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide',
        'Gold Coast', 'Newcastle', 'Canberra', 'Sunshine Coast', 'Wollongong'
      ],
      'DE': [
        'Berlin', 'Hamburg', 'Munich', 'Cologne', 'Frankfurt',
        'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig'
      ],
      'FR': [
        'Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice',
        'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille'
      ],
      'JP': [
        'Tokyo', 'Yokohama', 'Osaka', 'Nagoya', 'Sapporo',
        'Fukuoka', 'Kobe', 'Kyoto', 'Kawasaki', 'Saitama'
      ],
      'KR': [
        'Seoul', 'Busan', 'Incheon', 'Daegu', 'Daejeon',
        'Gwangju', 'Suwon', 'Ulsan', 'Changwon', 'Seongnam'
      ],
      'SG': [
        'Singapore'
      ]
    };

    return citiesData[countryCode] || [];
  }

  // Get country by code
  async getCountryByCode(code) {
    try {
      const countries = await this.getCountries();
      return countries.find(country => country.code === code);
    } catch (error) {
      console.error('Error getting country by code:', error);
      return null;
    }
  }

  // Get country by name
  async getCountryByName(name) {
    try {
      const countries = await this.getCountries();
      return countries.find(country => 
        country.name.toLowerCase() === name.toLowerCase()
      );
    } catch (error) {
      console.error('Error getting country by name:', error);
      return null;
    }
  }

  // Clear cache (useful for testing or when data needs to be refreshed)
  clearCache() {
    this.countriesCache = null;
    this.citiesCache = {};
  }

  // Alternative APIs for cities (commented out for reference)
  
  // GeoDB Cities API (requires RapidAPI key)
  /*
  async getCitiesFromGeoDB(countryCode) {
    const options = {
      method: 'GET',
      headers: {
        'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY',
        'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com'
      }
    };

    try {
      const response = await fetch(
        `https://wft-geo-db.p.rapidapi.com/v1/geo/cities?countryIds=${countryCode}&limit=10&sort=-population`,
        options
      );
      const data = await response.json();
      return data.data.map(city => city.city);
    } catch (error) {
      console.error('Error fetching cities from GeoDB:', error);
      return [];
    }
  }
  */

  // OpenCage Geocoding API (requires API key)
  /*
  async searchLocation(query) {
    const apiKey = 'YOUR_OPENCAGE_API_KEY';
    try {
      const response = await fetch(
        `https://api.opencagedata.com/geocode/v1/json?q=${encodeURIComponent(query)}&key=${apiKey}`
      );
      const data = await response.json();
      return data.results;
    } catch (error) {
      console.error('Error searching location:', error);
      return [];
    }
  }
  */
}

// Create singleton instance
const locationService = new LocationService();

export default locationService; 