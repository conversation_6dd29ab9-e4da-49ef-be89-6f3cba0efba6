# 👨‍💻 NERAFUS Platform Development Guidelines

## 📋 Table of Contents
- [Code Style & Formatting](#code-style--formatting)
- [Architecture Patterns](#architecture-patterns)
- [Git Workflow](#git-workflow)
- [Testing Standards](#testing-standards)
- [Performance Guidelines](#performance-guidelines)
- [Security Best Practices](#security-best-practices)
- [Documentation Standards](#documentation-standards)

## 🎨 Code Style & Formatting

### **Automated Formatting**
We use **ESLint** and **Prettier** for consistent code formatting across the platform.

```bash
# Format all code
npm run lint:fix

# Check formatting
npm run lint

# Format specific files
npx prettier --write "src/**/*.{js,jsx,ts,tsx}"
```

### **Naming Conventions**

#### **Files and Directories**
```
✅ Good
components/UserProfile.jsx
utils/apiHelpers.js
hooks/useAuth.js
services/auth-service/

❌ Bad
components/userprofile.jsx
utils/API_helpers.js
hooks/UseAuth.js
services/authService/
```

#### **Variables and Functions**
```javascript
// ✅ Good - camelCase
const userName = 'john_doe';
const fetchUserData = async () => {};
const isAuthenticated = true;

// ❌ Bad
const user_name = 'john_doe';
const FetchUserData = async () => {};
const IsAuthenticated = true;
```

#### **Constants**
```javascript
// ✅ Good - SCREAMING_SNAKE_CASE
const API_BASE_URL = 'https://api.nerafus.com';
const MAX_RETRY_ATTEMPTS = 3;
const DEFAULT_TIMEOUT = 5000;

// ❌ Bad
const apiBaseUrl = 'https://api.nerafus.com';
const maxRetryAttempts = 3;
```

#### **React Components**
```javascript
// ✅ Good - PascalCase
const UserProfile = () => {};
const ProjectCard = () => {};
const AuthProvider = () => {};

// ❌ Bad
const userProfile = () => {};
const projectcard = () => {};
```

### **Code Organization**

#### **Import Order**
```javascript
// 1. Node modules
import React from 'react';
import axios from 'axios';

// 2. Internal modules (absolute imports)
import { Button } from '@components/ui';
import { useAuth } from '@hooks/useAuth';

// 3. Relative imports
import './Component.css';
import { helper } from '../utils/helper';
```

#### **Component Structure**
```javascript
// ✅ Good structure
import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';

const UserProfile = ({ userId, onUpdate }) => {
  // 1. State declarations
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);

  // 2. Custom hooks
  const { isAuthenticated } = useAuth();

  // 3. Callbacks and handlers
  const handleUpdate = useCallback((data) => {
    onUpdate(data);
  }, [onUpdate]);

  // 4. Effects
  useEffect(() => {
    fetchUser();
  }, [userId]);

  // 5. Helper functions
  const fetchUser = async () => {
    setLoading(true);
    // ... fetch logic
    setLoading(false);
  };

  // 6. Early returns
  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }

  // 7. Main render
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
};

// 8. PropTypes
UserProfile.propTypes = {
  userId: PropTypes.string.isRequired,
  onUpdate: PropTypes.func,
};

// 9. Default props
UserProfile.defaultProps = {
  onUpdate: () => {},
};

export default UserProfile;
```

## 🏗️ Architecture Patterns

### **Service Layer Pattern**
```javascript
// ✅ Good - Separate concerns
// services/userService.js
export const userService = {
  async getUser(id) {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },
  
  async updateUser(id, data) {
    const response = await api.put(`/users/${id}`, data);
    return response.data;
  }
};

// components/UserProfile.jsx
import { userService } from '@services/userService';

const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    userService.getUser(userId).then(setUser);
  }, [userId]);
  
  return <div>{user?.name}</div>;
};
```

### **Custom Hooks Pattern**
```javascript
// ✅ Good - Reusable logic
// hooks/useUser.js
export const useUser = (userId) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const fetchUser = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const userData = await userService.getUser(userId);
      setUser(userData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [userId]);
  
  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId, fetchUser]);
  
  return { user, loading, error, refetch: fetchUser };
};

// components/UserProfile.jsx
const UserProfile = ({ userId }) => {
  const { user, loading, error } = useUser(userId);
  
  if (loading) return <Loading />;
  if (error) return <Error message={error} />;
  
  return <div>{user?.name}</div>;
};
```

### **Error Boundary Pattern**
```javascript
// ✅ Good - Graceful error handling
import { ErrorBoundary } from '@components/ErrorBoundary';

const App = () => (
  <ErrorBoundary fallback={<ErrorFallback />}>
    <Router>
      <Routes>
        {/* Your routes */}
      </Routes>
    </Router>
  </ErrorBoundary>
);
```

## 🔄 Git Workflow

### **Branch Naming**
```bash
# Feature branches
feature/user-authentication
feature/project-dashboard
feature/payment-integration

# Bug fixes
bugfix/login-validation-error
bugfix/memory-leak-in-chat

# Hotfixes
hotfix/security-vulnerability
hotfix/critical-payment-bug

# Releases
release/v1.2.0
release/v2.0.0-beta
```

### **Commit Messages**
Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```bash
# ✅ Good commit messages
feat: add user authentication with Firebase
fix: resolve memory leak in chat component
docs: update API documentation for user endpoints
style: format code with prettier
refactor: extract user service logic
test: add unit tests for auth service
chore: update dependencies

# ❌ Bad commit messages
fixed stuff
update
changes
wip
```

### **Pull Request Process**
1. **Create feature branch** from `main`
2. **Make changes** following guidelines
3. **Write tests** for new functionality
4. **Run full test suite** locally
5. **Create PR** with descriptive title and description
6. **Request review** from team members
7. **Address feedback** and update PR
8. **Merge** after approval and passing CI

### **PR Template**
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No console errors
```

## 🧪 Testing Standards

### **Test Coverage Requirements**
- **Unit Tests**: 80% minimum coverage
- **Integration Tests**: All critical user flows
- **E2E Tests**: Key user journeys

### **Test Naming**
```javascript
// ✅ Good - Descriptive test names
describe('UserProfile Component', () => {
  it('should display user name when user data is loaded', () => {});
  it('should show loading spinner while fetching user data', () => {});
  it('should display error message when user fetch fails', () => {});
});

// ❌ Bad - Vague test names
describe('UserProfile', () => {
  it('should work', () => {});
  it('test user', () => {});
});
```

### **Test Structure**
```javascript
// ✅ Good - AAA pattern (Arrange, Act, Assert)
it('should update user profile when form is submitted', async () => {
  // Arrange
  const mockUser = { id: '1', name: 'John Doe' };
  const mockUpdateUser = jest.fn().mockResolvedValue(mockUser);
  render(<UserProfile user={mockUser} onUpdate={mockUpdateUser} />);
  
  // Act
  await userEvent.type(screen.getByLabelText(/name/i), 'Jane Doe');
  await userEvent.click(screen.getByRole('button', { name: /save/i }));
  
  // Assert
  expect(mockUpdateUser).toHaveBeenCalledWith({
    ...mockUser,
    name: 'Jane Doe'
  });
});
```

## ⚡ Performance Guidelines

### **React Performance**
```javascript
// ✅ Good - Memoization
const ExpensiveComponent = React.memo(({ data, onUpdate }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveCalculation(item));
  }, [data]);
  
  const handleUpdate = useCallback((id, changes) => {
    onUpdate(id, changes);
  }, [onUpdate]);
  
  return (
    <div>
      {processedData.map(item => (
        <Item key={item.id} data={item} onUpdate={handleUpdate} />
      ))}
    </div>
  );
});

// ❌ Bad - No optimization
const ExpensiveComponent = ({ data, onUpdate }) => {
  const processedData = data.map(item => expensiveCalculation(item));
  
  return (
    <div>
      {processedData.map(item => (
        <Item 
          key={item.id} 
          data={item} 
          onUpdate={(id, changes) => onUpdate(id, changes)} 
        />
      ))}
    </div>
  );
};
```

### **Bundle Optimization**
```javascript
// ✅ Good - Code splitting
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

const App = () => (
  <Suspense fallback={<Loading />}>
    <LazyComponent />
  </Suspense>
);

// ✅ Good - Tree shaking
import { debounce } from 'lodash/debounce';

// ❌ Bad - Imports entire library
import _ from 'lodash';
```

## 🔒 Security Best Practices

### **Input Validation**
```javascript
// ✅ Good - Server-side validation
const { body, validationResult } = require('express-validator');

app.post('/api/users', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
], (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  // Process request
});
```

### **Authentication**
```javascript
// ✅ Good - Secure token handling
const authMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};
```

### **Environment Variables**
```javascript
// ✅ Good - Environment-based configuration
const config = {
  port: process.env.PORT || 3000,
  dbUrl: process.env.DATABASE_URL,
  jwtSecret: process.env.JWT_SECRET,
  nodeEnv: process.env.NODE_ENV || 'development'
};

// Validate required environment variables
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET'];
requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
});
```

## 📚 Documentation Standards

### **Code Comments**
```javascript
// ✅ Good - Meaningful comments
/**
 * Calculates the total project cost including taxes and fees
 * @param {number} baseAmount - The base project amount
 * @param {number} taxRate - Tax rate as decimal (e.g., 0.08 for 8%)
 * @param {Object} fees - Additional fees object
 * @param {number} fees.platform - Platform fee amount
 * @param {number} fees.processing - Processing fee amount
 * @returns {number} Total cost including all fees and taxes
 */
const calculateTotalCost = (baseAmount, taxRate, fees) => {
  const tax = baseAmount * taxRate;
  const totalFees = fees.platform + fees.processing;
  return baseAmount + tax + totalFees;
};

// ❌ Bad - Obvious comments
// Increment counter by 1
counter++;

// Set user name
user.name = 'John';
```

### **README Structure**
```markdown
# Project Name

Brief description of the project

## 🚀 Quick Start
Installation and setup instructions

## 📋 Features
List of key features

## 🛠️ Development
Development setup and guidelines

## 📚 API Documentation
Link to API docs

## 🧪 Testing
How to run tests

## 🚀 Deployment
Deployment instructions

## 🤝 Contributing
Contribution guidelines
```

### **JSDoc Standards**
```javascript
/**
 * User authentication service
 * @class AuthService
 */
class AuthService {
  /**
   * Authenticates user with email and password
   * @async
   * @param {string} email - User's email address
   * @param {string} password - User's password
   * @returns {Promise<Object>} Authentication result
   * @returns {Object} returns.user - User object
   * @returns {string} returns.token - JWT token
   * @throws {AuthenticationError} When credentials are invalid
   * @example
   * const result = await authService.login('<EMAIL>', 'password123');
   * console.log(result.user.name); // 'John Doe'
   */
  async login(email, password) {
    // Implementation
  }
}
```

---

**🎉 Following these guidelines ensures consistent, maintainable, and high-quality code across the entire NERAFUS platform!**
