#!/usr/bin/env node

/**
 * Code quality automation script for NERAFUS platform
 * Handles linting, formatting, type checking, and quality gates
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..', '..');

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Execute command with proper error handling
 */
function execCommand(command, cwd = ROOT_DIR, options = {}) {
  try {
    log.info(`Executing: ${command}`);
    const result = execSync(command, { 
      cwd, 
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8'
    });
    return result;
  } catch (error) {
    log.error(`Command failed: ${command}`);
    if (options.exitOnError !== false) {
      process.exit(1);
    }
    return null;
  }
}

/**
 * Check if required tools are available
 */
function checkToolsAvailability() {
  const tools = [
    { name: 'ESLint', command: 'npx eslint --version' },
    { name: 'Prettier', command: 'npx prettier --version' }
  ];

  const missingTools = [];

  tools.forEach(tool => {
    try {
      execSync(tool.command, { stdio: 'pipe' });
      log.success(`${tool.name} is available`);
    } catch (error) {
      log.error(`${tool.name} is not available`);
      missingTools.push(tool.name);
    }
  });

  if (missingTools.length > 0) {
    log.error(`Missing tools: ${missingTools.join(', ')}`);
    log.info('Install missing tools with: npm install --save-dev eslint prettier');
    return false;
  }

  return true;
}

/**
 * Run ESLint
 */
function runLinting(options = {}) {
  log.info('Running ESLint...');
  
  const eslintArgs = [
    'eslint',
    options.fix ? '--fix' : '',
    options.quiet ? '--quiet' : '',
    options.format ? `--format ${options.format}` : '--format stylish',
    options.outputFile ? `--output-file ${options.outputFile}` : '',
    options.paths || 'client/src services/*/src scripts'
  ].filter(Boolean).join(' ');

  const result = execCommand(`npx ${eslintArgs}`, ROOT_DIR, { exitOnError: false });
  
  if (result === null) {
    log.error('ESLint found issues');
    return false;
  }
  
  log.success('ESLint passed');
  return true;
}

/**
 * Run Prettier
 */
function runFormatting(options = {}) {
  log.info('Running Prettier...');
  
  const prettierArgs = [
    'prettier',
    options.check ? '--check' : '--write',
    options.paths || '"**/*.{js,jsx,ts,tsx,json,css,md}"'
  ].filter(Boolean).join(' ');

  const result = execCommand(`npx ${prettierArgs}`, ROOT_DIR, { exitOnError: false });
  
  if (result === null) {
    if (options.check) {
      log.error('Code formatting issues found');
      log.info('Run "npm run format" to fix formatting issues');
    } else {
      log.error('Prettier formatting failed');
    }
    return false;
  }
  
  log.success(options.check ? 'Code formatting is correct' : 'Code formatted successfully');
  return true;
}

/**
 * Run type checking
 */
function runTypeChecking(options = {}) {
  log.info('Running type checking...');
  
  // Check if TypeScript is configured
  const tsConfigPaths = [
    path.join(ROOT_DIR, 'tsconfig.json'),
    path.join(ROOT_DIR, 'client', 'tsconfig.json')
  ];
  
  const hasTSConfig = tsConfigPaths.some(configPath => fs.existsSync(configPath));
  
  if (!hasTSConfig) {
    log.info('No TypeScript configuration found, skipping type checking');
    return true;
  }
  
  const result = execCommand('npx tsc --noEmit', ROOT_DIR, { exitOnError: false });
  
  if (result === null) {
    log.error('Type checking failed');
    return false;
  }
  
  log.success('Type checking passed');
  return true;
}

/**
 * Run dependency audit
 */
function runDependencyAudit(options = {}) {
  log.info('Running dependency audit...');
  
  const auditLevel = options.level || 'moderate';
  const result = execCommand(`npm audit --audit-level ${auditLevel}`, ROOT_DIR, { exitOnError: false });
  
  if (result === null) {
    log.error(`Dependency audit failed (level: ${auditLevel})`);
    log.info('Run "npm audit fix" to fix automatically fixable issues');
    return false;
  }
  
  log.success('Dependency audit passed');
  return true;
}

/**
 * Check code complexity
 */
function runComplexityCheck(options = {}) {
  log.info('Checking code complexity...');
  
  try {
    // Check if complexity tool is available
    execSync('npx complexity-report --version', { stdio: 'pipe' });
  } catch (error) {
    log.warn('Complexity report tool not available');
    log.info('Install with: npm install --save-dev complexity-report');
    return true; // Don't fail if tool is not available
  }
  
  const complexityArgs = [
    'complexity-report',
    '--format json',
    '--output complexity-report.json',
    options.paths || 'client/src services/*/src'
  ].join(' ');
  
  const result = execCommand(`npx ${complexityArgs}`, ROOT_DIR, { exitOnError: false });
  
  if (result === null) {
    log.warn('Complexity check completed with warnings');
    return true; // Don't fail on complexity warnings
  }
  
  log.success('Code complexity check passed');
  return true;
}

/**
 * Generate quality report
 */
function generateQualityReport(options = {}) {
  log.info('Generating quality report...');
  
  const report = {
    timestamp: new Date().toISOString(),
    platform: 'NERAFUS',
    checks: {}
  };
  
  // Run all quality checks
  report.checks.linting = runLinting({ ...options, outputFile: 'eslint-report.json', format: 'json' });
  report.checks.formatting = runFormatting({ ...options, check: true });
  report.checks.typeChecking = runTypeChecking(options);
  report.checks.dependencyAudit = runDependencyAudit(options);
  report.checks.complexity = runComplexityCheck(options);
  
  // Calculate overall score
  const passedChecks = Object.values(report.checks).filter(Boolean).length;
  const totalChecks = Object.keys(report.checks).length;
  report.score = Math.round((passedChecks / totalChecks) * 100);
  report.status = report.score >= 80 ? 'PASS' : 'FAIL';
  
  // Save report
  const reportPath = path.join(ROOT_DIR, 'quality-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  // Display summary
  console.log('\n📊 Quality Report Summary:');
  console.log('─'.repeat(40));
  
  Object.entries(report.checks).forEach(([check, passed]) => {
    const icon = passed ? '✅' : '❌';
    const status = passed ? 'PASSED' : 'FAILED';
    console.log(`${icon} ${check.padEnd(20)} ${status}`);
  });
  
  console.log('─'.repeat(40));
  console.log(`Overall Score: ${report.score}% (${report.status})`);
  console.log(`Report saved: ${reportPath}`);
  
  return report.status === 'PASS';
}

/**
 * Set up pre-commit hooks
 */
function setupPreCommitHooks(options = {}) {
  log.info('Setting up pre-commit hooks...');
  
  const hookContent = `#!/bin/sh
# NERAFUS Platform Pre-commit Hook
# Runs code quality checks before commit

echo "🔍 Running pre-commit quality checks..."

# Run linting
npm run lint:check
if [ $? -ne 0 ]; then
  echo "❌ Linting failed. Please fix issues before committing."
  exit 1
fi

# Run formatting check
npm run format:check
if [ $? -ne 0 ]; then
  echo "❌ Code formatting issues found. Run 'npm run format' to fix."
  exit 1
fi

# Run type checking
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ Type checking failed. Please fix type errors."
  exit 1
fi

echo "✅ Pre-commit checks passed!"
`;

  const hookPath = path.join(ROOT_DIR, '.git', 'hooks', 'pre-commit');
  
  try {
    fs.writeFileSync(hookPath, hookContent);
    fs.chmodSync(hookPath, '755');
    log.success('Pre-commit hook installed successfully');
  } catch (error) {
    log.error('Failed to install pre-commit hook:', error.message);
    return false;
  }
  
  return true;
}

/**
 * Run all quality checks
 */
function runAllChecks(options = {}) {
  log.info('Running complete code quality suite...');
  
  const results = {
    linting: false,
    formatting: false,
    typeChecking: false,
    dependencyAudit: false,
    complexity: false
  };
  
  // Run checks in order
  if (!options.skipLint) {
    results.linting = runLinting(options);
  }
  
  if (!options.skipFormat) {
    results.formatting = runFormatting({ ...options, check: true });
  }
  
  if (!options.skipTypeCheck) {
    results.typeChecking = runTypeChecking(options);
  }
  
  if (!options.skipAudit) {
    results.dependencyAudit = runDependencyAudit(options);
  }
  
  if (!options.skipComplexity) {
    results.complexity = runComplexityCheck(options);
  }
  
  // Print summary
  console.log('\n📊 Quality Check Results:');
  console.log('─'.repeat(40));
  
  Object.entries(results).forEach(([check, passed]) => {
    if (passed !== false) {
      const icon = passed ? '✅' : '❌';
      const status = passed ? 'PASSED' : 'FAILED';
      console.log(`${icon} ${check.padEnd(20)} ${status}`);
    }
  });
  
  const failedChecks = Object.entries(results).filter(([, passed]) => passed === false);
  
  if (failedChecks.length > 0) {
    console.log(`\n❌ ${failedChecks.length} quality check(s) failed`);
    process.exit(1);
  } else {
    console.log('\n🎉 All quality checks passed!');
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'check';
  
  const options = {
    fix: args.includes('--fix'),
    check: args.includes('--check'),
    quiet: args.includes('--quiet'),
    skipLint: args.includes('--skip-lint'),
    skipFormat: args.includes('--skip-format'),
    skipTypeCheck: args.includes('--skip-type-check'),
    skipAudit: args.includes('--skip-audit'),
    skipComplexity: args.includes('--skip-complexity'),
    level: args.find(arg => arg.startsWith('--level='))?.split('=')[1] || 'moderate',
    format: args.find(arg => arg.startsWith('--format='))?.split('=')[1],
    paths: args.find(arg => arg.startsWith('--paths='))?.split('=')[1]
  };

  if (!checkToolsAvailability()) {
    process.exit(1);
  }

  switch (command) {
    case 'lint':
      runLinting(options);
      break;

    case 'format':
      runFormatting(options);
      break;

    case 'type-check':
      runTypeChecking(options);
      break;

    case 'audit':
      runDependencyAudit(options);
      break;

    case 'complexity':
      runComplexityCheck(options);
      break;

    case 'report':
      generateQualityReport(options);
      break;

    case 'setup-hooks':
      setupPreCommitHooks(options);
      break;

    case 'check':
    case 'all':
      runAllChecks(options);
      break;

    default:
      console.log(`
Usage: node quality.js <command> [options]

Commands:
  lint            Run ESLint
  format          Run Prettier formatting
  type-check      Run TypeScript type checking
  audit           Run dependency audit
  complexity      Check code complexity
  report          Generate quality report
  setup-hooks     Install pre-commit hooks
  check|all       Run all quality checks (default)

Options:
  --fix           Auto-fix issues where possible
  --check         Check formatting without fixing
  --quiet         Reduce output verbosity
  --skip-lint     Skip linting
  --skip-format   Skip formatting check
  --skip-type-check  Skip type checking
  --skip-audit    Skip dependency audit
  --skip-complexity  Skip complexity check
  --level=<level> Audit level (low|moderate|high|critical)
  --format=<fmt>  ESLint output format
  --paths=<paths> Specific paths to check

Examples:
  node quality.js lint --fix
  node quality.js format --check
  node quality.js all --skip-complexity
  node quality.js audit --level=high
  node quality.js setup-hooks
      `);
      process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  runLinting,
  runFormatting,
  runTypeChecking,
  runDependencyAudit,
  runComplexityCheck,
  generateQualityReport,
  setupPreCommitHooks,
  runAllChecks
};
