/**
 * Insert Mock Data for Team Service Testing
 * This script will populate the database with sample data for testing
 */

const { query, transaction } = require('../src/config/postgresql');
const { v4: uuidv4 } = require('uuid');
const logger = require('../src/utils/logger');

// Mock data
const mockTeams = [
  {
    id: uuidv4(),
    name: "TechDream Team",
    slogan: "Biến ý tưởng thành hiện thực",
    description: "Đội ngũ chuyên gia phát triển ứng dụng di động và web với kinh nghiệm 5+ năm. Chúng tôi chuyên về React Native, Node.js, và UI/UX Design.",
    logo_url: "https://via.placeholder.com/150/3B82F6/FFFFFF?text=TD",
    leader_id: "user_leader_1",
    member_count: 3,
    status: "active",
    category: "development",
    rating: 4.9,
    total_projects: 8,
    total_earnings: 25000
  },
  {
    id: uuidv4(),
    name: "Creative Studio",
    slogan: "Sáng tạo không giới hạn",
    description: "Studio thiết kế đồ họa và branding chuyên nghiệp. Chúng tôi tạo ra những thiết kế độc đáo và ấn tượng.",
    logo_url: "https://via.placeholder.com/150/10B981/FFFFFF?text=CS",
    leader_id: "user_leader_2",
    member_count: 3,
    status: "active",
    category: "design",
    rating: 4.8,
    total_projects: 12,
    total_earnings: 18500
  },
  {
    id: uuidv4(),
    name: "Digital Marketing Pro",
    slogan: "Tăng trưởng doanh thu cùng chúng tôi",
    description: "Đội ngũ marketing chuyên nghiệp với kinh nghiệm SEO, SEM, Social Media Marketing và Content Marketing.",
    logo_url: "https://via.placeholder.com/150/F59E0B/FFFFFF?text=DM",
    leader_id: "user_leader_3",
    member_count: 4,
    status: "active",
    category: "marketing",
    rating: 4.7,
    total_projects: 15,
    total_earnings: 32000
  }
];

const mockUsers = [
  {
    id: "user_leader_1",
    name: "Nguyễn Văn A",
    email: "<EMAIL>",
    rating: 4.9,
    completed_projects: 25
  },
  {
    id: "user_member_1",
    name: "Trần Thị B", 
    email: "<EMAIL>",
    rating: 4.8,
    completed_projects: 18
  },
  {
    id: "user_member_2",
    name: "Lê Văn C",
    email: "<EMAIL>", 
    rating: 4.9,
    completed_projects: 22
  },
  {
    id: "user_leader_2",
    name: "Phạm Thị D",
    email: "<EMAIL>",
    rating: 4.9,
    completed_projects: 30
  },
  {
    id: "user_member_3",
    name: "Hoàng Văn E",
    email: "<EMAIL>",
    rating: 4.8,
    completed_projects: 16
  },
  {
    id: "user_member_4",
    name: "Vũ Thị F",
    email: "<EMAIL>",
    rating: 4.9,
    completed_projects: 20
  },
  {
    id: "user_leader_3",
    name: "Đỗ Văn G",
    email: "<EMAIL>",
    rating: 4.7,
    completed_projects: 28
  },
  {
    id: "user_member_5",
    name: "Bùi Thị H",
    email: "<EMAIL>",
    rating: 4.8,
    completed_projects: 19
  },
  {
    id: "user_member_6",
    name: "Ngô Văn I",
    email: "<EMAIL>",
    rating: 4.7,
    completed_projects: 17
  },
  {
    id: "user_member_7",
    name: "Lý Thị K",
    email: "<EMAIL>",
    rating: 4.8,
    completed_projects: 21
  }
];

const mockTeamMembers = [
  // TechDream Team members
  {
    team_id: mockTeams[0].id,
    user_id: "user_leader_1",
    role: "leader",
    position: "Full-stack Developer",
    profit_share: 40,
    status: "active"
  },
  {
    team_id: mockTeams[0].id,
    user_id: "user_member_1", 
    role: "member",
    position: "Backend Developer",
    profit_share: 30,
    status: "active"
  },
  {
    team_id: mockTeams[0].id,
    user_id: "user_member_2",
    role: "member", 
    position: "UI/UX Designer",
    profit_share: 30,
    status: "active"
  },
  
  // Creative Studio members
  {
    team_id: mockTeams[1].id,
    user_id: "user_leader_2",
    role: "leader",
    position: "Graphic Designer",
    profit_share: 35,
    status: "active"
  },
  {
    team_id: mockTeams[1].id,
    user_id: "user_member_3",
    role: "member",
    position: "Brand Strategist", 
    profit_share: 35,
    status: "active"
  },
  {
    team_id: mockTeams[1].id,
    user_id: "user_member_4",
    role: "member",
    position: "Illustrator",
    profit_share: 30,
    status: "active"
  },
  
  // Digital Marketing Pro members
  {
    team_id: mockTeams[2].id,
    user_id: "user_leader_3",
    role: "leader",
    position: "Marketing Manager",
    profit_share: 30,
    status: "active"
  },
  {
    team_id: mockTeams[2].id,
    user_id: "user_member_5",
    role: "member",
    position: "SEO Specialist",
    profit_share: 25,
    status: "active"
  },
  {
    team_id: mockTeams[2].id,
    user_id: "user_member_6",
    role: "member",
    position: "Content Creator",
    profit_share: 25,
    status: "active"
  },
  {
    team_id: mockTeams[2].id,
    user_id: "user_member_7",
    role: "member",
    position: "Social Media Manager",
    profit_share: 20,
    status: "active"
  }
];

const mockProjects = [
  {
    id: uuidv4(),
    title: "Ứng dụng E-commerce",
    description: "Phát triển ứng dụng mua sắm online với React Native",
    budget: 8000,
    category: "development"
  },
  {
    id: uuidv4(),
    title: "Website Corporate",
    description: "Thiết kế và phát triển website công ty",
    budget: 5500,
    category: "development"
  },
  {
    id: uuidv4(),
    title: "Brand Identity Design",
    description: "Thiết kế bộ nhận diện thương hiệu hoàn chỉnh",
    budget: 3000,
    category: "design"
  },
  {
    id: uuidv4(),
    title: "Marketing Campaign",
    description: "Chiến dịch marketing tổng thể cho sản phẩm mới",
    budget: 5000,
    category: "marketing"
  }
];

const mockTeamProjects = [
  {
    team_id: mockTeams[0].id,
    project_id: mockProjects[0].id,
    status: "completed",
    earnings: 8000
  },
  {
    team_id: mockTeams[0].id,
    project_id: mockProjects[1].id,
    status: "completed", 
    earnings: 5500
  },
  {
    team_id: mockTeams[1].id,
    project_id: mockProjects[2].id,
    status: "completed",
    earnings: 3000
  },
  {
    team_id: mockTeams[2].id,
    project_id: mockProjects[3].id,
    status: "active",
    earnings: 5000
  }
];

const mockChatMessages = [
  {
    team_id: mockTeams[0].id,
    sender_id: "user_leader_1",
    message: "Chào team! Hôm nay chúng ta sẽ bắt đầu dự án mới.",
    message_type: "text"
  },
  {
    team_id: mockTeams[0].id,
    sender_id: "user_member_1",
    message: "Tôi đã chuẩn bị sẵn sàng cho backend development.",
    message_type: "text"
  },
  {
    team_id: mockTeams[0].id,
    sender_id: "user_member_2",
    message: "UI/UX design đã hoàn thành 80%, sẽ xong trong 2 ngày nữa.",
    message_type: "text"
  },
  {
    team_id: mockTeams[1].id,
    sender_id: "user_leader_2",
    message: "Creative Studio team meeting vào 2h chiều hôm nay nhé!",
    message_type: "text"
  },
  {
    team_id: mockTeams[1].id,
    sender_id: "user_member_3",
    message: "Tôi sẽ chuẩn bị presentation cho brand strategy.",
    message_type: "text"
  },
  {
    team_id: mockTeams[2].id,
    sender_id: "user_leader_3",
    message: "Marketing campaign đang chạy tốt, KPI đạt 120%!",
    message_type: "text"
  }
];

const mockInvitations = [
  {
    team_id: mockTeams[0].id,
    inviter_id: "user_leader_1",
    invitee_id: "user_invitee_1",
    message: "Bạn được mời tham gia TechDream Team",
    status: "pending"
  },
  {
    team_id: mockTeams[1].id,
    inviter_id: "user_leader_2", 
    invitee_id: "user_invitee_2",
    message: "Bạn được mời tham gia Creative Studio",
    status: "pending"
  }
];

async function insertMockData() {
  try {
    console.log('🚀 Bắt đầu insert mock data...');
    
    await transaction(async (client) => {
      // Insert mock users (if users table exists)
      console.log('📝 Inserting mock users...');
      for (const user of mockUsers) {
        try {
          await client.query(`
            INSERT INTO users (id, name, email, rating, completed_projects)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (id) DO UPDATE SET
              name = EXCLUDED.name,
              email = EXCLUDED.email,
              rating = EXCLUDED.rating,
              completed_projects = EXCLUDED.completed_projects
          `, [user.id, user.name, user.email, user.rating, user.completed_projects]);
        } catch (error) {
          console.log(`⚠️ Users table might not exist, skipping: ${error.message}`);
        }
      }
      
      // Insert mock teams
      console.log('🏢 Inserting mock teams...');
      for (const team of mockTeams) {
        await client.query(`
          INSERT INTO teams (id, name, slogan, description, logo_url, leader_id, member_count, status, category, rating, total_projects, total_earnings)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          ON CONFLICT (id) DO UPDATE SET
            name = EXCLUDED.name,
            slogan = EXCLUDED.slogan,
            description = EXCLUDED.description,
            logo_url = EXCLUDED.logo_url,
            leader_id = EXCLUDED.leader_id,
            member_count = EXCLUDED.member_count,
            status = EXCLUDED.status,
            category = EXCLUDED.category,
            rating = EXCLUDED.rating,
            total_projects = EXCLUDED.total_projects,
            total_earnings = EXCLUDED.total_earnings
        `, [team.id, team.name, team.slogan, team.description, team.logo_url, team.leader_id, team.member_count, team.status, team.category, team.rating, team.total_projects, team.total_earnings]);
      }
      
      // Insert mock team members
      console.log('👥 Inserting mock team members...');
      for (const member of mockTeamMembers) {
        await client.query(`
          INSERT INTO team_members (team_id, user_id, role, position, profit_share, status)
          VALUES ($1, $2, $3, $4, $5, $6)
          ON CONFLICT (team_id, user_id) DO UPDATE SET
            role = EXCLUDED.role,
            position = EXCLUDED.position,
            profit_share = EXCLUDED.profit_share,
            status = EXCLUDED.status
        `, [member.team_id, member.user_id, member.role, member.position, member.profit_share, member.status]);
      }
      
      // Insert mock projects (if projects table exists)
      console.log('📋 Inserting mock projects...');
      for (const project of mockProjects) {
        try {
          await client.query(`
            INSERT INTO projects (id, title, description, budget, category)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (id) DO UPDATE SET
              title = EXCLUDED.title,
              description = EXCLUDED.description,
              budget = EXCLUDED.budget,
              category = EXCLUDED.category
          `, [project.id, project.title, project.description, project.budget, project.category]);
        } catch (error) {
          console.log(`⚠️ Projects table might not exist, skipping: ${error.message}`);
        }
      }
      
      // Insert mock team projects
      console.log('🔗 Inserting mock team projects...');
      for (const teamProject of mockTeamProjects) {
        await client.query(`
          INSERT INTO team_projects (team_id, project_id, status, earnings)
          VALUES ($1, $2, $3, $4)
          ON CONFLICT (team_id, project_id) DO UPDATE SET
            status = EXCLUDED.status,
            earnings = EXCLUDED.earnings
        `, [teamProject.team_id, teamProject.project_id, teamProject.status, teamProject.earnings]);
      }
      
      // Insert mock chat messages
      console.log('💬 Inserting mock chat messages...');
      for (const message of mockChatMessages) {
        await client.query(`
          INSERT INTO team_chat_messages (team_id, sender_id, message, message_type)
          VALUES ($1, $2, $3, $4)
        `, [message.team_id, message.sender_id, message.message, message.message_type]);
      }
      
      // Insert mock invitations
      console.log('📨 Inserting mock invitations...');
      for (const invitation of mockInvitations) {
        await client.query(`
          INSERT INTO team_invitations (team_id, inviter_id, invitee_id, message, status)
          VALUES ($1, $2, $3, $4, $5)
          ON CONFLICT (team_id, invitee_id) DO UPDATE SET
            inviter_id = EXCLUDED.inviter_id,
            message = EXCLUDED.message,
            status = EXCLUDED.status
        `, [invitation.team_id, invitation.inviter_id, invitation.invitee_id, invitation.message, invitation.status]);
      }
    });
    
    console.log('✅ Mock data inserted successfully!');
    console.log(`📊 Inserted ${mockTeams.length} teams`);
    console.log(`👥 Inserted ${mockTeamMembers.length} team members`);
    console.log(`💬 Inserted ${mockChatMessages.length} chat messages`);
    console.log(`📨 Inserted ${mockInvitations.length} invitations`);
    
    // Print team IDs for testing
    console.log('\n🔍 Team IDs for testing:');
    mockTeams.forEach((team, index) => {
      console.log(`${index + 1}. ${team.name}: ${team.id}`);
    });
    
  } catch (error) {
    console.error('❌ Error inserting mock data:', error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  insertMockData()
    .then(() => {
      console.log('🎉 Mock data insertion completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Mock data insertion failed:', error);
      process.exit(1);
    });
}

module.exports = { insertMockData, mockTeams, mockUsers, mockTeamMembers }; 