#!/usr/bin/env node

/**
 * Automated Community Service Deployment Script for Render
 * Handles database setup, environment configuration, and deployment verification
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

class CommunityServiceDeployer {
  constructor() {
    this.serviceName = 'nerafus-community-service';
    this.serviceDir = path.join(__dirname, '../services/community-service');
    this.renderYamlPath = path.join(this.serviceDir, 'render.yaml');
  }

  /**
   * Validate prerequisites
   */
  validatePrerequisites() {
    log('🔍 Validating prerequisites...', 'blue');
    
    // Check if render.yaml exists
    if (!fs.existsSync(this.renderYamlPath)) {
      throw new Error('render.yaml not found in community-service directory');
    }
    
    // Check required environment variables
    const requiredEnvVars = [
      'DATABASE_URL',
      'FIREBASE_PROJECT_ID',
      'FIREBASE_CLIENT_EMAIL',
      'FIREBASE_PRIVATE_KEY'
    ];
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
    
    log('✅ Prerequisites validation passed', 'green');
  }

  /**
   * Generate environment variables configuration
   */
  generateEnvConfig() {
    log('⚙️ Generating environment configuration...', 'blue');
    
    const envConfig = {
      NODE_ENV: 'production',
      PORT: '10000',
      DB_TYPE: 'postgresql',
      DATABASE_URL: process.env.DATABASE_URL,
      FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
      FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
      FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY,
      ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS || 'https://nerafus-client.onrender.com,https://vwork-api-gateway.onrender.com,https://nerafus.com',
      CORS_ORIGIN: process.env.CORS_ORIGIN || 'https://nerafus-client.onrender.com,https://vwork-api-gateway.onrender.com,https://nerafus.com',
      CORS_CREDENTIALS: 'true',
      LOG_LEVEL: 'info',
      LOG_FORMAT: 'json',
      ENABLE_COMPRESSION: 'true',
      ENABLE_HELMET: 'true',
      TRUST_PROXY: 'true',
      DB_POOL_MAX: '10',
      DB_POOL_MIN: '2',
      DB_CONNECTION_TIMEOUT: '5000',
      DB_IDLE_TIMEOUT: '30000'
    };
    
    // Save to file for reference
    const envFilePath = path.join(this.serviceDir, '.env.production');
    const envContent = Object.entries(envConfig)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
    
    fs.writeFileSync(envFilePath, envContent);
    log(`✅ Environment configuration saved to ${envFilePath}`, 'green');
    
    return envConfig;
  }

  /**
   * Test database connection
   */
  async testDatabaseConnection() {
    log('🔌 Testing database connection...', 'blue');
    
    try {
      const { Pool } = require('pg');
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: { rejectUnauthorized: false }
      });
      
      const client = await pool.connect();
      const result = await client.query('SELECT NOW() as current_time, version() as version');
      client.release();
      await pool.end();
      
      log('✅ Database connection successful', 'green');
      log(`   Database: ${result.rows[0].version.split(' ')[0]} ${result.rows[0].version.split(' ')[1]}`, 'cyan');
      log(`   Current time: ${result.rows[0].current_time}`, 'cyan');
      
    } catch (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  /**
   * Initialize database schema
   */
  async initializeDatabase() {
    log('📊 Initializing database schema...', 'blue');
    
    try {
      // Change to service directory
      const originalDir = process.cwd();
      process.chdir(this.serviceDir);
      
      // Run database initialization
      execSync('npm run db:init', { stdio: 'inherit' });
      
      // Change back to original directory
      process.chdir(originalDir);
      
      log('✅ Database schema initialized successfully', 'green');
      
    } catch (error) {
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }

  /**
   * Deploy to Render using render.yaml
   */
  async deployToRender() {
    log('🚀 Deploying to Render...', 'blue');
    
    try {
      // Check if render CLI is installed
      try {
        execSync('render --version', { stdio: 'pipe' });
      } catch (error) {
        log('⚠️ Render CLI not found. Please install it first:', 'yellow');
        log('   npm install -g @render/cli', 'cyan');
        log('   Then run: render login', 'cyan');
        return false;
      }
      
      // Deploy using render.yaml
      const originalDir = process.cwd();
      process.chdir(this.serviceDir);
      
      execSync('render deploy', { stdio: 'inherit' });
      
      process.chdir(originalDir);
      
      log('✅ Deployment initiated successfully', 'green');
      return true;
      
    } catch (error) {
      throw new Error(`Deployment failed: ${error.message}`);
    }
  }

  /**
   * Wait for deployment to complete
   */
  async waitForDeployment() {
    log('⏳ Waiting for deployment to complete...', 'blue');
    
    const maxWaitTime = 300000; // 5 minutes
    const checkInterval = 10000; // 10 seconds
    let elapsed = 0;
    
    while (elapsed < maxWaitTime) {
      try {
        // Check service status using render CLI
        const status = execSync(`render ps ${this.serviceName}`, { encoding: 'utf8' });
        
        if (status.includes('Live')) {
          log('✅ Service is live!', 'green');
          return true;
        }
        
        log(`⏳ Service status: ${status.trim()}`, 'yellow');
        
      } catch (error) {
        log(`⏳ Still deploying... (${elapsed / 1000}s elapsed)`, 'yellow');
      }
      
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      elapsed += checkInterval;
    }
    
    throw new Error('Deployment timeout - service did not become live within 5 minutes');
  }

  /**
   * Test deployed service
   */
  async testDeployedService() {
    log('🧪 Testing deployed service...', 'blue');
    
    try {
      // Get service URL
      const serviceUrl = `https://${this.serviceName}.onrender.com`;
      
      // Test health endpoint
      const healthResponse = execSync(`curl -s ${serviceUrl}/health`, { encoding: 'utf8' });
      const healthData = JSON.parse(healthResponse);
      
      if (healthData.status === 'healthy') {
        log('✅ Health check passed', 'green');
      } else {
        throw new Error(`Health check failed: ${healthData.status}`);
      }
      
      // Test basic endpoint
      const basicResponse = execSync(`curl -s ${serviceUrl}/`, { encoding: 'utf8' });
      const basicData = JSON.parse(basicResponse);
      
      if (basicData.message === 'community-service is running') {
        log('✅ Basic endpoint test passed', 'green');
      }
      
      // Test API info endpoint
      const apiResponse = execSync(`curl -s ${serviceUrl}/api`, { encoding: 'utf8' });
      const apiData = JSON.parse(apiResponse);
      
      if (apiData.service === 'community-service') {
        log('✅ API info endpoint test passed', 'green');
      }
      
      log(`🎉 Service deployed successfully at: ${serviceUrl}`, 'green');
      return serviceUrl;
      
    } catch (error) {
      throw new Error(`Service testing failed: ${error.message}`);
    }
  }

  /**
   * Generate deployment summary
   */
  generateDeploymentSummary(serviceUrl) {
    log('\n📋 Deployment Summary', 'magenta');
    log('====================', 'magenta');
    log(`Service Name: ${this.serviceName}`, 'cyan');
    log(`Service URL: ${serviceUrl}`, 'cyan');
    log(`Health Check: ${serviceUrl}/health`, 'cyan');
    log(`API Info: ${serviceUrl}/api`, 'cyan');
    log(`Database: PostgreSQL (connected)`, 'cyan');
    log(`Environment: Production`, 'cyan');
    
    log('\n🔗 Useful Endpoints:', 'magenta');
    log(`- Posts: ${serviceUrl}/api/posts`, 'cyan');
    log(`- Comments: ${serviceUrl}/api/comments`, 'cyan');
    log(`- Users: ${serviceUrl}/api/users`, 'cyan');
    log(`- Likes: ${serviceUrl}/api/likes`, 'cyan');
    
    log('\n📝 Next Steps:', 'magenta');
    log('1. Update frontend configuration to use new service URL', 'yellow');
    log('2. Test all API endpoints with your application', 'yellow');
    log('3. Monitor service logs on Render dashboard', 'yellow');
    log('4. Set up custom domain if needed', 'yellow');
  }

  /**
   * Main deployment process
   */
  async deploy() {
    try {
      log('🚀 Starting Community Service deployment to Render...', 'blue');
      log('==================================================', 'blue');
      
      // Step 1: Validate prerequisites
      this.validatePrerequisites();
      
      // Step 2: Generate environment configuration
      this.generateEnvConfig();
      
      // Step 3: Test database connection
      await this.testDatabaseConnection();
      
      // Step 4: Initialize database schema
      await this.initializeDatabase();
      
      // Step 5: Deploy to Render
      const deploySuccess = await this.deployToRender();
      
      if (deploySuccess) {
        // Step 6: Wait for deployment
        await this.waitForDeployment();
        
        // Step 7: Test deployed service
        const serviceUrl = await this.testDeployedService();
        
        // Step 8: Generate summary
        this.generateDeploymentSummary(serviceUrl);
        
        log('\n🎉 Community Service deployment completed successfully!', 'green');
      } else {
        log('\n⚠️ Manual deployment required. Please follow the guide in COMMUNITY_SERVICE_DEPLOYMENT_GUIDE.md', 'yellow');
      }
      
    } catch (error) {
      log(`💥 Deployment failed: ${error.message}`, 'red');
      log('\n🔧 Troubleshooting tips:', 'yellow');
      log('1. Check your DATABASE_URL is correct', 'yellow');
      log('2. Verify Firebase credentials are set', 'yellow');
      log('3. Ensure you have Render CLI installed and logged in', 'yellow');
      log('4. Check the deployment guide for manual steps', 'yellow');
      process.exit(1);
    }
  }
}

// CLI interface
if (require.main === module) {
  const deployer = new CommunityServiceDeployer();
  
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    log('Community Service Render Deployment Script', 'blue');
    log('==========================================', 'blue');
    log('Usage: node deploy-community-service.js [options]', 'cyan');
    log('', 'reset');
    log('Options:', 'magenta');
    log('  --help, -h     Show this help message', 'cyan');
    log('  --test-only    Only test database connection', 'cyan');
    log('  --init-only    Only initialize database schema', 'cyan');
    log('', 'reset');
    log('Environment Variables Required:', 'magenta');
    log('  DATABASE_URL           PostgreSQL connection string', 'cyan');
    log('  FIREBASE_PROJECT_ID    Firebase project ID', 'cyan');
    log('  FIREBASE_CLIENT_EMAIL  Firebase client email', 'cyan');
    log('  FIREBASE_PRIVATE_KEY   Firebase private key', 'cyan');
    log('  ALLOWED_ORIGINS        CORS allowed origins (optional)', 'cyan');
    process.exit(0);
  }
  
  if (args.includes('--test-only')) {
    deployer.testDatabaseConnection()
      .then(() => {
        log('✅ Database connection test completed successfully', 'green');
        process.exit(0);
      })
      .catch(error => {
        log(`❌ Database connection test failed: ${error.message}`, 'red');
        process.exit(1);
      });
  } else if (args.includes('--init-only')) {
    deployer.initializeDatabase()
      .then(() => {
        log('✅ Database initialization completed successfully', 'green');
        process.exit(0);
      })
      .catch(error => {
        log(`❌ Database initialization failed: ${error.message}`, 'red');
        process.exit(1);
      });
  } else {
    deployer.deploy();
  }
}

module.exports = { CommunityServiceDeployer }; 