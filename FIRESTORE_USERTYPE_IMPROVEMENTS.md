# Cải thiện Logic userType trong Firestore

## Tổng quan
Tài liệu này mô tả những cải thiện đã thực hiện để đảm bảo `userType` được lưu trữ và truy xuất một cách nhất quán trong Firestore cho cả `tempUsers` và `users` collections.

## Vấn đề ban đầu
- `userType` không được lưu trữ nhất quán giữa các collection
- Khi user đăng ký chọn "client" nhưng vẫn được chuyển đến flow onboarding của freelancer
- Thiếu logic đồng bộ hóa `userType` giữa `tempUsers` và `users`

## Cải thiện đã thực hiện

### 1. <PERSON><PERSON>i thiện trong `AuthContext.js`

#### 1.1. <PERSON><PERSON>m bảo userType được lưu trực tiếp trong Firestore
```javascript
// Trong hàm register()
const tempUserDocData = {
  name: userData.name,
  email: email,
  userType: userData.userType, // Đảm bảo userType được lưu trực tiếp trong Firestore
  // ... other fields
};
```

#### 1.2. Đồng bộ hóa userType khi chuyển từ tempUsers sang users
```javascript
// Trong onAuthStateChanged listener
const verifiedUserData = {
  ...userData,
  isVerified: true,
  emailVerifiedAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  userType: userData.userType, // Đảm bảo userType được giữ nguyên khi chuyển từ tempUsers sang users
  // ... other fields
};
```

#### 1.3. Thêm logging để debug userType
```javascript
// Log userType từ các collection
console.log('🔍 userType from users collection:', userData.userType);
console.log('🔍 userType from tempUsers collection:', userData.userType);
console.log('🔍 Basic user created with userType:', basicUser.userType);
```

#### 1.4. Cải thiện logic fallback cho userType
```javascript
// Try to get userType from multiple sources with priority order
userType: firebaseUser.customClaims?.userType || 
          localStorage.getItem('pending_user_type') || 
          'freelancer', // Fallback default
```

### 2. Thêm hàm đồng bộ hóa userType

#### 2.1. Hàm syncUserType
```javascript
const syncUserType = async (userId, userType) => {
  // Kiểm tra và cập nhật trong collection users
  const userRef = doc(db, 'users', userId);
  const userDoc = await getDoc(userRef);
  
  if (userDoc.exists()) {
    const userData = userDoc.data();
    if (userData.userType !== userType) {
      await updateDoc(userRef, {
        userType: userType,
        updatedAt: new Date().toISOString()
      });
    }
  }

  // Kiểm tra và cập nhật trong collection tempUsers
  const tempUserRef = doc(db, 'tempUsers', userId);
  const tempUserDoc = await getDoc(tempUserRef);
  
  if (tempUserDoc.exists()) {
    const tempUserData = tempUserDoc.data();
    if (tempUserData.userType !== userType) {
      await updateDoc(tempUserRef, {
        userType: userType,
        updatedAt: new Date().toISOString()
      });
    }
  }
};
```

#### 2.2. Tự động đồng bộ hóa khi đăng ký
```javascript
// Trong hàm register()
await setDoc(doc(db, 'tempUsers', firebaseUser.uid), tempUserDocData);
console.log('✅ Temporary user data saved successfully');

// Đồng bộ hóa userType để đảm bảo tính nhất quán
await syncUserType(firebaseUser.uid, userData.userType);
```

### 3. Cải thiện updateUserProfile

#### 3.1. Đảm bảo userType không bị mất khi cập nhật profile
```javascript
const updateData = {
  ...profileData,
  updatedAt: new Date().toISOString(),
};

// Đảm bảo userType được giữ nguyên nếu không được cung cấp trong profileData
if (!profileData.userType && user.userType) {
  updateData.userType = user.userType;
}
```

## Flow hoạt động mới

### 1. Đăng ký user
1. User chọn `userType` (client/freelancer)
2. Lưu vào `tempUsers` collection với `userType`
3. Đồng bộ hóa `userType` giữa các collection
4. Lưu backup vào `localStorage`

### 2. Xác thực email
1. Khi email được xác thực, chuyển data từ `tempUsers` sang `users`
2. Đảm bảo `userType` được giữ nguyên trong quá trình chuyển đổi
3. Xóa backup từ `localStorage`

### 3. Đăng nhập
1. Đọc `userType` từ Firestore (ưu tiên `users` collection)
2. Fallback về `localStorage` nếu cần
3. Fallback về `'freelancer'` nếu không tìm thấy

### 4. Cập nhật profile
1. Đảm bảo `userType` không bị ghi đè khi cập nhật profile
2. Sử dụng hàm `syncUserType` để đồng bộ hóa nếu cần

## Lợi ích

### 1. Tính nhất quán
- `userType` được lưu trữ nhất quán trong cả `tempUsers` và `users`
- Không bị mất `userType` trong quá trình chuyển đổi collection

### 2. Debugging tốt hơn
- Thêm logging chi tiết để theo dõi `userType`
- Dễ dàng xác định nguồn gốc của `userType`

### 3. Fallback robust
- Nhiều lớp fallback để đảm bảo `userType` luôn có giá trị
- Ưu tiên rõ ràng: Firestore > localStorage > default

### 4. Đồng bộ hóa tự động
- Tự động đồng bộ hóa `userType` giữa các collection
- Đảm bảo tính nhất quán dữ liệu

## Kiểm tra

### 1. Test đăng ký client
1. Đăng ký với `userType: 'client'`
2. Kiểm tra `tempUsers` collection có `userType: 'client'`
3. Xác thực email
4. Kiểm tra `users` collection có `userType: 'client'`
5. Đăng nhập và kiểm tra flow onboarding client

### 2. Test đăng ký freelancer
1. Đăng ký với `userType: 'freelancer'`
2. Kiểm tra flow onboarding freelancer
3. Xác thực email và kiểm tra tính nhất quán

### 3. Test fallback
1. Xóa data từ Firestore
2. Kiểm tra fallback về `localStorage`
3. Kiểm tra fallback về default value

## Kết luận

Những cải thiện này đảm bảo:
- `userType` được lưu trữ và truy xuất một cách nhất quán
- Client sẽ được chuyển đến đúng flow onboarding
- Freelancer sẽ được chuyển đến đúng flow onboarding
- Tính ổn định và reliability của hệ thống được cải thiện 