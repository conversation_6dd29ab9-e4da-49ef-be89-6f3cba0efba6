import React from 'react';
import { HeartIcon, SparklesIcon } from '@heroicons/react/24/outline';

const EncouragementMessage = ({ retryCount = 0, errorType = null }) => {
 const getEncouragementMessage = () => {
  if (retryCount === 0) return null;
  
  const messages = {
   1: {
    icon: SparklesIcon,
    iconColor: 'text-blue-500',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    title: 'Đừng lo lắng!',
    message: 'Lỗi này khá phổ biến. Hãy thử lại nhé! 💪'
   },
   2: {
    icon: HeartIcon,
    iconColor: 'text-green-500',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    textColor: 'text-green-800',
    title: 'Bạn đang làm rất tốt!',
    message: '<PERSON><PERSON><PERSON> khi cần thử vài lần. Chúng tôi tin bạn sẽ thành công! ✨'
   },
   3: {
    icon: SparklesIcon,
    iconColor: 'text-purple-500',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    textColor: 'text-purple-800',
    title: 'Kiên trì là chìa khóa!',
    message: 'Bạn gần thành công rồi. Hãy thử phương thức chuyển hướng bên dưới! 🚀'
   }
  };

  // For retry count > 3, use the last message
  const messageKey = retryCount > 3 ? 3 : retryCount;
  return messages[messageKey];
 };

 const encouragement = getEncouragementMessage();
 if (!encouragement) return null;

 const { icon: Icon, iconColor, bgColor, borderColor, textColor, title, message } = encouragement;

 return (
  <div className={`${bgColor} border ${borderColor} rounded-lg p-3 mt-3 animate-in fade-in duration-500`}>
   <div className="flex items-start space-x-3">
    <div className="flex-shrink-0">
     <Icon className={`w-5 h-5 ${iconColor}`} />
    </div>
    <div className={`text-sm ${textColor}`}>
     <p className="font-medium mb-1">{title}</p>
     <p className="text-xs opacity-90">{message}</p>
    </div>
   </div>
  </div>
 );
};

export default EncouragementMessage;
