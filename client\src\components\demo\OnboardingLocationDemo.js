import React, { useState } from 'react';
import { motion } from 'framer-motion';
import LocationDropdown from '../common/LocationDropdown';
import { useLanguage } from '../../contexts/LanguageContext';

const OnboardingLocationDemo = () => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    displayName: '',
    bio: '',
    location: {
      country: '',
      city: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    },
    phoneNumber: '',
    website: ''
  });
  const [errors, setErrors] = useState({});

  const handleLocationChange = (location) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        country: location.country,
        city: location.city
      }
    }));

    // Clear location errors when user makes changes
    if (errors.country || errors.city) {
      setErrors(prev => ({
        ...prev,
        country: null,
        city: null
      }));
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.displayName.trim()) {
      newErrors.displayName = t('nameRequired');
    }

    if (!formData.bio.trim()) {
      newErrors.bio = t('bioRequired');
    } else if (formData.bio.length < 50) {
      newErrors.bio = t('bioTooShort', { min: 50 });
    }

    if (!formData.location.country.trim()) {
      newErrors.country = t('countryRequired');
    }

    if (!formData.location.city.trim()) {
      newErrors.city = t('cityRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      alert(`Form submitted successfully!\nLocation: ${formData.location.city}, ${formData.location.country}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-medieval-gold-50 to-medieval-brown-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-lg p-8"
        >
          <div className="text-center mb-8">
            <h1 className="text-3xl font-medium font-bold text-gray-900 mb-2">
              🌍 Onboarding Location Demo
            </h1>
            <p className="text-gray-600">
              Test LocationDropdown trong form onboarding
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div>
              <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                <span className="inline mr-1">👤</span>
                {t('displayName')} *
              </label>
              <input
                type="text"
                value={formData.displayName}
                onChange={(e) => handleInputChange('displayName', e.target.value)}
                className={`form-input w-full ${errors.displayName ? 'border-red-500' : ''}`}
                placeholder={t('enterDisplayName')}
              />
              {errors.displayName && (
                <p className="text-red-500 text-sm mt-1">{errors.displayName}</p>
              )}
            </div>

            {/* Bio */}
            <div>
              <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                <span className="inline mr-1">📝</span>
                {t('bio')} *
              </label>
              <textarea
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                rows={4}
                className={`form-input w-full ${errors.bio ? 'border-red-500' : ''}`}
                placeholder={t('bioPlaceholder')}
              />
              {errors.bio && (
                <p className="text-red-500 text-sm mt-1">{errors.bio}</p>
              )}
            </div>

            {/* Location Dropdown */}
            <LocationDropdown
              value={{
                country: formData.location.country,
                city: formData.location.city
              }}
              onChange={handleLocationChange}
              error={{
                country: errors.country,
                city: errors.city
              }}
            />

            {/* Optional Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                  <span className="inline mr-1">📞</span>
                  {t('phoneNumber')} ({t('optional')})
                </label>
                <input
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  className="form-input w-full"
                  placeholder={t('enterPhoneNumber')}
                />
              </div>

              <div>
                <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
                  <span className="inline mr-1">🌐</span>
                  {t('website')} ({t('optional')})
                </label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  className="form-input w-full"
                  placeholder="https://yourwebsite.com"
                />
              </div>
            </div>

            {/* Display selected location */}
            {(formData.location.country || formData.location.city) && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-blue-50 border border-blue-200 rounded-lg p-4"
              >
                <h3 className="font-medium font-semibold text-gray-800 mb-2">
                  📍 Selected Location:
                </h3>
                <div className="space-y-1 text-sm">
                  {formData.location.country && (
                    <p><span className="font-medium">Country:</span> {formData.location.country}</p>
                  )}
                  {formData.location.city && (
                    <p><span className="font-medium">City:</span> {formData.location.city}</p>
                  )}
                </div>
              </motion.div>
            )}

            {/* Submit Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
            >
              Submit Form
            </motion.button>
          </form>

          {/* Features List */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="font-medium font-semibold text-gray-800 mb-3">
              ✨ Features:
            </h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• 🌍 Dropdown quốc gia với flag emoji</li>
              <li>• 🔍 Tìm kiếm quốc gia và thành phố</li>
              <li>• 🏙️ Dropdown thành phố theo quốc gia đã chọn</li>
              <li>• ✅ Validation và error handling</li>
              <li>• 🌐 Hỗ trợ đa ngôn ngữ (EN/VI)</li>
              <li>• 📱 Responsive design</li>
              <li>• ⚡ Cache dữ liệu để tối ưu performance</li>
              <li>• 🎨 Animation với Framer Motion</li>
            </ul>
          </div>

          {/* API Information */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium font-semibold text-blue-800 mb-2">
              🔗 API Information:
            </h4>
            <p className="text-sm text-blue-700">
              <strong>Countries:</strong> REST Countries API (free, no API key required)<br/>
              <strong>Cities:</strong> Mock data (can be replaced with GeoDB Cities API)
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default OnboardingLocationDemo; 