/**
 * Team Chat Routes
 * Handle team chat functionality
 */

const express = require('express');
const router = express.Router();

// Import middleware
const { verifyFirebaseToken, requireTeamMembership } = require('../middleware/auth');
const { validateBody, validateQuery, schemas } = require('../middleware/validation');
const { responseMiddleware, createPagination } = require('../utils/response');
const { query } = require('../config/postgresql');
const logger = require('../utils/logger');

// Apply response middleware
router.use(responseMiddleware);

// Get team chat messages
router.get('/team/:teamId', verifyFirebaseToken, requireTeamMembership, validateQuery(schemas.pagination), async (req, res) => {
  try {
    const { teamId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    const messagesQuery = `
      SELECT 
        tcm.*,
        u.email as sender_email,
        u.name as sender_name
      FROM team_chat_messages tcm
      LEFT JOIN users u ON tcm.sender_id = u.id
      WHERE tcm.team_id = $1
      ORDER BY tcm.created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const result = await query(messagesQuery, [teamId, limit, offset]);
    const messages = result.rows.reverse(); // Reverse to get chronological order

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total FROM team_chat_messages WHERE team_id = $1
    `;
    const countResult = await query(countQuery, [teamId]);
    const total = parseInt(countResult.rows[0].total);

    const pagination = createPagination(page, limit, total);

    logger.info(`Retrieved ${messages.length} chat messages for team: ${teamId}`);
    res.apiSuccess(messages, 'Chat messages retrieved successfully', pagination);

  } catch (error) {
    logger.error('Get chat messages failed:', error.message);
    res.apiError('Failed to get chat messages', 'GET_CHAT_MESSAGES_ERROR', 500);
  }
});

// Send chat message
router.post('/team/:teamId', verifyFirebaseToken, requireTeamMembership, validateBody(schemas.chatMessage), async (req, res) => {
  try {
    const { teamId } = req.params;
    const { message, message_type = 'text' } = req.body;
    const senderId = req.user.uid;

    const insertQuery = `
      INSERT INTO team_chat_messages (team_id, sender_id, message, message_type)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await query(insertQuery, [teamId, senderId, message, message_type]);
    const newMessage = result.rows[0];

    // Get sender info
    const senderQuery = `
      SELECT email, name FROM users WHERE id = $1
    `;
    const senderResult = await query(senderQuery, [senderId]);
    const sender = senderResult.rows[0] || { email: 'Unknown', name: 'Unknown' };

    const messageWithSender = {
      ...newMessage,
      sender_email: sender.email,
      sender_name: sender.name
    };

    logger.info(`Chat message sent: team ${teamId} by user ${senderId}`);
    res.apiSuccess(messageWithSender, 'Message sent successfully');

  } catch (error) {
    logger.error('Send chat message failed:', error.message);
    res.apiError('Failed to send message', 'SEND_CHAT_MESSAGE_ERROR', 500);
  }
});

// Delete chat message (sender only)
router.delete('/message/:messageId', verifyFirebaseToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user.uid;

    // Check if user is the sender
    const messageQuery = `
      SELECT * FROM team_chat_messages WHERE id = $1 AND sender_id = $2
    `;
    const messageResult = await query(messageQuery, [messageId, userId]);

    if (messageResult.rows.length === 0) {
      return res.apiNotFound('Message not found or you are not the sender');
    }

    // Delete message
    await query('DELETE FROM team_chat_messages WHERE id = $1', [messageId]);

    logger.info(`Chat message deleted: ${messageId} by user: ${userId}`);
    res.apiSuccess(null, 'Message deleted successfully');

  } catch (error) {
    logger.error('Delete chat message failed:', error.message);
    res.apiError('Failed to delete message', 'DELETE_CHAT_MESSAGE_ERROR', 500);
  }
});

// Get unread message count
router.get('/team/:teamId/unread', verifyFirebaseToken, requireTeamMembership, async (req, res) => {
  try {
    const { teamId } = req.params;
    const userId = req.user.uid;

    // For now, return total messages (in future, implement read status tracking)
    const countQuery = `
      SELECT COUNT(*) as total FROM team_chat_messages 
      WHERE team_id = $1 AND sender_id != $2
    `;

    const result = await query(countQuery, [teamId, userId]);
    const unreadCount = parseInt(result.rows[0].total);

    logger.info(`Unread count for team ${teamId}, user ${userId}: ${unreadCount}`);
    res.apiSuccess({ unread_count: unreadCount }, 'Unread count retrieved successfully');

  } catch (error) {
    logger.error('Get unread count failed:', error.message);
    res.apiError('Failed to get unread count', 'GET_UNREAD_COUNT_ERROR', 500);
  }
});

// Mark messages as read (future implementation)
router.put('/team/:teamId/read', verifyFirebaseToken, requireTeamMembership, async (req, res) => {
  try {
    const { teamId } = req.params;
    const userId = req.user.uid;

    // TODO: Implement read status tracking
    // This would require adding a read_status table or field

    logger.info(`Messages marked as read: team ${teamId} by user ${userId}`);
    res.apiSuccess(null, 'Messages marked as read successfully');

  } catch (error) {
    logger.error('Mark messages as read failed:', error.message);
    res.apiError('Failed to mark messages as read', 'MARK_READ_ERROR', 500);
  }
});

// Get recent messages for multiple teams (for user dashboard)
router.get('/my-teams/recent', verifyFirebaseToken, async (req, res) => {
  try {
    const userId = req.user.uid;

    const recentMessagesQuery = `
      SELECT 
        tcm.*,
        t.name as team_name,
        u.email as sender_email,
        u.name as sender_name
      FROM team_chat_messages tcm
      JOIN teams t ON tcm.team_id = t.id
      JOIN team_members tm ON t.id = tm.team_id
      LEFT JOIN users u ON tcm.sender_id = u.id
      WHERE tm.user_id = $1 AND tm.status = 'active'
      AND tcm.created_at = (
        SELECT MAX(created_at) 
        FROM team_chat_messages 
        WHERE team_id = tcm.team_id
      )
      ORDER BY tcm.created_at DESC
      LIMIT 10
    `;

    const result = await query(recentMessagesQuery, [userId]);
    const recentMessages = result.rows;

    logger.info(`Retrieved ${recentMessages.length} recent messages for user: ${userId}`);
    res.apiSuccess(recentMessages, 'Recent messages retrieved successfully');

  } catch (error) {
    logger.error('Get recent messages failed:', error.message);
    res.apiError('Failed to get recent messages', 'GET_RECENT_MESSAGES_ERROR', 500);
  }
});

// Get chat statistics
router.get('/team/:teamId/stats', verifyFirebaseToken, requireTeamMembership, async (req, res) => {
  try {
    const { teamId } = req.params;

    const statsQuery = `
      SELECT 
        COUNT(*) as total_messages,
        COUNT(DISTINCT sender_id) as unique_senders,
        MIN(created_at) as first_message,
        MAX(created_at) as last_message,
        COUNT(CASE WHEN message_type = 'file' THEN 1 END) as file_messages,
        COUNT(CASE WHEN message_type = 'image' THEN 1 END) as image_messages
      FROM team_chat_messages 
      WHERE team_id = $1
    `;

    const result = await query(statsQuery, [teamId]);
    const stats = result.rows[0];

    logger.info(`Retrieved chat stats for team: ${teamId}`);
    res.apiSuccess(stats, 'Chat statistics retrieved successfully');

  } catch (error) {
    logger.error('Get chat stats failed:', error.message);
    res.apiError('Failed to get chat statistics', 'GET_CHAT_STATS_ERROR', 500);
  }
});

// Search messages in team chat
router.get('/team/:teamId/search', verifyFirebaseToken, requireTeamMembership, validateQuery(schemas.pagination), async (req, res) => {
  try {
    const { teamId } = req.params;
    const { page = 1, limit = 20, query: searchQuery } = req.query;
    const offset = (page - 1) * limit;

    if (!searchQuery) {
      return res.apiBadRequest('Search query is required');
    }

    const searchMessagesQuery = `
      SELECT 
        tcm.*,
        u.email as sender_email,
        u.name as sender_name
      FROM team_chat_messages tcm
      LEFT JOIN users u ON tcm.sender_id = u.id
      WHERE tcm.team_id = $1 AND tcm.message ILIKE $2
      ORDER BY tcm.created_at DESC
      LIMIT $3 OFFSET $4
    `;

    const result = await query(searchMessagesQuery, [teamId, `%${searchQuery}%`, limit, offset]);
    const messages = result.rows;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total FROM team_chat_messages 
      WHERE team_id = $1 AND message ILIKE $2
    `;
    const countResult = await query(countQuery, [teamId, `%${searchQuery}%`]);
    const total = parseInt(countResult.rows[0].total);

    const pagination = createPagination(page, limit, total);

    logger.info(`Search returned ${messages.length} messages for query: "${searchQuery}" in team: ${teamId}`);
    res.apiSuccess(messages, 'Message search completed successfully', pagination);

  } catch (error) {
    logger.error('Search messages failed:', error.message);
    res.apiError('Failed to search messages', 'SEARCH_MESSAGES_ERROR', 500);
  }
});

// Pin message (leader only)
router.put('/message/:messageId/pin', verifyFirebaseToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user.uid;

    // Check if user is team leader
    const messageQuery = `
      SELECT tcm.*, t.leader_id 
      FROM team_chat_messages tcm
      JOIN teams t ON tcm.team_id = t.id
      WHERE tcm.id = $1
    `;
    const messageResult = await query(messageQuery, [messageId]);

    if (messageResult.rows.length === 0) {
      return res.apiNotFound('Message not found');
    }

    const message = messageResult.rows[0];

    if (message.leader_id !== userId) {
      return res.apiForbidden('Only team leader can pin messages');
    }

    // TODO: Add pinned_messages table to database
    // For now, just return success
    logger.info(`Message ${messageId} pinned by leader ${userId}`);
    res.apiSuccess(null, 'Message pinned successfully');

  } catch (error) {
    logger.error('Pin message failed:', error.message);
    res.apiError('Failed to pin message', 'PIN_MESSAGE_ERROR', 500);
  }
});

// Get pinned messages
router.get('/team/:teamId/pinned', verifyFirebaseToken, requireTeamMembership, async (req, res) => {
  try {
    const { teamId } = req.params;

    // TODO: Get pinned messages from database
    // For now, return empty array
    const pinnedMessages = [];

    logger.info(`Retrieved pinned messages for team: ${teamId}`);
    res.apiSuccess(pinnedMessages, 'Pinned messages retrieved successfully');

  } catch (error) {
    logger.error('Get pinned messages failed:', error.message);
    res.apiError('Failed to get pinned messages', 'GET_PINNED_MESSAGES_ERROR', 500);
  }
});

// Mark message as read (for future implementation)
router.put('/message/:messageId/read', verifyFirebaseToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user.uid;

    // TODO: Implement read status tracking
    // This would require adding a message_reads table

    logger.info(`Message ${messageId} marked as read by user ${userId}`);
    res.apiSuccess(null, 'Message marked as read successfully');

  } catch (error) {
    logger.error('Mark message as read failed:', error.message);
    res.apiError('Failed to mark message as read', 'MARK_MESSAGE_READ_ERROR', 500);
  }
});

// Get message reactions (for future implementation)
router.get('/message/:messageId/reactions', verifyFirebaseToken, async (req, res) => {
  try {
    const { messageId } = req.params;

    // TODO: Implement message reactions
    // This would require adding a message_reactions table
    const reactions = [];

    res.apiSuccess(reactions, 'Message reactions retrieved successfully');

  } catch (error) {
    logger.error('Get message reactions failed:', error.message);
    res.apiError('Failed to get message reactions', 'GET_MESSAGE_REACTIONS_ERROR', 500);
  }
});

// Add reaction to message (for future implementation)
router.post('/message/:messageId/reactions', verifyFirebaseToken, validateBody(schemas.messageReaction), async (req, res) => {
  try {
    const { messageId } = req.params;
    const { reaction } = req.body;
    const userId = req.user.uid;

    // TODO: Implement message reactions
    // This would require adding a message_reactions table

    logger.info(`Reaction "${reaction}" added to message ${messageId} by user ${userId}`);
    res.apiSuccess(null, 'Reaction added successfully');

  } catch (error) {
    logger.error('Add reaction failed:', error.message);
    res.apiError('Failed to add reaction', 'ADD_REACTION_ERROR', 500);
  }
});

module.exports = router; 