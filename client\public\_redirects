# SPA Routing - Redirect all routes to index.html
/*    /index.html   200

# API routes should not be redirected (if you have backend proxy)
/api/*    /api/:splat   200

# Static assets should be served directly
/static/*    /static/:splat   200
/images/*    /images/:splat   200
/css/*       /css/:splat      200
/js/*        /js/:splat       200

# Security headers
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin

# Cache static assets
/static/*
  Cache-Control: public, max-age=********, immutable

# Don't cache HTML files
/*.html
  Cache-Control: no-cache, no-store, must-revalidate
