{"name": "vwork-user-service", "version": "1.0.0", "description": "VWork User Service - Comprehensive user management for VWork platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "db:migrate": "node -e \"require('./src/config/database').runMigrations()\"", "db:setup": "npm run db:migrate", "health": "curl -f http://localhost:3001/health || exit 1"}, "keywords": ["vwork", "user-service", "microservice", "node.js", "express", "postgresql", "firebase"], "author": "VWork Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "pg": "^8.11.3", "firebase-admin": "^11.11.1", "uuid": "^9.0.1", "bcrypt": "^5.1.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "repository": {"type": "git", "url": "https://github.com/vwork/vwork-platform.git", "directory": "services/user-service"}, "bugs": {"url": "https://github.com/vwork/vwork-platform/issues"}, "homepage": "https://vwork.com"}