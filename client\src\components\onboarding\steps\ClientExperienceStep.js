import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../contexts/AuthContext';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaQuestion, FaCheck, FaCreditCard, FaArrowRight } from 'react-icons/fa';

const ClientExperienceStep = () => {
 const { user } = useAuth();
 const { 
  onboardingData, 
  updateOnboardingData, 
  goToNextStep, 
  goToPreviousStep,
  loading 
 } = useOnboarding();
 const { t } = useLanguage();

 const [selectedExperience, setSelectedExperience] = useState('');
 const [errors, setErrors] = useState({});

 useEffect(() => {
  if (onboardingData.clientExperience) {
   setSelectedExperience(onboardingData.clientExperience);
  }
 }, [onboardingData]);

 const experienceOptions = [
  {
   id: 'never',
   icon: FaQuestion,
   title: t('neverUsed'),
   description: t('neverUsedDesc'),
   color: 'from-gray-400 to-gray-600',
   badge: t('newcomer')
  },
  {
   id: 'used',
   icon: FaCheck,
   title: t('usedBefore'),
   description: t('usedBeforeDesc'),
   color: 'from-blue-400 to-blue-600',
   badge: t('experienced')
  },
  {
   id: 'paid',
   icon: FaCreditCard,
   title: t('paidBefore'),
   description: t('paidBeforeDesc'),
   color: 'from-green-400 to-green-600',
   badge: t('expert')
  }
 ];

 const handleExperienceSelect = (experienceId) => {
  setSelectedExperience(experienceId);
  if (errors.experience) {
   setErrors(prev => ({ ...prev, experience: null }));
  }
 };

 const validateForm = () => {
  if (!selectedExperience) {
   setErrors({ experience: t('pleaseSelectExperience') });
   return false;
  }
  return true;
 };

 const handleNext = () => {
  console.log('🔘 ClientExperience Next button clicked', { selectedExperience, loading });
  if (validateForm()) {
   updateOnboardingData({
    clientExperience: selectedExperience
   });
   goToNextStep();
  } else {
   // Show visual feedback that user needs to select an option
   console.log('⚠️ Validation failed, showing error');
  }
 };

 return (
  <div className="p-8 md:p-12">
   {/* Header */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
   >
    <div className="w-16 h-16 bg-gradient-to-br from-medieval-gold-400 to-medieval-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
     <FaQuestion className="text-2xl text-white" />
    </div>
    
    <h2 className="text-2xl md:text-3xl font-medium font-bold text-gray-800 mb-2">
     {t('freelancerExperience')}
    </h2>
    
    <p className="text-gray-600 font-medium">
     {t('experienceDescription')}
    </p>
   </motion.div>

   {/* Experience Options */}
   <motion.div
    className="max-w-4xl mx-auto space-y-4"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
   >
    {experienceOptions.map((option, index) => (
     <motion.button
      key={option.id}
      onClick={() => handleExperienceSelect(option.id)}
      className={`w-full p-6 rounded-xl border-2 transition-all duration-300 transform ${
       selectedExperience === option.id
        ? 'border-blue-500 bg-blue-50 shadow-lg scale-105 ring-2 ring-blue-200'
        : 'border-gray-200 hover:border-blue-300 bg-white hover:shadow-md hover:scale-102 hover:bg-blue-25'
      }`}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.3 + index * 0.1 }}
      whileHover={{ scale: selectedExperience === option.id ? 1.05 : 1.02 }}
      whileTap={{ scale: 0.98 }}
     >
      <div className="flex items-start space-x-4">
       <div className={`w-12 h-12 bg-gradient-to-br ${option.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
        <option.icon className="text-xl text-white" />
       </div>
       <div className="flex-1 text-left">
        <div className="flex items-center justify-between mb-2">
         <h3 className="font-medium font-semibold text-gray-800">
          {option.title}
         </h3>
         <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          option.id === 'never' ? 'bg-gray-100 text-gray-600' :
          option.id === 'used' ? 'bg-blue-100 text-blue-600' :
          'bg-green-100 text-green-600'
         }`}>
          {option.badge}
         </span>
        </div>
        <p className="text-gray-600 text-sm">
         {option.description}
        </p>
       </div>
       {selectedExperience === option.id && (
        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
         <FaArrowRight className="text-white text-xs" />
        </div>
       )}
      </div>
     </motion.button>
    ))}
   </motion.div>

   {/* Error Message */}
   {errors.experience && (
    <motion.div
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     className="text-center mt-4"
    >
     <p className="text-red-500 text-sm font-medium">{errors.experience}</p>
    </motion.div>
   )}

   {/* Navigation */}
   <motion.div
    className="flex justify-between mt-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.4 }}
   >
    <button
     onClick={goToPreviousStep}
     disabled={loading}
     className="btn-secondary px-6 py-2 font-medium"
    >
     {t('previous')}
    </button>

    <button
     onClick={handleNext}
     disabled={loading}
     className={`px-6 py-2 font-medium transition-all duration-300 ${
      selectedExperience 
       ? 'btn-primary' 
       : 'btn-primary opacity-75 hover:opacity-90 hover:shadow-lg'
     }`}
     style={{ 
      pointerEvents: loading ? 'none' : 'auto',
      position: 'relative',
      zIndex: 10
     }}
    >
     {loading ? t('processing', 'Đang xử lý...') : t('next')}
    </button>
   </motion.div>
  </div>
 );
};

export default ClientExperienceStep; 