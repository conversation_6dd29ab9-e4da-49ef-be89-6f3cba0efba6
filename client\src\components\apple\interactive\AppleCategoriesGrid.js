import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
 CodeBracketIcon,
 PaintBrushIcon,
 MegaphoneIcon,
 PencilIcon,
 CameraIcon,
 ChartBarIcon,
 MusicalNoteIcon,
 HeartIcon,
} from '@heroicons/react/24/outline';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves for category animations
const categoryEases = {
 elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
 bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
 liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
 magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
 wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1"),
 grid: CustomEase.create("grid", "M0,0 C0.3,0 0.7,1 1,1")
};

// Fix unused variables and useEffect dependencies
const AppleCategoriesGrid = () => {
 const { t } = useLanguage();
 const navigate = useNavigate();
 const sectionRef = useRef(null);
 const titleRef = useRef(null);
 const gridRef = useRef(null);
 const particleGridRef = useRef(null);
 const morphingShapesRef = useRef([]);
 const categoryRefs = useRef([]);

 const categories = [
  {
   id: 1,
   name: t('developmentIT'),
   icon: CodeBracketIcon,
   description: t('developmentITDesc'),
   color: 'blue',
   gradient: 'from-blue-500 to-cyan-500',
   path: '/categories/development',
  },
  {
   id: 2,
   name: t('designCreative'),
   icon: PaintBrushIcon,
   description: t('designCreativeDesc'),
   color: 'purple',
   gradient: 'from-purple-500 to-pink-500',
   path: '/categories/design',
  },
  {
   id: 3,
   name: t('salesMarketing'),
   icon: MegaphoneIcon,
   description: t('salesMarketingDesc'),
   color: 'green',
   gradient: 'from-green-500 to-emerald-500',
   path: '/categories/marketing',
  },
  {
   id: 4,
   name: t('writingTranslation'),
   icon: PencilIcon,
   description: t('writingTranslationDesc'),
   color: 'orange',
   gradient: 'from-orange-500 to-red-500',
  },
  {
   id: 5,
   name: t('videoPhotography'),
   icon: CameraIcon,
   description: t('videoPhotographyDesc'),
   color: 'indigo',
   gradient: 'from-indigo-500 to-purple-500',
  },
  {
   id: 6,
   name: t('dataAnalytics'),
   icon: ChartBarIcon,
   description: t('dataAnalyticsDesc'),
   color: 'teal',
   gradient: 'from-teal-500 to-cyan-500',
  },
  {
   id: 7,
   name: t('musicAudio'),
   icon: MusicalNoteIcon,
   description: t('musicAudioDesc'),
   color: 'rose',
   gradient: 'from-rose-500 to-pink-500',
  },
  {
   id: 8,
   name: t('customerService'),
   icon: HeartIcon,
   description: t('customerServiceDesc'),
   color: 'amber',
   gradient: 'from-amber-500 to-orange-500',
  },
 ];

 // Advanced Floating Particle Grid System
 const createAdvancedParticleGrid = () => {
  if (!particleGridRef.current) return;

  const container = particleGridRef.current;
  const particles = [];

  // Optimized particle system - reduced count and simplified animation
  const particleCount = window.innerWidth < 768 ? 30 : 60; // Adaptive particle count
  for (let i = 0; i < particleCount; i++) {
   const particle = document.createElement('div');
   particle.className = 'absolute w-1 h-1 bg-blue-400 rounded-full opacity-20'; // Reduced opacity

   // Grid positioning
   const cols = Math.sqrt(particleCount);
   const row = Math.floor(i / cols);
   const col = i % cols;

   particle.style.left = (col * (100 / cols)) + '%';
   particle.style.top = (row * 8) + '%';
   container.appendChild(particle);
   particles.push(particle);

   // Simplified movement without physics2D for better performance
   gsap.to(particle, {
    x: (Math.random() - 0.5) * 20,
    y: (Math.random() - 0.5) * 20,
    duration: 4 + Math.random() * 2,
    repeat: -1,
    yoyo: true,
    ease: "power1.inOut"
   });

   // Simplified pulse effect for better performance
   if (i % 3 === 0) { // Only animate every 3rd particle
    gsap.to(particle, {
     scale: 1.2,
     opacity: 0.4,
     duration: 3,
     repeat: -1,
     yoyo: true,
     ease: "power1.inOut",
     delay: Math.random() * 1
    });
   }
  }
 };

 // Morphing Category Backgrounds
 const createMorphingCategoryShapes = () => {
  morphingShapesRef.current.forEach((shape, index) => {
   if (!shape) return;

   const colors = [
    'from-blue-400/15 to-cyan-400/15',
    'from-purple-400/15 to-pink-400/15',
    'from-green-400/15 to-emerald-400/15',
    'from-orange-400/15 to-red-400/15',
    'from-indigo-400/15 to-purple-400/15',
    'from-teal-400/15 to-cyan-400/15'
   ];

   shape.className = `absolute bg-gradient-to-br ${colors[index % colors.length]} rounded-full`;

   const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
   
   morphTimeline
    .to(shape, {
     borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
     scale: 1.3,
     rotation: 180,
     x: "random(-30, 30)",
     y: "random(-20, 20)",
     duration: 5,
     ease: categoryEases.liquid
    })
    .to(shape, {
     borderRadius: "40% 60% 70% 30% / 40% 70% 30% 60%",
     scale: 0.7,
     rotation: -90,
     x: "random(-20, 20)",
     y: "random(-30, 30)",
     duration: 3.5,
     ease: categoryEases.elastic
    })
    .to(shape, {
     borderRadius: "70% 30% 40% 60% / 30% 60% 40% 70%",
     scale: 1.1,
     rotation: 270,
     x: "random(-25, 25)",
     y: "random(-25, 25)",
     duration: 4.5,
     ease: categoryEases.wave
    });

   morphTimeline.delay(index * 0.8);
  });
 };

 // Advanced Title Animation with Grid Effect
 const createAdvancedTitleAnimation = () => {
  if (!titleRef.current) return;

  const titleElement = titleRef.current.querySelector('h2');
  const subtitleElement = titleRef.current.querySelector('p');

  if (titleElement) {
   const titleSplit = new SplitText(titleElement, { type: "chars,words" });

   gsap.fromTo(titleSplit.chars, {
    opacity: 0,
    y: 100,
    rotationX: -90,
    transformOrigin: "center bottom"
   }, {
    opacity: 1,
    y: 0,
    rotationX: 0,
    duration: 1.8,
    stagger: 0.03,
    ease: categoryEases.bounce,
    scrollTrigger: {
     trigger: titleRef.current,
     start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });
  }

  if (subtitleElement) {
   const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

   gsap.fromTo(subtitleSplit.words, {
    opacity: 0,
    y: 50,
    scale: 0.8
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    duration: 1.2,
    stagger: 0.1,
    ease: categoryEases.elastic,
    delay: 0.8,
     scrollTrigger: {
      trigger: titleRef.current,
      start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });
  }
 };

 // Advanced Magnetic Category Hover
 const createMagneticCategoryHover = (categoryElement, index) => {
  if (!categoryElement) return;

  const icon = categoryElement.querySelector('.category-icon');
  const content = categoryElement.querySelector('.category-content');
  const background = categoryElement.querySelector('.category-background');

  let isHovering = false;

  categoryElement.addEventListener('mouseenter', () => {
   isHovering = true;

   // Magnetic hover timeline
   const hoverTL = gsap.timeline();

   hoverTL
    .to(categoryElement, {
     scale: 1.08,
     y: -15,
     rotationY: 8,
     rotationX: 5,
     boxShadow: "0 30px 60px rgba(0,0,0,0.15)",
     duration: 0.7,
     ease: categoryEases.magnetic
    })
    .to(icon, {
     scale: 1.4,
     rotation: 360,
     duration: 1.2,
     ease: categoryEases.bounce
    }, 0)
    .to(background, {
     scale: 1.15,
     opacity: 0.9,
     duration: 0.6,
     ease: categoryEases.elastic
    }, 0.1);


  });

  categoryElement.addEventListener('mouseleave', () => {
   isHovering = false;

   gsap.to(categoryElement, {
    scale: 1,
    y: 0,
    rotationY: 0,
    rotationX: 0,
    boxShadow: "0 10px 25px rgba(0,0,0,0.08)",
    duration: 0.9,
    ease: categoryEases.elastic
   });

   gsap.to(icon, {
    scale: 1,
    rotation: 0,
    duration: 0.8,
    ease: categoryEases.bounce
   });

   gsap.to(background, {
    scale: 1,
    opacity: 1,
    duration: 0.5,
    ease: categoryEases.wave
   });
  });

  // Real-time mouse tracking for magnetic effect
  categoryElement.addEventListener('mousemove', (e) => {
   if (!isHovering) return;

   const rect = categoryElement.getBoundingClientRect();
   const centerX = rect.left + rect.width / 2;
   const centerY = rect.top + rect.height / 2;
   const mouseX = e.clientX - centerX;
   const mouseY = e.clientY - centerY;

   gsap.to(categoryElement, {
    x: mouseX * 0.08,
    y: mouseY * 0.08,
    duration: 0.3,
    ease: "power2.out"
   });

   gsap.to(icon, {
    x: mouseX * 0.12,
    y: mouseY * 0.12,
    duration: 0.2,
    ease: "power2.out"
   });
  });
 };



 // Advanced Grid Animation with Stagger
 const createAdvancedGridAnimation = () => {
  if (!gridRef.current) return;

  const categoryElements = Array.from(gridRef.current.children);

  categoryElements.forEach((category, index) => {
   // Store reference for hover effects
   categoryRefs.current[index] = category;

   // Create magnetic hover effect
   createMagneticCategoryHover(category, index);

   // Grid entrance animation with physics
   gsap.fromTo(category, {
    opacity: 0,
    y: 100,
    scale: 0.7,
    rotationY: 45,
    rotationX: 30
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    rotationY: 0,
    rotationX: 0,
    duration: 1.5,
    delay: index * 0.12,
    ease: categoryEases.elastic,
    scrollTrigger: {
     trigger: category,
     start: 'top 90%',
     toggleActions: 'play none none reverse'
    }
   });

   // Animate category content with wave effect
   const icon = category.querySelector('.category-icon');
   const content = category.querySelector('.category-content');

   if (icon) {
    gsap.fromTo(icon, {
     scale: 0,
     rotation: -180
    }, {
     scale: 1,
     rotation: 0,
     duration: 1.0,
     delay: index * 0.12 + 0.4,
     ease: categoryEases.bounce,
     scrollTrigger: {
      trigger: category,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }

   if (content) {
    const contentChildren = Array.from(content.children);
    gsap.fromTo(contentChildren, {
     opacity: 0,
     x: -50
    }, {
     opacity: 1,
     x: 0,
     duration: 0.8,
     stagger: 0.08,
     delay: index * 0.12 + 0.7,
     ease: categoryEases.wave,
     scrollTrigger: {
      trigger: category,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }
  });
 };

 useEffect(() => {
  const ctx = gsap.context(() => {
   // Initialize all advanced animations
   createAdvancedParticleGrid();
   createMorphingCategoryShapes();
   createAdvancedTitleAnimation();
   createAdvancedGridAnimation();
  }, sectionRef);

  return () => ctx.revert();
 // eslint-disable-next-line react-hooks/exhaustive-deps
 }, []);

 const getColorClasses = (color) => {
  const colorMap = {
   blue: {
    bg: 'bg-blue-50',
    text: 'text-blue-600',
    border: 'border-blue-200',
    hover: 'hover:bg-blue-100'
   },
   purple: {
    bg: 'bg-purple-50',
    text: 'text-purple-600',
    border: 'border-purple-200',
    hover: 'hover:bg-purple-100'
   },
   green: {
    bg: 'bg-green-50',
    text: 'text-green-600',
    border: 'border-green-200',
    hover: 'hover:bg-green-100'
   },
   orange: {
    bg: 'bg-orange-50',
    text: 'text-orange-600',
    border: 'border-orange-200',
    hover: 'hover:bg-orange-100'
   },
   indigo: {
    bg: 'bg-indigo-50',
    text: 'text-indigo-600',
    border: 'border-indigo-200',
    hover: 'hover:bg-indigo-100'
   },
   teal: {
    bg: 'bg-teal-50',
    text: 'text-teal-600',
    border: 'border-teal-200',
    hover: 'hover:bg-teal-100'
   },
   rose: {
    bg: 'bg-rose-50',
    text: 'text-rose-600',
    border: 'border-rose-200',
    hover: 'hover:bg-rose-100'
   },
   amber: {
    bg: 'bg-amber-50',
    text: 'text-amber-600',
    border: 'border-amber-200',
    hover: 'hover:bg-amber-100'
   }
  };
  return colorMap[color] || colorMap.blue;
 };

 return (
  <>
   {/* Enhanced CSS */}
   <style>{`
    @keyframes liquid-category {
     0%, 100% {
      border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
     }
     25% {
      border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
     }
     50% {
      border-radius: 70% 30% 40% 60% / 30% 60% 40% 70%;
     }
     75% {
      border-radius: 40% 70% 60% 30% / 70% 40% 60% 30%;
     }
    }
    
    .liquid-category {
     animation: liquid-category 12s ease-in-out infinite;
    }
    
    .category-card {
     transform-style: preserve-3d;
     transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .category-grid-particles {
     will-change: transform;
    }
    
    .category-card:hover {
     transform: perspective(1000px) rotateX(5deg) rotateY(8deg) translateZ(50px);
    }
   `}</style>

  <section
   ref={sectionRef}
    className='relative py-20 bg-white transition-colors duration-300 overflow-hidden'
   >
    {/* Advanced Multi-layer Background */}
    <div className='absolute inset-0'>
     <div 
      ref={el => morphingShapesRef.current[0] = el}
      className='absolute top-16 left-20 w-48 h-48 liquid-category'
     />
     <div 
      ref={el => morphingShapesRef.current[1] = el}
      className='absolute top-32 right-16 w-40 h-40 liquid-category'
     />
     <div 
      ref={el => morphingShapesRef.current[2] = el}
      className='absolute bottom-20 left-1/4 w-56 h-56 liquid-category'
     />
     <div 
      ref={el => morphingShapesRef.current[3] = el}
      className='absolute bottom-16 right-20 w-44 h-44 liquid-category'
     />
     <div 
      ref={el => morphingShapesRef.current[4] = el}
      className='absolute top-1/2 left-1/2 w-32 h-32 liquid-category'
     />
     <div 
      ref={el => morphingShapesRef.current[5] = el}
      className='absolute top-20 left-1/3 w-36 h-36 liquid-category'
     />
    </div>

    {/* Advanced Grid Particle Canvas */}
    <div 
     ref={particleGridRef}
     className='absolute inset-0 pointer-events-none overflow-hidden category-grid-particles'
    />

    <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
     {/* Enhanced Section Header */}
    <div ref={titleRef} className='text-center mb-16'>
      <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 mb-8 transition-colors duration-300'>
      {t('browseByCategory')}
     </h2>
      <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
      {t('findPerfectFreelancer')}
     </p>
    </div>

     {/* Enhanced Categories Grid */}
    <div
     ref={gridRef}
      className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-10'
     >
      {categories.map((category, index) => {
       const colors = getColorClasses(category.color);
       
       return (
        <div
         key={category.id}
         className='group relative category-card cursor-pointer transform-gpu'
         onClick={() => navigate(category.path)}
        >
         <div className='category-background absolute inset-0 bg-white rounded-3xl border border-gray-100 shadow-lg transition-all duration-500' />
         
         <div className='relative bg-white rounded-3xl p-8 border border-gray-100 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 transform-gpu'>
          {/* Enhanced Icon */}
          <div className='mb-8'>
           <div
            className={`category-icon inline-flex items-center justify-center w-20 h-20 rounded-3xl border-2 transition-all duration-500 ${colors.bg} ${colors.border} ${colors.hover} shadow-lg group-hover:shadow-xl`}
           >
            <category.icon className={`h-10 w-10 ${colors.text}`} />
        </div>
       </div>

          {/* Enhanced Content */}
          <div className='category-content'>
           <h3 className='text-2xl font-bold text-gray-900 mb-4 transition-colors duration-300'>
         {category.name}
        </h3>
           <p className='text-gray-600 text-lg mb-6 leading-relaxed transition-colors duration-300'>
         {category.description}
        </p>
        
           {/* Enhanced Category Icon */}
        <div className='flex items-center justify-end'>
            <div className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${category.gradient} opacity-20 group-hover:opacity-40 transition-opacity duration-500 shadow-lg`} />
        </div>
       </div>

          {/* Enhanced Hover Effect */}
          <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${category.gradient} opacity-0 group-hover:opacity-8 transition-opacity duration-500`} />
         </div>
      </div>
       );
      })}
    </div>

     {/* Enhanced View All Button */}
     <div className='text-center mt-16'>
      <button className='group px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'>
       <span className='mr-3'>{t('viewAllCategories')}</span>
       <span className='inline-block transition-transform duration-300 group-hover:translate-x-2'>→</span>
     </button>
    </div>
   </div>
  </section>
  </>
 );
};

export default AppleCategoriesGrid;
