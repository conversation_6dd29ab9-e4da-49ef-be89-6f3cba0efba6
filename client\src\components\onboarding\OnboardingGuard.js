import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingFlow from './OnboardingFlow';

const OnboardingGuard = ({ children }) => {
 const { user, loading: authLoading } = useAuth();
 const { isOnboardingRequired, loading: onboardingLoading } = useOnboarding();

 // Show loading while checking auth and onboarding status
 if (authLoading || onboardingLoading) {
  return (
   <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-gold-50 flex items-center justify-center">
    <div className="text-center">
     <div className="w-16 h-16 border-4 border-blue-200 border-t-medieval-gold-600 rounded-full animate-spin mx-auto mb-4"></div>
     <p className="text-gray-600 font-medium">Đang tải...</p>
    </div>
   </div>
  );
 }

 // If user is authenticated and needs onboarding, show onboarding flow
 if (user && user.emailVerified && isOnboardingRequired) {
  return <OnboardingFlow />;
 }

 // Otherwise, show the normal app content
 return children;
};

export default OnboardingGuard;
