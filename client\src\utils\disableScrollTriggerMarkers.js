/**
 * Utility to disable all ScrollTrigger markers globally
 * This prevents the red/blue debug lines from appearing
 */

import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Global function to disable all ScrollTrigger markers
export const disableAllScrollTriggerMarkers = () => {
 // Set global default to false
 if (ScrollTrigger && ScrollTrigger.defaults) {
  ScrollTrigger.defaults({
   markers: false
  });
 }

 // Disable markers on all existing ScrollTrigger instances
 if (ScrollTrigger && ScrollTrigger.getAll) {
  const allTriggers = ScrollTrigger.getAll();
  allTriggers.forEach(trigger => {
   if (trigger.vars) {
    trigger.vars.markers = false;
   }
  });
 }

 console.log('🚫 ScrollTrigger markers disabled globally');
};

// Override ScrollTrigger.create to always set markers: false
export const overrideScrollTriggerCreate = () => {
 if (ScrollTrigger && ScrollTrigger.create) {
  const originalCreate = ScrollTrigger.create;
  
  ScrollTrigger.create = function(vars) {
   // Force markers to false
   const modifiedVars = {
    ...vars,
    markers: false
   };
   
   return originalCreate.call(this, modifiedVars);
  };
  
  console.log('🔧 ScrollTrigger.create overridden to disable markers');
 }
};

// Initialize on import
export const initializeMarkerDisabling = () => {
 disableAllScrollTriggerMarkers();
 overrideScrollTriggerCreate();
 
 // Also disable on window load to catch any late initializations
 if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
   setTimeout(disableAllScrollTriggerMarkers, 100);
  });
 }
};

// Auto-initialize
initializeMarkerDisabling();

export default {
 disableAllScrollTriggerMarkers,
 overrideScrollTriggerCreate,
 initializeMarkerDisabling
};
