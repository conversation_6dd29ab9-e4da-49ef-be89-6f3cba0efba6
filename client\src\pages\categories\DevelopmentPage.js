import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  CodeBracketIcon, 
  StarIcon, 
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

// Mock data cho freelancers chuyên ngành IT
const mockDevelopers = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    title: 'Full Stack Developer',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 127,
    hourlyRate: 25,
    location: '<PERSON><PERSON>',
    skills: ['React', 'Node.js', 'MongoDB', 'TypeScript'],
    description: 'Chuyên gia phát triển web với 5+ năm kinh nghiệm. <PERSON><PERSON> hoàn thành 200+ dự án thành công.',
    completedJobs: 156,
    responseTime: '1 giờ',
    availability: 'Sẵn sàng',
    portfolio: [
      'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1555421689-491a97ff2040?w=300&h=200&fit=crop'
    ]
  },
  {
    id: 2,
    name: 'Trần Thị Bình',
    title: 'Mobile App Developer',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 89,
    hourlyRate: 30,
    location: 'Hà Nội',
    skills: ['React Native', 'Flutter', 'iOS', 'Android'],
    description: 'Chuyên phát triển ứng dụng di động cross-platform. Kinh nghiệm 4 năm với 100+ ứng dụng.',
    completedJobs: 98,
    responseTime: '30 phút',
    availability: 'Bận',
    portfolio: [
      'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=200&fit=crop'
    ]
  },
  {
    id: 3,
    name: 'Lê Minh Cường',
    title: 'DevOps Engineer',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 4.7,
    reviewCount: 64,
    hourlyRate: 35,
    location: 'Đà Nẵng',
    skills: ['AWS', 'Docker', 'Kubernetes', 'CI/CD'],
    description: 'Chuyên gia DevOps với kinh nghiệm triển khai hệ thống quy mô lớn.',
    completedJobs: 45,
    responseTime: '2 giờ',
    availability: 'Sẵn sàng',
    portfolio: []
  },
  {
    id: 4,
    name: 'Phạm Thị Dung',
    title: 'Frontend Developer',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 156,
    hourlyRate: 22,
    location: 'Hồ Chí Minh',
    skills: ['Vue.js', 'React', 'CSS3', 'JavaScript'],
    description: 'Chuyên tạo giao diện người dùng đẹp mắt và tối ưu trải nghiệm.',
    completedJobs: 203,
    responseTime: '45 phút',
    availability: 'Sẵn sàng',
    portfolio: [
      'https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=300&h=200&fit=crop',
      'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop'
    ]
  },
  {
    id: 5,
    name: 'Hoàng Văn Em',
    title: 'Backend Developer',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 4.6,
    reviewCount: 78,
    hourlyRate: 28,
    location: 'Hà Nội',
    skills: ['Python', 'Django', 'PostgreSQL', 'Redis'],
    description: 'Chuyên phát triển API và hệ thống backend hiệu suất cao.',
    completedJobs: 67,
    responseTime: '1.5 giờ',
    availability: 'Sẵn sàng',
    portfolio: []
  },
  {
    id: 6,
    name: 'Ngô Thị Phương',
    title: 'Data Scientist',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 92,
    hourlyRate: 40,
    location: 'Hồ Chí Minh',
    skills: ['Python', 'Machine Learning', 'TensorFlow', 'SQL'],
    description: 'Chuyên gia phân tích dữ liệu và machine learning với 6 năm kinh nghiệm.',
    completedJobs: 34,
    responseTime: '3 giờ',
    availability: 'Bận',
    portfolio: [
      'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=300&h=200&fit=crop'
    ]
  }
];

const DevelopmentPage = () => {
  const { t } = useLanguage();
  const [filteredDevelopers, setFilteredDevelopers] = useState(mockDevelopers);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSkill, setSelectedSkill] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [availability, setAvailability] = useState('');

  // Get all unique skills
  const allSkills = [...new Set(mockDevelopers.flatMap(dev => dev.skills))];

  // Filter function
  useEffect(() => {
    let filtered = mockDevelopers;

    if (searchQuery) {
      filtered = filtered.filter(dev => 
        dev.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dev.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        dev.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedSkill) {
      filtered = filtered.filter(dev => dev.skills.includes(selectedSkill));
    }

    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filtered = filtered.filter(dev => {
        if (max) {
          return dev.hourlyRate >= min && dev.hourlyRate <= max;
        } else {
          return dev.hourlyRate >= min;
        }
      });
    }

    if (availability) {
      filtered = filtered.filter(dev => dev.availability === availability);
    }

    setFilteredDevelopers(filtered);
  }, [searchQuery, selectedSkill, priceRange, availability]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <div className="p-3 bg-blue-100 rounded-lg">
              <CodeBracketIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Phát triển IT</h1>
              <p className="text-lg text-gray-600">Tìm kiếm các chuyên gia phát triển phần mềm hàng đầu</p>
            </div>
          </div>
          
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm developer..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Skill Filter */}
            <select
              value={selectedSkill}
              onChange={(e) => setSelectedSkill(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả kỹ năng</option>
              {allSkills.map(skill => (
                <option key={skill} value={skill}>{skill}</option>
              ))}
            </select>

            {/* Price Range Filter */}
            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả mức giá</option>
              <option value="0-20">$0 - $20/giờ</option>
              <option value="20-30">$20 - $30/giờ</option>
              <option value="30-40">$30 - $40/giờ</option>
              <option value="40">$40+/giờ</option>
            </select>

            {/* Availability Filter */}
            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Sẵn sàng">Sẵn sàng</option>
              <option value="Bận">Bận</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Tìm thấy <span className="font-semibold">{filteredDevelopers.length}</span> developer phù hợp
          </p>
        </div>

        {/* Developer Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDevelopers.map(developer => (
            <div key={developer.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
              {/* Developer Header */}
              <div className="flex items-start space-x-4 mb-4">
                <img
                  src={developer.avatar}
                  alt={developer.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{developer.name}</h3>
                  <p className="text-blue-600 font-medium">{developer.title}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="flex items-center">
                      <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">{developer.rating}</span>
                    </div>
                    <span className="text-gray-400">•</span>
                    <span className="text-sm text-gray-600">{developer.reviewCount} đánh giá</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{developer.description}</p>

              {/* Skills */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {developer.skills.slice(0, 4).map(skill => (
                    <span key={skill} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {skill}
                    </span>
                  ))}
                  {developer.skills.length > 4 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      +{developer.skills.length - 4}
                    </span>
                  )}
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center space-x-2">
                  <MapPinIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{developer.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{developer.responseTime}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">${developer.hourlyRate}/giờ</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full ${developer.availability === 'Sẵn sàng' ? 'bg-green-400' : 'bg-red-400'}`}></span>
                  <span className="text-gray-600">{developer.availability}</span>
                </div>
              </div>

              {/* Portfolio Preview */}
              {developer.portfolio.length > 0 && (
                <div className="mb-4">
                  <div className="flex space-x-2">
                    {developer.portfolio.slice(0, 2).map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt={`Portfolio ${index + 1}`}
                        className="w-16 h-12 rounded object-cover"
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm font-medium">
                  Liên hệ
                </button>
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
                  Xem hồ sơ
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {filteredDevelopers.length > 0 && (
          <div className="text-center mt-8">
            <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              Xem thêm developer
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DevelopmentPage;
