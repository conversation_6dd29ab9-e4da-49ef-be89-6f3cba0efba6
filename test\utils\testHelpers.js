/**
 * Test helper utilities
 * Common functions and utilities for testing across the platform
 */

const { render, screen, fireEvent, waitFor } = require('@testing-library/react');
const { renderHook, act } = require('@testing-library/react');
const userEvent = require('@testing-library/user-event');

/**
 * React Testing Library helpers
 */
const testingHelpers = {
  // Enhanced render with common providers
  renderWithProviders: (ui, options = {}) => {
    const {
      initialState = {},
      store = null,
      router = true,
      theme = true,
      ...renderOptions
    } = options;

    // Create wrapper with providers
    const AllTheProviders = ({ children }) => {
      let wrappedChildren = children;

      // Add theme provider if needed
      

      // Add router if needed
      if (router) {
        const { BrowserRouter } = require('react-router-dom');
        wrappedChildren = <BrowserRouter>{wrappedChildren}</BrowserRouter>;
      }

      // Add store provider if needed
      if (store) {
        wrappedChildren = <store.Provider>{wrappedChildren}</store.Provider>;
      }

      return wrappedChildren;
    };

    return render(ui, { wrapper: AllTheProviders, ...renderOptions });
  },

  // User event helpers
  user: userEvent.setup(),

  // Common queries
  queries: {
    getByTestId: (testId) => screen.getByTestId(testId),
    queryByTestId: (testId) => screen.queryByTestId(testId),
    findByTestId: (testId) => screen.findByTestId(testId),
    getByRole: (role, options) => screen.getByRole(role, options),
    getByText: (text, options) => screen.getByText(text, options),
    getByLabelText: (text, options) => screen.getByLabelText(text, options),
  },

  // Wait utilities
  waitFor,
  waitForElementToBeRemoved: require('@testing-library/react').waitForElementToBeRemoved,

  // Event utilities
  fireEvent,
  
  // Hook testing
  renderHook,
  act,
};

/**
 * Mock data generators
 */
const mockDataGenerators = {
  // Generate mock user
  createMockUser: (overrides = {}) => ({
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    avatar: null,
    role: 'user',
    isVerified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides,
  }),

  // Generate mock project
  createMockProject: (overrides = {}) => ({
    id: 'project-123',
    title: 'Test Project',
    description: 'A test project description',
    budget: 1000,
    currency: 'USD',
    status: 'open',
    skills: ['JavaScript', 'React'],
    clientId: 'client-123',
    freelancerId: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides,
  }),

  // Generate mock job
  createMockJob: (overrides = {}) => ({
    id: 'job-123',
    title: 'Test Job',
    description: 'A test job description',
    type: 'full-time',
    location: 'Remote',
    salary: { min: 50000, max: 80000, currency: 'USD' },
    skills: ['JavaScript', 'Node.js'],
    companyId: 'company-123',
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides,
  }),

  // Generate mock API response
  createMockApiResponse: (data, success = true, message = null) => ({
    success,
    data,
    message: message || (success ? 'Operation successful' : 'Operation failed'),
    timestamp: new Date().toISOString(),
  }),

  // Generate mock error
  createMockError: (message = 'Test error', code = 'TEST_ERROR', statusCode = 400) => {
    const error = new Error(message);
    error.code = code;
    error.statusCode = statusCode;
    return error;
  },
};

/**
 * API testing helpers
 */
const apiTestHelpers = {
  // Mock fetch responses
  mockFetch: (response, status = 200) => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: status >= 200 && status < 300,
        status,
        json: () => Promise.resolve(response),
        text: () => Promise.resolve(JSON.stringify(response)),
      })
    );
  },

  // Mock axios responses
  mockAxios: (response, status = 200) => {
    const axios = require('axios');
    axios.get.mockResolvedValue({ data: response, status });
    axios.post.mockResolvedValue({ data: response, status });
    axios.put.mockResolvedValue({ data: response, status });
    axios.delete.mockResolvedValue({ data: response, status });
    axios.patch.mockResolvedValue({ data: response, status });
  },

  // Create mock request
  createMockRequest: (overrides = {}) => ({
    method: 'GET',
    url: '/test',
    headers: {},
    body: {},
    params: {},
    query: {},
    user: null,
    ...overrides,
  }),

  // Create mock response
  createMockResponse: () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.setHeader = jest.fn().mockReturnValue(res);
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  },
};

/**
 * Database testing helpers
 */
const dbTestHelpers = {
  // Mock database operations
  mockDbOperations: () => ({
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  }),

  // Create test data
  createTestData: async (model, data) => {
    // This would typically interact with a test database
    return { id: 'test-id', ...data };
  },

  // Clean test data
  cleanTestData: async (model, conditions = {}) => {
    // This would typically clean up test database
    return true;
  },
};

/**
 * Performance testing helpers
 */
const performanceHelpers = {
  // Measure component render time
  measureRenderTime: async (renderFn) => {
    const start = performance.now();
    await renderFn();
    const end = performance.now();
    return end - start;
  },

  // Memory usage tracking
  trackMemoryUsage: () => {
    const initial = process.memoryUsage();
    
    return () => {
      const final = process.memoryUsage();
      return {
        heapUsed: final.heapUsed - initial.heapUsed,
        heapTotal: final.heapTotal - initial.heapTotal,
        external: final.external - initial.external,
        rss: final.rss - initial.rss,
      };
    };
  },

  // Simulate slow operations
  simulateSlowOperation: (ms = 100) => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
};

/**
 * Accessibility testing helpers
 */
const a11yHelpers = {
  // Check for accessibility violations
  checkA11y: async (container) => {
    const { axe, toHaveNoViolations } = require('jest-axe');
    expect.extend(toHaveNoViolations);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  },

  // Keyboard navigation testing
  testKeyboardNavigation: async (element, keys = ['Tab', 'Enter', 'Space']) => {
    for (const key of keys) {
      await testingHelpers.user.keyboard(`{${key}}`);
      // Add assertions based on expected behavior
    }
  },
};

/**
 * Visual regression testing helpers
 */
const visualTestHelpers = {
  // Take screenshot (would integrate with visual testing tools)
  takeScreenshot: async (element, name) => {
    // This would typically integrate with tools like Percy, Chromatic, etc.
    console.log(`Taking screenshot: ${name}`);
    return Promise.resolve();
  },

  // Compare visual differences
  compareVisual: async (baseline, current) => {
    // This would typically compare images
    return { match: true, difference: 0 };
  },
};

module.exports = {
  testingHelpers,
  mockDataGenerators,
  apiTestHelpers,
  dbTestHelpers,
  performanceHelpers,
  a11yHelpers,
  visualTestHelpers,
};
