#!/usr/bin/env node

console.log('🔥 VWork Firebase Configuration Guide');
console.log('=====================================\n');

console.log('📋 <PERSON><PERSON>c bước để lấy Firebase credentials:\n');

console.log('1️⃣ **Truy cập Firebase Console:**');
console.log('   https://console.firebase.google.com/\n');

console.log('2️⃣ **Chọn project VWork của bạn**\n');

console.log('3️⃣ **Tạo Service Account:**');
console.log('   • Vào Project Settings (⚙️)');
console.log('   • Tab "Service accounts"');
console.log('   • Click "Generate new private key"');
console.log('   • Download file JSON\n');

console.log('4️⃣ **Extract thông tin từ JSON file:**');
console.log('   Mở file JSON vừa download, bạn sẽ thấy:');
console.log('   ```json');
console.log('   {');
console.log('     "project_id": "your-project-id",');
console.log('     "client_email": "<EMAIL>",');
console.log('     "private_key": "-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n"');
console.log('   }');
console.log('   ```\n');

console.log('5️⃣ **Cài đặt trên Render:**');
console.log('   FIREBASE_PROJECT_ID = project_id từ JSON');
console.log('   FIREBASE_CLIENT_EMAIL = client_email từ JSON');
console.log('   FIREBASE_PRIVATE_KEY = private_key từ JSON (giữ nguyên \\n)\n');

console.log('6️⃣ **Database Configuration:**');
console.log('   Nếu chưa có PostgreSQL database:');
console.log('   • Vào Render Dashboard');
console.log('   • Tạo PostgreSQL database mới');
console.log('   • Copy DATABASE_URL\n');

console.log('🚨 **LƯU Ý QUAN TRỌNG:**');
console.log('   • FIREBASE_PRIVATE_KEY phải giữ nguyên \\n trong chuỗi');
console.log('   • Đừng để lộ thông tin này ra ngoài');
console.log('   • Sau khi cài đặt, redeploy service trên Render\n');

console.log('🔍 **Kiểm tra sau khi cài đặt:**');
console.log('   node scripts/check-deployment-status.js\n');

console.log('💡 **Nếu vẫn gặp lỗi:**');
console.log('   • Kiểm tra logs trên Render Dashboard');
console.log('   • Đảm bảo Firebase project đã enable Firestore');
console.log('   • Kiểm tra PostgreSQL connection'); 