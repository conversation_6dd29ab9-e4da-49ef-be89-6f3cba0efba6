{"name": "team-service", "version": "1.0.0", "description": "team-service microservice for NERAFUS platform", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:mock": "node test-without-db.js", "fix": "node fix-and-start.js", "db:init": "node scripts/init-postgresql.js", "db:setup": "node setup-database.js", "db:migrate": "psql -f migrations/team_schema.sql", "db:check": "node scripts/check-database-schema.js", "config:check": "node scripts/check-config.js", "test:service": "node test-service.js", "install:postgresql": "install-postgresql.bat"}, "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "firebase-admin": "^13.4.0", "helmet": "^7.1.0", "joi": "^17.13.3", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-pool": "^3.6.1", "uuid": "^9.0.1", "winston": "^3.11.0", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.1"}}