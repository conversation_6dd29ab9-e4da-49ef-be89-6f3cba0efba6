/**
 * User Service Utilities
 * Standalone utilities for user service (no shared dependencies)
 */

const Joi = require('joi');

// Response utilities
class ApiResponse {
  static success(data = null, message = 'Success', pagination = null) {
    const response = {
      success: true,
      data,
      message
    };

    if (pagination) {
      response.pagination = pagination;
    }

    return response;
  }

  static error(message = 'An error occurred', code = 'INTERNAL_ERROR', statusCode = 500) {
    return {
      success: false,
      error: message,
      message,
      code,
      statusCode
    };
  }

  static validationError(errors, message = 'Validation failed') {
    return {
      success: false,
      error: message,
      message,
      code: 'VALIDATION_ERROR',
      statusCode: 422,
      errors
    };
  }

  static unauthorized(message = 'Unauthorized access') {
    return {
      success: false,
      error: message,
      message,
      code: 'UNAUTHORIZED',
      statusCode: 401
    };
  }

  static forbidden(message = 'Access forbidden') {
    return {
      success: false,
      error: message,
      message,
      code: 'FORBIDDEN',
      statusCode: 403
    };
  }

  static notFound(message = 'Resource not found') {
    return {
      success: false,
      error: message,
      message,
      code: 'NOT_FOUND',
      statusCode: 404
    };
  }

  static badRequest(message = 'Bad request') {
    return {
      success: false,
      error: message,
      message,
      code: 'BAD_REQUEST',
      statusCode: 400
    };
  }
}

// Response middleware
const responseMiddleware = (req, res, next) => {
  res.apiSuccess = (data, message, pagination) => {
    const response = ApiResponse.success(data, message, pagination);
    res.status(200).json(response);
  };

  res.apiError = (message, code, statusCode = 500) => {
    const response = ApiResponse.error(message, code, statusCode);
    res.status(statusCode).json(response);
  };

  res.apiValidationError = (errors, message) => {
    const response = ApiResponse.validationError(errors, message);
    res.status(422).json(response);
  };

  res.apiUnauthorized = (message) => {
    const response = ApiResponse.unauthorized(message);
    res.status(401).json(response);
  };

  res.apiForbidden = (message) => {
    const response = ApiResponse.forbidden(message);
    res.status(403).json(response);
  };

  res.apiNotFound = (message) => {
    const response = ApiResponse.notFound(message);
    res.status(404).json(response);
  };

  res.apiBadRequest = (message) => {
    const response = ApiResponse.badRequest(message);
    res.status(400).json(response);
  };

  next();
};

// Validation utilities
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    req.body = value;
    next();
  };
};

const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    req.query = value;
    next();
  };
};

// Auth middleware
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.apiUnauthorized('No token provided');
    }

    // For now, just check if token exists
    // In production, this should call Auth Service
    if (token && token !== 'undefined' && token !== 'null') {
      // Mock user data for development
      req.user = {
        uid: 'mock-user-id',
        email: '<EMAIL>',
        name: 'Mock User',
        userType: 'freelancer'
      };
      next();
    } else {
      res.apiUnauthorized('Invalid token');
    }

  } catch (error) {
    console.error('❌ Token verification failed:', error.message);
    res.apiUnauthorized('Invalid token');
  }
};

const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token || token === 'undefined' || token === 'null') {
      return next(); // Continue without user data
    }

    // Mock user data for development
    req.user = {
      uid: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      userType: 'freelancer'
    };

    next();

  } catch (error) {
    console.error('❌ Optional auth failed:', error.message);
    next(); // Continue without user data
  }
};

const requireOwnership = (paramName = 'id') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.apiUnauthorized('Authentication required');
    }

    const resourceId = req.params[paramName];
    
    if (req.user.uid !== resourceId) {
      return res.apiForbidden('Access denied - not owner');
    }

    next();
  };
};

// Pagination utility
const createPagination = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

// User service validation schemas
const schemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }),

  userProfile: Joi.object({
    profile: Joi.object({
      bio: Joi.string().max(1000).optional(),
      avatar: Joi.string().uri().optional(),
      location: Joi.object({
        country: Joi.string().max(100).optional(),
        city: Joi.string().max(100).optional(),
        timezone: Joi.string().max(50).optional()
      }).optional(),
      website: Joi.string().uri().optional(),
      phoneNumber: Joi.string().max(20).optional(),
      skills: Joi.array().items(Joi.string().max(50)).max(20).optional(),
      hourlyRate: Joi.number().min(0).max(10000).optional(),
      availability: Joi.string().valid('available', 'busy', 'unavailable').optional(),
      isComplete: Joi.boolean().optional()
    }).required()
  })
};

async function getUserById(userId) {
  try {
    // TODO: Implement actual user retrieval from database with Firebase authentication
    throw new Error('User retrieval not implemented - requires Firebase authentication');
  } catch (error) {
    console.error('Error getting user by ID:', error);
    throw error;
  }
}

async function verifyUserToken(token) {
  try {
    // TODO: Implement Firebase token verification
    throw new Error('Token verification not implemented - requires Firebase admin SDK');
  } catch (error) {
    console.error('Error verifying user token:', error);
    throw error;
  }
}

module.exports = {
  ApiResponse,
  responseMiddleware,
  validateBody,
  validateQuery,
  verifyFirebaseToken,
  optionalAuth,
  requireOwnership,
  createPagination,
  schemas
}; 