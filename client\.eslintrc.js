module.exports = {
  // Disable all ESLint rules for production builds
  rules: process.env.NODE_ENV === 'production' ? {} : {
    // Keep development rules if needed
  },
  // Extend from Create React App's ESLint config
  extends: [
    'react-app',
    'react-app/jest'
  ],
  // Override rules to be less strict
  overrides: [
    {
      files: ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'],
      rules: {
        // Disable problematic rules
        'react/self-closing-comp': 'off',
        'comma-dangle': 'off',
        'indent': 'off',
        'quotes': 'off',
        'curly': 'off',
        'jsx-a11y/label-has-associated-control': 'off',
        'jsx-a11y/no-static-element-interactions': 'off',
        'no-return-assign': 'off',
        'require-await': 'off',
        'prefer-const': 'off',
        'space-before-function-paren': 'off',
        'no-duplicate-imports': 'off'
      }
    }
  ]
};
