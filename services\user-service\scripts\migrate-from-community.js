/**
 * Migration Script: Community Service to User Service
 * Transfers user data from Community Service database to User Service database
 */

const { Pool } = require('pg');
require('dotenv').config();

// Community Service Database Configuration
const communityPool = new Pool({
  connectionString: process.env.COMMUNITY_DATABASE_URL || process.env.COMMUNITY_DB_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// User Service Database Configuration  
const userPool = new Pool({
  connectionString: process.env.DATABASE_URL || process.env.USER_DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

console.log('🔄 Starting migration from Community Service to User Service...');

async function migrateUsers() {
  const communityClient = await communityPool.connect();
  const userClient = await userPool.connect();

  try {
    console.log('📊 Fetching users from Community Service...');
    
    // Get users from community service
    const communityUsersResult = await communityClient.query(`
      SELECT 
        id,
        email,
        password_hash,
        first_name,
        last_name,
        phone,
        avatar_url,
        email_verified,
        is_active,
        user_type,
        created_at,
        updated_at,
        last_login
      FROM users
      WHERE is_active = true
      ORDER BY created_at ASC
    `);

    console.log(`✅ Found ${communityUsersResult.rows.length} users to migrate`);

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const user of communityUsersResult.rows) {
      try {
        // Check if user already exists in User Service
        const existingUserResult = await userClient.query(
          'SELECT id FROM users WHERE id = $1',
          [user.id]
        );

        if (existingUserResult.rows.length > 0) {
          console.log(`⚠️ User ${user.id} already exists, skipping...`);
          skippedCount++;
          continue;
        }

        // Migrate user to User Service
        await userClient.query('BEGIN');

        // Insert into users table
        await userClient.query(`
          INSERT INTO users (
            id, email, email_verified, display_name, first_name, last_name,
            phone_number, avatar_url, user_type, is_active, created_at, updated_at, last_login
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        `, [
          user.id,
          user.email,
          user.email_verified || false,
          `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email?.split('@')[0] || 'User',
          user.first_name,
          user.last_name,
          user.phone,
          user.avatar_url,
          user.user_type || 'freelancer',
          user.is_active,
          user.created_at,
          user.updated_at,
          user.last_login
        ]);

        // Create default profile
        await userClient.query(`
          INSERT INTO user_profiles (user_id, language_preference)
          VALUES ($1, $2)
        `, [user.id, 'vi']);

        // Create default reputation
        await userClient.query(`
          INSERT INTO user_reputation (user_id)
          VALUES ($1)
        `, [user.id]);

        await userClient.query('COMMIT');
        
        console.log(`✅ Migrated user: ${user.email} (${user.id})`);
        migratedCount++;

      } catch (error) {
        await userClient.query('ROLLBACK');
        console.error(`❌ Error migrating user ${user.id}:`, error.message);
        errorCount++;
      }
    }

    console.log('📊 Migration Summary:');
    console.log(`   ✅ Migrated: ${migratedCount} users`);
    console.log(`   ⚠️ Skipped: ${skippedCount} users`);
    console.log(`   ❌ Errors: ${errorCount} users`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    communityClient.release();
    userClient.release();
  }
}

async function migrateUserReputation() {
  const communityClient = await communityPool.connect();
  const userClient = await userPool.connect();

  try {
    console.log('📊 Migrating user reputation data...');

    // Get reputation data from community service
    const reputationResult = await communityClient.query(`
      SELECT 
        user_id,
        reputation_score,
        level,
        total_posts,
        total_comments,
        total_likes_received,
        total_projects_completed,
        average_rating,
        total_reviews,
        helpful_answers,
        community_contributions,
        last_calculated,
        created_at,
        updated_at
      FROM user_reputation
    `);

    console.log(`✅ Found ${reputationResult.rows.length} reputation records to migrate`);

    let migratedCount = 0;
    let errorCount = 0;

    for (const reputation of reputationResult.rows) {
      try {
        // Check if user exists in User Service
        const userExists = await userClient.query(
          'SELECT id FROM users WHERE id = $1',
          [reputation.user_id]
        );

        if (userExists.rows.length === 0) {
          console.log(`⚠️ User ${reputation.user_id} not found in User Service, skipping reputation...`);
          continue;
        }

        // Update reputation in User Service
        await userClient.query(`
          UPDATE user_reputation SET
            reputation_score = $2,
            level = $3,
            total_posts = $4,
            total_comments = $5,
            total_likes_received = $6,
            total_projects_completed = $7,
            average_rating = $8,
            total_reviews = $9,
            helpful_answers = $10,
            community_contributions = $11,
            last_calculated = $12,
            updated_at = $13
          WHERE user_id = $1
        `, [
          reputation.user_id,
          reputation.reputation_score || 0,
          reputation.level || 'newcomer',
          reputation.total_posts || 0,
          reputation.total_comments || 0,
          reputation.total_likes_received || 0,
          reputation.total_projects_completed || 0,
          reputation.average_rating || 0,
          reputation.total_reviews || 0,
          reputation.helpful_answers || 0,
          reputation.community_contributions || 0,
          reputation.last_calculated,
          reputation.updated_at
        ]);

        console.log(`✅ Migrated reputation for user: ${reputation.user_id}`);
        migratedCount++;

      } catch (error) {
        console.error(`❌ Error migrating reputation for user ${reputation.user_id}:`, error.message);
        errorCount++;
      }
    }

    console.log('📊 Reputation Migration Summary:');
    console.log(`   ✅ Migrated: ${migratedCount} reputation records`);
    console.log(`   ❌ Errors: ${errorCount} reputation records`);

  } catch (error) {
    console.error('❌ Reputation migration failed:', error);
    throw error;
  } finally {
    communityClient.release();
    userClient.release();
  }
}

async function updateCommunityServiceReferences() {
  const communityClient = await communityPool.connect();

  try {
    console.log('🔄 Updating Community Service to reference User Service...');

    // Update posts table to remove user references (keep author_id as foreign key)
    console.log('📝 Posts table already uses user_id references, no changes needed');

    // Update comments table to remove user references (keep author_id as foreign key)
    console.log('📝 Comments table already uses user_id references, no changes needed');

    console.log('✅ Community Service references updated');

  } catch (error) {
    console.error('❌ Failed to update Community Service references:', error);
    throw error;
  } finally {
    communityClient.release();
  }
}

async function verifyMigration() {
  const communityClient = await communityPool.connect();
  const userClient = await userPool.connect();

  try {
    console.log('🔍 Verifying migration...');

    // Count users in both databases
    const communityCountResult = await communityClient.query('SELECT COUNT(*) as count FROM users WHERE is_active = true');
    const userCountResult = await userClient.query('SELECT COUNT(*) as count FROM users WHERE is_active = true');

    const communityCount = parseInt(communityCountResult.rows[0].count);
    const userCount = parseInt(userCountResult.rows[0].count);

    console.log(`📊 Community Service users: ${communityCount}`);
    console.log(`📊 User Service users: ${userCount}`);

    if (userCount >= communityCount) {
      console.log('✅ Migration verification passed');
    } else {
      console.log('⚠️ Migration verification failed - user counts do not match');
    }

    // Check sample users
    const sampleUsersResult = await communityClient.query('SELECT id, email FROM users WHERE is_active = true LIMIT 5');
    
    for (const user of sampleUsersResult.rows) {
      const userServiceResult = await userClient.query('SELECT id, email FROM users WHERE id = $1', [user.id]);
      
      if (userServiceResult.rows.length > 0) {
        console.log(`✅ Sample user ${user.email} found in User Service`);
      } else {
        console.log(`❌ Sample user ${user.email} NOT found in User Service`);
      }
    }

  } catch (error) {
    console.error('❌ Verification failed:', error);
    throw error;
  } finally {
    communityClient.release();
    userClient.release();
  }
}

async function main() {
  try {
    console.log('🚀 Starting User Service migration...');
    console.log('📋 Migration Plan:');
    console.log('   1. Migrate users from Community Service');
    console.log('   2. Migrate user reputation data');
    console.log('   3. Update Community Service references');
    console.log('   4. Verify migration');
    console.log('');

    // Step 1: Migrate users
    await migrateUsers();
    console.log('');

    // Step 2: Migrate reputation
    await migrateUserReputation();
    console.log('');

    // Step 3: Update references
    await updateCommunityServiceReferences();
    console.log('');

    // Step 4: Verify migration
    await verifyMigration();
    console.log('');

    console.log('🎉 Migration completed successfully!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('   1. Update Community Service to call User Service for user data');
    console.log('   2. Update API Gateway routing');
    console.log('   3. Test integration');
    console.log('   4. Drop user tables from Community Service (after testing)');

  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  } finally {
    await communityPool.end();
    await userPool.end();
  }
}

// Run migration if script is called directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  migrateUsers,
  migrateUserReputation,
  updateCommunityServiceReferences,
  verifyMigration
}; 