import React, { useRef, useEffect, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useLanguage } from '../../../contexts/LanguageContext';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves for client showcase animations
const clientEases = {
 elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
 bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
 liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
 magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
 wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1"),
 trust: CustomEase.create("trust", "M0,0 C0.25,0 0.75,1 1,1")
};

const AppleClientLogos = () => {
 const { t } = useLanguage();
 const sectionRef = useRef(null);
 const titleRef = useRef(null);
 const logosRef = useRef(null);
 const particleCanvasRef = useRef(null);
 const morphingShapesRef = useRef([]);
 const logoRefs = useRef([]);

 const clientLogos = [
  {
   id: 1,
   name: 'Microsoft',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Crect x="10" y="10" width="15" height="15" fill="%23F25022"/%3E%3Crect x="30" y="10" width="15" height="15" fill="%2300A4EF"/%3E%3Crect x="10" y="30" width="15" height="15" fill="%237FBA00"/%3E%3Crect x="30" y="30" width="15" height="15" fill="%23FFB900"/%3E%3Ctext x="55" y="32" font-family="Arial" font-size="14" font-weight="bold" fill="%23333"%3EMicrosoft%3C/text%3E%3C/svg%3E',
   description: 'Cloud computing and productivity software',
   color: 'blue',
  },
  {
   id: 2,
   name: 'Google',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Ctext x="15" y="35" font-family="Arial" font-size="18" font-weight="bold"%3E%3Ctspan fill="%234285F4"%3EG%3C/tspan%3E%3Ctspan fill="%23EA4335"%3Eo%3C/tspan%3E%3Ctspan fill="%23FBBC05"%3Eo%3C/tspan%3E%3Ctspan fill="%234285F4"%3Eg%3C/tspan%3E%3Ctspan fill="%2334A853"%3El%3C/tspan%3E%3Ctspan fill="%23EA4335"%3Ee%3C/tspan%3E%3C/text%3E%3C/svg%3E',
   description: 'Search engine and cloud services',
   color: 'blue',
  },
  {
   id: 3,
   name: 'Apple',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M25 15c-2 0-4 1-5 3-1 2 0 4 2 5 0-2 1-3 3-3z M20 20c-3 2-5 6-3 10 2 4 7 6 10 4 2-1 4-3 6-3s4 2 6 3c3 2 8 0 10-4 2-4 0-8-3-10-2-2-5-2-7 0-2 2-4 2-6 0-2-2-5-2-7 0z" fill="%23333"/%3E%3Ctext x="45" y="35" font-family="Arial" font-size="14" font-weight="bold" fill="%23333"%3EApple%3C/text%3E%3C/svg%3E',
   description: 'Consumer electronics and software',
   color: 'gray',
  },
  {
   id: 4,
   name: 'Netflix',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Crect x="15" y="15" width="4" height="30" fill="%23E50914"/%3E%3Crect x="22" y="15" width="4" height="30" fill="%23E50914"/%3E%3Crect x="29" y="15" width="4" height="30" fill="%23E50914"/%3E%3Cpolygon points="19,15 26,45 29,45 22,15" fill="%23E50914"/%3E%3Ctext x="40" y="35" font-family="Arial" font-size="14" font-weight="bold" fill="%23E50914"%3ENETFLIX%3C/text%3E%3C/svg%3E',
   description: 'Streaming entertainment platform',
   color: 'red',
  },
  {
   id: 5,
   name: 'Amazon',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Ctext x="15" y="30" font-family="Arial" font-size="16" font-weight="bold" fill="%23232F3E"%3Eamazon%3C/text%3E%3Cpath d="M20 35 Q35 45 50 35" stroke="%23FF9900" stroke-width="2" fill="none"/%3E%3Ccircle cx="52" cy="35" r="1.5" fill="%23FF9900"/%3E%3C/svg%3E',
   description: 'E-commerce and cloud computing',
   color: 'orange',
  },
  {
   id: 6,
   name: 'Meta',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Ctext x="15" y="35" font-family="Arial" font-size="16" font-weight="bold" fill="%231877F2"%3EMeta%3C/text%3E%3C/svg%3E',
   description: 'Social media and virtual reality',
   color: 'blue',
  },
  {
   id: 7,
   name: 'Tesla',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M20 20 L25 15 L35 15 L40 20 L35 25 L25 25 Z" fill="%23CC0000"/%3E%3Ctext x="45" y="35" font-family="Arial" font-size="14" font-weight="bold" fill="%23CC0000"%3ETESLA%3C/text%3E%3C/svg%3E',
   description: 'Electric vehicles and clean energy',
   color: 'red',
  },
  {
   id: 8,
   name: 'Spotify',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Ccircle cx="30" cy="30" r="12" fill="%231DB954"/%3E%3Cpath d="M22 25 Q30 22 38 25 M23 30 Q30 27 37 30 M24 35 Q30 32 36 35" stroke="white" stroke-width="2" fill="none"/%3E%3Ctext x="48" y="35" font-family="Arial" font-size="14" font-weight="bold" fill="%231DB954"%3ESpotify%3C/text%3E%3C/svg%3E',
   description: 'Music streaming platform',
   color: 'green',
  },
  {
   id: 9,
   name: 'Adobe',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cpolygon points="15,15 25,15 30,30 25,45 15,45" fill="%23FF0000"/%3E%3Cpolygon points="35,15 45,15 40,30 45,45 35,45" fill="%23FF0000"/%3E%3Ctext x="50" y="35" font-family="Arial" font-size="14" font-weight="bold" fill="%23FF0000"%3EAdobe%3C/text%3E%3C/svg%3E',
   description: 'Creative software and digital marketing',
   color: 'red',
  },
  {
   id: 10,
   name: 'Slack',
   logo: 'data:image/svg+xml,%3Csvg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg"%3E%3Crect x="15" y="25" width="8" height="4" rx="2" fill="%23E01E5A"/%3E%3Crect x="27" y="15" width="4" height="8" rx="2" fill="%2336C5F0"/%3E%3Crect x="27" y="37" width="4" height="8" rx="2" fill="%2336C5F0"/%3E%3Crect x="35" y="25" width="8" height="4" rx="2" fill="%23ECB22E"/%3E%3Ctext x="50" y="35" font-family="Arial" font-size="14" font-weight="bold" fill="%234A154B"%3ESlack%3C/text%3E%3C/svg%3E',
   description: 'Team collaboration platform',
   color: 'purple',
  },
 ];

 // Advanced Trust Network Particles
 const createAdvancedTrustParticles = () => {
  if (!particleCanvasRef.current) return;

  const container = particleCanvasRef.current;
  const particles = [];

  // Create 150 trust network particles
  for (let i = 0; i < 150; i++) {
   const particle = document.createElement('div');
   particle.className = 'absolute w-1 h-1 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full opacity-50';
   particle.style.left = Math.random() * 100 + '%';
   particle.style.top = Math.random() * 100 + '%';
   container.appendChild(particle);
   particles.push(particle);

   // Create trust network connections
   const connectionData = `M${Math.random() * 100},${Math.random() * 100} L${Math.random() * 100},${Math.random() * 100} L${Math.random() * 100},${Math.random() * 100}`;
   
   gsap.to(particle, {
    motionPath: {
     path: connectionData,
     autoRotate: true
    },
    duration: Math.random() * 20 + 15,
    repeat: -1,
    ease: clientEases.trust,
    delay: Math.random() * 6
   });

   // Trust reliability pulse
   gsap.to(particle, {
    scale: "random(0.4, 3.5)",
    opacity: "random(0.3, 0.9)",
    duration: "random(5, 10)",
    repeat: -1,
    yoyo: true,
    ease: clientEases.wave
   });
  }
 };

 // Morphing Trust Backgrounds
 const createMorphingTrustShapes = () => {
  morphingShapesRef.current.forEach((shape, index) => {
   if (!shape) return;

   const colors = [
    'from-blue-400/15 to-indigo-400/15',
    'from-indigo-400/15 to-purple-400/15',
    'from-purple-400/15 to-blue-400/15',
    'from-blue-400/15 to-cyan-400/15'
   ];

   shape.className = `absolute bg-gradient-to-br ${colors[index % colors.length]} rounded-full`;

   const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
   
   morphTimeline
    .to(shape, {
     borderRadius: "90% 10% 30% 70% / 20% 80% 70% 30%",
     scale: 1.8,
     rotation: 540,
     x: "random(-70, 70)",
     y: "random(-60, 60)",
     duration: 10,
     ease: clientEases.liquid
    })
    .to(shape, {
     borderRadius: "10% 90% 70% 30% / 80% 20% 30% 70%",
     scale: 0.7,
     rotation: -270,
     x: "random(-60, 60)",
     y: "random(-70, 70)",
     duration: 8,
     ease: clientEases.elastic
    })
    .to(shape, {
     borderRadius: "70% 30% 10% 90% / 30% 70% 90% 10%",
     scale: 1.5,
     rotation: 450,
     x: "random(-65, 65)",
     y: "random(-65, 65)",
     duration: 9,
     ease: clientEases.wave
    });

   morphTimeline.delay(index * 2.5);
  });
 };

 // Advanced Title Animation with Trust Theme
 const createAdvancedTitleAnimation = () => {
  if (!titleRef.current) return;

  const titleElement = titleRef.current.querySelector('h2');
  const subtitleElement = titleRef.current.querySelector('p');

  if (titleElement) {
   const titleSplit = new SplitText(titleElement, { type: "chars,words" });

   gsap.fromTo(titleSplit.chars, {
    opacity: 0,
    y: 150,
    rotationX: -90,
    transformOrigin: "center bottom"
   }, {
    opacity: 1,
    y: 0,
    rotationX: 0,
    duration: 2.6,
    stagger: 0.02,
    ease: clientEases.bounce,
     scrollTrigger: {
      trigger: titleRef.current,
      start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });

   // Add trust leadership glow
   gsap.to(titleElement, {
    textShadow: "0 0 60px rgba(59, 130, 246, 0.6)",
    duration: 6,
    repeat: -1,
    yoyo: true,
    ease: clientEases.wave
   });
  }

  if (subtitleElement) {
   const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

   gsap.fromTo(subtitleSplit.words, {
      opacity: 0,
    y: 90,
    scale: 0.4
   }, {
      opacity: 1,
      y: 0,
      scale: 1,
    duration: 2.0,
    stagger: 0.2,
    ease: clientEases.elastic,
    delay: 1.6,
      scrollTrigger: {
     trigger: titleRef.current,
       start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });
  }
 };

 // Advanced Magnetic Logo Hover
 const createMagneticLogoHover = (logoElement, index) => {
  if (!logoElement) return;

  const logoImage = logoElement.querySelector('.logo-image');
  const logoContent = logoElement.querySelector('.logo-content');

  let isHovering = false;

  logoElement.addEventListener('mouseenter', () => {
   isHovering = true;

   // Magnetic logo hover timeline
   const hoverTL = gsap.timeline();

   hoverTL
    .to(logoElement, {
     scale: 1.12,
     y: -25,
     rotationY: 8,
     rotationX: 4,
     boxShadow: "0 70px 140px rgba(0,0,0,0.25)",
     duration: 0.9,
     ease: clientEases.magnetic
    })
    .to(logoImage, {
     scale: 1.2,
     rotation: 3,
     duration: 1.1,
     ease: clientEases.elastic
    }, 0)
    .to(logoContent, {
     y: -8,
     scale: 1.08,
     duration: 0.8,
     ease: clientEases.bounce
    }, 0.2);


  });

  logoElement.addEventListener('mouseleave', () => {
   isHovering = false;

   gsap.to(logoElement, {
    scale: 1,
    y: 0,
    rotationY: 0,
    rotationX: 0,
    boxShadow: "0 30px 60px rgba(0,0,0,0.12)",
    duration: 1.2,
    ease: clientEases.elastic
   });

   gsap.to(logoImage, {
    scale: 1,
    rotation: 0,
    duration: 0.9,
    ease: clientEases.bounce
   });

   gsap.to(logoContent, {
    y: 0,
    scale: 1,
    duration: 0.7,
    ease: clientEases.wave
   });
  });

  // Real-time mouse tracking for trust interaction
  logoElement.addEventListener('mousemove', (e) => {
   if (!isHovering) return;

   const rect = logoElement.getBoundingClientRect();
   const centerX = rect.left + rect.width / 2;
   const centerY = rect.top + rect.height / 2;
   const mouseX = e.clientX - centerX;
   const mouseY = e.clientY - centerY;

   gsap.to(logoElement, {
    x: mouseX * 0.03,
    y: mouseY * 0.03,
    duration: 0.3,
    ease: "power2.out"
   });

   gsap.to(logoImage, {
    x: mouseX * 0.05,
    y: mouseY * 0.05,
    duration: 0.2,
    ease: "power2.out"
   });
  });
 };



 // Advanced Logo Grid Animation
 const createAdvancedLogoGrid = () => {
  if (!logosRef.current) return;

  const logoElements = Array.from(logosRef.current.children);

  logoElements.forEach((logo, index) => {
   // Store reference for hover effects
   logoRefs.current[index] = logo;

   // Create magnetic hover effect
   createMagneticLogoHover(logo, index);

   // Trust showcase entrance animation
   gsap.fromTo(logo, {
    opacity: 0,
    y: 160,
    scale: 0.5,
    rotationY: 60,
    rotationX: 40
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    rotationY: 0,
    rotationX: 0,
    duration: 2.4,
    delay: index * 0.12,
    ease: clientEases.elastic,
    scrollTrigger: {
     trigger: logo,
     start: 'top 90%',
     toggleActions: 'play none none reverse'
    }
   });

   // Animate logo components
   const logoImage = logo.querySelector('.logo-image');
   const logoContent = logo.querySelector('.logo-content');

   if (logoImage) {
    gsap.fromTo(logoImage, {
     scale: 0,
     rotation: -450
    }, {
     scale: 1,
     rotation: 0,
     duration: 1.6,
     delay: index * 0.12 + 0.6,
     ease: clientEases.bounce,
     scrollTrigger: {
      trigger: logo,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }

   if (logoContent) {
    const contentChildren = Array.from(logoContent.children);
    gsap.fromTo(contentChildren, {
     opacity: 0,
     x: -100
    }, {
     opacity: 1,
     x: 0,
     duration: 1.4,
     stagger: 0.18,
     delay: index * 0.12 + 1.2,
     ease: clientEases.wave,
     scrollTrigger: {
      trigger: logo,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }

   // Add infinite floating animation
   gsap.to(logo, {
    y: "random(-15, 15)",
    x: "random(-10, 10)",
    rotation: "random(-2, 2)",
    duration: "random(4, 8)",
    repeat: -1,
    yoyo: true,
    ease: clientEases.wave,
    delay: Math.random() * 2
   });
  });
 };

 // Setup hover animations using useCallback for proper cleanup
 const setupHoverAnimations = useCallback(() => {
  const eventListeners = [];
  
  if (logosRef.current && logosRef.current.children) {
   const logoItems = logosRef.current.children;
   
   Array.from(logoItems).forEach((logoItem, index) => {
    const handleMouseEnter = () => {
     gsap.to(logoItem, {
      scale: 1.05,
      y: -5,
      duration: 0.3,
      ease: clientEases.elastic
     });
    };
    
    const handleMouseLeave = () => {
     gsap.to(logoItem, {
      scale: 1,
      y: 0,
      duration: 0.3,
      ease: clientEases.bounce
     });
    };
    
    logoItem.addEventListener('mouseenter', handleMouseEnter);
    logoItem.addEventListener('mouseleave', handleMouseLeave);
    
    eventListeners.push(
     { element: logoItem, type: 'mouseenter', handler: handleMouseEnter },
     { element: logoItem, type: 'mouseleave', handler: handleMouseLeave }
    );
   });
  }
  
  return eventListeners;
 }, []);

 useEffect(() => {
  let eventListeners = []; // Track event listeners for cleanup

  const ctx = gsap.context(() => {
   // Initialize all advanced animations
   createAdvancedTrustParticles();
   createMorphingTrustShapes();
   createAdvancedTitleAnimation();
   createAdvancedLogoGrid();

   // Add hover animations using external function and track for cleanup
   eventListeners = setupHoverAnimations();
  }, sectionRef);

  return () => {
   // Clean up event listeners
   eventListeners.forEach(({ element, type, handler }) => {
    element.removeEventListener(type, handler);
   });
   ctx.revert();
  };
 // eslint-disable-next-line react-hooks/exhaustive-deps
 }, [setupHoverAnimations]);

 return (
  <>
   {/* Enhanced CSS */}
   <style>{`
    @keyframes liquid-trust {
     0%, 100% {
      border-radius: 90% 10% 30% 70% / 20% 80% 70% 30%;
     }
     25% {
      border-radius: 10% 90% 70% 30% / 80% 20% 30% 70%;
     }
     50% {
      border-radius: 70% 30% 10% 90% / 30% 70% 90% 10%;
     }
     75% {
      border-radius: 30% 70% 90% 10% / 70% 30% 10% 90%;
     }
    }
    
    .liquid-trust {
     animation: liquid-trust 25s ease-in-out infinite;
    }
    
    .logo-card {
     transform-style: preserve-3d;
     transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .trust-particles {
     will-change: transform;
    }
    
    .logo-card:hover {
     transform: perspective(1000px) rotateX(4deg) rotateY(8deg) translateZ(60px);
    }
   `}</style>

   <section
    ref={sectionRef}
    className='relative py-20 bg-gray-50 transition-colors duration-300 overflow-hidden'
   >
    {/* Advanced Multi-layer Background */}
    <div className='absolute inset-0'>
     <div 
      ref={el => morphingShapesRef.current[0] = el}
      className='absolute top-32 left-28 w-64 h-64 liquid-trust'
     />
     <div 
      ref={el => morphingShapesRef.current[1] = el}
      className='absolute top-56 right-32 w-56 h-56 liquid-trust'
     />
     <div 
      ref={el => morphingShapesRef.current[2] = el}
      className='absolute bottom-36 left-1/4 w-72 h-72 liquid-trust'
     />
     <div 
      ref={el => morphingShapesRef.current[3] = el}
      className='absolute bottom-32 right-28 w-60 h-60 liquid-trust'
     />
    </div>

    {/* Advanced Trust Particle Canvas */}
    <div 
     ref={particleCanvasRef}
     className='absolute inset-0 pointer-events-none overflow-hidden trust-particles'
    />

    <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
     {/* Enhanced Section Header */}
    <div ref={titleRef} className='text-center mb-16'>
      <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 mb-8 transition-colors duration-300'>
       {t('trustedByIndustryLeaders')}
       <span className='block text-transparent bg-gradient-to-r from-blue-500 to-indigo-500 bg-clip-text'>
        {t('leaders')}
       </span>
     </h2>
      <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
       {t('joinThousandsOfCompanies')}
     </p>
    </div>

     {/* Enhanced Client Logos Grid */}
    <div
     ref={logosRef}
      className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 lg:gap-10'
     >
      {clientLogos.map((client, index) => (
       <div
        key={client.id}
        className='group logo-card bg-white rounded-3xl p-8 border border-gray-100 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 transform-gpu'
       >
        {/* Enhanced Logo Image */}
        <div className='logo-image mb-6 flex items-center justify-center'>
         <img
          src={client.logo}
          alt={client.name}
          className='max-w-full h-16 object-contain filter grayscale group-hover:grayscale-0 transition-all duration-500'
         />
         </div>

        {/* Enhanced Logo Content */}
        <div className='logo-content text-center'>
         <h3 className='text-lg font-bold text-gray-900 mb-2 transition-colors duration-300'>
          {client.name}
         </h3>
         <p className='text-sm text-gray-600 transition-colors duration-300'>
          {client.description}
         </p>
        </div>

        {/* Enhanced Hover Gradient */}
        <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br from-${client.color}-500/5 to-${client.color}-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
       </div>
      ))}
       </div>

     {/* Enhanced Stats Section */}
     <div className='mt-16 grid grid-cols-1 md:grid-cols-4 gap-8'>
      <div className='text-center'>
       <div className='text-4xl font-black text-blue-600 mb-2'>
        0+
       </div>
       <div className='text-lg text-gray-600'>
        {t('trustedCompanies')}
       </div>
      </div>
       <div className='text-center'>
       <div className='text-4xl font-black text-green-600 mb-2'>
        0+
       </div>
       <div className='text-lg text-gray-600'>
        {t('projectsDelivered')}
       </div>
      </div>
      <div className='text-center'>
       <div className='text-4xl font-black text-purple-600 mb-2'>
        0%
       </div>
       <div className='text-lg text-gray-600'>
        {t('clientSatisfaction')}
       </div>
    </div>
      <div className='text-center'>
       <div className='text-4xl font-black text-orange-600 mb-2'>
        24/7
     </div>
       <div className='text-lg text-gray-600'>
        {t('supportAvailable')}
       </div>
     </div>
    </div>

     {/* Enhanced Partnership CTA */}
     <div className='text-center mt-16'>
      <button className='group px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'>
       <span className='mr-3'>{t('becomePartner')}</span>
       <span className='inline-block transition-transform duration-300 group-hover:translate-x-2'>→</span>
      </button>
    </div>
   </div>
  </section>
  </>
 );
};

export default AppleClientLogos;
