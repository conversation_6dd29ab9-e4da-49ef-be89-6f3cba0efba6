const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

// Load centralized configuration
const config = require('./src/config/config');

// Initialize Firebase Admin SDK
require('./src/config/firebase');

const logger = require('./src/utils/logger');

// Initialize PostgreSQL connection
let db = null;
let dbHealthy = false;

async function initializeDatabase() {
    try {
        // Initialize PostgreSQL (only supported database)
        const { postgresqlConfig } = require('./src/config/postgresql');
        await postgresqlConfig.initialize();
        db = postgresqlConfig;
        dbHealthy = true;
        logger.info('✅ PostgreSQL initialized successfully for Community Service');
        
        // Auto-migrate database schema if needed (production fix)
        try {
            const { autoMigrate } = require('./scripts/auto-migrate-on-startup');
            logger.info('🔄 Checking database schema...');
            const migrationSuccess = await autoMigrate();
            if (migrationSuccess) {
                logger.info('✅ Database schema verified/updated successfully');
            } else {
                logger.warn('⚠️ Database schema check/migration had issues (service will continue)');
            }
        } catch (migrationError) {
            logger.warn('⚠️ Auto-migration check failed (service will continue):', migrationError.message);
        }
        
    } catch (error) {
        logger.error('❌ PostgreSQL initialization failed for Community Service:', error);
        logger.error('🚫 Community Service requires database connection to start');
        dbHealthy = false;
        db = null;
        throw error; // Fail fast - no database, no service
    }
}

const app = express();
// Service configuration
const service = {
    name: config.SERVICE_NAME,
    version: config.SERVICE_VERSION,
    port: config.PORT
};



// CORS configuration for credentials support
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        // Get allowed origins from centralized config
        const allowedOrigins = config.ALLOWED_ORIGINS;

        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            console.warn(`⚠️ CORS blocked origin: ${origin}`);
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: config.CORS_CREDENTIALS,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Correlation-ID',
        'X-Request-ID',
        'Cache-Control',
        'x-auth-token',
        'Pragma',
        'Expires'
    ],
    exposedHeaders: ['X-Correlation-ID', 'Content-Length', 'X-Requested-With'],
    preflightContinue: false,
    optionsSuccessStatus: 200
};

// Middleware
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));
app.use(cors(corsOptions));
app.use(morgan('combined', { stream: logger.stream }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Handle preflight requests explicitly
app.options('*', cors(corsOptions));

// Prevent caching for all API routes to ensure fresh data
app.use((req, res, next) => {
    res.header('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.header('Pragma', 'no-cache');
    res.header('Expires', '0');
    next();
});

// Request logging middleware
app.use(logger.requestLogger());

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        const healthChecks = {
            memory: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB'
                },
                timestamp: new Date().toISOString()
            },
            uptime: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    uptime: Math.round(process.uptime()) + 's'
                },
                timestamp: new Date().toISOString()
            },
            environment: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    nodeVersion: process.version,
                    environment: process.env.NODE_ENV || 'development',
                    databaseType: 'postgresql'
                },
                timestamp: new Date().toISOString()
            },
            database: {
                status: dbHealthy ? 'healthy' : 'unhealthy',
                duration: '0ms',
                result: db ? await db.healthCheck() : { error: 'Database not initialized' },
                timestamp: new Date().toISOString()
            }
        };



        const overallStatus = Object.values(healthChecks).every(check => check.status === 'healthy') ? 'healthy' : 'degraded';

        res.json({
            service: config.SERVICE_NAME,
            status: overallStatus,
            timestamp: new Date().toISOString(),
            port: config.PORT,
            version: config.SERVICE_VERSION,
            checks: healthChecks
        });

    } catch (error) {
        logger.error('Health check failed', { error: error.message });
        res.status(500).json({
            service: config.SERVICE_NAME,
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// API Gateway health check endpoint (for compatibility)
app.get('/api/v1/community/health', async (req, res) => {
    try {
        const healthChecks = {
            memory: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB'
                },
                timestamp: new Date().toISOString()
            },
            uptime: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    uptime: Math.round(process.uptime()) + 's'
                },
                timestamp: new Date().toISOString()
            },
            environment: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    nodeVersion: process.version,
                    environment: config.NODE_ENV,
                    databaseType: 'postgresql'
                },
                timestamp: new Date().toISOString()
            },
            database: {
                status: dbHealthy ? 'healthy' : 'unhealthy',
                duration: '0ms',
                result: db ? await db.healthCheck() : { error: 'Database not initialized' },
                timestamp: new Date().toISOString()
            }
        };

        const overallStatus = Object.values(healthChecks).every(check => check.status === 'healthy') ? 'healthy' : 'degraded';

        res.json({
            service: config.SERVICE_NAME,
            status: overallStatus,
            timestamp: new Date().toISOString(),
            port: config.PORT,
            version: config.SERVICE_VERSION,
            checks: healthChecks
        });

    } catch (error) {
        logger.error('API Gateway health check failed', { error: error.message });
        res.status(500).json({
            service: config.SERVICE_NAME,
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// API health check endpoint (for API Gateway compatibility)
app.get('/api/health', async (req, res) => {
    try {
        const healthChecks = {
            memory: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB'
                },
                timestamp: new Date().toISOString()
            },
            uptime: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    uptime: Math.round(process.uptime()) + 's'
                },
                timestamp: new Date().toISOString()
            },
            environment: {
                status: 'healthy',
                duration: '0ms',
                result: {
                    nodeVersion: process.version,
                    environment: config.NODE_ENV,
                    databaseType: 'postgresql'
                },
                timestamp: new Date().toISOString()
            },
            database: {
                status: dbHealthy ? 'healthy' : 'unhealthy',
                duration: '0ms',
                result: db ? await db.healthCheck() : { error: 'Database not initialized' },
                timestamp: new Date().toISOString()
            }
        };

        const overallStatus = Object.values(healthChecks).every(check => check.status === 'healthy') ? 'healthy' : 'degraded';

        res.json({
            service: config.SERVICE_NAME,
            status: overallStatus,
            timestamp: new Date().toISOString(),
            port: config.PORT,
            version: config.SERVICE_VERSION,
            checks: healthChecks
        });

    } catch (error) {
        logger.error('API health check failed', { error: error.message });
        res.status(500).json({
            service: config.SERVICE_NAME,
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Basic route
app.get('/', (req, res) => {
    res.json({ 
        message: `${config.SERVICE_NAME} is running`,
        version: config.SERVICE_VERSION,
        port: config.PORT,
        timestamp: new Date().toISOString()
    });
});

// Import API routes
const postsRoutes = require('./src/routes/posts');
const commentsRoutes = require('./src/routes/comments');
const usersRoutes = require('./src/routes/users');
const likesRoutes = require('./src/routes/likes');
const statsRoutes = require('./src/routes/stats');
const categoriesRoutes = require('./src/routes/categories');

// Import authentication middleware
const { verifyFirebaseToken } = require('./src/middleware/auth');

// API routes (auth middleware applied selectively)
app.use('/api/posts', postsRoutes);
app.use('/api/comments', commentsRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/likes', likesRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/categories', categoriesRoutes);

// API info endpoint
app.get('/api', (req, res) => {
    res.json({
        service: config.SERVICE_NAME,
        message: 'Community API ready',
        version: config.SERVICE_VERSION,
        eventDriven: false,
        endpoints: {
            posts: [
                'GET /api/posts - Get all posts with pagination and filtering',
                'POST /api/posts - Create new post',
                'GET /api/posts/:id - Get post by ID',
                'PUT /api/posts/:id - Update post',
                'DELETE /api/posts/:id - Delete post',
                'POST /api/posts/:id/share - Share post'
            ],
            comments: [
                'GET /api/comments/:postId - Get comments for post',
                'POST /api/comments - Create new comment',
                'PUT /api/comments/:id - Update comment',
                'DELETE /api/comments/:id - Delete comment (soft delete)'
            ],
            users: [
                'GET /api/users - Get all users with pagination and search',
                'GET /api/users/:id - Get user profile with stats',
                'POST /api/users - Create new user',
                'PUT /api/users/:id - Update user profile'
            ],
            likes: [
                'POST /api/likes - Like/unlike post or comment (legacy)',
                'GET /api/likes/post/:postId - Get likes for post',
                'GET /api/likes/comment/:commentId - Get likes for comment'
            ],
            stats: [
                'GET /api/stats - Get community statistics',
                'GET /api/stats/user/:userId - Get user statistics'
            ],
            categories: [
                'GET /api/categories - Get all categories with post counts',
                'GET /api/categories/:category/posts - Get posts by category',
                'GET /api/categories/:category/stats - Get category statistics'
            ]
        },
        features: {
            posts: true,
            comments: true,
            likes: true,
            shares: true,
            votes: false
        }
    });
});

// Import and use comprehensive error handler
const errorHandler = require('./src/middleware/errorHandler');
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not found',
        service: config.SERVICE_NAME,
        path: req.originalUrl,
        timestamp: new Date().toISOString()
    });
});



// Start server
async function startServer() {
    try {
        // Initialize database first
        await initializeDatabase();

        const server = app.listen(config.PORT, '0.0.0.0', () => {
            logger.info('Community Service started successfully', {
                port: config.PORT,
                environment: config.NODE_ENV,
                databaseType: 'postgresql',
                databaseHealthy: dbHealthy,
                healthCheck: config.HEALTH_CHECK_URL
            });
        });

        return server;
    } catch (error) {
        logger.error('Failed to start Community Service', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    }
}

// Start the server
const serverPromise = startServer();

// Graceful shutdown
async function gracefulShutdown(signal) {
    logger.info(`${signal} received, shutting down gracefully`);

    try {
        // Close server
        const server = await serverPromise;
        if (server) {
            server.close(() => {
                logger.info('Community Service shut down successfully');
                process.exit(0);
            });
        } else {
            process.exit(0);
        }
    } catch (error) {
        logger.error('Error during graceful shutdown', {
            error: error.message,
            stack: error.stack
        });
        // Force exit only if really necessary
        setTimeout(() => process.exit(1), 1000);
    }
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    logger.error('Uncaught Exception', {
        error: err.message,
        stack: err.stack
    });
    // Don't exit in development, just log the error
    if (process.env.NODE_ENV === 'production') {
        process.exit(1);
    }
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection', {
        reason: reason.toString(),
        promise: promise.toString()
    });
    // Don't exit in development, just log the error
    if (process.env.NODE_ENV === 'production') {
        process.exit(1);
    }
});

module.exports = app;