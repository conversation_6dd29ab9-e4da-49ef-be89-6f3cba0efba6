import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';
import { apiService } from '../../../services/api';
import {
 MagnifyingGlassIcon,
 FunnelIcon,
 MapPinIcon,
 CurrencyDollarIcon,
 ClockIcon,
 BriefcaseIcon,
 StarIcon,
 EyeIcon,
 ChatBubbleLeftRightIcon,
 BookmarkIcon,
 ShareIcon,
 ArrowUpIcon,
 ArrowDownIcon,
} from '@heroicons/react/24/outline';

const AppleJobsPage = () => {
 const { t } = useLanguage();
 const { user } = useAuth();
 const [jobs, setJobs] = useState([]);
 const [loading, setLoading] = useState(true);
 const [filters, setFilters] = useState({
  category: '',
  budgetMin: '',
  budgetMax: '',
  location: '',
  experience: '',
  search: '',
 });
 const [sortBy, setSortBy] = useState('newest');
 const [showFilters, setShowFilters] = useState(false);

 // Mock data for jobs
 const mockJobs = [
  {
   id: 1,
   title: 'Thiết kế Website E-commerce',
   company: 'TechCorp Vietnam',
   location: 'Hồ Chí Minh',
   budget: { min: 15000000, max: 25000000 },
   category: 'Web Development',
   experience: '3-5 years',
   postedDate: '2024-01-15',
   description: 'Cần freelancer thiết kế website bán hàng với giao diện hiện đại, responsive và tích hợp thanh toán online.',
   skills: ['React', 'Node.js', 'MongoDB', 'Payment Gateway'],
   views: 245,
   proposals: 12,
   isUrgent: true,
   isFeatured: true,
  },
  {
   id: 2,
   title: 'Phát triển App Mobile iOS',
   company: 'StartupHub',
   location: 'Hà Nội',
   budget: { min: 8000000, max: 15000000 },
   category: 'Mobile Development',
   experience: '2-4 years',
   postedDate: '2024-01-14',
   description: 'Phát triển ứng dụng iOS cho startup fintech với tính năng quản lý tài chính cá nhân.',
   skills: ['Swift', 'iOS', 'Core Data', 'Firebase'],
   views: 189,
   proposals: 8,
   isUrgent: false,
   isFeatured: false,
  },
  {
   id: 3,
   title: 'Thiết kế Logo & Brand Identity',
   company: 'Creative Studio',
   location: 'Remote',
   budget: { min: 5000000, max: 8000000 },
   category: 'Design',
   experience: '1-3 years',
   postedDate: '2024-01-13',
   description: 'Thiết kế logo và bộ nhận diện thương hiệu cho công ty công nghệ mới.',
   skills: ['Adobe Illustrator', 'Photoshop', 'Brand Design'],
   views: 156,
   proposals: 15,
   isUrgent: false,
   isFeatured: true,
  },
  {
   id: 4,
   title: 'Viết Content Marketing',
   company: 'Digital Marketing Pro',
   location: 'Remote',
   budget: { min: 3000000, max: 6000000 },
   category: 'Content Writing',
   experience: '2-5 years',
   postedDate: '2024-01-12',
   description: 'Viết nội dung marketing cho các kênh social media và website công ty.',
   skills: ['Content Writing', 'SEO', 'Social Media'],
   views: 98,
   proposals: 6,
   isUrgent: true,
   isFeatured: false,
  },
  {
   id: 5,
   title: 'Phát triển AI Chatbot',
   company: 'AI Solutions',
   location: 'Hồ Chí Minh',
   budget: { min: 20000000, max: 35000000 },
   category: 'AI/ML',
   experience: '4-7 years',
   postedDate: '2024-01-11',
   description: 'Phát triển chatbot AI cho website hỗ trợ khách hàng với khả năng xử lý tiếng Việt.',
   skills: ['Python', 'TensorFlow', 'NLP', 'API Integration'],
   views: 312,
   proposals: 18,
   isUrgent: true,
   isFeatured: true,
  },
 ];

 // Initialize with mock data to prevent undefined errors
 useEffect(() => {
  setJobs(mockJobs || []);
 }, []);

 useEffect(() => {
  const fetchJobs = async () => {
   try {
    setLoading(true);
    console.log('Fetching jobs with filters:', filters);
    const response = await apiService.jobs.getAll(filters);
    console.log('Jobs API response:', response);

    if (response && response.success && response.data && Array.isArray(response.data.jobs)) {
     setJobs(response.data.jobs);
    } else if (response && Array.isArray(response.data)) {
     // Handle case where response.data is directly an array
     setJobs(response.data);
    } else if (response && Array.isArray(response)) {
     // Handle case where response is directly an array
     setJobs(response);
    } else {
     console.warn('Unexpected API response structure, using mock data:', response);
     // Fallback to mock data if API fails
     setJobs(mockJobs || []);
    }
   } catch (error) {
    console.error('Error fetching jobs:', error);
    // Fallback to mock data if API fails
    setJobs(mockJobs || []);
   } finally {
    setLoading(false);
   }
  };

  fetchJobs();
 }, [filters, mockJobs]);

 const formatBudget = (budget) => {
  return `${budget.min.toLocaleString('vi-VN')} - ${budget.max.toLocaleString('vi-VN')} VNĐ`;
 };

 const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return 'Hôm qua';
  if (diffDays < 7) return `${diffDays} ngày trước`;
  return date.toLocaleDateString('vi-VN');
 };

 const handleFilterChange = (key, value) => {
  setFilters(prev => ({ ...prev, [key]: value }));
 };

 const clearFilters = () => {
  setFilters({
   category: '',
   budgetMin: '',
   budgetMax: '',
   location: '',
   experience: '',
   search: '',
  });
 };

 const filteredJobs = (jobs || []).filter(job => {
  if (filters.search && !job.title.toLowerCase().includes(filters.search.toLowerCase())) return false;
  if (filters.category && job.category !== filters.category) return false;
  if (filters.location && !job.location.toLowerCase().includes(filters.location.toLowerCase())) return false;
  if (filters.experience && job.experience !== filters.experience) return false;
  if (filters.budgetMin && job.budget.min < parseInt(filters.budgetMin)) return false;
  if (filters.budgetMax && job.budget.max > parseInt(filters.budgetMax)) return false;
  return true;
 });

 const sortedJobs = [...filteredJobs].sort((a, b) => {
  switch (sortBy) {
   case 'newest':
    return new Date(b.postedDate) - new Date(a.postedDate);
   case 'oldest':
    return new Date(a.postedDate) - new Date(b.postedDate);
   case 'budget-high':
    return b.budget.max - a.budget.max;
   case 'budget-low':
    return a.budget.min - b.budget.min;
   default:
    return 0;
  }
 });

 if (loading) {
  return (
   <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
     <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
     <p className="mt-4 text-gray-600">Đang tải công việc...</p>
    </div>
   </div>
  );
 }

 return (
  <div className="min-h-screen bg-gray-50">
   {/* Header Section */}
   <div className="bg-white border-b border-gray-200">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
     <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">
       {t('findJobs') || 'Tìm Công Việc'}
      </h1>
      <p className="text-xl text-gray-600 mb-8">
       {t('jobsDescription') || 'Khám phá hàng nghìn cơ hội việc làm từ các công ty hàng đầu'}
      </p>
      
      {/* Search Bar */}
      <div className="max-w-2xl mx-auto">
       <div className="relative">
        <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
         type="text"
         placeholder={t('searchJobs') || 'Tìm kiếm công việc...'}
         value={filters.search}
         onChange={(e) => handleFilterChange('search', e.target.value)}
         className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-2xl bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
       </div>
      </div>
     </div>
    </div>
   </div>

   {/* Main Content */}
   <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div className="flex flex-col lg:flex-row gap-8">
     {/* Filters Sidebar */}
     <div className="lg:w-1/4">
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
         {t('filters') || 'Bộ Lọc'}
        </h3>
        <button
         onClick={() => setShowFilters(!showFilters)}
         className="lg:hidden p-2 rounded-lg bg-gray-100"
        >
         <FunnelIcon className="h-5 w-5 text-gray-600" />
        </button>
       </div>

       <div className={`space-y-4 ${showFilters ? 'block' : 'hidden lg:block'}`}>
        {/* Category Filter */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('category') || 'Danh Mục'}
         </label>
         <select
          value={filters.category}
          onChange={(e) => handleFilterChange('category', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900"
         >
          <option value="">{t('allCategories') || 'Tất cả danh mục'}</option>
          <option value="Web Development">Web Development</option>
          <option value="Mobile Development">Mobile Development</option>
          <option value="Design">Design</option>
          <option value="Content Writing">Content Writing</option>
          <option value="AI/ML">AI/ML</option>
         </select>
        </div>

        {/* Location Filter */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('location') || 'Địa Điểm'}
         </label>
         <input
          type="text"
          placeholder={t('enterLocation') || 'Nhập địa điểm...'}
          value={filters.location}
          onChange={(e) => handleFilterChange('location', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900"
         />
        </div>

        {/* Budget Range */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('budgetRange') || 'Khoảng Ngân Sách (VNĐ)'}
         </label>
         <div className="grid grid-cols-2 gap-2">
          <input
           type="number"
           placeholder="Từ"
           value={filters.budgetMin}
           onChange={(e) => handleFilterChange('budgetMin', e.target.value)}
           className="px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900"
          />
          <input
           type="number"
           placeholder="Đến"
           value={filters.budgetMax}
           onChange={(e) => handleFilterChange('budgetMax', e.target.value)}
           className="px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900"
          />
         </div>
        </div>

        {/* Experience Level */}
        <div>
         <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('experienceLevel') || 'Mức Độ Kinh Nghiệm'}
         </label>
         <select
          value={filters.experience}
          onChange={(e) => handleFilterChange('experience', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900"
         >
          <option value="">{t('allLevels') || 'Tất cả mức độ'}</option>
          <option value="0-1 years">0-1 năm</option>
          <option value="1-3 years">1-3 năm</option>
          <option value="3-5 years">3-5 năm</option>
          <option value="5+ years">5+ năm</option>
         </select>
        </div>

        {/* Clear Filters */}
        <button
         onClick={clearFilters}
         className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
        >
         {t('clearFilters') || 'Xóa Bộ Lọc'}
        </button>
       </div>
      </div>
     </div>

     {/* Jobs List */}
     <div className="lg:w-3/4">
      {/* Sort and Results Info */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
       <div className="text-gray-600 mb-4 sm:mb-0">
        {t('showingResults') || 'Hiển thị'} {sortedJobs.length} {t('jobs') || 'công việc'}
       </div>
       
       <div className="flex items-center space-x-4">
        <Link
         to="/jobs/create"
         className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
        >
         + Đăng việc
        </Link>
        
        <span className="text-sm text-gray-600">
         {t('sortBy') || 'Sắp xếp theo'}:
        </span>
        <select
         value={sortBy}
         onChange={(e) => setSortBy(e.target.value)}
         className="px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 text-sm"
        >
         <option value="newest">{t('newest') || 'Mới nhất'}</option>
         <option value="oldest">{t('oldest') || 'Cũ nhất'}</option>
         <option value="budget-high">{t('budgetHigh') || 'Ngân sách cao'}</option>
         <option value="budget-low">{t('budgetLow') || 'Ngân sách thấp'}</option>
        </select>
       </div>
      </div>

      {/* Jobs Grid */}
      <div className="space-y-4">
       {sortedJobs.map((job) => (
        <div
         key={job.id}
         className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
        >
         <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
          {/* Job Info */}
          <div className="flex-1">
           <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
             <div className="flex items-center space-x-2 mb-2">
              {job.isUrgent && (
               <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">
                {t('urgent') || 'Khẩn cấp'}
               </span>
              )}
              {job.isFeatured && (
               <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                {t('featured') || 'Nổi bật'}
               </span>
              )}
             </div>
             
             <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {job.title}
             </h3>
             
             <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
              <div className="flex items-center space-x-1">
               <BriefcaseIcon className="h-4 w-4" />
               <span>{job.company}</span>
              </div>
              <div className="flex items-center space-x-1">
               <MapPinIcon className="h-4 w-4" />
               <span>{job.location}</span>
              </div>
              <div className="flex items-center space-x-1">
               <ClockIcon className="h-4 w-4" />
               <span>{formatDate(job.postedDate)}</span>
              </div>
             </div>
            </div>
            
            <div className="text-right">
             <div className="text-lg font-semibold text-green-600">
              {formatBudget(job.budget)}
             </div>
             <div className="text-sm text-gray-500">
              {job.category}
             </div>
            </div>
           </div>
           
           <p className="text-gray-600 mb-4 line-clamp-2">
            {job.description}
           </p>
           
           <div className="flex flex-wrap gap-2 mb-4">
            {job.skills.map((skill, index) => (
             <span
              key={index}
              className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
             >
              {skill}
             </span>
            ))}
           </div>
           
           <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
             <div className="flex items-center space-x-1">
              <EyeIcon className="h-4 w-4" />
              <span>{job.views} lượt xem</span>
             </div>
             <div className="flex items-center space-x-1">
              <ChatBubbleLeftRightIcon className="h-4 w-4" />
              <span>{job.proposals} đề xuất</span>
             </div>
            </div>
            
            <div className="flex items-center space-x-2">
             <span className="text-gray-600">
              {job.experience}
             </span>
            </div>
           </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col space-y-2 mt-4 lg:mt-0 lg:ml-4">
           <Link
            to={`/jobs/${job.id}`}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors text-center"
           >
            {t('viewDetails') || 'Xem Chi Tiết'}
           </Link>
           
           <div className="flex space-x-2">
            <button className="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
             <BookmarkIcon className="h-4 w-4" />
            </button>
            <button className="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
             <ShareIcon className="h-4 w-4" />
            </button>
           </div>
          </div>
         </div>
        </div>
       ))}
      </div>

      {/* Empty State */}
      {sortedJobs.length === 0 && (
       <div className="text-center py-12">
        <BriefcaseIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
         {t('noJobsFound') || 'Không tìm thấy công việc'}
        </h3>
        <p className="text-gray-600 mb-6">
         {t('tryAdjustingFilters') || 'Thử điều chỉnh bộ lọc hoặc tìm kiếm với từ khóa khác'}
        </p>
        <button
         onClick={clearFilters}
         className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
        >
         {t('clearAllFilters') || 'Xóa Tất Cả Bộ Lọc'}
        </button>
       </div>
      )}
     </div>
    </div>
   </div>
  </div>
 );
};

export default AppleJobsPage; 
