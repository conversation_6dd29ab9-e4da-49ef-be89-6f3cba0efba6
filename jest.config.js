/**
 * Jest configuration for NERAFUS platform
 * Supports both client and server-side testing
 */

module.exports = {
  // Test environment setup
  projects: [
    // Client-side tests
    {
      displayName: 'client',
      testMatch: ['<rootDir>/client/src/**/__tests__/**/*.{js,jsx,ts,tsx}', '<rootDir>/client/src/**/*.{test,spec}.{js,jsx,ts,tsx}'],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/client/src/setupTests.js'],
      moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/client/src/$1',
        '^@components/(.*)$': '<rootDir>/client/src/components/$1',
        '^@pages/(.*)$': '<rootDir>/client/src/pages/$1',
        '^@utils/(.*)$': '<rootDir>/client/src/utils/$1',
        '^@hooks/(.*)$': '<rootDir>/client/src/hooks/$1',
        '^@contexts/(.*)$': '<rootDir>/client/src/contexts/$1',
        '^@services/(.*)$': '<rootDir>/client/src/services/$1',
        '^@styles/(.*)$': '<rootDir>/client/src/styles/$1',
        '^@assets/(.*)$': '<rootDir>/client/src/assets/$1',
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
        '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/test/mocks/fileMock.js'
      },
      transform: {
        '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', {
          presets: [
            ['@babel/preset-env', { targets: { node: 'current' } }],
            ['@babel/preset-react', { runtime: 'automatic' }]
          ]
        }]
      },
      collectCoverageFrom: [
        'client/src/**/*.{js,jsx,ts,tsx}',
        '!client/src/**/*.d.ts',
        '!client/src/index.js',
        '!client/src/setupTests.js',
        '!client/src/reportWebVitals.js'
      ],
      coverageThreshold: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    },
    
    // Server-side tests
    {
      displayName: 'server',
      testMatch: ['<rootDir>/services/**/__tests__/**/*.{js,ts}', '<rootDir>/services/**/*.{test,spec}.{js,ts}'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/test/setup/server.js'],
      moduleNameMapping: {
        // Shared modules removed - each service has independent utilities
      },
      transform: {
        '^.+\\.(js|ts)$': ['babel-jest', {
          presets: [
            ['@babel/preset-env', { targets: { node: 'current' } }]
          ]
        }]
      },
      collectCoverageFrom: [
        'services/**/*.{js,ts}',
        '!services/**/*.d.ts',
        '!services/**/node_modules/**',
        '!services/shared/templates/**'
      ],
      coverageThreshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Integration tests
    {
      displayName: 'integration',
      testMatch: ['<rootDir>/test/integration/**/*.{test,spec}.{js,ts}'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/test/setup/integration.js'],
      testTimeout: 30000,
      transform: {
        '^.+\\.(js|ts)$': ['babel-jest', {
          presets: [
            ['@babel/preset-env', { targets: { node: 'current' } }]
          ]
        }]
      }
    }
  ],
  
  // Global configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  
  // Test execution
  maxWorkers: '50%',
  testTimeout: 10000,
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/client/node_modules/',
    '<rootDir>/services/.*/node_modules/',
    '<rootDir>/client/build/',
    '<rootDir>/coverage/'
  ],
  
  // Watch mode
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
    '<rootDir>/client/build/'
  ],
  
  // Globals
  globals: {
    'process.env.NODE_ENV': 'test'
  }
};
