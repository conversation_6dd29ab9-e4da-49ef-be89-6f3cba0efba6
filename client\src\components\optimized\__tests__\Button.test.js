/** * Button component tests */ import React from 'react'; import { render, screen, fireEvent } from '@testing-library/react'; import userEvent from '@testing-library/user-event'; import { Button } from '../index'; describe('Button Component', () => { const user = userEvent.setup(); describe('Rendering', () => { it('renders with default props', () => { render(<Button>Click me</Button>); const button = screen.getByRole('button', { name: /click me/i } ); expect(button).toBeInTheDocument(); expect(button).toHaveClass('bg-blue-600'); // primary variant } ); it('renders with custom className', () => { render(<Button className="custom-class">Test</Button>); const button = screen.getByRole('button'); expect(button).toHaveClass('custom-class'); } ); it('renders different variants correctly', () => { const { rerender } = render(<Button variant="secondary">Secondary</Button>); expect(screen.getByRole('button')).toHaveClass('bg-gray-200'); rerender(<Button variant="danger">Danger</Button>); expect(screen.getByRole('button')).toHaveClass('bg-red-600'); rerender(<Button variant="ghost">Ghost</Button>); expect(screen.getByRole('button')).toHaveClass('bg-transparent'); } ); it('renders different sizes correctly', () => { const { rerender } = render(<Button size="sm">Small</Button>); expect(screen.getByRole('button')).toHaveClass('px-3 py-1.5 text-sm'); rerender(<Button size="lg">Large</Button>); expect(screen.getByRole('button')).toHaveClass('px-6 py-3 text-lg'); } ); } ); describe('States', () => { it('handles disabled state', () => { render(<Button disabled>Disabled</Button>); const button = screen.getByRole('button'); expect(button).toBeDisabled(); expect(button).toHaveClass('opacity-50 cursor-not-allowed'); } ); it('handles loading state', () => { render(<Button loading>Loading</Button>); const button = screen.getByRole('button'); expect(button).toBeDisabled(); expect(screen.getByRole('img', { hidden: true } )).toBeInTheDocument(); // Loading spinner } ); it('does not call onClick when disabled', async () => { const handleClick = jest.fn(); render(<Button disabled onClick= { handleClick } >Disabled</Button>); await user.click(screen.getByRole('button')); expect(handleClick).not.toHaveBeenCalled(); } ); it('does not call onClick when loading', async () => { const handleClick = jest.fn(); render(<Button loading onClick= { handleClick } >Loading</Button>); await user.click(screen.getByRole('button')); expect(handleClick).not.toHaveBeenCalled(); } ); } ); describe('Interactions', () => { it('calls onClick when clicked', async () => { const handleClick = jest.fn(); render(<Button onClick= { handleClick } >Click me</Button>); await user.click(screen.getByRole('button')); expect(handleClick).toHaveBeenCalledTimes(1); } ); it('handles keyboard interactions', async () => { const handleClick = jest.fn(); render(<Button onClick= { handleClick } >Press me</Button>); const button = screen.getByRole('button'); button.focus(); await user.keyboard(' { Enter } '); expect(handleClick).toHaveBeenCalledTimes(1); await user.keyboard(' '); expect(handleClick).toHaveBeenCalledTimes(2); } ); it('prevents multiple rapid clicks', async () => { const handleClick = jest.fn(); render(<Button onClick= { handleClick } >Rapid click</Button>); const button = screen.getByRole('button'); // Simulate rapid clicks await user.click(button); await user.click(button); await user.click(button); expect(handleClick).toHaveBeenCalledTimes(3); } ); } ); describe('Accessibility', () => { it('has proper ARIA attributes', () => { render(<Button aria-label="Custom label">Button</Button>); const button = screen.getByRole('button'); expect(button).toHaveAttribute('aria-label', 'Custom label'); } ); it('is focusable', () => { render(<Button>Focusable</Button>); const button = screen.getByRole('button'); button.focus(); expect(button).toHaveFocus(); } ); it('has proper focus styles', () => { render(<Button>Focus me</Button>); const button = screen.getByRole('button'); expect(button).toHaveClass('focus:outline-none focus:ring-2'); } ); } ); describe('Performance', () => { it('memoizes properly', () => { const handleClick = jest.fn(); const { rerender } = render( <Button onClick= { handleClick } >Test</Button> ); // Re-render with same props should not cause re-render rerender(<Button onClick= { handleClick } >Test</Button>); // Component should still work fireEvent.click(screen.getByRole('button')); expect(handleClick).toHaveBeenCalledTimes(1); } ); it('handles ref forwarding', () => { const ref = React.createRef(); render(<Button ref= { ref } >Ref test</Button>); expect(ref.current).toBeInstanceOf(HTMLButtonElement); } ); } ); describe('Edge Cases', () => { it('handles undefined onClick gracefully', () => { expect(() => { render(<Button>No onClick</Button>); } ).not.toThrow(); } ); it('handles empty children', () => { render(<Button></Button>); expect(screen.getByRole('button')).toBeInTheDocument(); } ); it('handles complex children', () => { render( <Button> <span>Complex</span> <strong>Children</strong> </Button> ); expect(screen.getByText('Complex')).toBeInTheDocument(); expect(screen.getByText('Children')).toBeInTheDocument(); } ); } ); } ); 
