#!/bin/bash

# Azure PostgreSQL Database Setup Script for Community Service
# Usage: ./azure-setup-database.sh [app-name] [resource-group]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
APP_NAME=${1:-"vwork-community-service"}
RESOURCE_GROUP=${2:-"vwork-rg"}
LOCATION="eastus"
DB_SERVER_NAME="${APP_NAME}-db-server"
DB_NAME="vwork_community_service"
DB_USER="vwork_admin"

echo -e "${BLUE}🗄️  Azure PostgreSQL Database Setup Script${NC}"
echo -e "${BLUE}============================================${NC}"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if logged in to Azure
if ! az account show &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Azure. Please login first.${NC}"
    az login
fi

echo -e "${GREEN}✅ Azure CLI is ready${NC}"

# Generate a secure password
DB_PASSWORD=$(openssl rand -base64 32)
echo -e "${BLUE}🔐 Generated secure database password${NC}"

# Create PostgreSQL Flexible Server
echo -e "${BLUE}🗄️  Creating PostgreSQL Flexible Server: $DB_SERVER_NAME${NC}"
az postgres flexible-server create \
    --name $DB_SERVER_NAME \
    --resource-group $RESOURCE_GROUP \
    --location $LOCATION \
    --admin-user $DB_USER \
    --admin-password "$DB_PASSWORD" \
    --sku-name "Standard_B1ms" \
    --tier "Burstable" \
    --storage-size 32 \
    --version 14 \
    --output none

echo -e "${GREEN}✅ PostgreSQL server created successfully${NC}"

# Create database
echo -e "${BLUE}📊 Creating database: $DB_NAME${NC}"
az postgres flexible-server db create \
    --resource-group $RESOURCE_GROUP \
    --server-name $DB_SERVER_NAME \
    --database-name $DB_NAME \
    --output none

echo -e "${GREEN}✅ Database created successfully${NC}"

# Configure firewall rules to allow Azure services
echo -e "${BLUE}🔥 Configuring firewall rules${NC}"
az postgres flexible-server firewall-rule create \
    --resource-group $RESOURCE_GROUP \
    --name $DB_SERVER_NAME \
    --rule-name "AllowAzureServices" \
    --start-ip-address "0.0.0.0" \
    --end-ip-address "***************" \
    --output none

echo -e "${GREEN}✅ Firewall rules configured${NC}"

# Get connection information
DB_HOST=$(az postgres flexible-server show \
    --resource-group $RESOURCE_GROUP \
    --name $DB_SERVER_NAME \
    --query "fullyQualifiedDomainName" \
    --output tsv)

echo -e "${GREEN}✅ Database setup completed!${NC}"
echo -e "${BLUE}📋 Database Information:${NC}"
echo -e "${BLUE}   Server: $DB_HOST${NC}"
echo -e "${BLUE}   Database: $DB_NAME${NC}"
echo -e "${BLUE}   Username: $DB_USER${NC}"
echo -e "${BLUE}   Password: $DB_PASSWORD${NC}"

# Create connection string
DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:5432/${DB_NAME}?sslmode=require"

echo -e "${BLUE}🔗 Connection String:${NC}"
echo -e "${YELLOW}$DATABASE_URL${NC}"

# Update App Service with database configuration
echo -e "${BLUE}⚙️  Updating App Service configuration${NC}"
az webapp config appsettings set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --settings \
    DATABASE_URL="$DATABASE_URL" \
    DB_HOST="$DB_HOST" \
    DB_NAME="$DB_NAME" \
    DB_USER="$DB_USER" \
    DB_PASSWORD="$DB_PASSWORD" \
    --output none

echo -e "${GREEN}✅ App Service configuration updated${NC}"

# Run database migrations
echo -e "${BLUE}🔄 Running database migrations${NC}"
echo -e "${YELLOW}⚠️  You need to run migrations manually after deployment:${NC}"
echo -e "${YELLOW}   az webapp ssh --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${YELLOW}   npm run db:setup${NC}"

# Save credentials to file (for reference)
CREDS_FILE="azure-db-credentials.txt"
cat > $CREDS_FILE << EOF
# Azure PostgreSQL Database Credentials
# Generated on: $(date)

Server: $DB_HOST
Database: $DB_NAME
Username: $DB_USER
Password: $DB_PASSWORD

Connection String:
$DATABASE_URL

# Important: Keep this file secure and delete after deployment
EOF

echo -e "${GREEN}✅ Credentials saved to $CREDS_FILE${NC}"
echo -e "${YELLOW}⚠️  Remember to delete $CREDS_FILE after deployment${NC}"

# Show next steps
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${BLUE}1. Deploy your application using azure-deploy.sh${NC}"
echo -e "${BLUE}2. Connect to the app and run database migrations${NC}"
echo -e "${BLUE}3. Test the database connection${NC}"
echo -e "${BLUE}4. Delete the credentials file: rm $CREDS_FILE${NC}"

# Show database status
echo -e "${BLUE}📊 Database Status:${NC}"
az postgres flexible-server show \
    --resource-group $RESOURCE_GROUP \
    --name $DB_SERVER_NAME \
    --query "{name:name, state:state, fullyQualifiedDomainName:fullyQualifiedDomainName, version:version}" \
    --output table 