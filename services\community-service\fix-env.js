const fs = require('fs');

console.log('=== Fixing environment variables ===');

// Read the .env file and manually parse Firebase variables
const envPath = 'C:\\Project\\Vwork\\.env';
const content = fs.readFileSync(envPath, 'utf8');

console.log('File content length:', content.length);

// Manually extract Firebase variables
const lines = content.split('\n');
let firebaseVars = {};

lines.forEach((line, index) => {
  const trimmedLine = line.trim();
  if (trimmedLine.includes('FIREBASE_PROJECT_ID=')) {
    firebaseVars.FIREBASE_PROJECT_ID = trimmedLine.split('=')[1];
    console.log(`Line ${index + 1}: Found FIREBASE_PROJECT_ID = "${firebaseVars.FIREBASE_PROJECT_ID}"`);
  }
  if (trimmedLine.includes('FIREBASE_CLIENT_EMAIL=')) {
    firebaseVars.FIREBASE_CLIENT_EMAIL = trimmedLine.split('=')[1];
    console.log(`Line ${index + 1}: Found FIREBASE_CLIENT_EMAIL = "${firebaseVars.FIREBASE_CLIENT_EMAIL}"`);
  }
  if (trimmedLine.includes('FIREBASE_PRIVATE_KEY=')) {
    // This might be multiline, handle specially
    const parts = trimmedLine.split('=');
    let privateKey = parts.slice(1).join('=');
    
    // If it starts with quote, it's multiline
    if (privateKey.startsWith('"')) {
      privateKey = privateKey.substring(1); // Remove opening quote
      
      // Look for the closing quote in subsequent lines
      let currentIndex = index + 1;
      while (currentIndex < lines.length) {
        if (lines[currentIndex].includes('"') && lines[currentIndex].endsWith('"')) {
          privateKey += '\n' + lines[currentIndex].slice(0, -1); // Add line without closing quote
          break;
        } else {
          privateKey += '\n' + lines[currentIndex];
        }
        currentIndex++;
      }
    }
    
    firebaseVars.FIREBASE_PRIVATE_KEY = privateKey;
    console.log(`Line ${index + 1}: Found FIREBASE_PRIVATE_KEY (length: ${privateKey.length})`);
  }
});

console.log('\nManually extracted variables:');
Object.keys(firebaseVars).forEach(key => {
  console.log(`${key}: ${firebaseVars[key] ? '✅ Found' : '❌ Missing'}`);
  if (key === 'FIREBASE_PRIVATE_KEY') {
    console.log(`  Length: ${firebaseVars[key]?.length || 0}`);
    console.log(`  Starts: ${firebaseVars[key]?.substring(0, 30) || 'N/A'}`);
  }
});

// Set them manually in process.env
Object.keys(firebaseVars).forEach(key => {
  if (firebaseVars[key]) {
    process.env[key] = firebaseVars[key];
    console.log(`✅ Set process.env.${key}`);
  }
});

console.log('\nAfter manual setting:');
console.log('process.env.FIREBASE_PROJECT_ID:', process.env.FIREBASE_PROJECT_ID);
console.log('process.env.FIREBASE_CLIENT_EMAIL:', process.env.FIREBASE_CLIENT_EMAIL);
console.log('process.env.FIREBASE_PRIVATE_KEY exists:', !!process.env.FIREBASE_PRIVATE_KEY);

// Test Firebase initialization
console.log('\n=== Testing Firebase Admin SDK ===');
const admin = require('firebase-admin');

try {
  if (!admin.apps.length) {
    const serviceAccount = {
      type: 'service_account',
      project_id: process.env.FIREBASE_PROJECT_ID,
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
    };

    console.log('Service account config:');
    console.log('  project_id:', serviceAccount.project_id);
    console.log('  client_email:', serviceAccount.client_email);
    console.log('  private_key length:', serviceAccount.private_key?.length);

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });

    console.log('✅ Firebase Admin SDK initialized successfully!');
  }
} catch (error) {
  console.log('❌ Firebase initialization failed:', error.message);
} 