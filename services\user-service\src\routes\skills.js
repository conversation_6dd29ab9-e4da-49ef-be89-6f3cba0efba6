/**
 * Skills Routes for User Service
 * Handles user skills management
 */

const express = require('express');
const { verifyFirebaseToken } = require('../middleware/auth');
const database = require('../config/database');

const router = express.Router();

/**
 * @route GET /api/v1/skills/:userId
 * @desc Get user skills
 * @access Public
 */
router.get('/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    const query = `
      SELECT skill_name, skill_level, years_experience, is_verified, created_at
      FROM user_skills
      WHERE user_id = $1
      ORDER BY skill_level DESC, years_experience DESC
    `;

    const result = await database.query(query, [userId]);

    res.success(result.rows, 'Skills retrieved successfully');

  } catch (error) {
    console.error('❌ Get skills error:', error);
    res.error('Failed to retrieve skills', 500, error.message);
  }
});

/**
 * @route POST /api/v1/skills/:userId
 * @desc Add user skill
 * @access Protected (own profile only)
 */
router.post('/:userId', verifyFirebaseToken, async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (req.user.uid !== userId) {
      return res.error('You can only update your own skills', 403);
    }

    const { skillName, skillLevel = 'intermediate', yearsExperience = 0 } = req.body;

    if (!skillName) {
      return res.error('Skill name is required', 400);
    }

    const insertQuery = `
      INSERT INTO user_skills (user_id, skill_name, skill_level, years_experience)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (user_id, skill_name) DO UPDATE SET
        skill_level = EXCLUDED.skill_level,
        years_experience = EXCLUDED.years_experience
      RETURNING *
    `;

    const result = await database.query(insertQuery, [userId, skillName, skillLevel, yearsExperience]);

    res.success(result.rows[0], 'Skill added successfully', 201);

  } catch (error) {
    console.error('❌ Add skill error:', error);
    res.error('Failed to add skill', 500, error.message);
  }
});

module.exports = router; 