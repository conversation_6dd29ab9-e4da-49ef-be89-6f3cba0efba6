/**
 * Authentication Middleware for Community Service
 * Handles Firebase token verification
 */

const admin = require('firebase-admin');
const logger = require('../utils/logger');

/**
 * Verify Firebase ID token
 */
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    // Debug logs
    console.log('🔍 Auth Debug:', {
      hasAuthHeader: !!authHeader,
      authHeader: authHeader ? authHeader.substring(0, 20) + '...' : 'none'
    });
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'No token provided or invalid format'
      });
    }

    const token = authHeader.split('Bearer ')[1];
    
    if (!token) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'No token provided'
      });
    }

    // Verify the Firebase token
    const decodedToken = await admin.auth().verifyIdToken(token);
    
    // Add user info to request with consistent structure
    req.user = {
      id: decodedToken.uid, // For backward compatibility
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      name: decodedToken.name || decodedToken.display_name || decodedToken.displayName || 'User',
      picture: decodedToken.picture,
      firebase_user: true
    };

    logger.debug('✅ Firebase token verified successfully', { 
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: req.user.name
    });
    
    next();

  } catch (error) {
    logger.error('❌ Firebase token verification failed', { error: error.message });
    
    if (error.code === 'auth/id-token-expired') {
      return res.status(401).json({
        error: 'Token expired',
        message: 'Authentication token has expired'
      });
    }
    
    if (error.code === 'auth/id-token-revoked') {
      return res.status(401).json({
        error: 'Token revoked',
        message: 'Authentication token has been revoked'
      });
    }

    return res.status(401).json({
      error: 'Invalid token',
      message: 'Authentication token is invalid'
    });
  }
};

/**
 * Optional authentication middleware
 * Allows requests to proceed even without valid token, but verifies token if present
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      req.user = null;
      return next();
    }

    const token = authHeader.split('Bearer ')[1];
    
    if (!token) {
      req.user = null;
      return next();
    }

    // Verify the Firebase token
    const decodedToken = await admin.auth().verifyIdToken(token);
    
    // Add user info to request with consistent structure
    req.user = {
      id: decodedToken.uid, // For backward compatibility
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      name: decodedToken.name || decodedToken.display_name || decodedToken.displayName || 'User',
      picture: decodedToken.picture,
      firebase_user: true
    };

    logger.debug('✅ Optional auth: Firebase token verified successfully', { 
      uid: decodedToken.uid,
      name: req.user.name
    });
    
    next();

  } catch (error) {
    logger.debug('⚠️ Optional auth: Token verification failed, proceeding without auth', { 
      error: error.message 
    });
    req.user = null;
    next();
  }
};

/**
 * Check if user is authenticated
 */
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication required',
      message: 'You must be logged in to perform this action'
    });
  }
  next();
};

/**
 * Check if user is the owner of a resource
 */
const requireOwnership = (field = 'author_id') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'You must be logged in to perform this action'
      });
    }

    // For now, we'll check ownership in the route handlers
    // This middleware just ensures user is authenticated
    next();
  };
};

module.exports = {
  verifyFirebaseToken,
  optionalAuth,
  requireAuth,
  requireOwnership
}; 