/**
 * Deploy Community Service with Production Configuration
 * Fix database connection and environment issues
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Deploying Community Service with Production Configuration...\n');

// Check current environment
console.log('🔍 Checking current environment...');
try {
  const envCheck = execSync('node scripts/check-environment.js', { encoding: 'utf8' });
  console.log(envCheck);
} catch (error) {
  console.log('⚠️ Environment check failed, continuing with deployment...');
}

console.log('');

// Check git status
console.log('📋 Checking git status...');
try {
  const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' });
  if (gitStatus.trim()) {
    console.log('⚠️ Uncommitted changes detected:');
    console.log(gitStatus);
  } else {
    console.log('✅ Working directory is clean');
  }
} catch (error) {
  console.log('❌ Git status check failed:', error.message);
  process.exit(1);
}

console.log('');

// Check current branch
console.log('🌿 Checking current branch...');
try {
  const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  console.log(`   Current branch: ${currentBranch}`);
  
  if (currentBranch !== 'main') {
    console.log('⚠️ Not on main branch, switching...');
    execSync('git checkout main', { stdio: 'inherit' });
  }
} catch (error) {
  console.log('❌ Branch check failed:', error.message);
  process.exit(1);
}

console.log('');

// Verify Community Service files exist
console.log('📁 Verifying Community Service files...');
const requiredFiles = [
  'services/community-service/app.js',
  'services/community-service/package.json',
  'services/community-service/render.yaml',
  'services/community-service/src/routes/categories.js',
  'services/community-service/src/routes/stats.js',
  'services/community-service/src/config/postgresql.js'
];

for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`);
  } else {
    console.log(`   ❌ ${file} - MISSING`);
    process.exit(1);
  }
}

console.log('');

// Add all changes
console.log('📦 Adding all changes to git...');
try {
  execSync('git add .', { stdio: 'inherit' });
  console.log('✅ All files added to git');
} catch (error) {
  console.log('❌ Git add failed:', error.message);
  process.exit(1);
}

console.log('');

// Commit changes
console.log('💾 Committing production configuration...');
try {
  const commitMessage = `fix: Production configuration for Community Service

- Fixed database connection handling
- Updated environment configuration
- Improved error handling for production
- Added proper fail-fast behavior
- Fixed CORS configuration for nerafus.com
- Added missing endpoints (categories, stats)
- Updated API documentation

Database: PostgreSQL with proper SSL configuration
Environment: Production mode with strict error handling
CORS: Configured for nerafus.com and related domains`;

  execSync(`git commit -m "${commitMessage}"`, { stdio: 'inherit' });
  console.log('✅ Changes committed');
} catch (error) {
  console.log('❌ Git commit failed:', error.message);
  process.exit(1);
}

console.log('');

// Push to remote
console.log('🚀 Pushing to remote repository...');
try {
  execSync('git push origin main', { stdio: 'inherit' });
  console.log('✅ Changes pushed to remote repository');
} catch (error) {
  console.log('❌ Git push failed:', error.message);
  process.exit(1);
}

console.log('');

// Summary
console.log('🎉 Production Deployment Summary:');
console.log('==================================');
console.log('');
console.log('✅ Files Updated:');
console.log('   • services/community-service/app.js - Fixed database initialization');
console.log('   • services/community-service/src/routes/categories.js - Added proper database handling');
console.log('   • services/community-service/src/routes/stats.js - Added proper database handling');
console.log('   • services/community-service/render.yaml - Production environment configuration');
console.log('');
console.log('🔧 Configuration Changes:');
console.log('   • Fail-fast database connection (no mock data)');
console.log('   • Production mode environment handling');
console.log('   • Proper SSL configuration for database');
console.log('   • CORS configuration for nerafus.com');
console.log('   • Health check endpoints');
console.log('');
console.log('📋 Required Environment Variables (Render Dashboard):');
console.log('   • DATABASE_URL - PostgreSQL connection string');
console.log('   • NODE_ENV=production');
console.log('   • ALLOWED_ORIGINS - CORS configuration');
console.log('   • FIREBASE_PROJECT_ID - Firebase configuration');
console.log('   • FIREBASE_CLIENT_EMAIL - Firebase configuration');
console.log('   • FIREBASE_PRIVATE_KEY - Firebase configuration');
console.log('');
console.log('⏳ Deployment Process:');
console.log('   1. Render will detect the push and start deployment');
console.log('   2. Service will attempt to connect to database');
console.log('   3. If DATABASE_URL is not set, service will fail to start');
console.log('   4. Check Render dashboard for deployment status');
console.log('');
console.log('🔍 Next Steps:');
console.log('   1. Set DATABASE_URL in Render dashboard');
console.log('   2. Wait 3-5 minutes for deployment to complete');
console.log('   3. Check service logs in Render dashboard');
console.log('   4. Test endpoints after deployment');
console.log('   5. Verify frontend functionality');
console.log('');
console.log('📊 Testing URLs:');
console.log('   • Community Service: https://nerafus-community-service.onrender.com/health');
console.log('   • API Gateway: https://vwork-api-gateway.onrender.com/health');
console.log('   • Frontend: https://nerafus.com/#/community');
console.log('');
console.log('🚨 Important Notes:');
console.log('   • Service will NOT start without DATABASE_URL');
console.log('   • No mock data will be used - real database only');
console.log('   • Production mode with strict error handling');
console.log('   • SSL required for database connections');
console.log('');
console.log('✅ Deployment completed successfully!'); 