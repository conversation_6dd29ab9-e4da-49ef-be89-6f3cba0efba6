/**
 * Performance optimization utilities for React client
 */

import { lazy, Suspense, memo, useMemo, useCallback, useRef, useEffect, useState } from 'react';

/**
 * Enhanced lazy loading with error boundaries
 */
export const createLazyComponent = (importFn, fallback = null) => {
 const LazyComponent = lazy(importFn);
 
 return memo((props) => (
  <Suspense fallback={fallback || <div className="animate-pulse">Loading...</div>}>
   <LazyComponent {...props} />
  </Suspense>
 ));
};

/**
 * Preload component for better UX
 */
export const preloadComponent = (importFn) => {
 const componentImport = importFn();
 return componentImport;
};

/**
 * Bundle splitting utility
 */
export const createAsyncRoute = (importFn) => {
 return {
  Component: createLazyComponent(importFn),
  preload: () => preloadComponent(importFn)
 };
};

/**
 * Performance monitoring hook
 */
export const usePerformanceMonitor = (componentName) => {
 const renderCount = useRef(0);
 const mountTime = useRef(Date.now());
 
 useEffect(() => {
  renderCount.current += 1;
  
  if (process.env.NODE_ENV === 'development') {
   console.log(`[Performance] ${componentName} rendered ${renderCount.current} times`);
  }
 });
 
 useEffect(() => {
  const loadTime = Date.now() - mountTime.current;
  
  if (process.env.NODE_ENV === 'development') {
   console.log(`[Performance] ${componentName} mounted in ${loadTime}ms`);
  }
  
  // Report to analytics in production
  if (process.env.NODE_ENV === 'production' && window.gtag) {
   window.gtag('event', 'component_load_time', {
    component_name: componentName,
    load_time: loadTime
   });
  }
 }, [componentName]);
 
 return { renderCount: renderCount.current };
};

/**
 * Optimized image loading component
 */
export const OptimizedImage = memo(({ 
 src, 
 alt, 
 className = '', 
 lazy = true,
 placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiNhYWEiPkxvYWRpbmc8L3RleHQ+PC9zdmc+',
 ...props 
}) => {
 const imgRef = useRef(null);
 const [isLoaded, setIsLoaded] = useState(false);
 const [isInView, setIsInView] = useState(!lazy);
 
 useEffect(() => {
  if (!lazy) return;
  
  const observer = new IntersectionObserver(
   ([entry]) => {
    if (entry.isIntersecting) {
     setIsInView(true);
     observer.disconnect();
    }
   },
   { threshold: 0.1 }
  );
  
  if (imgRef.current) {
   observer.observe(imgRef.current);
  }
  
  return () => observer.disconnect();
 }, [lazy]);
 
 return (
  <div ref={imgRef} className={`relative ${className}`}>
   {!isLoaded && (
    <img
     src={placeholder}
     alt=""
     className="absolute inset-0 w-full h-full object-cover blur-sm"
    />
   )}
   {isInView && (
    <img
     src={src}
     alt={alt}
     className={`w-full h-full object-cover transition-opacity duration-300 ${
      isLoaded ? 'opacity-100' : 'opacity-0'
     }`}
     onLoad={() => setIsLoaded(true)}
     {...props}
    />
   )}
  </div>
 );
});

/**
 * Virtual scrolling hook for large lists
 */
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
 const [scrollTop, setScrollTop] = useState(0);
 
 const visibleItems = useMemo(() => {
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
   startIndex + Math.ceil(containerHeight / itemHeight) + 1,
   items.length
  );
  
  return items.slice(startIndex, endIndex).map((item, index) => ({
   ...item,
   index: startIndex + index
  }));
 }, [items, itemHeight, containerHeight, scrollTop]);
 
 const totalHeight = items.length * itemHeight;
 const offsetY = Math.floor(scrollTop / itemHeight) * itemHeight;
 
 return {
  visibleItems,
  totalHeight,
  offsetY,
  onScroll: (e) => setScrollTop(e.target.scrollTop)
 };
};

/**
 * Debounced callback hook
 */
export const useDebouncedCallback = (callback, delay) => {
 const timeoutRef = useRef(null);
 
 return useCallback((...args) => {
  if (timeoutRef.current) {
   clearTimeout(timeoutRef.current);
  }
  
  timeoutRef.current = setTimeout(() => {
   callback(...args);
  }, delay);
 }, [callback, delay]);
};

/**
 * Throttled callback hook
 */
export const useThrottledCallback = (callback, delay) => {
 const lastRun = useRef(Date.now());
 
 return useCallback((...args) => {
  if (Date.now() - lastRun.current >= delay) {
   callback(...args);
   lastRun.current = Date.now();
  }
 }, [callback, delay]);
};

/**
 * Memory usage monitor
 */
export const useMemoryMonitor = () => {
 const [memoryInfo, setMemoryInfo] = useState(null);
 
 useEffect(() => {
  if (!performance.memory) return;
  
  const updateMemoryInfo = () => {
   setMemoryInfo({
    usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
    totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1048576), // MB
    jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) // MB
   });
  };
  
  updateMemoryInfo();
  const interval = setInterval(updateMemoryInfo, 5000);
  
  return () => clearInterval(interval);
 }, []);
 
 return memoryInfo;
};

/**
 * Bundle analyzer utility
 */
export const analyzeBundleSize = () => {
 if (process.env.NODE_ENV !== 'development') return;
 
 const scripts = Array.from(document.querySelectorAll('script[src]'));
 const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
 
 console.group('Bundle Analysis');
 console.log('Scripts:', scripts.map(s => s.src));
 console.log('Styles:', styles.map(s => s.href));
 console.groupEnd();
};

/**
 * Component size tracker
 */
export const useComponentSize = (ref) => {
 const [size, setSize] = useState({ width: 0, height: 0 });
 
 useEffect(() => {
  if (!ref.current) return;
  
  const resizeObserver = new ResizeObserver(entries => {
   const entry = entries[0];
   setSize({
    width: entry.contentRect.width,
    height: entry.contentRect.height
   });
  });
  
  resizeObserver.observe(ref.current);
  
  return () => resizeObserver.disconnect();
 }, [ref]);
 
 return size;
};

/**
 * Render optimization utilities
 */
export const RenderOptimizer = {
 // Prevent unnecessary re-renders
 areEqual: (prevProps, nextProps) => {
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
 },
 
 // Shallow comparison for props
 shallowEqual: (prevProps, nextProps) => {
  const keys1 = Object.keys(prevProps);
  const keys2 = Object.keys(nextProps);
  
  if (keys1.length !== keys2.length) return false;
  
  for (let key of keys1) {
   if (prevProps[key] !== nextProps[key]) return false;
  }
  
  return true;
 }
};
