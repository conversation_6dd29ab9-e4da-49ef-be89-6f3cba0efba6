import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';
import { apiService } from '../../../services/api';
import {
 ArrowLeftIcon,
 MapPinIcon,
 CurrencyDollarIcon,
 ClockIcon,
 BriefcaseIcon,
 CalendarIcon,
 UserIcon,
 ChatBubbleLeftRightIcon,
 BookmarkIcon,
 ShareIcon,
 EyeIcon,
 StarIcon,
 CheckCircleIcon,
 ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const AppleJobDetailPage = () => {
 const { id } = useParams();
 const { t } = useLanguage();
 const { user } = useAuth();
 const navigate = useNavigate();
 const [job, setJob] = useState(null);
 const [loading, setLoading] = useState(true);
 const [isBookmarked, setIsBookmarked] = useState(false);

 // Mock job data
 const mockJob = {
  id: id,
  title: 'Thi<PERSON><PERSON> kế Website E-commerce',
  company: 'TechCorp Vietnam',
  location: 'Hồ Chí Minh',
  budget: { min: 15000000, max: 25000000 },
  category: 'Web Development',
  experience: '3-5 years',
  postedDate: '2024-01-15',
  deadline: '2024-02-15',
  description: `Cần freelancer thiết kế website bán hàng với giao diện hiện đại, responsive và tích hợp thanh toán online.

**Yêu cầu chính:**
- Thiết kế giao diện người dùng hiện đại và thân thiện
- Responsive design cho mobile và desktop
- Tích hợp hệ thống thanh toán online
- Quản lý đơn hàng và kho hàng
- Hệ thống đánh giá và review sản phẩm
- Tối ưu SEO và tốc độ tải trang

**Kỹ năng yêu cầu:**
- React.js hoặc Vue.js
- Node.js backend
- MongoDB hoặc PostgreSQL
- Payment gateway integration
- Responsive design
- SEO optimization

**Thời gian dự kiến:** 6-8 tuần
**Ngân sách:** 15,000,000 - 25,000,000 VNĐ`,
  skills: ['React', 'Node.js', 'MongoDB', 'Payment Gateway', 'Responsive Design', 'SEO'],
  requirements: [
   'Ít nhất 3 năm kinh nghiệm trong lĩnh vực web development',
   'Có portfolio các dự án e-commerce đã hoàn thành',
   'Thành thạo React.js và Node.js',
   'Có kinh nghiệm tích hợp payment gateway',
   'Có khả năng làm việc độc lập và đúng deadline',
  ],
  benefits: [
   'Làm việc với công ty công nghệ hàng đầu',
   'Môi trường làm việc chuyên nghiệp',
   'Cơ hội thăng tiến trong tương lai',
   'Được đào tạo và phát triển kỹ năng',
   'Lương thưởng cạnh tranh',
  ],
  views: 245,
  proposals: 12,
  isUrgent: true,
  isFeatured: true,
  companyInfo: {
   name: 'TechCorp Vietnam',
   description: 'Công ty công nghệ hàng đầu tại Việt Nam, chuyên về phát triển phần mềm và giải pháp số.',
   founded: '2018',
   employees: '50-100',
   website: 'https://techcorp.vn',
   rating: 4.8,
   totalProjects: 150,
   completionRate: 98,
  },
 };

 useEffect(() => {
  const fetchJobDetail = async () => {
   try {
    setLoading(true);
    const response = await apiService.jobs.getById(id);
    if (response.success) {
     setJob(response.data.job);
    } else {
     console.error('Error fetching job detail:', response.message);
     // Fallback to mock data if API fails
     setJob(mockJob);
    }
   } catch (error) {
    console.error('Error fetching job detail:', error);
    // Fallback to mock data if API fails
    setJob(mockJob);
   } finally {
    setLoading(false);
   }
  };

  fetchJobDetail();
 }, [id]);

 const formatBudget = (budget) => {
  return `${budget.min.toLocaleString('vi-VN')} - ${budget.max.toLocaleString('vi-VN')} VNĐ`;
 };

 const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN');
 };

 const handleApply = () => {
  // Navigate to application form or show modal
  navigate(`/jobs/${id}/apply`);
 };

 const handleBookmark = () => {
  setIsBookmarked(!isBookmarked);
  // In real app, this would call API to save/unsave job
 };

 const handleShare = () => {
  // Share functionality
  if (navigator.share) {
   navigator.share({
    title: job.title,
    text: job.description.substring(0, 100) + '...',
    url: window.location.href,
   });
  } else {
   // Fallback: copy to clipboard
   navigator.clipboard.writeText(window.location.href);
  }
 };

 if (loading) {
  return (
   <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
     <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
     <p className="mt-4 text-gray-600">Đang tải thông tin công việc...</p>
    </div>
   </div>
  );
 }

 if (!job) {
  return (
   <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="text-center">
     <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
     <h3 className="text-lg font-medium text-gray-900 mb-2">
      Không tìm thấy công việc
     </h3>
     <p className="text-gray-600 mb-6">
      Công việc này có thể đã bị xóa hoặc không tồn tại.
     </p>
     <Link
      to="/jobs"
      className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
     >
      Quay lại danh sách công việc
     </Link>
    </div>
   </div>
  );
 }

 return (
  <div className="min-h-screen bg-gray-50">
   {/* Header */}
   <div className="bg-white border-b border-gray-200">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
     <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
       <button
        onClick={() => navigate('/jobs')}
        className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
       >
        <ArrowLeftIcon className="h-5 w-5 text-gray-600" />
       </button>
       <div>
        <h1 className="text-2xl font-bold text-gray-900">
         {job.title}
        </h1>
        <p className="text-gray-600">
         {job.company} • {job.location}
        </p>
       </div>
      </div>
      
      <div className="flex items-center space-x-2">
       <button
        onClick={handleBookmark}
        className={`p-2 rounded-lg transition-colors ${
         isBookmarked
          ? 'bg-yellow-100 text-yellow-600'
          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
        }`}
       >
        <BookmarkIcon className="h-5 w-5" />
       </button>
       <button
        onClick={handleShare}
        className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
       >
        <ShareIcon className="h-5 w-5" />
       </button>
      </div>
     </div>
    </div>
   </div>

   {/* Main Content */}
   <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
     {/* Main Content */}
     <div className="lg:col-span-2 space-y-6">
      {/* Job Overview */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <div className="flex items-start justify-between mb-6">
        <div className="flex-1">
         <div className="flex items-center space-x-2 mb-4">
          {job.isUrgent && (
           <span className="px-3 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-full">
            Khẩn cấp
           </span>
          )}
          {job.isFeatured && (
           <span className="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full">
            Nổi bật
           </span>
          )}
         </div>
         
         <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {job.title}
         </h2>
         
         <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="flex items-center space-x-2">
           <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
           <div>
            <p className="text-sm text-gray-500">Ngân sách</p>
            <p className="font-semibold text-gray-900">
             {formatBudget(job.budget)}
            </p>
           </div>
          </div>
          
          <div className="flex items-center space-x-2">
           <MapPinIcon className="h-5 w-5 text-blue-600" />
           <div>
            <p className="text-sm text-gray-500">Địa điểm</p>
            <p className="font-semibold text-gray-900">
             {job.location}
            </p>
           </div>
          </div>
          
          <div className="flex items-center space-x-2">
           <BriefcaseIcon className="h-5 w-5 text-purple-600" />
           <div>
            <p className="text-sm text-gray-500">Kinh nghiệm</p>
            <p className="font-semibold text-gray-900">
             {job.experience}
            </p>
           </div>
          </div>
          
          <div className="flex items-center space-x-2">
           <CalendarIcon className="h-5 w-5 text-orange-600" />
           <div>
            <p className="text-sm text-gray-500">Hạn nộp</p>
            <p className="font-semibold text-gray-900">
             {formatDate(job.deadline)}
            </p>
           </div>
          </div>
         </div>
        </div>
       </div>
       
       <div className="flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center space-x-4">
         <div className="flex items-center space-x-1">
          <EyeIcon className="h-4 w-4" />
          <span>{job.views} lượt xem</span>
         </div>
         <div className="flex items-center space-x-1">
          <ChatBubbleLeftRightIcon className="h-4 w-4" />
          <span>{job.proposals} đề xuất</span>
         </div>
        </div>
        
        <div className="text-right">
         <p>Đăng ngày {formatDate(job.postedDate)}</p>
        </div>
       </div>
      </div>

      {/* Job Description */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <h3 className="text-xl font-semibold text-gray-900 mb-4">
        Mô tả công việc
       </h3>
       <div className="prose prose-gray max-w-none">
        <pre className="whitespace-pre-wrap text-gray-600 leading-relaxed">
         {job.description}
        </pre>
       </div>
      </div>

      {/* Requirements */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <h3 className="text-xl font-semibold text-gray-900 mb-4">
        Yêu cầu
       </h3>
       <ul className="space-y-3">
        {job.requirements.map((requirement, index) => (
         <li key={index} className="flex items-start space-x-3">
          <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
          <span className="text-gray-600">{requirement}</span>
         </li>
        ))}
       </ul>
      </div>

      {/* Benefits */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <h3 className="text-xl font-semibold text-gray-900 mb-4">
        Quyền lợi
       </h3>
       <ul className="space-y-3">
        {job.benefits.map((benefit, index) => (
         <li key={index} className="flex items-start space-x-3">
          <StarIcon className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <span className="text-gray-600">{benefit}</span>
         </li>
        ))}
       </ul>
      </div>
     </div>

     {/* Sidebar */}
     <div className="space-y-6">
      {/* Apply Button */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <button
        onClick={handleApply}
        className="w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-colors mb-4"
       >
        Ứng tuyển ngay
       </button>
       
       <div className="text-center text-sm text-gray-500">
        <p>Hạn nộp: {formatDate(job.deadline)}</p>
        <p className="mt-1">{job.proposals} người đã ứng tuyển</p>
       </div>
      </div>

      {/* Skills */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Kỹ năng yêu cầu
       </h3>
       <div className="flex flex-wrap gap-2">
        {job.skills.map((skill, index) => (
         <span
          key={index}
          className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
         >
          {skill}
         </span>
        ))}
       </div>
      </div>

      {/* Company Info */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
       <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Thông tin công ty
       </h3>
       
       <div className="space-y-4">
        <div>
         <h4 className="font-medium text-gray-900 mb-2">
          {job.companyInfo.name}
         </h4>
         <p className="text-sm text-gray-600 mb-3">
          {job.companyInfo.description}
         </p>
        </div>
        
        <div className="space-y-2 text-sm">
         <div className="flex justify-between">
          <span className="text-gray-500">Thành lập:</span>
          <span className="text-gray-900">{job.companyInfo.founded}</span>
         </div>
         <div className="flex justify-between">
          <span className="text-gray-500">Nhân viên:</span>
          <span className="text-gray-900">{job.companyInfo.employees}</span>
         </div>
         <div className="flex justify-between">
          <span className="text-gray-500">Dự án:</span>
          <span className="text-gray-900">{job.companyInfo.totalProjects}</span>
         </div>
         <div className="flex justify-between">
          <span className="text-gray-500">Tỷ lệ hoàn thành:</span>
          <span className="text-gray-900">{job.companyInfo.completionRate}%</span>
         </div>
        </div>
        
        <div className="flex items-center space-x-2">
         <div className="flex items-center">
          {[...Array(5)].map((_, i) => (
           <StarIcon
            key={i}
            className={`h-4 w-4 ${
             i < Math.floor(job.companyInfo.rating)
              ? 'text-yellow-400 fill-current'
              : 'text-gray-300'
            }`}
           />
          ))}
         </div>
         <span className="text-sm text-gray-600">
          {job.companyInfo.rating}/5
         </span>
        </div>
        
        <a
         href={job.companyInfo.website}
         target="_blank"
         rel="noopener noreferrer"
         className="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-center text-sm"
        >
         Xem website công ty
        </a>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 );
};

export default AppleJobDetailPage; 
