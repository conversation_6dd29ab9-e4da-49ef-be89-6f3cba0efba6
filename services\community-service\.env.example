# Community Service Environment Configuration
# Copy this file to .env and update the values

# ==================== SERVICE CONFIGURATION ====================
NODE_ENV=development
PORT=3003
SERVICE_NAME=community-service
SERVICE_VERSION=1.0.0

# ==================== EVENT-DRIVEN ARCHITECTURE ====================
# Enable/disable event-driven features
ENABLE_EVENT_DRIVEN=true

# Event Bus Service URL
EVENT_BUS_SERVICE_URL=http://localhost:3007

# Event subscription patterns (comma-separated)
EVENT_PATTERNS=auth.user.*,link.analysis.*,community.*,admin.content.*

# ==================== LOGGING CONFIGURATION ====================
LOG_LEVEL=info
LOG_FORMAT=json

# ==================== DATABASE CONFIGURATION ====================
# PostgreSQL Database (only supported database)
DB_TYPE=postgresql

# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_community_service
DB_USER=vwork_admin
DB_PASSWORD=VWork2024!

# Production: Use DATABASE_URL (for Render, Heroku, etc.)
# DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Connection pool settings (optimized for production)
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000
DB_QUERY_TIMEOUT=30000

# Firebase configuration (for authentication only)
GOOGLE_APPLICATION_CREDENTIALS=./config/serviceAccountKey.json
FIRESTORE_PROJECT_ID=your-project-id

# ==================== REDIS CONFIGURATION ====================
# Redis for caching and session storage
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# ==================== SECURITY CONFIGURATION ====================
# JWT configuration
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRES_IN=24h

# CORS configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==================== COMMUNITY FEATURES ====================
# Post configuration
MAX_POST_LENGTH=10000
MAX_TITLE_LENGTH=200
ALLOWED_POST_CATEGORIES=general,phishing-alert,scam-warning,security-tip,question,discussion

# Comment configuration
MAX_COMMENT_LENGTH=2000
MAX_COMMENT_DEPTH=5

# Reputation system
INITIAL_REPUTATION=0
UPVOTE_POST_POINTS=5
UPVOTE_COMMENT_POINTS=2
DOWNVOTE_POST_PENALTY=-2
DOWNVOTE_COMMENT_PENALTY=-1

# Moderation
AUTO_MODERATION_ENABLED=true
SPAM_DETECTION_THRESHOLD=0.8
PROFANITY_FILTER_ENABLED=true

# ==================== EXTERNAL SERVICES ====================
# Link analysis service integration
LINK_SERVICE_URL=http://localhost:3002
LINK_ANALYSIS_TIMEOUT=30000

# User service integration
AUTH_SERVICE_URL=http://localhost:3001
USER_PROFILE_CACHE_TTL=3600

# Admin service integration
ADMIN_SERVICE_URL=http://localhost:3005

# ==================== MONITORING & OBSERVABILITY ====================
# Metrics
ENABLE_METRICS=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Health checks
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1000

# ==================== FILE UPLOAD CONFIGURATION ====================
# Image uploads for posts
ENABLE_IMAGE_UPLOADS=true
MAX_IMAGE_SIZE=5242880
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp
UPLOAD_PATH=./uploads/images

# Cloud storage (optional)
CLOUD_STORAGE_BUCKET=your-storage-bucket
CLOUD_STORAGE_PATH=community/images

# ==================== NOTIFICATION CONFIGURATION ====================
# Real-time notifications
ENABLE_REAL_TIME_NOTIFICATIONS=true
WEBSOCKET_PORT=3004

# Email notifications
ENABLE_EMAIL_NOTIFICATIONS=true
EMAIL_SERVICE_URL=http://localhost:3006

# Push notifications
ENABLE_PUSH_NOTIFICATIONS=false
PUSH_SERVICE_URL=http://localhost:3008

# ==================== CACHING CONFIGURATION ====================
# Cache settings
CACHE_TTL_DEFAULT=3600
CACHE_TTL_POSTS=1800
CACHE_TTL_COMMENTS=900
CACHE_TTL_USER_PROFILES=3600
CACHE_TTL_REPUTATION=7200

# Cache prefixes
CACHE_PREFIX_POSTS=community:posts:
CACHE_PREFIX_COMMENTS=community:comments:
CACHE_PREFIX_USERS=community:users:
CACHE_PREFIX_REPUTATION=community:reputation:

# ==================== DEVELOPMENT CONFIGURATION ====================
# Development tools
ENABLE_DEBUG_ROUTES=false
ENABLE_MOCK_DATA=false
MOCK_USER_COUNT=100
MOCK_POST_COUNT=500

# Testing
TEST_DATABASE_URL=
TEST_REDIS_URL=redis://localhost:6379/1

# ==================== PRODUCTION CONFIGURATION ====================
# Production optimizations
ENABLE_COMPRESSION=true
ENABLE_ETAG=true
TRUST_PROXY=true

# Security headers
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=false

# SSL/TLS
FORCE_HTTPS=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# ==================== FEATURE FLAGS ====================
# Feature toggles
FEATURE_VOTING_ENABLED=true
FEATURE_COMMENTS_ENABLED=true
FEATURE_REPORTING_ENABLED=true
FEATURE_REPUTATION_ENABLED=true
FEATURE_BADGES_ENABLED=true
FEATURE_TRENDING_ENABLED=true
FEATURE_SEARCH_ENABLED=true
FEATURE_TAGS_ENABLED=true

# Experimental features
FEATURE_AI_MODERATION=false
FEATURE_SENTIMENT_ANALYSIS=false
FEATURE_AUTO_TAGGING=false
FEATURE_DUPLICATE_DETECTION=false

# ==================== INTEGRATION CONFIGURATION ====================
# Third-party services
ENABLE_ANALYTICS=false
ANALYTICS_SERVICE_URL=
ANALYTICS_API_KEY=

# Social media integration
ENABLE_SOCIAL_SHARING=true
TWITTER_API_KEY=
FACEBOOK_APP_ID=

# Content moderation services
ENABLE_EXTERNAL_MODERATION=false
MODERATION_SERVICE_URL=
MODERATION_API_KEY=
