import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLoginFlow } from '../../hooks/useAuthFlow';
import UserStatusCard from './UserStatusCard';

const LoginSuccessHandler = () => {
 const { user } = useAuth();
 const navigate = useNavigate();
 const { userStatus, shouldRedirect } = useLoginFlow();
 const [countdown, setCountdown] = useState(3);

 useEffect(() => {
  if (shouldRedirect && userStatus?.redirectTo) {
   const timer = setInterval(() => {
    setCountdown(prev => {
     if (prev <= 1) {
      clearInterval(timer);
      navigate(userStatus.redirectTo);
      return 0;
     }
     return prev - 1;
    });
   }, 1000);

   return () => clearInterval(timer);
  }
 }, [shouldRedirect, userStatus, navigate]);

 if (!user || !userStatus) {
  return (
   <div className="flex justify-center items-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
   </div>
  );
 }

 return (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
   <motion.div
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    className="w-full max-w-md"
   >
    <div className="text-center mb-6">
     <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: 0.2 }}
      className="text-6xl mb-4"
     >
      🎉
     </motion.div>
     <h1 className="text-2xl font-bold text-gray-800 mb-2">
      Đăng nhập thành công!
     </h1>
     <p className="text-gray-600">
      Chào mừng bạn đến với VWork Platform
     </p>
    </div>

    <UserStatusCard className="mb-6" />

    {/* Countdown and Auto-redirect */}
    {shouldRedirect && userStatus?.redirectTo && (
     <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5 }}
      className="text-center bg-white rounded-lg p-4 shadow-md"
     >
      <div className="flex items-center justify-center space-x-2 mb-3">
       <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
       <span className="text-sm text-gray-600">
        Đang chuyển hướng trong {countdown} giây...
       </span>
      </div>
      
      <button
       onClick={() => navigate(userStatus.redirectTo)}
       className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
      >
       Tiếp tục ngay
      </button>
      
      <button
       onClick={() => navigate('/dashboard')}
       className="w-full mt-2 text-gray-600 hover:text-gray-800 transition-colors"
      >
       Đi tới Dashboard
      </button>
     </motion.div>
    )}

    {/* No redirect needed - Active user */}
    {!shouldRedirect && userStatus.status === 'ACTIVE' && (
     <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5 }}
      className="text-center"
     >
      <button
       onClick={() => navigate('/dashboard')}
       className="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors font-medium"
      >
       Vào Dashboard
      </button>
     </motion.div>
    )}

    {/* Debug info in development */}
    {process.env.NODE_ENV === 'development' && (
     <div className="mt-6 p-4 bg-gray-100 rounded-lg text-xs">
      <strong>Debug Info:</strong>
      <pre className="mt-2 text-gray-600">
       {JSON.stringify({
        status: userStatus.status,
        nextAction: userStatus.nextAction,
        redirectTo: userStatus.redirectTo,
        shouldRedirect,
        emailVerified: user.emailVerified,
        userType: user.userType
       }, null, 2)}
      </pre>
     </div>
    )}
   </motion.div>
  </div>
 );
};

export default LoginSuccessHandler;
