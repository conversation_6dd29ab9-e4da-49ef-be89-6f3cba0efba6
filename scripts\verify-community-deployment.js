#!/usr/bin/env node

/**
 * Verify Community Service Deployment
 * Tests the new /api/stats endpoint and checks deployment status
 */

const https = require('https');
const http = require('http');

console.log('🔍 Verifying Community Service Deployment...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

// Make HTTP request
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    const req = client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testEndpoint(url, description) {
  try {
    logInfo(`Testing ${description}...`);
    const response = await makeRequest(url);
    
    if (response.statusCode === 200) {
      logSuccess(`${description} - Status: ${response.statusCode}`);
      
      if (response.data && typeof response.data === 'object') {
        logInfo(`Response keys: ${Object.keys(response.data).join(', ')}`);
      }
      
      return { success: true, response };
    } else {
      logWarning(`${description} - Status: ${response.statusCode}`);
      return { success: false, statusCode: response.statusCode };
    }
  } catch (error) {
    logError(`${description} - Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function main() {
  log('🔍 VWork Community Service Deployment Verification', 'bright');
  log('Testing the new /api/stats endpoint and checking deployment status', 'blue');
  
  const endpoints = [
    {
      url: 'https://nerafus-community-service.onrender.com/health',
      description: 'Community Service Health Check'
    },
    {
      url: 'https://nerafus-community-service.onrender.com/api/stats',
      description: 'Community Statistics Endpoint'
    },
    {
      url: 'https://vwork-api-gateway.onrender.com/health',
      description: 'API Gateway Health Check'
    },
    {
      url: 'https://vwork-api-gateway.onrender.com/api/v1/community/stats',
      description: 'API Gateway Community Stats Proxy'
    }
  ];
  
  logStep('1', 'Testing Community Service endpoints...');
  
  const results = [];
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint.url, endpoint.description);
    results.push({ ...endpoint, ...result });
    
    // Add delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  logStep('2', 'Analyzing results...');
  
  const successfulEndpoints = results.filter(r => r.success);
  const failedEndpoints = results.filter(r => !r.success);
  
  logInfo(`Successful endpoints: ${successfulEndpoints.length}/${results.length}`);
  logInfo(`Failed endpoints: ${failedEndpoints.length}/${results.length}`);
  
  if (successfulEndpoints.length > 0) {
    log('\n✅ Successful Endpoints:', 'green');
    successfulEndpoints.forEach(endpoint => {
      log(`• ${endpoint.description}`, 'green');
    });
  }
  
  if (failedEndpoints.length > 0) {
    log('\n❌ Failed Endpoints:', 'red');
    failedEndpoints.forEach(endpoint => {
      log(`• ${endpoint.description}`, 'red');
      if (endpoint.error) {
        log(`  Error: ${endpoint.error}`, 'yellow');
      } else if (endpoint.statusCode) {
        log(`  Status: ${endpoint.statusCode}`, 'yellow');
      }
    });
  }
  
  logStep('3', 'Deployment Status Summary');
  
  const communityServiceHealthy = results.find(r => r.description.includes('Community Service Health Check'))?.success;
  const statsEndpointWorking = results.find(r => r.description.includes('Community Statistics Endpoint'))?.success;
  const apiGatewayHealthy = results.find(r => r.description.includes('API Gateway Health Check'))?.success;
  const apiGatewayStatsWorking = results.find(r => r.description.includes('API Gateway Community Stats Proxy'))?.success;
  
  if (communityServiceHealthy && statsEndpointWorking) {
    logSuccess('Community Service deployment successful!');
    log('• Health check: ✅', 'green');
    log('• Stats endpoint: ✅', 'green');
  } else {
    logWarning('Community Service deployment may still be in progress...');
    if (!communityServiceHealthy) {
      log('• Health check: ❌', 'red');
    }
    if (!statsEndpointWorking) {
      log('• Stats endpoint: ❌', 'red');
    }
  }
  
  if (apiGatewayHealthy && apiGatewayStatsWorking) {
    logSuccess('API Gateway proxy working correctly!');
    log('• Health check: ✅', 'green');
    log('• Stats proxy: ✅', 'green');
  } else {
    logWarning('API Gateway may need more time to update...');
    if (!apiGatewayHealthy) {
      log('• Health check: ❌', 'red');
    }
    if (!apiGatewayStatsWorking) {
      log('• Stats proxy: ❌', 'red');
    }
  }
  
  log('\n📋 Next Steps:', 'cyan');
  
  if (communityServiceHealthy && statsEndpointWorking) {
    log('1. ✅ Community Service is ready', 'green');
    log('2. Test the frontend at https://nerafus.com/#/community', 'yellow');
    log('3. Check browser console for any remaining errors', 'yellow');
  } else {
    log('1. ⏳ Wait 2-3 more minutes for deployment to complete', 'yellow');
    log('2. Run this script again to recheck', 'yellow');
    log('3. Check Render dashboard for deployment status', 'yellow');
  }
  
  if (apiGatewayHealthy && apiGatewayStatsWorking) {
    log('4. ✅ API Gateway proxy is working', 'green');
  } else {
    log('4. ⏳ API Gateway may need more time to update', 'yellow');
  }
  
  log('\n🔍 Manual Testing:', 'cyan');
  log('• Community Service: https://nerafus-community-service.onrender.com/api/stats', 'yellow');
  log('• API Gateway: https://vwork-api-gateway.onrender.com/api/v1/community/stats', 'yellow');
  log('• Frontend: https://nerafus.com/#/community', 'yellow');
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:');
  logError(`Promise: ${promise}`);
  logError(`Reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError('Uncaught Exception:');
  logError(error.message);
  logError(error.stack);
  process.exit(1);
});

// Run the verification
main().catch(error => {
  logError('Verification failed:');
  logError(error.message);
  process.exit(1);
}); 