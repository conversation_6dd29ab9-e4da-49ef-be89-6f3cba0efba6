# Freelancer Team Mock

## T<PERSON><PERSON> quan

Mock UI cho tính năng **Freelancer Team** của <PERSON>RAF<PERSON>, đ<PERSON><PERSON><PERSON> thiết kế dựa trên user story từ tài liệu `Freelancer team NERAFUS.txt`.

## T<PERSON>h năng chính

### 1. <PERSON><PERSON> (Main View)
- **Hero section** với slogan "Hợp tác và phát triển"
- **Thống kê** về số lượng teams, tổng thu nhập, tỷ lệ hài lòng
- **Nút tạo team** và **khám phá teams**
- **Điều kiện thành lập team** đư<PERSON>c hiển thị rõ ràng

### 2. Tạo Team (4 bước)
- **Bước 1**: Thông tin cơ bản (tên, slogan, mô tả, số thành viên)
- **Bước 2**: Thông tin thành viên (leader + c<PERSON><PERSON> thành viên kh<PERSON>c)
- **Bước 3**: <PERSON><PERSON> sẻ lợ<PERSON> (% cho từng thành viên)
- **Bước 4**: Xác nhận và đồng ý điều khoản

### 3. Khám phá Teams
- **Tìm kiếm và lọc** teams theo lĩnh vực
- **Card hiển thị** thông tin team với:
  - Logo, tên, slogan
  - Danh sách thành viên
  - Thống kê dự án và thu nhập
  - Nút thuê, trò chuyện, lưu hồ sơ

### 4. Hồ sơ Team chi tiết
- **Header** với thông tin tổng quan
- **Giới thiệu** về team
- **Danh sách thành viên** chi tiết
- **Dự án nổi bật** đã hoàn thành
- **Các action** để thuê team

## User Stories được implement

### Cho Freelancer:
- ✅ Tạo team mới với quy trình 4 bước
- ✅ Xem danh sách teams
- ✅ Xem hồ sơ team chi tiết
- ✅ Điều kiện thành lập team được hiển thị rõ ràng

### Cho Client:
- ✅ Khám phá danh sách teams
- ✅ Xem hồ sơ team chi tiết
- ✅ Các action để thuê team

## Điều kiện thành lập Team

1. **Tối thiểu 3 thành viên** (phải là bạn bè ít nhất 5 ngày)
2. **Hoàn thành 10+ dự án** (mỗi thành viên)
3. **Đánh giá 4.8/5 sao** (chất lượng cao)
4. **Có trưởng nhóm** (người tạo team)

## Cách sử dụng

### Sử dụng trực tiếp component
```jsx
import FreelancerTeamMock from './components/team/FreelancerTeamMock';

// Sử dụng trong component
<FreelancerTeamMock />
```

### Truy cập qua routing
```jsx
// Navigate to team page
navigate('/team');

// Hoặc sử dụng Link component
<Link to="/team">Team</Link>
```

### URL: `/team`
- **Route**: `/team`
- **Component**: `AppleTeamPage`
- **Access**: Protected (cần đăng nhập)
- **Title**: "Freelancer Team - NERAFUS"

## 🎨 Modern UI/UX Features

### GSAP Animations
- **Hero Section**: Staggered text animations với easing curves
- **Stats Counter**: Animated number counting với ScrollTrigger
- **Cards**: Stagger animations cho grid items
- **Micro-interactions**: Hover effects và button feedback

### Framer Motion
- **Page Transitions**: Smooth slide transitions giữa các views
- **Component Animations**: Scale, fade, và slide effects
- **Loading States**: Skeleton loading với pulse animation
- **Interactive Elements**: Hover và tap gestures

### Glass Morphism Design
- **Backdrop Blur**: CSS backdrop-filter với fallback
- **Transparency**: Semi-transparent backgrounds
- **Modern Aesthetics**: Giống iOS và modern web apps

### Micro-interactions
- **Button Hover**: Scale và shadow effects
- **Card Hover**: Lift và glow effects
- **Form Focus**: Ring và scale animations
- **Progress Feedback**: Real-time visual feedback

## 📱 Responsive Design

Mock được thiết kế mobile-first với:
- **Desktop (1024px+)**: Full grid layout với 3-4 columns
- **Tablet (768-1024px)**: 2 columns, adjusted spacing
- **Mobile (640-768px)**: Single column, stacked elements
- **Small Mobile (<640px)**: Minimal layout, hidden secondary info

### Breakpoints
```css
@media (max-width: 1024px) { /* Tablet */ }
@media (max-width: 768px)  { /* Mobile */ }
@media (max-width: 640px)  { /* Small Mobile */ }
```

## 🎯 Accessibility

- **Keyboard Navigation**: Full support cho tab navigation
- **Screen Readers**: Proper ARIA labels và semantic HTML
- **High Contrast**: Support cho prefers-contrast: high
- **Reduced Motion**: Respect prefers-reduced-motion setting
- **Focus States**: Visible focus rings với proper contrast

## 🌈 Color System

### Primary Palette
- **Blue**: #3B82F6 (Primary actions)
- **Indigo**: #6366F1 (Secondary actions)
- **Purple**: #8B5CF6 (Accents)

### Functional Colors
- **Success**: #10B981 (Green)
- **Warning**: #F59E0B (Amber)
- **Error**: #EF4444 (Red)
- **Info**: #06B6D4 (Cyan)

### Neutral Palette
- **Gray-50**: #F9FAFB (Light backgrounds)
- **Gray-100**: #F3F4F6 (Card backgrounds)
- **Gray-600**: #4B5563 (Secondary text)
- **Gray-800**: #1F2937 (Primary text)

## Các file liên quan

- `FreelancerTeamMock.js` - Component chính
- `FreelancerTeamMock.css` - Styling
- `TeamDemo.js` - Component demo
- `README.md` - Tài liệu này

## Lưu ý

- Đây là mock UI, chưa có logic backend
- Dữ liệu là mock data để demo
- Cần tích hợp với API thực tế khi implement
- Có thể cần điều chỉnh theo design system của dự án 