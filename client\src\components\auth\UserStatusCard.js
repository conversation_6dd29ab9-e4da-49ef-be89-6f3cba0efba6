import React from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { checkUserStatus, getProfileCompletionPercentage } from '../../utils/userStatus';

const UserStatusCard = ({ className = '' }) => {
 const { user } = useAuth();
 
 if (!user) return null;

 const status = checkUserStatus(user);
 const completionPercentage = getProfileCompletionPercentage(user);

 const getStatusColor = () => {
  switch (status.status) {
   case 'EMAIL_NOT_VERIFIED':
    return 'bg-yellow-500';
   case 'NEW_USER':
    return 'bg-blue-500';
   case 'PROFILE_INCOMPLETE':
    return 'bg-orange-500';
   case 'ACTIVE':
    return 'bg-green-500';
   default:
    return 'bg-gray-500';
  }
 };

 const getStatusIcon = () => {
  switch (status.status) {
   case 'EMAIL_NOT_VERIFIED':
    return '📧';
   case 'NEW_USER':
    return '👋';
   case 'PROFILE_INCOMPLETE':
    return '📝';
   case 'ACTIVE':
    return '✅';
   default:
    return '❓';
  }
 };

 return (
  <motion.div
   initial={{ opacity: 0, y: 20 }}
   animate={{ opacity: 1, y: 0 }}
   className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${getStatusColor()} ${className}`}
  >
   <div className="flex items-center justify-between mb-4">
    <div className="flex items-center space-x-3">
     <span className="text-2xl">{getStatusIcon()}</span>
     <div>
      <h3 className="font-semibold text-gray-800">
       Xin chào, {user.displayName || user.firstName || 'User'}!
      </h3>
      <p className="text-sm text-gray-600">{status.message}</p>
     </div>
    </div>
    {status.status === 'ACTIVE' && (
     <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
      Hoạt động
     </span>
    )}
   </div>

   {/* Email Verification Status */}
   {!user.emailVerified && (
    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
     <div className="flex items-center space-x-2">
      <span className="text-yellow-600">⚠️</span>
      <span className="text-sm text-yellow-800">
       Email chưa được xác thực: {user.email}
      </span>
     </div>
    </div>
   )}

   {/* Profile Completion */}
   {status.status !== 'ACTIVE' && (
    <div className="mb-4">
     <div className="flex items-center justify-between mb-2">
      <span className="text-sm font-medium text-gray-700">
       Hoàn thành hồ sơ
      </span>
      <span className="text-sm text-gray-500">
       {completionPercentage}%
      </span>
     </div>
     <div className="w-full bg-gray-200 rounded-full h-2">
      <motion.div
       initial={{ width: 0 }}
       animate={{ width: `${completionPercentage}%` }}
       transition={{ duration: 1, ease: "easeOut" }}
       className={`h-2 rounded-full ${getStatusColor()}`}
      />
     </div>
    </div>
   )}

   {/* Action Button */}
   {status.redirectTo && status.status !== 'ACTIVE' && (
    <motion.button
     whileHover={{ scale: 1.02 }}
     whileTap={{ scale: 0.98 }}
     onClick={() => window.location.href = status.redirectTo}
     className={`w-full py-2 px-4 rounded-lg text-white font-medium transition-colors ${
      status.status === 'EMAIL_NOT_VERIFIED' 
       ? 'bg-yellow-500 hover:bg-yellow-600' 
       : 'bg-blue-500 hover:bg-blue-600'
     }`}
    >
     {status.status === 'EMAIL_NOT_VERIFIED' 
      ? 'Xác thực Email' 
      : 'Hoàn thành hồ sơ'
     }
    </motion.button>
   )}

   {/* User Type Badge */}
   {user.userType && (
    <div className="mt-4 flex items-center space-x-2">
     <span className="text-sm text-gray-500">Loại tài khoản:</span>
     <span className={`px-2 py-1 text-xs rounded-full ${
      user.userType === 'freelancer' 
       ? 'bg-purple-100 text-purple-800' 
       : 'bg-blue-100 text-blue-800'
     }`}>
      {user.userType === 'freelancer' ? 'Freelancer' : 'Client'}
     </span>
    </div>
   )}
  </motion.div>
 );
};

export default UserStatusCard;
