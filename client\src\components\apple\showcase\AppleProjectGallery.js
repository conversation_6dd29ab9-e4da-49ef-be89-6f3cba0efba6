import React, { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
 EyeIcon,
 HeartIcon,
 ArrowTopRightOnSquareIcon,
 StarIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves for project gallery animations
const projectEases = {
 elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
 bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
 liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
 magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
 wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1"),
 creative: CustomEase.create("creative", "M0,0 C0.35,0 0.65,1 1,1")
};

const AppleProjectGallery = () => {
 const { t } = useLanguage();
 const sectionRef = useRef(null);
 const titleRef = useRef(null);
 const galleryRef = useRef(null);
 const particleCanvasRef = useRef(null);
 const morphingShapesRef = useRef([]);
 const projectCardRefs = useRef([]);
 const filterTabsRef = useRef(null);
 const [selectedCategory, setSelectedCategory] = useState('all');
 const [likedProjects, setLikedProjects] = useState(new Set());

 const categories = [
  { id: 'all', name: t('allProjects') },
  { id: 'web', name: t('webDesign') },
  { id: 'mobile', name: t('mobileApps') },
  { id: 'branding', name: t('branding') },
  { id: 'illustration', name: t('illustration') },
 ];

 const featuredProjects = [
  {
   id: 1,
   title: 'Modern E-commerce Platform',
   category: 'web',
   image: 'https://images.unsplash.com/photo-*************-afdab827c52f?w=600&h=400&fit=crop',
   freelancer: 'Sarah Chen',
   rating: 4.9,
   views: 2450,
   likes: 184,
   tags: ['React', 'E-commerce', 'UI/UX'],
   description: 'A sleek and modern e-commerce platform with advanced features.',
  },
  {
   id: 2,
   title: 'Mobile Banking App',
   category: 'mobile',
   image: 'https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=600&h=400&fit=crop',
   freelancer: 'Marcus Johnson',
   rating: 4.8,
   views: 1890,
   likes: 152,
   tags: ['React Native', 'FinTech', 'Mobile'],
   description: 'Secure and intuitive mobile banking application.',
  },
  {
   id: 3,
   title: 'Brand Identity Package',
   category: 'branding',
   image: 'https://images.unsplash.com/photo-*************-2531766767d1?w=600&h=400&fit=crop',
   freelancer: 'Elena Rodriguez',
   rating: 4.9,
   views: 3200,
   likes: 278,
   tags: ['Branding', 'Logo Design', 'Identity'],
   description: 'Complete brand identity system for a tech startup.',
  },
  {
   id: 4,
   title: 'Fitness Tracking Dashboard',
   category: 'web',
   image: 'https://images.unsplash.com/photo-*************-80b023f02d71?w=600&h=400&fit=crop',
   freelancer: 'David Kim',
   rating: 4.7,
   views: 1650,
   likes: 134,
   tags: ['Dashboard', 'Health', 'Analytics'],
   description: 'Comprehensive fitness tracking and analytics platform.',
  },
  {
   id: 5,
   title: 'Restaurant Mobile App',
   category: 'mobile',
   image: 'https://images.unsplash.com/photo-**********-0b77e0d5fac6?w=600&h=400&fit=crop',
   freelancer: 'Priya Patel',
   rating: 4.8,
   views: 2100,
   likes: 198,
   tags: ['Mobile App', 'Food', 'Ordering'],
   description: 'User-friendly restaurant ordering and delivery app.',
  },
  {
   id: 6,
   title: 'Creative Portfolio Site',
   category: 'web',
   image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop',
   freelancer: 'Alex Thompson',
   rating: 4.9,
   views: 2780,
   likes: 245,
   tags: ['Portfolio', 'Creative', 'Web Design'],
   description: 'Stunning portfolio website for creative professionals.',
  },
 ];

 const filteredProjects = selectedCategory === 'all' 
  ? featuredProjects 
  : featuredProjects.filter(project => project.category === selectedCategory);

 // Advanced Creative Particles System
 const createAdvancedCreativeParticles = () => {
  if (!particleCanvasRef.current) return;

  const container = particleCanvasRef.current;
  const particles = [];

  // Create 120 creative particles with design themes
  for (let i = 0; i < 120; i++) {
   const particle = document.createElement('div');
   particle.className = 'absolute w-1 h-1 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full opacity-40';
   particle.style.left = Math.random() * 100 + '%';
   particle.style.top = Math.random() * 100 + '%';
   container.appendChild(particle);
   particles.push(particle);

   // Create artistic motion paths
   const pathData = `M${Math.random() * 100},${Math.random() * 100} C${Math.random() * 100},${Math.random() * 100} ${Math.random() * 100},${Math.random() * 100} ${Math.random() * 100},${Math.random() * 100}`;
   
   gsap.to(particle, {
    motionPath: {
     path: pathData,
     autoRotate: true
    },
    duration: Math.random() * 15 + 10,
    repeat: -1,
    ease: projectEases.creative,
    delay: Math.random() * 5
   });

   // Creative inspiration pulse
   gsap.to(particle, {
    scale: "random(0.3, 4)",
    opacity: "random(0.2, 0.8)",
    duration: "random(4, 8)",
    repeat: -1,
    yoyo: true,
    ease: projectEases.wave
   });
  }
 };

 // Morphing Creative Backgrounds
 const createMorphingCreativeShapes = () => {
  morphingShapesRef.current.forEach((shape, index) => {
   if (!shape) return;

   const colors = [
    'from-orange-400/15 to-pink-400/15',
    'from-pink-400/15 to-purple-400/15',
    'from-purple-400/15 to-indigo-400/15',
    'from-indigo-400/15 to-blue-400/15',
    'from-blue-400/15 to-cyan-400/15',
    'from-cyan-400/15 to-green-400/15'
   ];

   shape.className = `absolute bg-gradient-to-br ${colors[index % colors.length]} rounded-full`;

   const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
   
   morphTimeline
    .to(shape, {
     borderRadius: "80% 20% 40% 60% / 30% 70% 60% 40%",
     scale: 1.6,
     rotation: 450,
     x: "random(-60, 60)",
     y: "random(-50, 50)",
     duration: 8,
     ease: projectEases.liquid
    })
    .to(shape, {
     borderRadius: "20% 80% 60% 40% / 70% 30% 40% 60%",
     scale: 0.8,
     rotation: -225,
     x: "random(-50, 50)",
     y: "random(-60, 60)",
     duration: 6,
     ease: projectEases.elastic
    })
    .to(shape, {
     borderRadius: "60% 40% 20% 80% / 40% 60% 80% 20%",
     scale: 1.4,
     rotation: 360,
     x: "random(-55, 55)",
     y: "random(-55, 55)",
     duration: 7,
     ease: projectEases.wave
    });

   morphTimeline.delay(index * 2);
  });
 };

 // Advanced Title Animation with Creative Theme
 const createAdvancedTitleAnimation = () => {
  if (!titleRef.current) return;

  const titleElement = titleRef.current.querySelector('h2');
  const subtitleElement = titleRef.current.querySelector('p');

  if (titleElement) {
   const titleSplit = new SplitText(titleElement, { type: "chars,words" });

   gsap.fromTo(titleSplit.chars, {
    opacity: 0,
    y: 140,
    rotationX: -90,
    transformOrigin: "center bottom"
   }, {
    opacity: 1,
    y: 0,
    rotationX: 0,
    duration: 2.4,
    stagger: 0.025,
    ease: projectEases.bounce,
    scrollTrigger: {
     trigger: titleRef.current,
     start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });

   // Add creative inspiration glow
   gsap.to(titleElement, {
    textShadow: "0 0 50px rgba(251, 146, 60, 0.6)",
    duration: 5,
    repeat: -1,
    yoyo: true,
    ease: projectEases.wave
   });
  }

  if (subtitleElement) {
   const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

   gsap.fromTo(subtitleSplit.words, {
    opacity: 0,
    y: 80,
    scale: 0.5
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    duration: 1.8,
    stagger: 0.18,
    ease: projectEases.elastic,
    delay: 1.4,
    scrollTrigger: {
     trigger: titleRef.current,
     start: 'top 80%',
     toggleActions: 'play none none reverse'
    }
   });
  }
 };

 // Enhanced Filter Tabs Animation
 const createAdvancedFilterAnimation = () => {
  if (!filterTabsRef.current) return;

  const filterButtons = Array.from(filterTabsRef.current.children);

  filterButtons.forEach((button, index) => {
   gsap.fromTo(button, {
    opacity: 0,
    y: 40,
    scale: 0.8
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    duration: 0.8,
    delay: index * 0.1,
    ease: projectEases.bounce,
    scrollTrigger: {
     trigger: filterTabsRef.current,
     start: 'top 85%',
     toggleActions: 'play none none reverse'
    }
   });
  });
 };

 // Advanced Magnetic Project Card Hover
 const createMagneticProjectHover = (projectElement, index) => {
  if (!projectElement) return;

  const image = projectElement.querySelector('.project-image');
  const content = projectElement.querySelector('.project-content');
  const overlay = projectElement.querySelector('.project-overlay');
  const tags = projectElement.querySelector('.project-tags');

  let isHovering = false;

  projectElement.addEventListener('mouseenter', () => {
   isHovering = true;

   // Magnetic project hover timeline
   const hoverTL = gsap.timeline();

   hoverTL
    .to(projectElement, {
     scale: 1.06,
     y: -20,
     rotationY: 6,
     rotationX: 3,
     boxShadow: "0 60px 120px rgba(0,0,0,0.3)",
     duration: 0.8,
     ease: projectEases.magnetic
    })
    .to(image, {
     scale: 1.15,
     rotation: 2,
     duration: 1.0,
     ease: projectEases.elastic
    }, 0)
    .to(overlay, {
     opacity: 1,
     duration: 0.6,
     ease: projectEases.wave
    }, 0.2)
    .to(content, {
     y: -10,
     duration: 0.7,
     ease: projectEases.bounce
    }, 0.3)
    .to(tags, {
     y: -5,
     scale: 1.05,
     duration: 0.5,
     ease: projectEases.elastic
    }, 0.4);


  });

  projectElement.addEventListener('mouseleave', () => {
   isHovering = false;

   gsap.to(projectElement, {
    scale: 1,
    y: 0,
    rotationY: 0,
    rotationX: 0,
    boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
    duration: 1.0,
    ease: projectEases.elastic
   });

   gsap.to(image, {
    scale: 1,
    rotation: 0,
    duration: 0.8,
    ease: projectEases.bounce
   });

   gsap.to(overlay, {
    opacity: 0,
    duration: 0.4,
    ease: projectEases.wave
   });

   gsap.to(content, {
    y: 0,
    duration: 0.6,
    ease: projectEases.wave
   });

   gsap.to(tags, {
    y: 0,
    scale: 1,
    duration: 0.5,
    ease: projectEases.bounce
   });
  });

  // Real-time mouse tracking for creative interaction
  projectElement.addEventListener('mousemove', (e) => {
   if (!isHovering) return;

   const rect = projectElement.getBoundingClientRect();
   const centerX = rect.left + rect.width / 2;
   const centerY = rect.top + rect.height / 2;
   const mouseX = e.clientX - centerX;
   const mouseY = e.clientY - centerY;

   gsap.to(projectElement, {
    x: mouseX * 0.04,
    y: mouseY * 0.04,
    duration: 0.3,
    ease: "power2.out"
   });

   gsap.to(image, {
    x: mouseX * 0.06,
    y: mouseY * 0.06,
    duration: 0.2,
    ease: "power2.out"
   });

   gsap.to(content, {
    x: mouseX * 0.02,
    y: mouseY * 0.02,
    duration: 0.4,
    ease: "power2.out"
   });
  });
 };



 // Advanced Project Grid Animation
 const createAdvancedProjectGrid = () => {
  if (!galleryRef.current) return;

  const projectElements = Array.from(galleryRef.current.children);

  projectElements.forEach((project, index) => {
   // Store reference for hover effects
   projectCardRefs.current[index] = project;

   // Create magnetic hover effect
   createMagneticProjectHover(project, index);

   // Creative showcase entrance animation
   gsap.fromTo(project, {
    opacity: 0,
    y: 140,
    scale: 0.6,
    rotationY: 50,
    rotationX: 30
   }, {
    opacity: 1,
    y: 0,
    scale: 1,
    rotationY: 0,
    rotationX: 0,
    duration: 2.2,
    delay: index * 0.18,
    ease: projectEases.elastic,
    scrollTrigger: {
     trigger: project,
     start: 'top 90%',
     toggleActions: 'play none none reverse'
    }
   });

   // Animate project components
   const image = project.querySelector('.project-image');
   const content = project.querySelector('.project-content');

   if (image) {
    gsap.fromTo(image, {
     scale: 0,
     rotation: -360
    }, {
     scale: 1,
     rotation: 0,
     duration: 1.4,
     delay: index * 0.18 + 0.5,
     ease: projectEases.bounce,
     scrollTrigger: {
      trigger: project,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }

   if (content) {
    const contentChildren = Array.from(content.children);
    gsap.fromTo(contentChildren, {
     opacity: 0,
     x: -80
    }, {
     opacity: 1,
     x: 0,
     duration: 1.2,
     stagger: 0.15,
     delay: index * 0.18 + 1.0,
     ease: projectEases.wave,
     scrollTrigger: {
      trigger: project,
      start: 'top 90%',
      toggleActions: 'play none none reverse'
     }
    });
   }
  });
 };

 const toggleLikeProject = (projectId) => {
  const newLikedProjects = new Set(likedProjects);
  if (newLikedProjects.has(projectId)) {
   newLikedProjects.delete(projectId);
  } else {
   newLikedProjects.add(projectId);
  }
  setLikedProjects(newLikedProjects);
 };

 const handleCategoryChange = (categoryId) => {
  setSelectedCategory(categoryId);
  
  // Animate category change
  if (galleryRef.current) {
   const projects = galleryRef.current.children;
   
   gsap.fromTo(projects, {
    opacity: 0,
    scale: 0.8,
    y: 30
   }, {
    opacity: 1,
    scale: 1,
    y: 0,
    duration: 0.8,
    stagger: 0.1,
    ease: projectEases.elastic
   });
  }
 };

 useEffect(() => {
  const ctx = gsap.context(() => {
   // Initialize all advanced animations
   createAdvancedCreativeParticles();
   createMorphingCreativeShapes();
   createAdvancedTitleAnimation();
   createAdvancedFilterAnimation();
   createAdvancedProjectGrid();
  }, sectionRef);

  return () => ctx.revert();
 // eslint-disable-next-line react-hooks/exhaustive-deps
 }, []);

 useEffect(() => {
  // Re-animate when category changes
  createAdvancedProjectGrid();
 // eslint-disable-next-line react-hooks/exhaustive-deps
 }, [selectedCategory]);

 return (
  <>
   {/* Enhanced CSS */}
   <style>{`
    @keyframes liquid-creative {
     0%, 100% {
      border-radius: 80% 20% 40% 60% / 30% 70% 60% 40%;
     }
     25% {
      border-radius: 20% 80% 60% 40% / 70% 30% 40% 60%;
     }
     50% {
      border-radius: 60% 40% 20% 80% / 40% 60% 80% 20%;
     }
     75% {
      border-radius: 40% 60% 80% 20% / 80% 20% 60% 40%;
     }
    }
    
    .liquid-creative {
     animation: liquid-creative 20s ease-in-out infinite;
    }
    
    .project-card {
     transform-style: preserve-3d;
     transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .creative-particles {
     will-change: transform;
    }
    
    .project-overlay {
     background: linear-gradient(
      135deg,
      rgba(251, 146, 60, 0.9) 0%,
      rgba(244, 114, 182, 0.9) 50%,
      rgba(167, 139, 250, 0.9) 100%
     );
    }
   `}</style>

   <section ref={sectionRef} className='relative py-20 bg-white transition-colors duration-300 overflow-hidden'>
    {/* Advanced Multi-layer Background */}
    <div className='absolute inset-0'>
     <div 
      ref={el => morphingShapesRef.current[0] = el}
      className='absolute top-28 left-24 w-60 h-60 liquid-creative'
     />
     <div 
      ref={el => morphingShapesRef.current[1] = el}
      className='absolute top-52 right-28 w-52 h-52 liquid-creative'
     />
     <div 
      ref={el => morphingShapesRef.current[2] = el}
      className='absolute bottom-32 left-1/4 w-68 h-68 liquid-creative'
     />
     <div 
      ref={el => morphingShapesRef.current[3] = el}
      className='absolute bottom-28 right-24 w-56 h-56 liquid-creative'
     />
     <div 
      ref={el => morphingShapesRef.current[4] = el}
      className='absolute top-1/3 left-1/2 w-44 h-44 liquid-creative'
     />
     <div 
      ref={el => morphingShapesRef.current[5] = el}
      className='absolute top-28 left-1/3 w-48 h-48 liquid-creative'
     />
    </div>

    {/* Advanced Creative Particle Canvas */}
    <div 
     ref={particleCanvasRef}
     className='absolute inset-0 pointer-events-none overflow-hidden creative-particles'
    />

    <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
     {/* Enhanced Section Header */}
    <div ref={titleRef} className='text-center mb-16'>
      <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 mb-8 transition-colors duration-300'>
      {t('featuredProjects')}
       <span className='block text-transparent bg-gradient-to-r from-orange-500 to-pink-500 bg-clip-text'>
        {t('projectsText')}
       </span>
     </h2>
      <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
      {t('exploreOutstandingWork')}
     </p>
     </div>

     {/* Enhanced Category Filter */}
     <div ref={filterTabsRef} className='flex flex-wrap justify-center gap-4 mb-16'>
      {categories.map(category => (
       <button
        key={category.id}
        onClick={() => handleCategoryChange(category.id)}
        className={`px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 ${
         selectedCategory === category.id
          ? 'bg-gradient-to-r from-orange-600 to-pink-600 text-white shadow-2xl scale-105'
          : 'bg-white text-gray-600 hover:bg-gray-50 
        }`}
       >
        {category.name}
       </button>
      ))}
    </div>

     {/* Enhanced Projects Gallery */}
    <div
     ref={galleryRef}
      className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 lg:gap-12'
    >
      {filteredProjects.map((project, index) => (
      <div
       key={project.id}
        className='group project-card bg-white rounded-3xl overflow-hidden border border-gray-100 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 transform-gpu'
      >
        {/* Enhanced Image */}
        <div className='relative overflow-hidden'>
        <img
         src={project.image}
         alt={project.title}
          className='project-image w-full h-64 object-cover transition-transform duration-700'
         />
         
         {/* Enhanced Overlay */}
         <div className='project-overlay absolute inset-0 opacity-0 transition-opacity duration-500 flex items-center justify-center'>
          <div className='flex space-x-4'>
           <button className='p-4 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-colors duration-200'>
            <EyeIcon className='h-6 w-6 text-white' />
           </button>
         <button
            onClick={() => toggleLikeProject(project.id)}
            className='p-4 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-colors duration-200'
         >
          {likedProjects.has(project.id) ? (
             <HeartSolidIcon className='h-6 w-6 text-red-400' />
          ) : (
             <HeartIcon className='h-6 w-6 text-white' />
          )}
         </button>
           <button className='p-4 bg-white/20 backdrop-blur-sm rounded-2xl hover:bg-white/30 transition-colors duration-200'>
            <ArrowTopRightOnSquareIcon className='h-6 w-6 text-white' />
         </button>
        </div>
         </div>

         {/* Enhanced Stats */}
         <div className='absolute top-4 left-4 flex space-x-2'>
          <span className='px-3 py-1 bg-black/50 backdrop-blur-sm text-white text-sm font-medium rounded-full flex items-center'>
           <EyeIcon className='h-4 w-4 mr-1' />
           {project.views}
          </span>
          <span className='px-3 py-1 bg-black/50 backdrop-blur-sm text-white text-sm font-medium rounded-full flex items-center'>
           <HeartIcon className='h-4 w-4 mr-1' />
           {project.likes}
          </span>
        </div>
       </div>

        {/* Enhanced Content */}
        <div className='project-content p-8'>
         <h3 className='text-2xl font-bold text-gray-900 mb-3 transition-colors duration-300'>
         {project.title}
        </h3>

         <div className='flex items-center justify-between mb-4'>
          <p className='text-lg text-gray-600 transition-colors duration-300'>
          {t('byFreelancer')} {project.freelancer}
         </p>
         <div className='flex items-center'>
           <StarIcon className='w-5 h-5 text-yellow-400 fill-current mr-1' />
           <span className='text-lg font-bold text-gray-900 
           {project.rating}
          </span>
         </div>
        </div>

         <p className='text-gray-600 mb-6 leading-relaxed'>
          {project.description}
         </p>

         {/* Enhanced Tags */}
         <div className='project-tags flex flex-wrap gap-2'>
         {project.tags.slice(0, 2).map(tag => (
          <span
           key={tag}
            className='bg-gradient-to-r from-orange-50 to-pink-50  text-orange-600 text-sm font-medium px-3 py-1 rounded-full'
          >
           {tag}
          </span>
         ))}
         {project.tags.length > 2 && (
           <span className='text-gray-500 text-sm'>
           +{project.tags.length - 2}
          </span>
         )}
        </div>
       </div>
      </div>
     ))}
    </div>

     {/* Enhanced Bottom CTA */}
     <div className='text-center mt-16'>
      <button className='group px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-orange-600 to-pink-600 hover:from-orange-700 hover:to-pink-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'>
       <span className='mr-3'>{t('viewAllProjects')}</span>
       <span className='inline-block transition-transform duration-300 group-hover:translate-x-2'>→</span>
     </button>
    </div>
   </div>
  </section>
  </>
 );
};

export default AppleProjectGallery;
