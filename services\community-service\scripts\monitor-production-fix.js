#!/usr/bin/env node

/**
 * Monitor Production Fix
 * Monitors the deployment and tests if the database fix worked
 */

const https = require('https');
const { URL } = require('url');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const logSection = (title) => {
  console.log('\n' + '='.repeat(60));
  log(title, 'bright');
  console.log('='.repeat(60));
};

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'VWork-Production-Monitor/1.0',
        ...options.headers
      }
    };

    const req = https.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: jsonData,
            url: url
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: data,
            url: url,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function monitorProductionFix() {
  const baseUrl = 'https://vwork-community-service.onrender.com';
  let attemptCount = 0;
  const maxAttempts = 20; // Monitor for ~10 minutes (30 seconds per attempt)
  
  try {
    logSection('🚀 Monitoring Production Deployment Fix');
    log(`📍 Service URL: ${baseUrl}`, 'blue');
    log(`⏱️ Will monitor for up to ${maxAttempts * 30} seconds (${maxAttempts} attempts)`, 'cyan');
    
    while (attemptCount < maxAttempts) {
      attemptCount++;
      const timestamp = new Date().toISOString();
      
      logSection(`🔍 Attempt ${attemptCount}/${maxAttempts} - ${timestamp}`);
      
      try {
        // Test 1: Health Check
        log('🏥 Testing health check...', 'blue');
        const healthResponse = await makeRequest(`${baseUrl}/health`);
        
        if (healthResponse.status === 200) {
          log('✅ Health check: OK', 'green');
          
          // Check for auto-migration logs in health response
          if (healthResponse.data.checks?.database) {
            const dbCheck = healthResponse.data.checks.database;
            log(`📊 Database Status: ${dbCheck.status}`, 
                dbCheck.status === 'healthy' ? 'green' : 'red');
          }
        } else {
          log(`❌ Health check failed: ${healthResponse.status}`, 'red');
          log('⏭️ Service may still be deploying...', 'yellow');
          await sleep(30000); // Wait 30 seconds
          continue;
        }
        
        // Test 2: Database Endpoints
        log('🗄️ Testing database endpoints...', 'blue');
        
        const endpoints = [
          { path: '/api/posts?limit=1', name: 'Posts' },
          { path: '/api/users?limit=1', name: 'Users' },
          { path: '/api/stats', name: 'Stats' }
        ];
        
        let allEndpointsWorking = true;
        
        for (const endpoint of endpoints) {
          try {
            const response = await makeRequest(`${baseUrl}${endpoint.path}`);
            
            if (response.status === 200) {
              log(`  ✅ ${endpoint.name}: Working (200 OK)`, 'green');
            } else if (response.status === 500) {
              log(`  ❌ ${endpoint.name}: Still returning 500`, 'red');
              allEndpointsWorking = false;
            } else {
              log(`  ⚠️ ${endpoint.name}: Status ${response.status}`, 'yellow');
            }
          } catch (error) {
            log(`  ❌ ${endpoint.name}: Request failed - ${error.message}`, 'red');
            allEndpointsWorking = false;
          }
        }
        
        // Check if fix is successful
        if (allEndpointsWorking) {
          logSection('🎉 PRODUCTION FIX SUCCESSFUL!');
          log('✅ All database endpoints are now working correctly', 'green');
          log('✅ Community Service 500 errors have been resolved', 'green');
          log('✅ Database schema migration completed successfully', 'green');
          
          logSection('🧪 Final Verification');
          log('✅ Health check: Working', 'green');
          log('✅ Posts endpoint: Working', 'green');
          log('✅ Users endpoint: Working', 'green');
          log('✅ Stats endpoint: Working', 'green');
          
          logSection('🌐 Client Testing');
          log('Now you can test your VWork application:', 'blue');
          log('1. Visit your community page in the browser', 'cyan');
          log('2. Posts should load without errors', 'cyan');
          log('3. User data should display correctly', 'cyan');
          log('4. No more 500 Internal Server Errors', 'cyan');
          
          return true;
        }
        
        // If not all endpoints working, continue monitoring
        log(`⏱️ Not all endpoints working yet. Waiting 30 seconds for next check...`, 'yellow');
        
      } catch (error) {
        log(`❌ Monitoring attempt ${attemptCount} failed: ${error.message}`, 'red');
      }
      
      // Wait before next attempt
      if (attemptCount < maxAttempts) {
        await sleep(30000); // Wait 30 seconds
      }
    }
    
    // If we get here, max attempts reached
    logSection('⏰ Monitoring Timeout');
    log('Maximum monitoring time reached', 'yellow');
    log('The fix may need more time to deploy or may require additional troubleshooting', 'yellow');
    
    logSection('🔍 Next Steps');
    log('1. Check Render deployment logs:', 'cyan');
    log('   - Go to Render Dashboard > nerafus-community-service > Logs', 'blue');
    log('   - Look for auto-migration messages', 'blue');
    
    log('2. Test endpoints manually:', 'cyan');
    log('   curl https://vwork-community-service.onrender.com/health', 'blue');
    log('   curl https://vwork-community-service.onrender.com/api/posts?limit=1', 'blue');
    
    log('3. If still failing, run local migration:', 'cyan');
    log('   npm run db:auto-migrate', 'blue');
    
    return false;
    
  } catch (error) {
    log(`❌ Production monitoring failed: ${error.message}`, 'red');
    console.error('Full error:', error);
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  monitorProductionFix()
    .then(success => {
      if (success) {
        log('\n🎉 Production fix monitoring completed successfully!', 'green');
        process.exit(0);
      } else {
        log('\n⚠️ Production fix monitoring completed with issues', 'yellow');
        process.exit(1);
      }
    })
    .catch(error => {
      log(`\n❌ Production monitoring error: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { monitorProductionFix }; 