import React from 'react';
import { 
  StarIcon, 
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  CheckBadgeIcon,
  HeartIcon,
  ShareIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const FreelancerProfileCard = ({ freelancer, onContact, onSaveProfile, onShare }) => {
  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIconSolid key={i} className="h-5 w-5 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <StarIcon className="h-5 w-5 text-gray-300" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <StarIconSolid className="h-5 w-5 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-5 w-5 text-gray-300" />
        );
      }
    }
    return stars;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
      {/* Header with Cover Photo */}
      <div className="relative h-32 bg-gradient-to-r from-blue-500 to-purple-600">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div className="absolute top-4 right-4 flex space-x-2">
          <button 
            onClick={onSaveProfile}
            className="p-2 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all duration-200"
          >
            <HeartIcon className="h-5 w-5 text-white" />
          </button>
          <button 
            onClick={onShare}
            className="p-2 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all duration-200"
          >
            <ShareIcon className="h-5 w-5 text-white" />
          </button>
        </div>
      </div>

      {/* Profile Info */}
      <div className="relative px-6 pb-6">
        {/* Avatar */}
        <div className="flex justify-center -mt-12 mb-4">
          <div className="relative">
            <img
              src={freelancer.avatar}
              alt={freelancer.name}
              className="w-24 h-24 rounded-full border-4 border-white object-cover shadow-lg"
            />
            {freelancer.isOnline && (
              <div className="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
            )}
            {freelancer.isVerified && (
              <div className="absolute -bottom-1 -right-1">
                <CheckBadgeIcon className="h-6 w-6 text-blue-500" />
              </div>
            )}
          </div>
        </div>

        {/* Name and Title */}
        <div className="text-center mb-4">
          <h3 className="text-xl font-bold text-gray-900 mb-1">{freelancer.name}</h3>
          <p className="text-blue-600 font-medium mb-2">{freelancer.title}</p>
          
          {/* Rating */}
          <div className="flex items-center justify-center space-x-2 mb-2">
            <div className="flex space-x-1">
              {renderStars(freelancer.rating)}
            </div>
            <span className="text-sm font-medium text-gray-700">{freelancer.rating}</span>
            <span className="text-sm text-gray-500">({freelancer.reviewCount} đánh giá)</span>
          </div>

          {/* Location and Response Time */}
          <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <MapPinIcon className="h-4 w-4" />
              <span>{freelancer.location}</span>
            </div>
            <div className="flex items-center space-x-1">
              <ClockIcon className="h-4 w-4" />
              <span>{freelancer.responseTime}</span>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm text-center mb-4 line-clamp-3">
          {freelancer.description}
        </p>

        {/* Skills */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2 justify-center">
            {freelancer.skills.slice(0, 6).map(skill => (
              <span 
                key={skill} 
                className="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium"
              >
                {skill}
              </span>
            ))}
            {freelancer.skills.length > 6 && (
              <span className="px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-full font-medium">
                +{freelancer.skills.length - 6} more
              </span>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">{freelancer.completedJobs}</div>
            <div className="text-xs text-gray-600">Dự án hoàn thành</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">${freelancer.hourlyRate}</div>
            <div className="text-xs text-gray-600">USD/giờ</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">{freelancer.successRate || '98'}%</div>
            <div className="text-xs text-gray-600">Tỷ lệ thành công</div>
          </div>
        </div>

        {/* Availability Status */}
        <div className="mb-4">
          <div className={`flex items-center justify-center space-x-2 px-3 py-2 rounded-lg ${
            freelancer.availability === 'Sẵn sàng' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            <span className={`w-2 h-2 rounded-full ${
              freelancer.availability === 'Sẵn sàng' ? 'bg-green-400' : 'bg-red-400'
            }`}></span>
            <span className="text-sm font-medium">{freelancer.availability}</span>
          </div>
        </div>

        {/* Portfolio Preview */}
        {freelancer.portfolio && freelancer.portfolio.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2 text-center">Portfolio</h4>
            <div className="grid grid-cols-3 gap-2">
              {freelancer.portfolio.slice(0, 3).map((image, index) => (
                <img
                  key={index}
                  src={image}
                  alt={`Portfolio ${index + 1}`}
                  className="w-full h-20 object-cover rounded-lg hover:opacity-80 transition-opacity cursor-pointer"
                />
              ))}
            </div>
          </div>
        )}

        {/* Badges/Certifications */}
        {freelancer.badges && freelancer.badges.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2 text-center">Chứng chỉ</h4>
            <div className="flex flex-wrap gap-1 justify-center">
              {freelancer.badges.slice(0, 3).map(badge => (
                <span 
                  key={badge} 
                  className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded font-medium"
                >
                  {badge}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button 
            onClick={onContact}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium flex items-center justify-center space-x-2"
          >
            <ChatBubbleLeftIcon className="h-5 w-5" />
            <span>Liên hệ ngay</span>
          </button>
          
          <div className="grid grid-cols-2 gap-3">
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium">
              Xem hồ sơ
            </button>
            <button className="px-4 py-2 border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors duration-200 text-sm font-medium">
              Mời làm việc
            </button>
          </div>
        </div>

        {/* Last Active */}
        {freelancer.lastActive && (
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              Hoạt động lần cuối: {freelancer.lastActive}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FreelancerProfileCard;
