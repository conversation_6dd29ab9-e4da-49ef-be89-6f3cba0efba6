/**
 * Community Categories API Routes
 * PostgreSQL implementation for category management
 */

const express = require('express');
const logger = require('../utils/logger');

const router = express.Router();

// Get PostgreSQL database connection
let db = null;

function getDatabase() {
  if (!db) {
    try {
      const { postgresqlConfig } = require('../config/postgresql');
      db = postgresqlConfig;
    } catch (error) {
      logger.error('Failed to get PostgreSQL in categories routes:', error);
      return null;
    }
  }
  return db;
}

/**
 * Get all categories with post counts
 * GET /api/categories
 */
router.get('/', async (req, res) => {
  try {
    const db = getDatabase();
    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    logger.info('Fetching categories');

    // Get categories with post counts
    const query = `
      SELECT 
        category,
        COUNT(*) as post_count,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_posts
      FROM posts 
      WHERE status = 'published'
      GROUP BY category 
      ORDER BY post_count DESC
    `;

    const result = await db.query(query);
    
    const categories = result.rows.map(row => ({
      name: row.category,
      slug: row.category.toLowerCase().replace(/\s+/g, '-'),
      postCount: parseInt(row.post_count),
      recentPosts: parseInt(row.recent_posts || 0)
    }));

    // Add default categories if none exist
    if (categories.length === 0) {
      const defaultCategories = [
        { name: 'General', slug: 'general', postCount: 0, recentPosts: 0 },
        { name: 'Scam', slug: 'scam', postCount: 0, recentPosts: 0 },
        { name: 'Phishing', slug: 'phishing', postCount: 0, recentPosts: 0 },
        { name: 'Malware', slug: 'malware', postCount: 0, recentPosts: 0 },
        { name: 'Fraud', slug: 'fraud', postCount: 0, recentPosts: 0 },
        { name: 'Security Tips', slug: 'security-tips', postCount: 0, recentPosts: 0 }
      ];
      categories.push(...defaultCategories);
    }

    logger.info('Categories fetched successfully', { count: categories.length });

    res.json({
      categories,
      total: categories.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching categories', { error: error.message });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch categories'
    });
  }
});

/**
 * Get posts by category
 * GET /api/categories/:category/posts
 */
router.get('/:category/posts', async (req, res) => {
  try {
    const { category } = req.params;
    const { page = 1, limit = 20, sortBy = 'created_at' } = req.query;

    const db = getDatabase();
    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    logger.info('Fetching posts by category', { category, page, limit });

    // Build query
    let query = `
      SELECT p.*,
             u.first_name, u.last_name, u.avatar_url,
             COUNT(*) OVER() as total_count
      FROM posts p
      LEFT JOIN users u ON p.author_id = u.id
      WHERE p.status = 'published' AND p.category = $1
    `;

    const queryParams = [category];
    let paramIndex = 2;

    // Add sorting
    const sortColumn = sortBy === 'shares' ? 'share_count' :
                      sortBy === 'upvotes' ? 'upvotes' : 'created_at';
    query += ` ORDER BY p.${sortColumn} DESC`;

    // Add pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(parseInt(limit), offset);

    const result = await db.query(query, queryParams);
    const posts = result.rows;
    const totalCount = posts.length > 0 ? parseInt(posts[0].total_count) : 0;

    res.json({
      category,
      posts: posts.map(post => ({
        id: post.id,
        title: post.title,
        content: post.content,
        postType: post.post_type,
        category: post.category,
        tags: post.tags,
        likes: post.like_count || 0,
        upvotes: post.upvotes,
        downvotes: post.downvotes,
        viewCount: post.view_count,
        commentCount: post.comment_count,
        shareCount: post.share_count,
        isPinned: post.is_pinned,
        isLocked: post.is_locked,
        isFeatured: post.is_featured,
        url: post.url,
        imageUrl: post.image_url,
        createdAt: post.created_at,
        updatedAt: post.updated_at,
        author: {
          id: post.author_id,
          firstName: post.first_name,
          lastName: post.last_name,
          avatarUrl: post.avatar_url
        }
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });

  } catch (error) {
    logger.error('Error fetching posts by category', { error: error.message, category: req.params.category });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch posts by category'
    });
  }
});

/**
 * Get category statistics
 * GET /api/categories/:category/stats
 */
router.get('/:category/stats', async (req, res) => {
  try {
    const { category } = req.params;

    const db = getDatabase();
    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    logger.info('Fetching category statistics', { category });

    const query = `
      SELECT 
        COUNT(*) as total_posts,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_posts,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as monthly_posts,
        AVG(upvotes) as avg_upvotes,
        AVG(view_count) as avg_views,
        AVG(comment_count) as avg_comments,
        SUM(upvotes) as total_upvotes,
        SUM(view_count) as total_views,
        SUM(comment_count) as total_comments
      FROM posts 
      WHERE status = 'published' AND category = $1
    `;

    const result = await db.query(query, [category]);
    const stats = result.rows[0];

    res.json({
      category,
      stats: {
        totalPosts: parseInt(stats.total_posts || 0),
        recentPosts: parseInt(stats.recent_posts || 0),
        monthlyPosts: parseInt(stats.monthly_posts || 0),
        avgUpvotes: parseFloat(stats.avg_upvotes || 0).toFixed(1),
        avgViews: parseFloat(stats.avg_views || 0).toFixed(1),
        avgComments: parseFloat(stats.avg_comments || 0).toFixed(1),
        totalUpvotes: parseInt(stats.total_upvotes || 0),
        totalViews: parseInt(stats.total_views || 0),
        totalComments: parseInt(stats.total_comments || 0)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error fetching category statistics', { error: error.message, category: req.params.category });
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch category statistics'
    });
  }
});

module.exports = router; 