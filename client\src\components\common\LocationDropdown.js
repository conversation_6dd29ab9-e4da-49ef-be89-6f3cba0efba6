import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FaMapMarkerAlt, FaChevronDown, FaSearch } from 'react-icons/fa';
import { useLanguage } from '../../contexts/LanguageContext';

const LocationDropdown = ({ 
  value = { country: '', city: '' }, 
  onChange, 
  placeholder = 'Select location',
  className = '',
  error = null 
}) => {
  const { t, getCountries, getCities, locationData } = useLanguage();
  const [countries, setCountries] = useState([]);
  const [cities, setCities] = useState([]);
  const [isCountryOpen, setIsCountryOpen] = useState(false);
  const [isCityOpen, setIsCityOpen] = useState(false);
  const [countrySearchTerm, setCountrySearchTerm] = useState('');
  const [citySearchTerm, setCitySearchTerm] = useState('');
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [selectedCountryCode, setSelectedCountryCode] = useState('');
  
  // Refs để tracking abort controllers cho async operations
  const citiesAbortController = useRef(null);
  const mountedRef = useRef(true);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (citiesAbortController.current) {
      citiesAbortController.current.abort();
      citiesAbortController.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      cleanup();
    };
  }, [cleanup]);

  // Load countries when locationData changes
  useEffect(() => {
    const loadCountries = async () => {
      if (!mountedRef.current) return;
      
      try {
        setLoadingCountries(true);
        const countriesData = getCountries();
        
        // If countries are not loaded yet from LanguageContext, wait
        if (countriesData.length === 0 && locationData.isLoading) {
          return;
        }
        
        if (mountedRef.current) {
          setCountries(countriesData);
          console.log('🌍 Loaded countries for dropdown:', countriesData.length);
        }
      } catch (error) {
        console.error('Error loading countries:', error);
      } finally {
        if (mountedRef.current) {
          setLoadingCountries(false);
        }
      }
    };

    loadCountries();
  }, [getCountries, locationData.isLoading, locationData.countries]);

  // Load cities when selected country changes
  useEffect(() => {
    const loadCitiesForCountry = async () => {
      if (!mountedRef.current) return;
      
      // Cleanup previous cities request
      cleanup();
      
      // Reset cities và tìm kiếm khi country thay đổi
      setCities([]);
      setCitySearchTerm('');
      
      if (!value.country || countries.length === 0) {
        setSelectedCountryCode('');
        return;
      }

      // Find country code
      const country = countries.find(c => c.name === value.country);
      if (!country) {
        console.warn('🌍 Country not found:', value.country);
        setSelectedCountryCode('');
        return;
      }

      try {
        if (!mountedRef.current) return;
        
        setLoadingCities(true);
        setSelectedCountryCode(country.code);
        
        // Create new abort controller cho request này
        citiesAbortController.current = new AbortController();
        
        const countryCities = await getCities(country.code);
        
        // Check if component is still mounted và request không bị abort
        if (!mountedRef.current || citiesAbortController.current?.signal?.aborted) {
          return;
        }
        
        setCities(countryCities);
        console.log('🏙️ Loaded cities for', country.name, ':', countryCities.length);
      } catch (error) {
        if (error.name !== 'AbortError' && mountedRef.current) {
          console.error('Error loading cities:', error);
          setCities([]);
        }
      } finally {
        if (mountedRef.current) {
          setLoadingCities(false);
        }
      }
    };

    loadCitiesForCountry();
  }, [value.country, countries, getCities, cleanup]);

  const handleCountrySelect = useCallback(async (country) => {
    if (!mountedRef.current) return;
    
    // Update form data - clear city when country changes
    onChange({ 
      country: country.name, 
      city: '' // Always reset city when country changes
    });
    
    setIsCountryOpen(false);
    setCountrySearchTerm('');
    
    // Cities sẽ được load tự động qua useEffect
  }, [onChange]);

  const handleCitySelect = useCallback((city) => {
    if (!mountedRef.current) return;
    
    onChange({ ...value, city });
    setIsCityOpen(false);
    setCitySearchTerm('');
  }, [onChange, value]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.location-dropdown')) {
        setIsCountryOpen(false);
        setIsCityOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter functions
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(countrySearchTerm.toLowerCase())
  );

  const filteredCities = cities.filter(city =>
    city.toLowerCase().includes(citySearchTerm.toLowerCase())
  );

  const getCountryDisplayName = (countryName) => {
    const country = countries.find(c => c.name === countryName);
    return country ? `${country.flag} ${country.name}` : countryName;
  };

  return (
    <div className={`location-dropdown space-y-4 ${className}`}>
      {/* Country Dropdown */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <FaMapMarkerAlt className="inline mr-1" />
          {t('selectCountry', 'Select Country')}
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => {
              setIsCountryOpen(!isCountryOpen);
              setIsCityOpen(false);
            }}
            className={`w-full px-4 py-3 text-left bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow flex items-center justify-between ${
              error?.country ? 'border-red-300' : 'border-gray-200'
            }`}
          >
            <span className={value.country ? 'text-gray-900' : 'text-gray-500'}>
              {value.country ? getCountryDisplayName(value.country) : t('selectCountry', 'Select Country')}
            </span>
            <FaChevronDown className={`text-gray-400 transition-transform ${isCountryOpen ? 'rotate-180' : ''}`} />
          </button>

          {isCountryOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-hidden"
            >
              {/* Search Input */}
              <div className="p-3 border-b border-gray-100">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={countrySearchTerm}
                    onChange={(e) => setCountrySearchTerm(e.target.value)}
                    placeholder={t('searchCountries', 'Search countries...')}
                    className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-medieval-brown-500"
                    autoFocus
                  />
                </div>
              </div>

              {/* Countries List */}
              <div className="max-h-48 overflow-y-auto">
                {loadingCountries ? (
                  <div className="p-4 text-center text-gray-500">
                    {t('loadingCountries', 'Loading countries...')}
                  </div>
                ) : filteredCountries.length > 0 ? (
                  filteredCountries.map((country) => (
                    <button
                      key={country.code}
                      type="button"
                      onClick={() => handleCountrySelect(country)}
                      className="w-full px-4 py-3 text-left hover:bg-medieval-brown-50 flex items-center space-x-3 transition-colors"
                    >
                      <span className="text-lg">{country.flag}</span>
                      <span className="flex-1">{country.name}</span>
                      {country.region && (
                        <span className="text-sm text-gray-500">
                          {country.region}
                        </span>
                      )}
                    </button>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {t('noCountriesFound', 'No countries found')}
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
        {error?.country && (
          <p className="text-red-500 text-sm mt-1">{error.country}</p>
        )}
      </div>

      {/* City Dropdown */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <FaMapMarkerAlt className="inline mr-1" />
          {t('selectCity', 'Select City')}
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => {
              if (value.country) {
                setIsCityOpen(!isCityOpen);
                setIsCountryOpen(false);
              }
            }}
            disabled={!value.country}
            className={`w-full px-4 py-3 text-left bg-white border rounded-lg shadow-sm transition-shadow flex items-center justify-between ${
              !value.country 
                ? 'bg-gray-50 text-gray-400 cursor-not-allowed' 
                : 'hover:shadow-md cursor-pointer'
            } ${
              error?.city ? 'border-red-300' : 'border-gray-200'
            }`}
          >
            <span className={value.city ? 'text-gray-900' : 'text-gray-500'}>
              {loadingCities 
                ? t('loadingCities', 'Loading cities...') 
                : value.city || t('selectCity', 'Select City')
              }
            </span>
            <FaChevronDown className={`text-gray-400 transition-transform ${isCityOpen ? 'rotate-180' : ''}`} />
          </button>

          {isCityOpen && value.country && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-hidden"
            >
              {/* Search Input */}
              <div className="p-3 border-b border-gray-100">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={citySearchTerm}
                    onChange={(e) => setCitySearchTerm(e.target.value)}
                    placeholder={t('searchCities', 'Search cities...')}
                    className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-medieval-brown-500"
                    autoFocus
                  />
                </div>
              </div>

              {/* Cities List */}
              <div className="max-h-48 overflow-y-auto">
                {loadingCities ? (
                  <div className="p-4 text-center text-gray-500">
                    {t('loadingCities', 'Loading cities...')}
                  </div>
                ) : filteredCities.length > 0 ? (
                  filteredCities.map((city, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => handleCitySelect(city)}
                      className="w-full px-4 py-3 text-left hover:bg-medieval-brown-50 transition-colors"
                    >
                      {city}
                    </button>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {t('noCitiesFound', 'No cities available')}
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
        {error?.city && (
          <p className="text-red-500 text-sm mt-1">{error.city}</p>
        )}
      </div>
    </div>
  );
};

export default LocationDropdown;