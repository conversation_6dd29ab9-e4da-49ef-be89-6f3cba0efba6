# Frontend Architecture Guide

## 📁 Cấu trúc Th<PERSON> mục

```
src/
├── components/          # Reusable components
│   ├── apple/          # Apple design system components
│   ├── common/         # Shared components
│   ├── auth/           # Authentication components
│   └── layout/         # Layout components
├── pages/              # Page components
├── contexts/           # React contexts
├── hooks/              # Custom hooks
├── utils/              # Utility functions
├── services/           # API and business logic
└── styles/             # CSS files
```

## 🎨 Theme System

### Modern Design System
- **Modern UI**: Clean and professional design using Tailwind CSS
- **Consistent Styling**: Standardized components and utility classes
- **Responsive Design**: Mobile-first approach with adaptive layouts

### Usage Examples
```javascript
// ✅ Recommended approach
import { Card, Button } from '../components/common';

const MyComponent = () => {
  return (
    <Card>
      <h1 className="text-2xl font-bold text-gray-800">
        Title
      </h1>
      <Button variant="primary">
        Click me
      </Button>
    </Card>
  );
};
```

## 🎬 Animation System

### Animation Controller
- **animations.js**: Centralized animation controller
- **gsapConfig.js**: GSAP configuration
- **useAnimation.js**: Custom hook for animations

### Usage Examples
```javascript
// ✅ Recommended approach
import { useAnimation } from '../hooks/useAnimation';

const MyComponent = () => {
  const { elementRef, animateIn, animateCard } = useAnimation();
  
  useEffect(() => {
    animateIn({ duration: 0.8, stagger: 0.1 });
    animateCard();
  }, []);
  
  return <div ref={elementRef}>Content</div>;
};
```

## 🚫 Anti-patterns to Avoid

### ❌ Don't do this:
```javascript
// Hardcoded styling
className="bg-white text-gray-900 border border-gray-200"

// Direct GSAP usage
import { gsap } from 'gsap';
const tl = gsap.timeline();
```

### ✅ Do this instead:
```javascript
// Use utility classes
className="card"

// Use animation controller
const { animateIn } = useAnimation();
animateIn();
```

## 📋 Best Practices

1. **Always use utility classes** for consistent styling
2. **Use custom hooks** for animations and state management
3. **Import GSAP** only from `gsapConfig.js`
4. **Use common components** for consistent styling
5. **Keep components small** and focused on single responsibility

## 🔧 Migration Guide

### Converting Existing Components

1. **Replace hardcoded dark mode classes**:
```javascript
// Before
className="bg-white dark:bg-gray-900"

// After
className={getCommonClass('pageContainer')}
```

2. **Replace direct GSAP usage**:
```javascript
// Before
import { gsap } from 'gsap';
const tl = gsap.timeline();

// After
import { useAnimation } from '../hooks/useAnimation';
const { animateIn } = useAnimation();
```

3. **Use common components**:
```javascript
// Before
<div className="bg-white border border-gray-200 p-4 rounded-lg">

// After
<Card>
```

## 📊 Performance Considerations

1. **Animation cleanup**: Always cleanup animations in useEffect
2. **Reduced motion**: Respect user preferences
3. **Lazy loading**: Use React.lazy for large components
4. **Memoization**: Use React.memo for expensive components 