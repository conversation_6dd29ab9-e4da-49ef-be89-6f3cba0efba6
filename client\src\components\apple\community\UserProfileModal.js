/**
 * User Profile Modal for updating display name
 */

import React, { useState } from 'react';
import { XMarkIcon, UserIcon } from '@heroicons/react/24/outline';
import { userSyncService } from '../../../services/userSyncService';
import { useAuth } from '../../../contexts/AuthContext';
import { toast } from 'react-hot-toast';

const UserProfileModal = ({ isOpen, onClose }) => {
 const { user } = useAuth();
 const [displayName, setDisplayName] = useState(user?.displayName || '');
 const [isLoading, setIsLoading] = useState(false);

 const handleSubmit = async (e) => {
  e.preventDefault();
  
  if (!displayName.trim()) {
   toast.error('Vui lòng nhập tên hiển thị');
   return;
  }

  setIsLoading(true);
  
  try {
   console.log('🔄 Updating user profile:', displayName);
   
   // Update Firebase profile
   const result = await userSyncService.updateFirebaseProfile(displayName.trim());
   
   if (result && result.success) {
    toast.success('Cập nhật tên hiển thị thành công!');
    onClose();
    
    // Reload page to reflect changes
    setTimeout(() => {
     window.location.reload();
    }, 1000);
   } else {
    toast.error('Có lỗi xảy ra khi cập nhật tên');
   }
  } catch (error) {
   console.error('Error updating profile:', error);
   toast.error('Có lỗi xảy ra khi cập nhật tên');
  } finally {
   setIsLoading(false);
  }
 };

 if (!isOpen) return null;

 return (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
   <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
    <div className="flex items-center justify-between mb-4">
     <h2 className="text-lg font-semibold text-gray-900">
      Cập nhật thông tin cá nhân
     </h2>
     <button
      onClick={onClose}
      className="text-gray-400 hover:text-gray-600"
     >
      <XMarkIcon className="w-6 h-6" />
     </button>
    </div>

    <form onSubmit={handleSubmit}>
     <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
       Tên hiển thị
      </label>
      <div className="relative">
       <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
       <input
        type="text"
        value={displayName}
        onChange={(e) => setDisplayName(e.target.value)}
        placeholder="Nhập tên thật của bạn"
        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900"
        disabled={isLoading}
       />
      </div>
      <p className="text-xs text-gray-500 mt-1">
       Tên này sẽ hiển thị trong các bình luận và bài viết của bạn
      </p>
     </div>

     <div className="mb-4 p-3 bg-blue-50 rounded-lg">
      <p className="text-sm text-blue-700">
       <strong>Thông tin hiện tại:</strong>
      </p>
      <p className="text-sm text-blue-600">
       Email: {user?.email}
      </p>
      <p className="text-sm text-blue-600">
       Tên hiện tại: {user?.displayName || 'Chưa có tên hiển thị'}
      </p>
     </div>

     <div className="flex space-x-3">
      <button
       type="button"
       onClick={onClose}
       className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
       disabled={isLoading}
      >
       Hủy
      </button>
      <button
       type="submit"
       disabled={isLoading || !displayName.trim()}
       className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
       {isLoading ? 'Đang cập nhật...' : 'Cập nhật'}
      </button>
     </div>
    </form>
   </div>
  </div>
 );
};

export default UserProfileModal;
