/**
 * Post Aggregate Repository
 * Handles persistence and retrieval of Post aggregates using Event Sourcing
 */

const PostAggregate = require('../aggregates/PostAggregate');
const EventStoreClient = require('../../utils/eventStoreClient');
const logger = require('../../utils/logger');

class PostRepository {
  constructor(config = {}) {
    this.eventStoreClient = new EventStoreClient({
      serviceName: 'community-service',
      ...config
    });
    
    this.snapshotFrequency = config.snapshotFrequency || 100;
    
    this.stats = {
      aggregatesLoaded: 0,
      aggregatesSaved: 0,
      snapshotsCreated: 0,
      snapshotsLoaded: 0
    };
  }

  /**
   * Get aggregate by ID
   */
  async getById(postId) {
    try {
      // Try to load from snapshot first
      const snapshotResult = await this.loadFromSnapshot(postId);
      let aggregate = snapshotResult.aggregate;
      let fromVersion = snapshotResult.fromVersion;
      
      // Load events since snapshot (or from beginning if no snapshot)
      const streamName = `post-${postId}`;
      const eventsResult = await this.eventStoreClient.readStream(streamName, {
        fromRevision: fromVersion,
        direction: 'forwards',
        maxCount: 1000,
        includeMetadata: true
      });
      
      if (!eventsResult.success) {
        if (eventsResult.error.includes('not found') || eventsResult.events?.length === 0) {
          return null; // Aggregate doesn't exist
        }
        throw new Error(`Failed to load events: ${eventsResult.error}`);
      }
      
      // If no snapshot and no events, aggregate doesn't exist
      if (!aggregate && eventsResult.events.length === 0) {
        return null;
      }
      
      // Rebuild aggregate from events
      if (aggregate) {
        // Apply events since snapshot
        eventsResult.events.forEach(event => {
          aggregate.applyEvent(event, false);
        });
      } else {
        // Rebuild from all events
        aggregate = PostAggregate.fromHistory(eventsResult.events);
      }
      
      this.stats.aggregatesLoaded++;
      logger.debug('Post aggregate loaded successfully', {
        postId,
        version: aggregate.version,
        eventsApplied: eventsResult.events.length,
        fromSnapshot: !!snapshotResult.aggregate
      });
      
      return aggregate;
      
    } catch (error) {
      logger.error('Failed to load post aggregate', {
        postId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Save aggregate
   */
  async save(aggregate) {
    try {
      const postId = aggregate.id;
      const streamName = `post-${postId}`;
      const uncommittedEvents = aggregate.getUncommittedEvents();
      
      if (uncommittedEvents.length === 0) {
        logger.debug('No uncommitted events to save', { postId });
        return { success: true, eventsSaved: 0 };
      }
      
      // Append events to stream
      const appendResults = [];
      for (const event of uncommittedEvents) {
        const result = await this.eventStoreClient.appendToStream(streamName, {
          eventType: event.type,
          eventData: event.data,
          metadata: {
            aggregateId: postId,
            aggregateType: 'Post',
            version: event.version
          }
        });
        
        if (!result.success) {
          throw new Error(`Failed to append event: ${result.error}`);
        }
        
        appendResults.push(result);
      }
      
      // Mark events as committed
      aggregate.markEventsAsCommitted();
      
      // Create snapshot if needed
      if (aggregate.needsSnapshot(this.snapshotFrequency)) {
        await this.createSnapshot(aggregate);
      }
      
      this.stats.aggregatesSaved++;
      logger.debug('Post aggregate saved successfully', {
        postId,
        eventsSaved: uncommittedEvents.length,
        version: aggregate.version
      });
      
      return {
        success: true,
        eventsSaved: uncommittedEvents.length,
        version: aggregate.version
      };
      
    } catch (error) {
      logger.error('Failed to save post aggregate', {
        postId: aggregate.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Create snapshot
   */
  async createSnapshot(aggregate) {
    try {
      const postId = aggregate.id;
      const streamName = `post-${postId}`;
      const snapshot = aggregate.getSnapshot();
      
      const result = await this.eventStoreClient.createSnapshot(streamName, postId, snapshot, aggregate.version);
      
      if (result.success) {
        this.stats.snapshotsCreated++;
        logger.debug('Snapshot created successfully', {
          postId,
          version: aggregate.version
        });
      }
      
      return result;
      
    } catch (error) {
      logger.error('Failed to create snapshot', {
        postId: aggregate.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Load from snapshot
   */
  async loadFromSnapshot(postId) {
    try {
      const streamName = `post-${postId}`;
      const result = await this.eventStoreClient.getLatestSnapshot(streamName, postId);
      
      if (result.success && result.snapshot) {
        this.stats.snapshotsLoaded++;
        const aggregate = PostAggregate.fromSnapshot(result.snapshot);
        
        logger.debug('Snapshot loaded successfully', {
          postId,
          version: result.version
        });
        
        return {
          aggregate,
          fromVersion: result.version
        };
      }
      
      return {
        aggregate: null,
        fromVersion: 0
      };
      
    } catch (error) {
      logger.warn('Failed to load snapshot, starting from beginning', {
        postId,
        error: error.message
      });
      
      return {
        aggregate: null,
        fromVersion: 0
      };
    }
  }

  /**
   * Check if aggregate exists
   */
  async exists(postId) {
    try {
      const aggregate = await this.getById(postId);
      return aggregate !== null;
    } catch (error) {
      logger.error('Failed to check if post exists', {
        postId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Get current version
   */
  async getVersion(postId) {
    try {
      const aggregate = await this.getById(postId);
      return aggregate ? aggregate.version : 0;
    } catch (error) {
      logger.error('Failed to get post version', {
        postId,
        error: error.message
      });
      return 0;
    }
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      ...this.stats,
      snapshotFrequency: this.snapshotFrequency
    };
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      const eventStoreHealth = await this.eventStoreClient.healthCheck();
      
      return {
        status: eventStoreHealth.status,
        message: eventStoreHealth.message,
        eventStore: eventStoreHealth,
        stats: this.getStats()
      };
      
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Repository health check failed: ${error.message}`,
        error: error.message
      };
    }
  }
}

module.exports = PostRepository;
