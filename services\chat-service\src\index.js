const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

// Import local utilities
const { responseMiddleware } = require('./utils');

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'https://frontend-ce4z.onrender.com', 'https://nerafus.com'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Chat Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Chat routes
app.get('/messages', (req, res) => {
  console.log('💬 Get messages request');
  res.json({
    success: true,
    message: 'Messages retrieved successfully',
    data: []
  });
});

app.post('/messages', (req, res) => {
  console.log('📨 Send message request:', req.body);
  res.json({
    success: true,
    message: 'Message sent successfully',
    data: { id: Date.now(), ...req.body }
  });
});

// Chatbot routes
app.post('/chatbot/chat', async (req, res) => {
  console.log('🤖 Chatbot message request:', req.body);
  const { message } = req.body;

  if (!message) {
    return res.status(400).json({
      success: false,
      error: 'Message is required'
    });
  }

  try {
    // Check if Gemini API key is available
    if (!process.env.GEMINI_API_KEY) {
      console.log('⚠️ GEMINI_API_KEY not found, using mock response');
      const mockResponse = {
        id: Date.now(),
        message: `Xin chào! Tôi đã nhận được tin nhắn của bạn: "${message}". Tôi là chatbot hỗ trợ của VWork. Tôi có thể giúp bạn tìm hiểu về các dịch vụ của chúng tôi.`,
        timestamp: new Date().toISOString(),
        role: 'model'
      };

      return res.json({
        success: true,
        message: 'Chatbot response generated successfully (mock)',
        data: mockResponse
      });
    }

    // Create context for VWork platform
    const context = `Bạn là chatbot hỗ trợ khách hàng của VWork - một nền tảng freelancer hiện đại kết nối tài năng với cơ hội.
    VWork cung cấp các dịch vụ:
    - Tìm kiếm và đăng dự án freelance
    - Kết nối freelancer với khách hàng
    - Quản lý công việc và thanh toán
    - Cộng đồng freelancer
    - Hỗ trợ tìm việc làm

    Hãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp bằng tiếng Việt. Nếu được hỏi về thông tin không liên quan đến VWork, hãy lịch sự chuyển hướng về các dịch vụ của VWork.`;

    const prompt = `${context}\n\nCâu hỏi của khách hàng: ${message}`;

    console.log('🤖 Calling Gemini AI...');
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const aiMessage = response.text();

    console.log('✅ Gemini AI response received');

    const botResponse = {
      id: Date.now(),
      message: aiMessage,
      timestamp: new Date().toISOString(),
      role: 'model'
    };

    res.json({
      success: true,
      message: 'Chatbot response generated successfully',
      data: botResponse
    });

  } catch (error) {
    console.error('❌ Gemini AI error:', error);

    // Fallback to mock response if AI fails
    const fallbackResponse = {
      id: Date.now(),
      message: `Xin lỗi, tôi đang gặp sự cố kỹ thuật. Tôi là chatbot hỗ trợ của VWork. Bạn có thể thử lại sau hoặc liên hệ với đội ngũ hỗ trợ của chúng tôi. Câu hỏi của bạn: "${message}"`,
      timestamp: new Date().toISOString(),
      role: 'model'
    };

    res.json({
      success: true,
      message: 'Chatbot response generated successfully (fallback)',
      data: fallbackResponse
    });
  }
});

app.get('/chatbot/history', (req, res) => {
  console.log('🤖 Get chatbot history request');
  res.json({
    success: true,
    message: 'Chatbot history retrieved successfully',
    data: []
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Chat Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
