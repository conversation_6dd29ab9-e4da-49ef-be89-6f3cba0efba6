import React from 'react';

/**
 * ApplePageWrapper - Clean, minimalist page wrapper for Apple-style design
 *
 * Usage:
 * <ApplePageWrapper className="additional-classes">
 *  <YourPageContent />
 * </ApplePageWrapper>
 *
 * Props:
 * - children: Page content
 * - className: Additional CSS classes
 * - variant: 'page' (default) | 'hero' | 'full' | 'gray'
 * - noPadding: disable default padding (default: false)
 */
const ApplePageWrapper = ({
 children,
 className = '',
 variant = 'page',
 noPadding = false,
 ...props
}) => {
 const getBackgroundClass = () => {
  switch (variant) {
   case 'hero':
    return 'bg-gradient-to-br from-blue-50 via-white to-purple-50';
   case 'full':
    return 'bg-white min-h-screen';
   case 'gray':
    return 'bg-gray-50';
   default:
    return 'bg-white';
  }
 };

 const paddingClass = noPadding ? '' : 'pt-20 pb-16';

 return (
  <div
   className={`${getBackgroundClass()} ${paddingClass} ${className}`}
   {...props}
  >
   <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    {children}
   </div>
  </div>
 );
};

export default ApplePageWrapper;
