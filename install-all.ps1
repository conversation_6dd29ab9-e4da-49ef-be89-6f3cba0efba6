# VWork Platform - Install All Dependencies (PowerShell)
# Usage: .\install-all.ps1

param(
    [switch]$Clean,
    [switch]$AuditOnly,
    [switch]$Help
)

# Colors for console output
$Colors = @{
    Reset = "`e[0m"
    Bright = "`e[1m"
    Red = "`e[31m"
    Green = "`e[32m"
    Yellow = "`e[33m"
    Blue = "`e[34m"
    Magenta = "`e[35m"
    Cyan = "`e[36m"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "Reset"
    )
    Write-Host "$($Colors[$Color])$Message$($Colors.Reset)"
}

function Write-Step {
    param([string]$Step, [string]$Message)
    Write-ColorOutput "`n$Step $Message" "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

# Project structure
$Projects = @(
    @{ Name = "Root Project"; Path = "." },
    @{ Name = "Client"; Path = "client" },
    @{ Name = "API Gateway"; Path = "services\api-gateway" },
    @{ Name = "Chat Service"; Path = "services\chat-service" },
    @{ Name = "Community Service"; Path = "services\community-service" },
    @{ Name = "Job Service"; Path = "services\job-service" },
    @{ Name = "Payment Service"; Path = "services\payment-service" },
    @{ Name = "Project Service"; Path = "services\project-service" },
    @{ Name = "Team Service"; Path = "services\team-service" },
    @{ Name = "User Service"; Path = "services\user-service" }
)

function Test-Prerequisites {
    Write-Step "Checking" "prerequisites..."
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $majorVersion = [int]($nodeVersion.TrimStart('v').Split('.')[0])
            if ($majorVersion -ge 18) {
                Write-Success "Node.js version: $nodeVersion"
            } else {
                Write-Error "Node.js version $nodeVersion is too old. Please install Node.js 18+"
                exit 1
            }
        } else {
            Write-Error "Node.js is not installed or not accessible"
            exit 1
        }
    } catch {
        Write-Error "Node.js is not installed or not accessible"
        exit 1
    }
    
    # Check npm
    try {
        $npmVersion = npm --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $majorVersion = [int]($npmVersion.Split('.')[0])
            if ($majorVersion -ge 9) {
                Write-Success "npm version: $npmVersion"
            } else {
                Write-Warning "npm version $npmVersion is old. Consider upgrading to npm 9+"
            }
        } else {
            Write-Error "npm is not installed or not accessible"
            exit 1
        }
    } catch {
        Write-Error "npm is not installed or not accessible"
        exit 1
    }
}

function Install-Dependencies {
    param([string]$ProjectPath, [string]$ProjectName)
    
    $fullPath = Resolve-Path $ProjectPath -ErrorAction SilentlyContinue
    if (-not $fullPath) {
        Write-Warning "Skipping $ProjectName - directory not found: $ProjectPath"
        return $false
    }
    
    $packageJsonPath = Join-Path $fullPath "package.json"
    if (-not (Test-Path $packageJsonPath)) {
        Write-Warning "Skipping $ProjectName - no package.json found"
        return $false
    }
    
    try {
        Write-Step "Installing" "$ProjectName dependencies..."
        
        Push-Location $fullPath
        $result = npm install 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$ProjectName dependencies installed successfully"
            Pop-Location
            return $true
        } else {
            Write-Error "Failed to install $ProjectName dependencies"
            Pop-Location
            return $false
        }
    } catch {
        Write-Error "Failed to install $ProjectName dependencies: $($_.Exception.Message)"
        Pop-Location
        return $false
    }
}

function Test-SecurityAudit {
    param([string]$ProjectPath, [string]$ProjectName)
    
    $fullPath = Resolve-Path $ProjectPath -ErrorAction SilentlyContinue
    if (-not $fullPath) { return }
    
    $packageJsonPath = Join-Path $fullPath "package.json"
    if (-not (Test-Path $packageJsonPath)) { return }
    
    try {
        Push-Location $fullPath
        $auditResult = npm audit --audit-level=high 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$ProjectName - No high/critical vulnerabilities found"
        } else {
            Write-Warning "$ProjectName - Vulnerabilities found. Run 'npm audit fix' to fix them."
        }
        Pop-Location
    } catch {
        Write-Warning "$ProjectName - Vulnerabilities found. Run 'npm audit fix' to fix them."
        Pop-Location
    }
}

function Clean-All {
    Write-Step "Cleaning" "all node_modules and package-lock files..."
    
    foreach ($project in $Projects) {
        $fullPath = Resolve-Path $project.Path -ErrorAction SilentlyContinue
        if ($fullPath) {
            $nodeModulesPath = Join-Path $fullPath "node_modules"
            $packageLockPath = Join-Path $fullPath "package-lock.json"
            
            if (Test-Path $nodeModulesPath) {
                Write-Info "Removing node_modules from $($project.Name)..."
                Remove-Item -Path $nodeModulesPath -Recurse -Force -ErrorAction SilentlyContinue
            }
            
            if (Test-Path $packageLockPath) {
                Write-Info "Removing package-lock.json from $($project.Name)..."
                Remove-Item -Path $packageLockPath -Force -ErrorAction SilentlyContinue
            }
        }
    }
    
    Write-Success "Cleanup completed"
}

# Main execution
if ($Help) {
    Write-ColorOutput "Usage: .\install-all.ps1 [options]" "Bright"
    Write-ColorOutput "Options:" "Bright"
    Write-ColorOutput "  -Clean      Clean all node_modules before installing"
    Write-ColorOutput "  -AuditOnly  Only run security audits"
    Write-ColorOutput "  -Help       Show this help message"
    exit 0
}

Write-ColorOutput "`nVWork Platform - Install All Dependencies (PowerShell)`n" "Bright"

if ($AuditOnly) {
    Write-ColorOutput "Running Security Audits Only`n" "Bright"
    
    foreach ($project in $Projects) {
        Test-SecurityAudit $project.Path $project.Name
    }
    
    Write-ColorOutput "`nSecurity audits completed!`n" "Bright"
    exit 0
}

# Check prerequisites
Test-Prerequisites

# Clean if requested
if ($Clean) {
    Clean-All
}

# Install dependencies
$successCount = 0
$totalCount = 0

foreach ($project in $Projects) {
    $totalCount++
    if (Install-Dependencies $project.Path $project.Name) {
        $successCount++
    }
}

# Run security audits
Write-Step "Running" "security audits..."
foreach ($project in $Projects) {
    Test-SecurityAudit $project.Path $project.Name
}

# Summary
Write-ColorOutput "`nInstallation Summary" "Bright"
Write-ColorOutput "Successfully installed: $successCount/$totalCount projects" "Cyan"

if ($successCount -eq $totalCount) {
    Write-Success "All dependencies installed successfully!"
    Write-Info "You can now run: npm start"
} else {
    Write-Warning "$($totalCount - $successCount) projects failed to install. Check the errors above."
}

Write-ColorOutput "`nSetup Complete!`n" "Bright" 