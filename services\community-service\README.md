# Community Service - Clean & Production Ready

Clean community service với Firebase Auth + PostgreSQL data, chỉ sử dụng like system (không có voting).

## 🚀 Tính năng chính

### ✅ Architecture
- **Firebase Auth** - Authentication từ client, token verification
- **PostgreSQL Database** - Tất cả data operations
- **Simple Like System** - Chỉ like/unlike, không có upvote/downvote
- **Clean API** - RESTful endpoints, consistent responses
- **Production Ready** - Docker, health checks, proper logging

### 📋 Posts Management
- ✅ Tạo, đọc, cập nhật, xóa posts
- ✅ Search và filter posts
- ✅ Pagination với performance optimization
- ✅ Like count tracking
- ✅ Comment count tracking
- ✅ Categories và tags

### 💬 Comments System
- ✅ Nested comments (replies)
- ✅ Comment likes
- ✅ Edit và delete comments
- ✅ Real-time comment count updates
- ✅ Soft delete support

### 👍 Like System
- ✅ Simple like/unlike cho posts và comments
- ✅ Like count tracking
- ✅ User like status checking
- ✅ Optimized like queries

## 🏗️ Architecture

```
Community Service
├── src/
│   ├── routes/
│   │   ├── posts.js          # Posts CRUD operations
│   │   ├── comments.js       # Comments management
│   │   ├── likes.js          # Like system
│   │   └── users.js          # User management
│   ├── config/
│   │   ├── firebase.js       # Firebase Auth only
│   │   └── postgresql.js     # PostgreSQL connection
│   ├── utils/
│   │   ├── logger.js         # Winston logging
│   │   └── response.js       # API response helpers
│   └── middleware/
│       └── errorHandler.js   # Error handling
├── migrations/
│   └── clean_schema.sql      # Database schema
└── app.js                   # Express app setup
```

## 🔧 API Endpoints

### Posts
```
GET    /api/posts             # Get posts with pagination & filters
POST   /api/posts             # Create new post (auth required)
GET    /api/posts/:id         # Get single post
PUT    /api/posts/:id         # Update post (auth required)
DELETE /api/posts/:id         # Delete post (auth required)
POST   /api/posts/:id/share   # Share post (auth required)
```

### Comments
```
GET    /api/comments/:postId  # Get comments for post
POST   /api/comments          # Create comment (auth required)
PUT    /api/comments/:id      # Update comment (auth required)
DELETE /api/comments/:id      # Delete comment (auth required)
```

### Likes
```
POST   /api/likes             # Like/unlike post or comment (auth required)
GET    /api/likes/post/:postId # Get likes for post
GET    /api/likes/comment/:commentId # Get likes for comment
GET    /api/likes/check/:targetType/:targetId # Check if user liked (auth required)
```

### Users
```
GET    /api/users             # Get users with pagination
GET    /api/users/:id         # Get user profile
POST   /api/users             # Create/update user profile
```

## 🗄️ Database Schema (PostgreSQL)

### Users Table
```sql
users (
  id VARCHAR(255) PRIMARY KEY,  -- Firebase UID
  email VARCHAR(255) UNIQUE NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  avatar_url TEXT,
  email_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
```

### Posts Table
```sql
posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  post_type VARCHAR(20) DEFAULT 'discussion',
  category VARCHAR(100) DEFAULT 'general',
  tags JSONB DEFAULT '[]',
  like_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  comment_count INTEGER DEFAULT 0,
  share_count INTEGER DEFAULT 0,
  is_pinned BOOLEAN DEFAULT FALSE,
  is_locked BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) DEFAULT 'published',
  url TEXT,
  image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
```

### Comments Table
```sql
comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  author_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  like_count INTEGER DEFAULT 0,
  reply_count INTEGER DEFAULT 0,
  depth INTEGER DEFAULT 0,
  is_edited BOOLEAN DEFAULT FALSE,
  is_deleted BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) DEFAULT 'published',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
```

### Likes Table
```sql
likes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  target_id VARCHAR(255) NOT NULL,  -- Post UUID or Comment UUID
  target_type VARCHAR(10) NOT NULL, -- 'post' or 'comment'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
```

## 🔧 Environment Variables

### Quick Setup
```bash
# Copy environment template
cp env.example .env

# Edit .env with your settings
nano .env
```

### Required Variables
```bash
# Firebase Auth (Required)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-service-account-email
FIREBASE_PRIVATE_KEY=your-private-key

# Database (Choose one option)
# Option 1: DATABASE_URL (recommended)
DATABASE_URL=postgresql://user:password@host:port/database

# Option 2: Individual variables
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_community_service
DB_USER=vwork_admin
DB_PASSWORD=VWork2024!
```

### Optional Variables
```bash
# Service Configuration
PORT=3006
NODE_ENV=development
SERVICE_HOST=localhost

# Database Pool Settings
DB_POOL_MAX=20
DB_POOL_MIN=2
DB_CONNECTION_TIMEOUT=5000
DB_IDLE_TIMEOUT=30000

# Content Limits
MAX_TITLE_LENGTH=200
MAX_POST_LENGTH=10000
MAX_COMMENT_LENGTH=2000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
CORS_CREDENTIALS=true

# Service URLs (auto-generated if not provided)
API_GATEWAY_URL=http://localhost:8080
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
JOB_SERVICE_URL=http://localhost:3004
CHAT_SERVICE_URL=http://localhost:3005
PAYMENT_SERVICE_URL=http://localhost:3007
EVENT_BUS_SERVICE_URL=http://localhost:3007

# Logging & Security
LOG_LEVEL=debug
LOG_FORMAT=simple
ENABLE_HELMET=true
ENABLE_COMPRESSION=true
TRUST_PROXY=false
```

## 🚀 Quick Start

### 1. Setup Database
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb vwork_community_service

# Run migrations
cd services/community-service
npm run migrate
```

### 2. Install Dependencies
```bash
cd services/community-service
npm install
```

### 3. Configure Environment
```bash
# Copy environment template
cp env.example .env

# Edit .env with your settings
nano .env

# Make sure to set your Firebase credentials:
# FIREBASE_PROJECT_ID=your-project-id
# FIREBASE_CLIENT_EMAIL=your-service-account-email
# FIREBASE_PRIVATE_KEY=your-private-key
```

### 4. Check Configuration
```bash
# Check configuration and validate required variables
npm run config:check

# Check configuration with full details
npm run config:check:full
```

### 5. Start Service
```bash
npm start
```

## 🧪 Testing

### Run Tests
```bash
# Run unit tests
npm test

# Test service endpoints
npm run test:service
```

### Test Coverage
```bash
npm run test:coverage
```

## 📊 Health Check

Service provides health check endpoint:
```
GET /health
```

Response:
```json
{
  "service": "community-service",
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "port": 3006,
  "version": "1.0.0",
  "checks": {
    "database": {
      "status": "healthy",
      "result": {
        "version": "PostgreSQL 14.5",
        "currentTime": "2024-01-01T00:00:00.000Z"
      }
    }
  }
}
```

## 🔒 Security

- **Firebase Auth** - Secure token verification
- **CORS** - Configurable allowed origins
- **Input Validation** - All inputs validated and sanitized
- **SQL Injection Protection** - Parameterized queries
- **Rate Limiting** - Built-in rate limiting
- **Helmet** - Security headers

## 📈 Performance

- **Connection Pooling** - Optimized PostgreSQL connections
- **Indexes** - Proper database indexes for fast queries
- **Pagination** - Efficient pagination for large datasets
- **Caching** - Optional Redis caching (future enhancement)

## 🐳 Docker

### Build Image
```bash
docker build -t community-service .
```

### Run Container
```bash
docker run -p 3006:3006 \
  -e DATABASE_URL=****************************** \
  -e FIREBASE_PROJECT_ID=your-project \
  community-service
```

## 📝 API Documentation

### Authentication
All protected endpoints require Firebase ID token in Authorization header:
```
Authorization: Bearer <firebase-id-token>
```

### Error Responses
```json
{
  "error": "Error message",
  "service": "community-service",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Success Responses
```json
{
  "message": "Success message",
  "data": { ... },
  "service": "community-service",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🔄 Migration from Old Version

If migrating from previous version:

1. **Backup existing data**
2. **Run new migrations**
3. **Update environment variables**
4. **Test thoroughly**

## 📞 Support

For issues and questions:
- Check health endpoint: `/health`
- Review logs for errors
- Test database connection
- Verify Firebase configuration

## 📄 License

This service is part of the VWork platform.
