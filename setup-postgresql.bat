@echo off
echo Setting up PostgreSQL database for VWork...

REM Set PostgreSQL bin directory to PATH
set PATH=%PATH%;"C:\Program Files\PostgreSQL\17\bin"

REM Try to connect with default password (common defaults)
echo Trying to connect to PostgreSQL...

REM Try common default passwords
for %%p in (postgres admin password 123456) do (
    echo Trying password: %%p
    echo %%p | psql -U postgres -c "SELECT 1;" >nul 2>&1
    if !errorlevel! equ 0 (
        echo Successfully connected with password: %%p
        goto :create_database
    )
)

echo Failed to connect with common passwords.
echo Please enter the PostgreSQL postgres user password:
set /p PG_PASSWORD=

:create_database
echo Creating VWork database...

REM Create database
echo %PG_PASSWORD% | psql -U postgres -c "CREATE DATABASE vwork_db;" 2>nul
if %errorlevel% neq 0 (
    echo Database might already exist, continuing...
)

REM Create user
echo %PG_PASSWORD% | psql -U postgres -c "CREATE USER vwork_user WITH PASSWORD 'vwork_password';" 2>nul
if %errorlevel% neq 0 (
    echo User might already exist, continuing...
)

REM Grant privileges
echo %PG_PASSWORD% | psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE vwork_db TO vwork_user;" 2>nul

echo Database setup completed!
echo Database: vwork_db
echo User: vwork_user  
echo Password: vwork_password

pause 