{"name": "community-service", "version": "1.0.0", "description": "community-service microservice", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "db:init": "node scripts/init-postgresql.js", "db:migrate": "psql -f migrations/clean_schema.sql", "db:setup": "npm run db:init && npm run db:migrate", "db:check": "node scripts/check-database-schema.js", "db:fix-production": "node scripts/fix-production-database.js", "db:get-url": "node scripts/get-production-database-url.js", "db:auto-migrate": "node scripts/auto-migrate-on-startup.js", "config:check": "node scripts/check-config.js", "config:check:full": "node scripts/check-config.js --full", "test:service": "node test-service.js", "test:production": "node scripts/test-production-with-db.js", "monitor:production": "node scripts/monitor-production-fix.js"}, "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "firebase-admin": "^13.4.0", "helmet": "^7.1.0", "joi": "^17.13.3", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-pool": "^3.6.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}}