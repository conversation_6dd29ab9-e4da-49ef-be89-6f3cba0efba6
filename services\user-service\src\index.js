/**
 * User Service - Main Application
 * Comprehensive user management service for VWork platform
 * Handles user profiles, authentication, skills, and reputation
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

// Import configurations
const config = require('./config/config');
const database = require('./config/database');
const { initialize: initializeFirebase, healthCheck: firebaseHealthCheck } = require('./config/firebase');

// Import middleware
const { optionalAuth, rateLimit: customRateLimit } = require('./middleware/auth');

// Import routes
const userRoutes = require('./routes/users');
const profileRoutes = require('./routes/profiles');
const skillsRoutes = require('./routes/skills');
const reputationRoutes = require('./routes/reputation');

const app = express();

/**
 * Application initialization
 */
async function initializeApp() {
  try {
    console.log('🚀 Initializing User Service...');
    console.log('📊 Configuration:', config.getAll());

    // Initialize Firebase
    await initializeFirebase();

    // Initialize Database (optional for now)
    try {
      await database.initialize();

      // Run migrations if in development
      if (config.isDevelopment) {
        try {
          await database.runMigrations();
        } catch (error) {
          console.warn('⚠️ Migration failed (continuing anyway):', error.message);
        }
      }
    } catch (error) {
      console.warn('⚠️ Database initialization failed (continuing with mock mode):', error.message);
    }

    console.log('✅ User Service initialization completed');
    
  } catch (error) {
    console.error('❌ Failed to initialize User Service:', error);
    if (config.isProduction) {
      process.exit(1);
    }
  }
}

/**
 * Configure middleware
 */
function setupMiddleware() {
  // Trust proxy if behind load balancer
  if (config.TRUST_PROXY) {
    app.set('trust proxy', 1);
  }

  // Security middleware
  if (config.ENABLE_HELMET) {
    app.use(helmet({
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: false
    }));
  }

  // Compression
  if (config.ENABLE_COMPRESSION) {
    app.use(compression());
  }

  // CORS configuration
  app.use(cors({
    origin: config.ALLOWED_ORIGINS,
    credentials: config.CORS_CREDENTIALS,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'x-auth-token',
      'x-request-id',
      'x-user-agent'
    ]
  }));

  // Rate limiting
  const limiter = rateLimit({
    windowMs: config.RATE_LIMIT_WINDOW,
    max: config.RATE_LIMIT_MAX_REQUESTS,
    message: {
      success: false,
      error: 'Too Many Requests',
      message: `Rate limit exceeded. Max ${config.RATE_LIMIT_MAX_REQUESTS} requests per ${config.RATE_LIMIT_WINDOW / 1000 / 60} minutes`,
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false
  });
  app.use(limiter);

  // Logging
  if (config.LOG_FORMAT !== 'none') {
    app.use(morgan(config.LOG_FORMAT));
  }

  // Body parsing
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Request ID and metadata
  app.use((req, res, next) => {
    req.id = Math.random().toString(36).substring(7);
    req.startTime = Date.now();
    
    res.setHeader('x-service', 'user-service');
    res.setHeader('x-version', config.SERVICE_VERSION);
    res.setHeader('x-request-id', req.id);
    
    next();
  });

  // Response helper middleware
  app.use((req, res, next) => {
    res.success = (data = null, message = 'Success', statusCode = 200) => {
      const responseTime = Date.now() - req.startTime;
      
      res.status(statusCode).json({
        success: true,
        data,
        message,
        meta: {
          requestId: req.id,
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`,
          service: 'user-service',
          version: config.SERVICE_VERSION
        }
      });
    };

    res.error = (message = 'Internal Server Error', statusCode = 500, details = null) => {
      const responseTime = Date.now() - req.startTime;
      
      res.status(statusCode).json({
        success: false,
        error: message,
        code: `HTTP_${statusCode}`,
        details: config.isDevelopment ? details : undefined,
        meta: {
          requestId: req.id,
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`,
          service: 'user-service',
          version: config.SERVICE_VERSION
        }
      });
    };

    next();
  });

  console.log('✅ Middleware configured');
}

/**
 * Setup routes
 */
function setupRoutes() {
  // Health check endpoint
  app.get('/health', async (req, res) => {
    try {
      const dbHealth = await database.healthCheck();
      const firebaseHealth = await firebaseHealthCheck();
      
      const health = {
        status: 'healthy',
        service: 'user-service',
        version: config.SERVICE_VERSION,
        environment: config.NODE_ENV,
        port: config.PORT,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        database: dbHealth,
        firebase: firebaseHealth,
        dependencies: {
          node: process.version,
          platform: process.platform
        }
      };

      // Overall health status
      const isHealthy = dbHealth.status === 'healthy' && firebaseHealth.status === 'healthy';
      
      res.status(isHealthy ? 200 : 503).json(health);
      
    } catch (error) {
      res.status(503).json({
        status: 'error',
        service: 'user-service',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  });

  // API information endpoint
  app.get('/api', (req, res) => {
    res.json({
      service: 'VWork User Service',
      version: config.SERVICE_VERSION,
      description: 'Comprehensive user management service for VWork platform',
      environment: config.NODE_ENV,
      endpoints: {
        health: '/health',
        users: '/api/v1/users',
        profiles: '/api/v1/profiles', 
        skills: '/api/v1/skills',
        reputation: '/api/v1/reputation'
      },
      documentation: 'https://api.vwork.com/docs/user-service',
      timestamp: new Date().toISOString()
    });
  });

  // API v1 routes
  const apiV1 = express.Router();

  // Apply optional authentication to all API routes
  apiV1.use(optionalAuth);

  // Mount route handlers
  apiV1.use('/users', userRoutes);
  apiV1.use('/profiles', profileRoutes);
  apiV1.use('/skills', skillsRoutes);
  apiV1.use('/reputation', reputationRoutes);

  // Mount API v1
  app.use('/api/v1', apiV1);

  // Root redirect
  app.get('/', (req, res) => {
    res.redirect('/api');
  });

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      error: 'Not Found',
      message: `Route ${req.method} ${req.originalUrl} not found`,
      code: 'ROUTE_NOT_FOUND',
      meta: {
        service: 'user-service',
        timestamp: new Date().toISOString()
      }
    });
  });

  console.log('✅ Routes configured');
}

/**
 * Global error handler
 */
function setupErrorHandler() {
  app.use((error, req, res, next) => {
    console.error('❌ Unhandled error:', {
      error: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      requestId: req.id
    });

    const statusCode = error.statusCode || error.status || 500;
    const message = config.isProduction ? 'Internal Server Error' : error.message;

    res.status(statusCode).json({
      success: false,
      error: message,
      code: `HTTP_${statusCode}`,
      details: config.isDevelopment ? {
        stack: error.stack,
        url: req.url,
        method: req.method
      } : undefined,
      meta: {
        requestId: req.id,
        timestamp: new Date().toISOString(),
        service: 'user-service'
      }
    });
  });

  console.log('✅ Error handler configured');
}

/**
 * Graceful shutdown
 */
function setupGracefulShutdown() {
  const gracefulShutdown = async (signal) => {
    console.log(`\n🔄 Received ${signal}, shutting down User Service gracefully...`);
    
    try {
      // Close database connections
      await database.close();
      
      console.log('✅ User Service shutdown completed');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // Nodemon restart
}

/**
 * Start the server
 */
async function startServer() {
  try {
    // Initialize application
    await initializeApp();
    
    // Setup middleware and routes
    setupMiddleware();
    setupRoutes();
    setupErrorHandler();
    setupGracefulShutdown();

    // Start listening
    const server = app.listen(config.PORT, () => {
      console.log('🎉 User Service started successfully!');
      console.log(`📍 Server running on port ${config.PORT}`);
      console.log(`🌍 Environment: ${config.NODE_ENV}`);
      console.log(`🔗 Health check: http://localhost:${config.PORT}/health`);
      console.log(`📚 API docs: http://localhost:${config.PORT}/api`);
      
      if (config.isDevelopment) {
        console.log(`🔧 Development mode - detailed logging enabled`);
      }
    });

    // Handle server errors
    server.on('error', (error) => {
      console.error('❌ Server error:', error);
      process.exit(1);
    });

    return server;

  } catch (error) {
    console.error('❌ Failed to start User Service:', error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { app, startServer };
