import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../contexts/AuthContext';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaDollarSign, FaClock, FaImage, FaUpload } from 'react-icons/fa';

const ProfileDetailsStep = () => {
 const { user } = useAuth();
 const { 
  onboardingData, 
  updateOnboardingData, 
  goToNextStep, 
  goToPreviousStep,
  loading 
 } = useOnboarding();
 const { t } = useLanguage();

 console.log('🔍 ProfileDetailsStep render:', { 
  user: user?.userType, 
  loading, 
  onboardingData: Object.keys(onboardingData || {}) 
 });

 const [formData, setFormData] = useState({
  hourlyRate: user?.userType === 'freelancer' ? 25 : null,
  availability: user?.userType === 'freelancer' ? 'available' : null,
  avatar: user?.photoURL || '',
  experience: '',
  education: '',
  languages: ['Vietnamese', 'English']
 });

 const [errors, setErrors] = useState({});

 useEffect(() => {
  console.log('🔍 useEffect triggered with onboardingData:', onboardingData);
  if (onboardingData.profile) {
   console.log('🔍 Updating formData with profile:', onboardingData.profile);
   setFormData(prev => {
    const newData = {
     ...prev,
     ...onboardingData.profile
    };
    console.log('🔍 New formData:', newData);
    return newData;
   });
  }
 }, [onboardingData]);

 const handleInputChange = (field, value) => {
  setFormData(prev => ({
   ...prev,
   [field]: value
  }));

  if (errors[field]) {
   setErrors(prev => ({
    ...prev,
    [field]: null
   }));
  }
 };

 const validateForm = () => {
  console.log('🔍 Validating form with data:', formData);
  const newErrors = {};

  if (user?.userType === 'freelancer') {
   if (!formData.hourlyRate || formData.hourlyRate < 5) {
    newErrors.hourlyRate = t('profileHourlyRateRequired');
   }

   if (!formData.availability) {
    newErrors.availability = t('profileAvailabilityRequired');
   }

   if (!formData.experience.trim()) {
    newErrors.experience = t('experienceRequired');
   }
  }

  console.log('🔍 Validation errors:', newErrors);
  setErrors(newErrors);
  const isValid = Object.keys(newErrors).length === 0;
  console.log('🔍 Form is valid:', isValid);
  return isValid;
 };

 const handleNext = () => {
  console.log('🔘 Next button clicked!', { loading, formData, errors });
  
  if (validateForm()) {
   console.log('✅ Form validation passed, updating data...');
   updateOnboardingData({
    profile: {
     ...onboardingData.profile,
     ...formData
    }
   });
   console.log('✅ Data updated, going to next step...');
   goToNextStep();
  } else {
   console.log('❌ Form validation failed:', errors);
  }
 };

 const availabilityOptions = [
  { value: 'available', label: t('profileAvailable'), color: 'green' },
  { value: 'busy', label: t('profileBusy'), color: 'yellow' },
  { value: 'unavailable', label: t('profileUnavailable'), color: 'red' }
 ];

 return (
  <div className="p-8 md:p-12">
   {/* Header */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
   >
    <div className="w-16 h-16 bg-gradient-to-br from-medieval-gold-400 to-medieval-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
     <FaImage className="text-2xl text-white" />
    </div>
    
    <h2 className="text-2xl md:text-3xl font-medium font-bold text-gray-800 mb-2">
     {t('profileDetails')}
    </h2>
    
    <p className="text-gray-600 font-medium">
     {user?.userType === 'freelancer' 
      ? t('profileDetailsFreelancerDesc')
      : t('profileDetailsClientDesc')
     }
    </p>
   </motion.div>

   {/* Form */}
   <motion.div
    className="max-w-2xl mx-auto space-y-6"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
   >
    {/* Avatar Upload */}
    <div>
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('profilePhoto')}
     </label>
     <div className="flex items-center space-x-4">
      <div className="w-20 h-20 rounded-full bg-medieval-brown-200 flex items-center justify-center overflow-hidden">
       {formData.avatar ? (
        <img 
         src={formData.avatar} 
         alt="Profile" 
         className="w-full h-full object-cover"
        />
       ) : (
        <FaImage className="text-2xl text-gray-400" />
       )}
      </div>
      <button
       type="button"
       className="btn-secondary px-4 py-2 text-sm font-medium flex items-center"
      >
       <FaUpload className="mr-2" />
       {t('uploadPhoto')}
      </button>
     </div>
    </div>

    {/* Freelancer-specific fields */}
    {user?.userType === 'freelancer' && (
     <>
      {/* Hourly Rate */}
      <div>
       <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
        <FaDollarSign className="inline mr-1" />
        {t('profileHourlyRate')} (USD) *
       </label>
       <input
        type="number"
        min="5"
        max="500"
        value={formData.hourlyRate || ''}
        onChange={(e) => handleInputChange('hourlyRate', parseInt(e.target.value))}
        className={`form-input w-full ${errors.hourlyRate ? 'border-red-500' : ''}`}
        placeholder="25"
       />
       {errors.hourlyRate && (
        <p className="text-red-500 text-sm mt-1">{errors.hourlyRate}</p>
       )}
       <p className="text-gray-500 text-sm mt-1">
        {t('profileHourlyRateHint')}
       </p>
      </div>

      {/* Availability */}
      <div>
       <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
        <FaClock className="inline mr-1" />
        {t('profileAvailability')} *
       </label>
       <div className="grid grid-cols-3 gap-3">
        {availabilityOptions.map((option) => (
         <button
          key={option.value}
          type="button"
          onClick={() => handleInputChange('availability', option.value)}
          className={`p-3 rounded-lg border-2 font-medium text-sm transition-all ${
           formData.availability === option.value
            ? `border-${option.color}-500 bg-${option.color}-50 text-${option.color}-700`
            : 'border-gray-200 hover:border-gray-300'
          }`}
         >
          {option.label}
         </button>
        ))}
       </div>
       {errors.availability && (
        <p className="text-red-500 text-sm mt-1">{errors.availability}</p>
       )}
      </div>

      {/* Experience */}
      <div>
       <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
        {t('experience')} *
       </label>
       <textarea
        value={formData.experience}
        onChange={(e) => handleInputChange('experience', e.target.value)}
        rows={4}
        className={`form-input w-full resize-none ${errors.experience ? 'border-red-500' : ''}`}
        placeholder={t('experiencePlaceholder')}
       />
       {errors.experience && (
        <p className="text-red-500 text-sm mt-1">{errors.experience}</p>
       )}
      </div>
     </>
    )}

    {/* Education (for both types) */}
    <div>
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('education')} ({t('optional')})
     </label>
     <textarea
      value={formData.education}
      onChange={(e) => handleInputChange('education', e.target.value)}
      rows={3}
      className="form-input w-full resize-none"
      placeholder={t('educationPlaceholder')}
     />
    </div>

    {/* Languages */}
    <div>
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('languages')}
     </label>
     <div className="flex flex-wrap gap-2">
      {formData.languages.map((lang, index) => (
       <span
        key={`lang-${lang}-${index}`}
        className="bg-blue-100 text-medieval-gold-800 px-3 py-1 rounded-full text-sm font-medium"
       >
        {lang}
       </span>
      ))}
     </div>
     <p className="text-gray-500 text-sm mt-1">
      {t('languagesHint')}
     </p>
    </div>
   </motion.div>

   {/* Navigation */}
   <motion.div
    className="flex justify-between mt-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.4 }}
    style={{ position: 'relative', zIndex: 999 }}
   >
    <button
     onClick={goToPreviousStep}
     disabled={loading}
     className="btn-secondary px-6 py-2 font-medium"
    >
     {t('previous')}
    </button>

    <button
     onClick={(e) => {
      console.log('🔘 Button clicked!', { loading, disabled: loading });
      e.preventDefault();
      e.stopPropagation();
      handleNext();
     }}
     disabled={loading}
     className="btn-primary px-6 py-2 font-medium"
     style={{ 
      pointerEvents: loading ? 'none' : 'auto',
      position: 'relative',
      zIndex: 1000
     }}
    >
     {loading ? t('processing') : t('next')}
    </button>
   </motion.div>
  </div>
 );
};

export default ProfileDetailsStep;
