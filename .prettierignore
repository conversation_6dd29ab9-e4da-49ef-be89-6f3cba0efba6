# Dependencies
node_modules/
*/node_modules/

# Build outputs
build/
dist/
*.min.js
*.min.css

# Coverage reports
coverage/
*.lcov

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Generated files
*.generated.js
*.generated.ts
*.generated.json

# Package files
*.tgz
*.tar.gz

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/

# Client specific
client/build/
client/public/static/

# Service specific ignores
services/*/build/
services/*/dist/

# Documentation that should maintain formatting
CHANGELOG.md
LICENSE
README.md

# Configuration files that need specific formatting
*.config.js
webpack.config.js
jest.config.js
.eslintrc.js

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Git files
.git/
.gitignore

# Markdown files with specific formatting
docs/api/
*.api.md
