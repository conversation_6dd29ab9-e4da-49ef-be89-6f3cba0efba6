/**
 * Custom React Hooks for GSAP Animations
 * VWork Freelancer Marketplace
 */

import { useEffect, useRef, useLayoutEffect } from 'react';
import { gsap, ScrollTrigger, safeGSAP } from '../utils/gsapConfig';

/**
 * Hook for GSAP context management
 * Automatically cleans up animations on unmount
 */
export const useGSAP = (callback, deps) => {
 const contextRef = useRef();

 useLayoutEffect(() => {
  // Create GSAP context
  contextRef.current = gsap.context(() => {
   callback(gsap);
  });

  // Cleanup function
  return () => {
   if (contextRef.current) {
    contextRef.current.revert();
   }
  };
  // eslint-disable-next-line react-hooks/exhaustive-deps
 }, deps || []);

 // Return context for manual control if needed
 return contextRef.current;
};

/**
 * Hook for scroll-triggered animations
 */
export const useScrollTrigger = (element, config = {}) => {
 const triggerRef = useRef();

 useEffect(() => {
  if (!element) return;

  triggerRef.current = ScrollTrigger.create({
   trigger: element,
   start: 'top 80%',
   end: 'bottom 20%',
   toggleActions: 'play none none reverse',
   ...config,
  });

  return () => {
   if (triggerRef.current) {
    triggerRef.current.kill();
   }
  };
 }, [element, config]);

 return triggerRef.current;
};

/**
 * Hook for motion path animations
 */
export const useMotionPath = (element, path, config = {}) => {
 const animationRef = useRef();

 useEffect(() => {
  if (!element || !path) return;

  animationRef.current = gsap.to(element, {
   motionPath: {
    path,
    autoRotate: false,
    ...config.motionPath,
   },
   duration: 1,
   ease: 'power2.inOut',
   ...config,
  });

  return () => {
   if (animationRef.current) {
    animationRef.current.kill();
   }
  };
 }, [element, path, config]);

 return animationRef.current;
};

/**
 * Hook for stagger animations
 */
export const useStagger = (elements, config = {}) => {
 const timelineRef = useRef();

 useEffect(() => {
  if (!elements || elements.length === 0) return;

  timelineRef.current = gsap.timeline();

  timelineRef.current.fromTo(
   elements,
   {
    opacity: 0,
    y: 30,
    scale: 0.8,
    ...config.from,
   },
   {
    opacity: 1,
    y: 0,
    scale: 1,
    duration: 0.6,
    ease: 'power2.out',
    stagger: {
     amount: 0.8,
     from: 'start',
     ...config.stagger,
    },
    ...config.to,
   }
  );

  return () => {
   if (timelineRef.current) {
    timelineRef.current.kill();
   }
  };
 }, [elements, config]);

 return timelineRef.current;
};

/**
 * Hook for hover animations
 */
export const useHover = (element, enterConfig = {}, leaveConfig = {}) => {
 const enterTween = useRef();
 const leaveTween = useRef();

 useEffect(() => {
  if (!element) return;

  const handleMouseEnter = () => {
   if (leaveTween.current) leaveTween.current.kill();

   enterTween.current = gsap.to(element, {
    scale: 1.05,
    y: -10,
    duration: 0.3,
    ease: 'power2.out',
    ...enterConfig,
   });
  };

  const handleMouseLeave = () => {
   if (enterTween.current) enterTween.current.kill();

   leaveTween.current = gsap.to(element, {
    scale: 1,
    y: 0,
    duration: 0.3,
    ease: 'power2.out',
    ...leaveConfig,
   });
  };

  element.addEventListener('mouseenter', handleMouseEnter);
  element.addEventListener('mouseleave', handleMouseLeave);

  return () => {
   element.removeEventListener('mouseenter', handleMouseEnter);
   element.removeEventListener('mouseleave', handleMouseLeave);

   if (enterTween.current) enterTween.current.kill();
   if (leaveTween.current) leaveTween.current.kill();
  };
 }, [element, enterConfig, leaveConfig]);

 return { enterTween: enterTween.current, leaveTween: leaveTween.current };
};

/**
 * Hook for floating animations
 */
export const useFloat = (element, config = {}) => {
 const animationRef = useRef();

 useEffect(() => {
  if (!element) return;

  animationRef.current = gsap.to(element, {
   y: -10,
   duration: 2,
   ease: 'power1.inOut',
   yoyo: true,
   repeat: -1,
   ...config,
  });

  return () => {
   if (animationRef.current) {
    animationRef.current.kill();
   }
  };
 }, [element, config]);

 return animationRef.current;
};

/**
 * Hook for reveal animations
 */
export const useReveal = (element, config = {}) => {
 useEffect(() => {
  if (!element) return;

  gsap.fromTo(
   element,
   {
    opacity: 0,
    y: 50,
    ...config.from,
   },
   {
    opacity: 1,
    y: 0,
    duration: 0.8,
    ease: 'power2.out',
    scrollTrigger: {
     trigger: element,
     start: 'top 80%',
     toggleActions: 'play none none reverse',
    },
    ...config.to,
   }
  );
 }, [element, config]);
};

export default {
 useGSAP,
 useScrollTrigger,
 useMotionPath,
 useStagger,
 useHover,
 useFloat,
 useReveal,
};
