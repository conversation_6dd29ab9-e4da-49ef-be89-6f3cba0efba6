import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../contexts/AuthContext';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaUser, FaMapMarkerAlt, FaPhone, FaGlobe } from 'react-icons/fa';
import LocationDropdown from '../../common/LocationDropdown';

const BasicInfoStep = () => {
 const { user } = useAuth();
 const { 
  onboardingData, 
  updateOnboardingData, 
  goToNextStep, 
  goToPreviousStep,
  loading 
 } = useOnboarding();
 const { t } = useLanguage();

 const [formData, setFormData] = useState({
  displayName: user?.displayName || user?.name || '',
  bio: '',
  location: {
   country: '',
   city: '',
   timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  },
  phoneNumber: '',
  website: ''
 });

 const [errors, setErrors] = useState({});

 useEffect(() => {
  // Load existing data if available
  if (onboardingData.profile) {
   setFormData(prev => ({
    ...prev,
    ...onboardingData.profile
   }));
  }
 }, [onboardingData]);

 const handleInputChange = (field, value) => {
  if (field.includes('.')) {
   const [parent, child] = field.split('.');
   setFormData(prev => ({
    ...prev,
    [parent]: {
     ...prev[parent],
     [child]: value
    }
   }));
  } else {
   setFormData(prev => ({
    ...prev,
    [field]: value
   }));
  }

  // Clear error when user starts typing
  if (errors[field]) {
   setErrors(prev => ({
    ...prev,
    [field]: null
   }));
  }
 };

 const handleLocationChange = (location) => {
  setFormData(prev => ({
   ...prev,
   location: {
    ...prev.location,
    country: location.country,
    city: location.city
   }
  }));

  // Clear location errors when user makes changes
  if (errors.country || errors.city) {
   setErrors(prev => ({
    ...prev,
    country: null,
    city: null
   }));
  }
 };

 const validateForm = () => {
  const newErrors = {};

  if (!formData.displayName.trim()) {
   newErrors.displayName = t('nameRequired');
  }

  if (!formData.bio.trim()) {
   newErrors.bio = t('bioRequired');
  } else if (formData.bio.length < 50) {
   newErrors.bio = t('bioTooShort', { min: 50 });
  }

  if (!formData.location.country.trim()) {
   newErrors.country = t('countryRequired');
  }

  if (!formData.location.city.trim()) {
   newErrors.city = t('cityRequired');
  }

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
 };

 const handleNext = () => {
  if (validateForm()) {
   updateOnboardingData({
    profile: formData
   });
   goToNextStep();
  }
 };

 return (
  <div className="p-8 md:p-12">
   {/* Header */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
   >
    <div className="w-16 h-16 bg-gradient-to-br from-medieval-gold-400 to-medieval-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
     <FaUser className="text-2xl text-white" />
    </div>
    
    <h2 className="text-2xl md:text-3xl font-medium font-bold text-gray-800 mb-2">
     {t('basicInformation')}
    </h2>
    
    <p className="text-gray-600 font-medium">
     {t('basicInfoDescription')}
    </p>
   </motion.div>

   {/* Form */}
   <motion.div
    className="max-w-2xl mx-auto space-y-6"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
   >
    {/* Display Name */}
    <div>
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('fullName')} *
     </label>
     <input
      type="text"
      value={formData.displayName}
      onChange={(e) => handleInputChange('displayName', e.target.value)}
      className={`form-input w-full ${errors.displayName ? 'border-red-500' : ''}`}
      placeholder={t('enterFullName')}
     />
     {errors.displayName && (
      <p className="text-red-500 text-sm mt-1">{errors.displayName}</p>
     )}
    </div>

    {/* Bio */}
    <div>
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('bio')} *
     </label>
     <textarea
      value={formData.bio}
      onChange={(e) => handleInputChange('bio', e.target.value)}
      rows={4}
      className={`form-input w-full resize-none ${errors.bio ? 'border-red-500' : ''}`}
      placeholder={t('bioPlaceholder')}
     />
     <div className="flex justify-between items-center mt-1">
      {errors.bio && (
       <p className="text-red-500 text-sm">{errors.bio}</p>
      )}
      <p className="text-gray-500 text-sm ml-auto">
       {formData.bio.length}/500
      </p>
     </div>
    </div>

    {/* Location */}
    <LocationDropdown
     value={{
      country: formData.location.country,
      city: formData.location.city
     }}
     onChange={handleLocationChange}
     error={{
      country: errors.country,
      city: errors.city
     }}
    />

    {/* Optional Fields */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
     <div>
      <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
       <FaPhone className="inline mr-1" />
       {t('phoneNumber')} ({t('optional')})
      </label>
      <input
       type="tel"
       value={formData.phoneNumber}
       onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
       className="form-input w-full"
       placeholder={t('enterPhoneNumber')}
      />
     </div>

     <div>
      <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
       <FaGlobe className="inline mr-1" />
       {t('website')} ({t('optional')})
      </label>
      <input
       type="url"
       value={formData.website}
       onChange={(e) => handleInputChange('website', e.target.value)}
       className="form-input w-full"
       placeholder="https://yourwebsite.com"
      />
     </div>
    </div>
   </motion.div>

   {/* Navigation */}
   <motion.div
    className="flex justify-between mt-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.4 }}
   >
    <button
     onClick={goToPreviousStep}
     disabled={loading}
     className="btn-secondary px-6 py-2 font-medium"
    >
     {t('previous')}
    </button>

    <button
     onClick={handleNext}
     disabled={loading}
     className="btn-primary px-6 py-2 font-medium"
     style={{ 
      pointerEvents: loading ? 'none' : 'auto',
      position: 'relative',
      zIndex: 10
     }}
    >
     {loading ? t('processing', 'Đang xử lý...') : t('next')}
    </button>
   </motion.div>
  </div>
 );
};

export default BasicInfoStep;
