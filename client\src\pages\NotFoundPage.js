import React from 'react';
import { Link } from 'react-router-dom';
import { HomeIcon } from '@heroicons/react/24/outline';

const NotFoundPage = () => {
 return (
  <div className='min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8'>
   <div className='max-w-md w-full text-center'>
    <div className='mb-8'>
     <h1 className='text-9xl font-bold text-primary-600'>
      404
     </h1>
     <h2 className='text-2xl font-bold text-gray-900 mt-4'>
      Page Not Found
     </h2>
     <p className='text-gray-600 mt-2'>
      The page you're looking for doesn't exist.
     </p>
    </div>

    <Link
     to='/'
     className='inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200'
    >
     <HomeIcon className='mr-2 h-5 w-5' />
     Go Home
    </Link>
   </div>
  </div>
 );
};

export default NotFoundPage;
