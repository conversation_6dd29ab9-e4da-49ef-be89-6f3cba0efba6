const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { verifyFirebaseToken } = require('../middleware/auth');

// Get PostgreSQL database connection
let db = null;
try {
  const { postgresqlConfig } = require('../config/postgresql');
  db = postgresqlConfig;
} catch (error) {
  logger.error('Failed to initialize PostgreSQL database in likes routes:', error);
}

/**
 * Like/Unlike a post or comment
 * POST /api/likes
 * Body: { targetId, targetType: 'post' | 'comment' }
 */
router.post('/', verifyFirebaseToken, async (req, res) => {
  try {
    const { targetId, targetType } = req.body;
    const userId = req.user?.uid || req.user?.id; // Get Firebase UID from auth middleware

    if (!targetId || !targetType) {
      return res.status(400).json({
        error: 'Missing required fields: targetId, targetType'
      });
    }

    if (!userId) {
      return res.status(401).json({
        error: 'User authentication required'
      });
    }

    if (!['post', 'comment'].includes(targetType)) {
      return res.status(400).json({
        error: 'targetType must be either "post" or "comment"'
      });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if like already exists
    const existingLike = await db.query(
      'SELECT id FROM likes WHERE user_id = $1 AND target_id = $2 AND target_type = $3',
      [userId, targetId, targetType]
    );

    if (existingLike.rows.length > 0) {
      // Unlike - remove the like
      await db.query(
        'DELETE FROM likes WHERE user_id = $1 AND target_id = $2 AND target_type = $3',
        [userId, targetId, targetType]
      );

      // Update like count
      if (targetType === 'post') {
        await db.query('UPDATE posts SET like_count = like_count - 1 WHERE id = $1', [targetId]);
      } else {
        await db.query('UPDATE comments SET like_count = like_count - 1 WHERE id = $1', [targetId]);
      }

      // Get updated like count
      const countQuery = targetType === 'post'
        ? 'SELECT like_count FROM posts WHERE id = $1'
        : 'SELECT like_count FROM comments WHERE id = $1';
      const countResult = await db.query(countQuery, [targetId]);
      const likeCount = countResult.rows[0]?.like_count || 0;

      logger.info('✅ Like removed successfully', { 
        userId: userId.substring(0, 8) + '...', 
        targetId, 
        targetType, 
        newCount: likeCount 
      });

      res.json({
        message: 'Like removed',
        liked: false,
        targetId,
        targetType,
        likeCount
      });
    } else {
      // Like - add the like
      const likeId = uuidv4();

      await db.query(
        'INSERT INTO likes (id, user_id, target_id, target_type) VALUES ($1, $2, $3, $4)',
        [likeId, userId, targetId, targetType]
      );

      // Update like count
      if (targetType === 'post') {
        await db.query('UPDATE posts SET like_count = like_count + 1 WHERE id = $1', [targetId]);
      } else {
        await db.query('UPDATE comments SET like_count = like_count + 1 WHERE id = $1', [targetId]);
      }

      // Get updated like count
      const countQuery = targetType === 'post'
        ? 'SELECT like_count FROM posts WHERE id = $1'
        : 'SELECT like_count FROM comments WHERE id = $1';
      const countResult = await db.query(countQuery, [targetId]);
      const likeCount = countResult.rows[0]?.like_count || 0;

      logger.info('✅ Like added successfully', { 
        userId: userId.substring(0, 8) + '...', 
        targetId, 
        targetType, 
        newCount: likeCount 
      });

      res.json({
        message: 'Like added',
        liked: true,
        targetId,
        targetType,
        likeCount
      });
    }

  } catch (error) {
    logger.error('❌ Error handling like/unlike', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get likes for a post
 * GET /api/likes/post/:postId
 */
router.get('/post/:postId', async (req, res) => {
  try {
    const { postId } = req.params;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    const result = await db.query(
      'SELECT * FROM likes WHERE target_id = $1 AND target_type = $2',
      [postId, 'post']
    );

    res.json({
      postId,
      likes: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    logger.error('❌ Error fetching post likes', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get likes for a comment
 * GET /api/likes/comment/:commentId
 */
router.get('/comment/:commentId', async (req, res) => {
  try {
    const { commentId } = req.params;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    const result = await db.query(
      'SELECT * FROM likes WHERE target_id = $1 AND target_type = $2',
      [commentId, 'comment']
    );

    res.json({
      commentId,
      likes: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    logger.error('❌ Error fetching comment likes', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Check if user liked a specific target
 * GET /api/likes/check/:targetType/:targetId
 */
router.get('/check/:targetType/:targetId', verifyFirebaseToken, async (req, res) => {
  try {
    const { targetType, targetId } = req.params;
    const userId = req.user?.uid || req.user?.id;

    if (!userId) {
      return res.status(401).json({
        error: 'User authentication required'
      });
    }

    if (!['post', 'comment'].includes(targetType)) {
      return res.status(400).json({
        error: 'targetType must be either "post" or "comment"'
      });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    const result = await db.query(
      'SELECT id FROM likes WHERE user_id = $1 AND target_id = $2 AND target_type = $3',
      [userId, targetId, targetType]
    );

    res.json({
      liked: result.rows.length > 0,
      targetId,
      targetType,
      userId: userId.substring(0, 8) + '...' // Partially mask user ID for privacy
    });
  } catch (error) {
    logger.error('❌ Error checking like status', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update likes table schema to use VARCHAR for user_id (Firebase UID)
 * POST /api/likes/update-schema
 */
router.post('/update-schema', async (req, res) => {
  try {
    console.log('🔄 Updating likes table schema for Firebase UID...');

    // Drop and recreate likes table with correct user_id type
    await db.query('DROP TABLE IF EXISTS likes');
    await db.query(`
      CREATE TABLE likes (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id VARCHAR(255) NOT NULL, -- Firebase UID
          target_id VARCHAR(255) NOT NULL, -- Can be post UUID or comment UUID
          target_type VARCHAR(10) NOT NULL, -- 'post' or 'comment'
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          
          -- Constraints
          CONSTRAINT likes_target_type_check CHECK (target_type IN ('post', 'comment')),
          UNIQUE(user_id, target_id, target_type)
      )
    `);

    // Create indexes
    await db.query(`
      CREATE INDEX idx_likes_user_id ON likes(user_id);
      CREATE INDEX idx_likes_target_id ON likes(target_id);
      CREATE INDEX idx_likes_target_type ON likes(target_type);
      CREATE INDEX idx_likes_created_at ON likes(created_at);
    `);

    console.log('✅ Likes table schema updated successfully for Firebase UID!');
    res.json({ message: 'Likes table schema updated successfully for Firebase UID!' });

  } catch (error) {
    console.error('❌ Error updating likes table schema:', error.message);
    res.status(500).json({ error: 'Failed to update likes table schema: ' + error.message });
  }
});

module.exports = router;
