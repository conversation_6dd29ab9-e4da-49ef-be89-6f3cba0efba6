/**
 * Validation Middleware for Team Service
 */

const Joi = require('joi');

// Validation middleware for request body
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    req.body = value;
    next();
  };
};

// Validation middleware for query parameters
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    req.query = value;
    next();
  };
};

// Validation schemas
const schemas = {
  // Pagination schema
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }),

  // Team creation schema
  createTeam: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    slogan: Joi.string().max(200).optional(),
    description: Joi.string().max(1000).optional(),
    logo_url: Joi.string().uri().optional(),
    member_count: Joi.number().integer().min(3).max(10).required(),
    members: Joi.array().items(
      Joi.object({
        user_id: Joi.string().required(),
        position: Joi.string().max(100).required(),
        profit_share: Joi.number().min(0).max(100).required()
      })
    ).min(3).max(10).required()
  }),

  // Team update schema
  updateTeam: Joi.object({
    name: Joi.string().min(2).max(100).optional(),
    slogan: Joi.string().max(200).optional(),
    description: Joi.string().max(1000).optional(),
    logo_url: Joi.string().uri().optional()
  }),

  // Team member schema
  teamMember: Joi.object({
    user_id: Joi.string().required(),
    position: Joi.string().max(100).required(),
    profit_share: Joi.number().min(0).max(100).required()
  }),

  // Profit share update schema
  profitShare: Joi.object({
    members: Joi.array().items(
      Joi.object({
        user_id: Joi.string().required(),
        profit_share: Joi.number().min(0).max(100).required()
      })
    ).min(1).required()
  }),

  // Team invitation schema
  teamInvitation: Joi.object({
    invitee_id: Joi.string().required(),
    message: Joi.string().max(500).optional()
  }),

  // Chat message schema
  chatMessage: Joi.object({
    message: Joi.string().min(1).max(1000).required(),
    message_type: Joi.string().valid('text', 'file', 'image').default('text')
  }),

  // Message reaction schema
  messageReaction: Joi.object({
    reaction: Joi.string().min(1).max(10).required()
  }),

  // Team search schema
  teamSearch: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    skills: Joi.string().optional(),
    location: Joi.string().optional(),
    min_rating: Joi.number().min(0).max(5).optional(),
    max_members: Joi.number().integer().min(1).max(10).optional(),
    category: Joi.string().optional(),
    status: Joi.string().valid('active', 'pending', 'inactive').default('active')
  }),

  // Team project schema
  teamProject: Joi.object({
    project_id: Joi.string().required(),
    status: Joi.string().valid('active', 'completed', 'cancelled').default('active')
  }),

  // Team member removal schema
  removeMember: Joi.object({
    user_id: Joi.string().required(),
    reason: Joi.string().max(500).optional()
  }),

  // Team settings update schema
  teamSettings: Joi.object({
    allow_member_invites: Joi.boolean().optional(),
    require_approval_for_projects: Joi.boolean().optional(),
    auto_accept_invitations: Joi.boolean().optional(),
    notification_preferences: Joi.object({
      email_notifications: Joi.boolean().default(true),
      push_notifications: Joi.boolean().default(true),
      chat_notifications: Joi.boolean().default(true)
    }).optional()
  })
};

module.exports = {
  validateBody,
  validateQuery,
  schemas
}; 