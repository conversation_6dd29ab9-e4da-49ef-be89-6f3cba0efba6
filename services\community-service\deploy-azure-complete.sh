#!/bin/bash

# Complete Azure Deployment Script for Community Service
# Usage: ./deploy-azure-complete.sh [app-name] [resource-group]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
APP_NAME=${1:-"vwork-community-service"}
RESOURCE_GROUP=${2:-"vwork-rg"}

echo -e "${BLUE}🚀 Complete Azure Deployment for Community Service${NC}"
echo -e "${BLUE}================================================${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check Azure CLI
if ! command_exists az; then
    echo -e "${RED}❌ Azure CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check if logged in
if ! az account show &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Azure. Please login first.${NC}"
    az login
fi

# Check if zip is available
if ! command_exists zip; then
    echo -e "${YELLOW}⚠️  zip command not found. Installing...${NC}"
    if command_exists apt-get; then
        sudo apt-get update && sudo apt-get install -y zip
    elif command_exists yum; then
        sudo yum install -y zip
    elif command_exists brew; then
        brew install zip
    else
        echo -e "${RED}❌ Cannot install zip. Please install it manually.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Step 1: Deploy Application
echo -e "${BLUE}📦 Step 1: Deploying Application${NC}"
echo -e "${BLUE}================================${NC}"

# Create resource group
echo -e "${BLUE}📦 Creating resource group: $RESOURCE_GROUP${NC}"
az group create --name $RESOURCE_GROUP --location eastus --output none

# Create App Service Plan
PLAN_NAME="${APP_NAME}-plan"
echo -e "${BLUE}📋 Creating App Service Plan: $PLAN_NAME${NC}"
az appservice plan create \
    --name $PLAN_NAME \
    --resource-group $RESOURCE_GROUP \
    --sku B1 \
    --is-linux \
    --output none

# Create Web App
echo -e "${BLUE}🌐 Creating Web App: $APP_NAME${NC}"
az webapp create \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --plan $PLAN_NAME \
    --runtime "NODE|18-lts" \
    --output none

# Configure basic settings
echo -e "${BLUE}⚙️  Configuring basic settings${NC}"
az webapp config set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --linux-fx-version "NODE|18-lts" \
    --startup-file "npm start" \
    --output none

# Enable logging
az webapp log config \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --web-server-logging filesystem \
    --output none

# Set basic environment variables
echo -e "${BLUE}🔧 Setting basic environment variables${NC}"
az webapp config appsettings set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --settings \
    NODE_ENV=production \
    PORT=8080 \
    WEBSITES_PORT=8080 \
    WEBSITES_CONTAINER_START_TIME_LIMIT=1800 \
    WEBSITES_ENABLE_APP_SERVICE_STORAGE=true \
    --output none

# Create deployment package
echo -e "${BLUE}📦 Creating deployment package${NC}"
DEPLOY_DIR=".azure-deploy"
rm -rf $DEPLOY_DIR
mkdir $DEPLOY_DIR

# Copy necessary files
echo -e "${BLUE}📋 Copying files...${NC}"
cp -r src $DEPLOY_DIR/
cp -r scripts $DEPLOY_DIR/
cp -r migrations $DEPLOY_DIR/
cp app.js $DEPLOY_DIR/
cp package.json $DEPLOY_DIR/
cp package-lock.json $DEPLOY_DIR/
cp web.config $DEPLOY_DIR/

# Create zip file
echo -e "${BLUE}🗜️  Creating zip file...${NC}"
cd $DEPLOY_DIR
zip -r ../azure-deploy.zip . -x "*.git*" "*.DS_Store*" "node_modules/*" "*.log"
cd ..

# Deploy to Azure
echo -e "${BLUE}🚀 Deploying to Azure...${NC}"
az webapp deployment source config-zip \
    --resource-group $RESOURCE_GROUP \
    --name $APP_NAME \
    --src azure-deploy.zip

# Clean up
echo -e "${BLUE}🧹 Cleaning up...${NC}"
rm -rf $DEPLOY_DIR
rm -f azure-deploy.zip

echo -e "${GREEN}✅ Application deployed successfully!${NC}"

# Step 2: Setup Database
echo -e "${BLUE}🗄️  Step 2: Setting up Database${NC}"
echo -e "${BLUE}==============================${NC}"

# Check if openssl is available
if ! command_exists openssl; then
    echo -e "${YELLOW}⚠️  openssl not found. Generating simple password...${NC}"
    DB_PASSWORD="VWork2024!$(date +%s)"
else
    DB_PASSWORD=$(openssl rand -base64 32)
fi

DB_SERVER_NAME="${APP_NAME}-db-server"
DB_NAME="vwork_community_service"
DB_USER="vwork_admin"

# Check if database server already exists
if az postgres flexible-server show --resource-group $RESOURCE_GROUP --name $DB_SERVER_NAME &> /dev/null; then
    echo -e "${YELLOW}⚠️  Database server $DB_SERVER_NAME already exists${NC}"
    DB_HOST=$(az postgres flexible-server show \
        --resource-group $RESOURCE_GROUP \
        --name $DB_SERVER_NAME \
        --query "fullyQualifiedDomainName" \
        --output tsv)
    echo -e "${GREEN}✅ Using existing database server: $DB_HOST${NC}"
else
    # Create PostgreSQL Flexible Server
    echo -e "${BLUE}🗄️  Creating PostgreSQL Flexible Server: $DB_SERVER_NAME${NC}"
    az postgres flexible-server create \
        --name $DB_SERVER_NAME \
        --resource-group $RESOURCE_GROUP \
        --location eastus \
        --admin-user $DB_USER \
        --admin-password "$DB_PASSWORD" \
        --sku-name "Standard_B1ms" \
        --tier "Burstable" \
        --storage-size 32 \
        --version 14 \
        --output none

    echo -e "${GREEN}✅ PostgreSQL server created successfully${NC}"
    
    # Get server hostname
    DB_HOST=$(az postgres flexible-server show \
        --resource-group $RESOURCE_GROUP \
        --name $DB_SERVER_NAME \
        --query "fullyQualifiedDomainName" \
        --output tsv)
fi

# Check if database exists
if az postgres flexible-server db show \
    --resource-group $RESOURCE_GROUP \
    --server-name $DB_SERVER_NAME \
    --database-name $DB_NAME &> /dev/null; then
    echo -e "${YELLOW}⚠️  Database $DB_NAME already exists${NC}"
else
    # Create database
    echo -e "${BLUE}📊 Creating database: $DB_NAME${NC}"
    az postgres flexible-server db create \
        --resource-group $RESOURCE_GROUP \
        --server-name $DB_SERVER_NAME \
        --database-name $DB_NAME \
        --output none

    echo -e "${GREEN}✅ Database created successfully${NC}"
fi

# Configure firewall rules
echo -e "${BLUE}🔥 Configuring firewall rules${NC}"
az postgres flexible-server firewall-rule create \
    --resource-group $RESOURCE_GROUP \
    --name $DB_SERVER_NAME \
    --rule-name "AllowAzureServices" \
    --start-ip-address "0.0.0.0" \
    --end-ip-address "***************" \
    --output none

echo -e "${GREEN}✅ Firewall rules configured${NC}"

# Create connection string
DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:5432/${DB_NAME}?sslmode=require"

# Update App Service with database configuration
echo -e "${BLUE}⚙️  Updating App Service configuration${NC}"
az webapp config appsettings set \
    --name $APP_NAME \
    --resource-group $RESOURCE_GROUP \
    --settings \
    DATABASE_URL="$DATABASE_URL" \
    DB_HOST="$DB_HOST" \
    DB_NAME="$DB_NAME" \
    DB_USER="$DB_USER" \
    DB_PASSWORD="$DB_PASSWORD" \
    --output none

echo -e "${GREEN}✅ Database setup completed!${NC}"

# Step 3: Wait for app to be ready
echo -e "${BLUE}⏳ Step 3: Waiting for app to be ready${NC}"
echo -e "${BLUE}=====================================${NC}"

# Get the app URL
APP_URL=$(az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "defaultHostName" --output tsv)
echo -e "${BLUE}🌐 App URL: https://$APP_URL${NC}"

# Wait for app to start
echo -e "${BLUE}⏳ Waiting for app to start...${NC}"
sleep 60

# Test health endpoint
echo -e "${BLUE}🏥 Testing health endpoint...${NC}"
MAX_RETRIES=10
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "https://$APP_URL/health" || echo "000")
    
    if [ "$HEALTH_RESPONSE" = "200" ]; then
        echo -e "${GREEN}✅ Health check passed${NC}"
        break
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        echo -e "${YELLOW}⚠️  Health check failed (HTTP $HEALTH_RESPONSE). Retrying... ($RETRY_COUNT/$MAX_RETRIES)${NC}"
        sleep 30
    fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo -e "${YELLOW}⚠️  Health check failed after $MAX_RETRIES attempts. Continuing anyway...${NC}"
fi

# Step 4: Run migrations
echo -e "${BLUE}🔄 Step 4: Running Database Migrations${NC}"
echo -e "${BLUE}=====================================${NC}"

echo -e "${YELLOW}⚠️  You need to run migrations manually via SSH:${NC}"
echo -e "${BLUE}   az webapp ssh --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   npm run db:setup${NC}"
echo -e "${BLUE}   exit${NC}"

# Final summary
echo -e "${GREEN}🎉 Complete deployment finished!${NC}"
echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo -e "${BLUE}   App Name: $APP_NAME${NC}"
echo -e "${BLUE}   Resource Group: $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   App URL: https://$APP_URL${NC}"
echo -e "${BLUE}   Database Server: $DB_HOST${NC}"
echo -e "${BLUE}   Database: $DB_NAME${NC}"
echo -e "${BLUE}   Azure Portal: https://portal.azure.com/#@/resource/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$APP_NAME${NC}"

# Show required environment variables
echo -e "${YELLOW}⚠️  REQUIRED: Set these environment variables in Azure Portal:${NC}"
echo -e "${YELLOW}   Configuration → Application settings → New application setting${NC}"
echo -e "${YELLOW}${NC}"
echo -e "${YELLOW}   FIREBASE_PROJECT_ID=your-firebase-project-id${NC}"
echo -e "${YELLOW}   FIREBASE_CLIENT_EMAIL=<EMAIL>${NC}"
echo -e "${YELLOW}   FIREBASE_PRIVATE_KEY=\"-----BEGIN PRIVATE KEY-----\\nYour private key\\n-----END PRIVATE KEY-----\\n\"${NC}"
echo -e "${YELLOW}   ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com${NC}"

# Show next steps
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${BLUE}1. Configure Firebase environment variables in Azure Portal${NC}"
echo -e "${BLUE}2. Run database migrations via SSH${NC}"
echo -e "${BLUE}3. Test the application endpoints${NC}"
echo -e "${BLUE}4. Set up monitoring and alerts${NC}"

# Show useful commands
echo -e "${BLUE}🔧 Useful Commands:${NC}"
echo -e "${BLUE}   View logs: az webapp log tail --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   SSH into app: az webapp ssh --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   Restart app: az webapp restart --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"
echo -e "${BLUE}   View settings: az webapp config appsettings list --name $APP_NAME --resource-group $RESOURCE_GROUP${NC}"

# Show current status
echo -e "${BLUE}📊 Current Status:${NC}"
az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query "{name:name, state:state, defaultHostName:defaultHostName, resourceGroup:resourceGroup}" --output table

echo -e "${GREEN}🎉 Complete deployment script finished!${NC}"
echo -e "${GREEN}📖 For detailed instructions, see: AZURE_DEPLOYMENT_GUIDE.md${NC}" 