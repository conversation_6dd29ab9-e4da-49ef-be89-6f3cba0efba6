const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
 // IMPORTANT: Only proxy in development
 // In production, API calls should go directly to REACT_APP_API_URL
 if (process.env.NODE_ENV === 'development') {
  console.log('🔄 Setting up development proxy for /api');
  app.use(
   '/api',
   createProxyMiddleware({
    target: process.env.REACT_APP_API_URL || 'http://localhost:8080',
    changeOrigin: true,
    secure: false,
    logLevel: 'debug',
    onError: (err, req, res) => {
     console.log('🚨 Proxy Error:', err.message);
    },
    onProxyReq: (proxyReq, req, res) => {
     console.log('🔄 Proxying:', req.method, req.url);
    },
   })
  );
 } else {
  console.log('🌐 Production mode: No proxy, using direct API calls');
 }
};
