/**
 * GSAP Premium Configuration
 * Centralized plugin registration and utilities for VWork platform
 */

import { gsap } from 'gsap';

// Core plugins (available in free version)
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { TextPlugin } from 'gsap/TextPlugin';

// Import marker disabling utility
import './disableScrollTriggerMarkers';

// Premium plugins (Club GreenSock)
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { DrawSVGPlugin } from 'gsap/DrawSVGPlugin';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { PhysicsPropsPlugin } from 'gsap/PhysicsPropsPlugin';
import { ScrambleTextPlugin } from 'gsap/ScrambleTextPlugin';
import { SplitText } from 'gsap/SplitText';
import { CustomEase } from 'gsap/CustomEase';
import { InertiaPlugin } from 'gsap/InertiaPlugin';

// Register all plugins once globally
const registerGSAPPlugins = () => {
 try {
  // Core plugins
  gsap.registerPlugin(ScrollTrigger, TextPlugin);

  // Register CustomEase first to avoid console warnings
  gsap.registerPlugin(CustomEase);

  // Premium plugins (with error handling)
  gsap.registerPlugin(
   MotionPathPlugin,
   MorphSVGPlugin,
   DrawSVGPlugin,
   Physics2DPlugin,
   PhysicsPropsPlugin,
   ScrambleTextPlugin,
   SplitText,
   InertiaPlugin
  );

  console.log('✅ GSAP Premium plugins registered successfully');

  // Set global defaults
  gsap.defaults({
   duration: 0.5,
   ease: 'power2.out',
  });

  // ScrollTrigger global settings
  ScrollTrigger.defaults({
   toggleActions: 'play pause resume reverse',
   markers: false, // Set to true for debugging
  });
 } catch (error) {
  console.warn('⚠️ Some GSAP plugins failed to register:', error.message);
 }
};

// Plugin availability checker
export const checkGSAPPlugins = () => {
 const plugins = {
  ScrollTrigger: !!window.ScrollTrigger || !!gsap.plugins.ScrollTrigger,
  MotionPathPlugin: !!window.MotionPathPlugin || !!gsap.plugins.motionPath,
  MorphSVGPlugin: !!window.MorphSVGPlugin || !!gsap.plugins.morphSVG,
  DrawSVGPlugin: !!window.DrawSVGPlugin || !!gsap.plugins.drawSVG,
  SplitText: !!window.SplitText,
  ScrambleTextPlugin:
   !!window.ScrambleTextPlugin || !!gsap.plugins.scrambleText,
  Physics2DPlugin: !!window.Physics2DPlugin || !!gsap.plugins.physics2D,
  CustomEase: !!window.CustomEase,
  InertiaPlugin: !!window.InertiaPlugin || !!gsap.plugins.inertia,
 };

 console.log('🔍 GSAP Plugin Status:', plugins);
 return plugins;
};

// Enhanced GSAP utilities with null checks
export const safeGSAP = {
 // Safe element selector with validation
 select: (selector, context = document) => {
  try {
   if (!selector) {
    console.warn('🎬 GSAP: Empty selector provided');
    return null;
   }

   if (typeof selector === 'string') {
    const element = context.querySelector(selector);
    if (!element) {
     console.warn(`🎬 GSAP: Element not found for selector: ${selector}`);
    }
    return element;
   }

   // If it's already an element, validate it exists in DOM
   if (selector && selector.nodeType === 1) {
    if (!document.contains(selector)) {
     console.warn('🎬 GSAP: Element not in DOM');
     return null;
    }
    return selector;
   }

   return selector;
  } catch (error) {
   console.error('🎬 GSAP: Error selecting element:', error);
   return null;
  }
 },

 // Safe animation with element validation
 to: (target, vars, position) => {
  const validTarget = safeGSAP.select(target);
  if (!validTarget) {
   console.warn('🎬 GSAP: Skipping animation - invalid target:', target);
   return gsap.timeline(); // Return empty timeline to prevent errors
  }

  try {
   return gsap.to(validTarget, vars, position);
  } catch (error) {
   console.error('🎬 GSAP: Animation error:', error);
   return gsap.timeline();
  }
 },

 // Safe fromTo animation
 fromTo: (target, fromVars, toVars, position) => {
  const validTarget = safeGSAP.select(target);
  if (!validTarget) {
   console.warn('🎬 GSAP: Skipping fromTo animation - invalid target:', target);
   return gsap.timeline();
  }

  try {
   return gsap.fromTo(validTarget, fromVars, toVars, position);
  } catch (error) {
   console.error('🎬 GSAP: FromTo animation error:', error);
   return gsap.timeline();
  }
 },

 // Safe set with validation
 set: (target, vars) => {
  const validTarget = safeGSAP.select(target);
  if (!validTarget) {
   console.warn('🎬 GSAP: Skipping set - invalid target:', target);
   return;
  }

  try {
   return gsap.set(validTarget, vars);
  } catch (error) {
   console.error('🎬 GSAP: Set error:', error);
  }
 },

 // Safe timeline creation
 timeline: (vars) => {
  try {
   return gsap.timeline(vars);
  } catch (error) {
   console.error('🎬 GSAP: Timeline creation error:', error);
   return gsap.timeline(); // Return basic timeline
  }
 },

 // Safe ScrollTrigger creation with forced markers: false
 scrollTrigger: (vars) => {
  if (!ScrollTrigger) {
   console.warn('🎬 GSAP: ScrollTrigger not available');
   return null;
  }

  // Validate trigger element
  if (vars.trigger) {
   const triggerElement = safeGSAP.select(vars.trigger);
   if (!triggerElement) {
    console.warn('🎬 GSAP: ScrollTrigger trigger element not found:', vars.trigger);
    return null;
   }
   vars.trigger = triggerElement;
  }

  // Force markers to false to prevent debug lines
  vars.markers = false;

  try {
   return ScrollTrigger.create(vars);
  } catch (error) {
   console.error('🎬 GSAP: ScrollTrigger creation error:', error);
   return null;
  }
 }
};

// Custom eases for VWork brand
export const VWorkEases = {
 appleBounce: CustomEase.create(
  'custom',
  'M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1'
 ),
 appleSpring: CustomEase.create(
  'spring',
  'M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1'
 ),
 applePop: CustomEase.create(
  'pop',
  'M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1'
 ),
};

// Initialize GSAP
registerGSAPPlugins();

// Export for use in components
export {
 gsap,
 ScrollTrigger,
 MotionPathPlugin,
 MorphSVGPlugin,
 DrawSVGPlugin,
 SplitText,
 ScrambleTextPlugin,
 Physics2DPlugin,
 CustomEase,
 InertiaPlugin,
};

export default registerGSAPPlugins;
