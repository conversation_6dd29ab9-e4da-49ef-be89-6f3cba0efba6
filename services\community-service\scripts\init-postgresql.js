#!/usr/bin/env node

/**
 * PostgreSQL Database Initialization Script
 * Sets up database, runs migrations, and creates initial data
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const config = require('../src/config/config');

class PostgreSQLInitializer {
  constructor() {
    // Database configuration
    this.config = config.DATABASE_URL ? {
      connectionString: config.DATABASE_URL,
      ssl: config.isProduction ? {
        rejectUnauthorized: false
      } : false
    } : {
      host: config.DB_HOST,
      port: config.DB_PORT,
      database: config.DB_NAME,
      user: config.DB_USER,
      password: config.DB_PASSWORD,
      ssl: config.isProduction ? {
        rejectUnauthorized: false
      } : false
    };

    this.pool = null;
  }

  async connect() {
    try {
      this.pool = new Pool(this.config);
      
      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      
      log('✅ Connected to PostgreSQL successfully', 'green');
      return true;
    } catch (error) {
      log(`❌ Failed to connect to PostgreSQL: ${error.message}`, 'red');
      return false;
    }
  }

  async createExtensions() {
    try {
      log('🔧 Creating PostgreSQL extensions...', 'blue');
      
      await this.pool.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
      await this.pool.query('CREATE EXTENSION IF NOT EXISTS "pgcrypto"');
      
      log('✅ Extensions created successfully', 'green');
    } catch (error) {
      log(`❌ Failed to create extensions: ${error.message}`, 'red');
      throw error;
    }
  }

  async runMigrations() {
    try {
      log('📊 Running database migrations...', 'blue');
      
      const migrationPath = path.join(__dirname, '../migrations/20241223120000_initial_schema.sql');
      
      if (!fs.existsSync(migrationPath)) {
        log('⚠️ Migration file not found, skipping...', 'yellow');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await this.pool.query(migrationSQL);
      
      log('✅ Migrations completed successfully', 'green');
    } catch (error) {
      log(`❌ Migration failed: ${error.message}`, 'red');
      throw error;
    }
  }

  async createSampleData() {
    try {
      log('🌱 Creating sample data...', 'blue');
      
      // Check if data already exists
      const result = await this.pool.query('SELECT COUNT(*) FROM posts');
      const postCount = parseInt(result.rows[0].count);
      
      if (postCount > 0) {
        log(`⚠️ Found ${postCount} existing posts, skipping sample data creation`, 'yellow');
        return;
      }
      
      // Create sample user
      const userId = 'sample-user-123';
      await this.pool.query(`
        INSERT INTO users (id, email, first_name, last_name, avatar_url)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (id) DO NOTHING
      `, [userId, '<EMAIL>', 'Demo', 'User', 'https://via.placeholder.com/40']);
      
      // Create sample posts
      const samplePosts = [
        {
          title: 'Welcome to the Community!',
          content: 'This is a sample post to get you started. Feel free to create your own posts and engage with the community.',
          post_type: 'announcement',
          category: 'general'
        },
        {
          title: 'How to Report Phishing Attempts',
          content: 'If you encounter suspicious links or phishing attempts, please report them immediately using our reporting system.',
          post_type: 'security-tip',
          category: 'phishing-alert'
        },
        {
          title: 'Community Guidelines',
          content: 'Please read and follow our community guidelines to maintain a safe and respectful environment for everyone.',
          post_type: 'discussion',
          category: 'general'
        }
      ];
      
      for (const post of samplePosts) {
        await this.pool.query(`
          INSERT INTO posts (id, author_id, title, content, post_type, category, tags)
          VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6)
        `, [userId, post.title, post.content, post.post_type, post.category, JSON.stringify(['sample'])]);
      }
      
      log('✅ Sample data created successfully', 'green');
    } catch (error) {
      log(`❌ Failed to create sample data: ${error.message}`, 'red');
      throw error;
    }
  }

  async verifySetup() {
    try {
      log('🔍 Verifying database setup...', 'blue');
      
      // Check tables exist
      const tables = ['users', 'posts', 'comments', 'votes'];
      for (const table of tables) {
        const result = await this.pool.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          )
        `, [table]);
        
        if (!result.rows[0].exists) {
          throw new Error(`Table '${table}' does not exist`);
        }
      }
      
      // Check data
      const postCount = await this.pool.query('SELECT COUNT(*) FROM posts');
      const userCount = await this.pool.query('SELECT COUNT(*) FROM users');
      
      log(`✅ Database verification successful:`, 'green');
      log(`   - Posts: ${postCount.rows[0].count}`, 'green');
      log(`   - Users: ${userCount.rows[0].count}`, 'green');
      
    } catch (error) {
      log(`❌ Database verification failed: ${error.message}`, 'red');
      throw error;
    }
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
      log('🔌 Database connection closed', 'blue');
    }
  }
}

// Main execution
async function main() {
  const initializer = new PostgreSQLInitializer();
  
  try {
    log('🚀 Starting PostgreSQL initialization...', 'blue');
    
    // Connect to database
    const connected = await initializer.connect();
    if (!connected) {
      process.exit(1);
    }
    
    // Create extensions
    await initializer.createExtensions();
    
    // Run migrations
    await initializer.runMigrations();
    
    // Create sample data
    await initializer.createSampleData();
    
    // Verify setup
    await initializer.verifySetup();
    
    log('🎉 PostgreSQL initialization completed successfully!', 'green');
    
  } catch (error) {
    log(`💥 Initialization failed: ${error.message}`, 'red');
    process.exit(1);
  } finally {
    await initializer.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { PostgreSQLInitializer };
