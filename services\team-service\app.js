/**
 * Team Service - Main Application
 * NERAFUS Team Management Microservice
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');

// Load centralized configuration
const config = require('./src/config/config');

// Initialize Firebase Admin SDK
const { initializeFirebase } = require('./src/config/firebase');

// Initialize PostgreSQL
const { initializePostgreSQL, healthCheck: dbHealthCheck } = require('./src/config/postgresql');

const logger = require('./src/utils/logger');

// Initialize database connection
let dbHealthy = false;

async function initializeDatabase() {
    try {
        // Initialize PostgreSQL
        await initializePostgreSQL();
        dbHealthy = true;
        logger.info('✅ PostgreSQL initialized successfully for Team Service');
        
    } catch (error) {
        logger.error('❌ PostgreSQL initialization failed for Team Service:', error);
        logger.error('🚫 Team Service requires database connection to start');
        dbHealthy = false;
        throw error; // Fail fast - no database, no service
    }
}

const app = express();

// Service configuration
const service = {
    name: config.SERVICE_NAME,
    version: config.SERVICE_VERSION,
    port: config.PORT
};

// CORS configuration
const corsOptions = {
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        // Get allowed origins from centralized config
        const allowedOrigins = config.ALLOWED_ORIGINS;

        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            console.warn(`⚠️ CORS blocked origin: ${origin}`);
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: config.CORS_CREDENTIALS,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Correlation-ID',
        'X-Request-ID',
        'Cache-Control',
        'x-auth-token',
        'Pragma',
        'Expires'
    ],
    exposedHeaders: ['X-Correlation-ID', 'Content-Length', 'X-Requested-With'],
    preflightContinue: false,
    optionsSuccessStatus: 200
};

// Middleware
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors(corsOptions));

// Handle preflight requests explicitly
app.options('*', cors(corsOptions));

// Trust proxy for production
if (config.TRUST_PROXY) {
    app.set('trust proxy', 1);
}

// Compression middleware
if (config.ENABLE_COMPRESSION) {
    app.use(compression());
}

// Logging middleware
app.use(morgan('combined', {
    stream: {
        write: (message) => logger.info(message.trim())
    }
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        const dbStatus = await dbHealthCheck();
        
        res.json({
            status: 'OK',
            service: service.name,
            version: service.version,
            timestamp: new Date().toISOString(),
            environment: config.NODE_ENV,
            database: dbStatus.status,
            port: service.port
        });
    } catch (error) {
        logger.error('Health check failed:', error.message);
        res.status(503).json({
            status: 'UNHEALTHY',
            service: service.name,
            version: service.version,
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Service info endpoint
app.get('/info', (req, res) => {
    res.json({
        service: service.name,
        version: service.version,
        description: 'NERAFUS Team Management Service',
        endpoints: {
            teams: '/api/teams',
            invitations: '/api/invitations',
            chat: '/api/chat',
            members: '/api/members'
        },
        documentation: 'https://github.com/nerafus/team-service'
    });
});

// API routes
app.use('/api/teams', require('./src/routes/teams'));
app.use('/api/invitations', require('./src/routes/invitations'));
app.use('/api/chat', require('./src/routes/chat'));

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        service: service.name
    });
});

// Global error handler
app.use((error, req, res, next) => {
    logger.error('🚨 Team Service Error:', error);
    
    // Handle specific error types
    if (error.code === 'ECONNREFUSED') {
        return res.status(503).json({
            success: false,
            error: 'Service Unavailable',
            message: 'Database connection failed',
            service: service.name
        });
    }
    
    if (error.code === 'ETIMEDOUT') {
        return res.status(504).json({
            success: false,
            error: 'Gateway Timeout',
            message: 'Request timed out',
            service: service.name
        });
    }
    
    res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        message: 'Something went wrong on the server',
        service: service.name,
        details: config.NODE_ENV === 'development' ? error.message : undefined
    });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('🛑 SIGTERM received, shutting down gracefully...');
    process.exit(0);
});

process.on('SIGINT', async () => {
    logger.info('🛑 SIGINT received, shutting down gracefully...');
    process.exit(0);
});

// Unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Uncaught exceptions
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Start server function
async function startServer() {
    try {
        // Initialize Firebase
        initializeFirebase();
        
        // Initialize database
        await initializeDatabase();
        
        // Start server
        app.listen(service.port, () => {
            logger.info(`🚀 ${service.name} v${service.version} running on port ${service.port}`);
            logger.info(`🏥 Health check: http://localhost:${service.port}/health`);
            logger.info(`📊 Service info: http://localhost:${service.port}/info`);
            logger.info(`🔗 API Base URL: http://localhost:${service.port}/api`);
            logger.info(`🌍 Environment: ${config.NODE_ENV}`);
        });
        
    } catch (error) {
        logger.error('❌ Failed to start Team Service:', error);
        process.exit(1);
    }
}

// Start the server
startServer();

module.exports = app; 