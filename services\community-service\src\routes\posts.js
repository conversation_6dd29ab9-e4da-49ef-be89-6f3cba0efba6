/**
 * Community Posts API Routes
 * PostgreSQL implementation for post management
 */

const express = require('express');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { verifyFirebaseToken } = require('../middleware/auth');

const router = express.Router();

// Get PostgreSQL database connection
let db = null;
try {
  const { postgresqlConfig } = require('../config/postgresql');
  db = postgresqlConfig;
} catch (error) {
  logger.error('Failed to initialize PostgreSQL in posts routes:', error);
}

/**
 * Get all posts
 */
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 20, sortBy = 'created_at', category, post_type } = req.query;

    logger.info('Fetching posts', {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      category,
      post_type
    });

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Build query
    let query = `
      SELECT p.*,
             u.first_name, u.last_name, u.avatar_url,
             COUNT(*) OVER() as total_count
      FROM posts p
      LEFT JOIN users u ON p.author_id = u.id
      WHERE p.status = 'published'
    `;

    const queryParams = [];
    let paramIndex = 1;

    // Add filters
    if (category && category !== 'undefined' && category !== 'null' && category !== 'all') {
      query += ` AND p.category = $${paramIndex}`;
      queryParams.push(category);
      paramIndex++;
    }

    if (post_type) {
      query += ` AND p.post_type = $${paramIndex}`;
      queryParams.push(post_type);
      paramIndex++;
    }

    // Add sorting
    const sortColumn = sortBy === 'shares' ? 'share_count' :
                      sortBy === 'upvotes' ? 'upvotes' : 'created_at';
    query += ` ORDER BY p.${sortColumn} DESC`;

    // Add pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(parseInt(limit), offset);

    const result = await db.query(query, queryParams);
    const posts = result.rows;
    const totalCount = posts.length > 0 ? parseInt(posts[0].total_count) : 0;

    res.json({
      posts: posts.map(post => ({
        id: post.id,
        title: post.title,
        content: post.content,
        postType: post.post_type,
        category: post.category,
        tags: post.tags,
        likes: post.like_count || 0,
        upvotes: post.upvotes,
        downvotes: post.downvotes,
        viewCount: post.view_count,
        commentCount: post.comment_count,
        shareCount: post.share_count,
        isPinned: post.is_pinned,
        isLocked: post.is_locked,
        isFeatured: post.is_featured,
        url: post.url,
        imageUrl: post.image_url,
        createdAt: post.created_at,
        updatedAt: post.updated_at,
        author: {
          id: post.author_id,
          firstName: post.first_name,
          lastName: post.last_name,
          avatarUrl: post.avatar_url
        }
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error fetching posts', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create a new post
 */
router.post('/', verifyFirebaseToken, async (req, res) => {
  try {
    const {
      title,
      content,
      postType = 'discussion',
      category = 'general',
      tags = [],
      url,
      imageUrl
    } = req.body;

    // Get authorId from authenticated user
    const authorId = req.user.uid;

    if (!title || !content) {
      return res.status(400).json({
        error: 'Missing required fields: title, content'
      });
    }

    // Validate title length
    if (title.length > (process.env.MAX_TITLE_LENGTH || 200)) {
      return res.status(400).json({
        error: `Title must be ${process.env.MAX_TITLE_LENGTH || 200} characters or less`
      });
    }

    // Validate content length
    if (content.length > (process.env.MAX_POST_LENGTH || 10000)) {
      return res.status(400).json({
        error: `Content must be ${process.env.MAX_POST_LENGTH || 10000} characters or less`
      });
    }

    // Validate post type
    const allowedPostTypes = ['discussion', 'question', 'announcement', 'phishing-alert', 'scam-warning', 'security-tip'];
    if (!allowedPostTypes.includes(postType)) {
      return res.status(400).json({
        error: `Invalid post type. Allowed types: ${allowedPostTypes.join(', ')}`
      });
    }

    if (!db) {
      logger.error('Database not available for post creation');
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if user exists in database, if not create user record
    const userCheckQuery = 'SELECT id FROM users WHERE id = $1';
    const userCheckResult = await db.query(userCheckQuery, [authorId]);
    
    if (userCheckResult.rows.length === 0) {
      // Create user record if not exists
      const createUserQuery = `
        INSERT INTO users (id, first_name, last_name, email, avatar_url)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (id) DO NOTHING
      `;
      
      const displayName = req.user.displayName || req.user.email?.split('@')[0] || 'User';
      const firstName = displayName.split(' ')[0] || displayName;
      const lastName = displayName.split(' ').slice(1).join(' ') || '';
      
      await db.query(createUserQuery, [
        authorId,
        firstName,
        lastName,
        req.user.email || null,
        req.user.photoURL || null
      ]);
    }

    const postId = uuidv4();

    // Insert new post - Use PostgreSQL syntax ($1, $2, etc.)
    const query = `
      INSERT INTO posts (
        id, author_id, title, content, post_type, category, tags, url, image_url
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    await db.query(query, [
      postId, authorId, title, content, postType, category,
      JSON.stringify(tags), url, imageUrl
    ]);

    // Get the created post
    const result = await db.query('SELECT * FROM posts WHERE id = $1', [postId]);

    const newPost = result.rows[0];

    // Get author info
    const authorQuery = `
      SELECT first_name, last_name, avatar_url
      FROM users WHERE id = $1
    `;
    const authorResult = await db.query(authorQuery, [authorId]);
    const author = authorResult.rows[0] || {};

    const responsePost = {
      id: newPost.id,
      title: newPost.title,
      content: newPost.content,
      postType: newPost.post_type,
      category: newPost.category,
      tags: newPost.tags,
      likes: newPost.like_count || 0,
      upvotes: newPost.upvotes,
      downvotes: newPost.downvotes,
      viewCount: newPost.view_count,
      commentCount: newPost.comment_count,
      shareCount: newPost.share_count,
      url: newPost.url,
      imageUrl: newPost.image_url,
      createdAt: newPost.created_at,
      updatedAt: newPost.updated_at,
      author: {
        id: authorId,
        firstName: author.first_name,
        lastName: author.last_name,
        avatarUrl: author.avatar_url
      }
    };

    logger.info('Post created', { postId: responsePost.id, authorId });

    res.status(201).json({
      message: 'Post created successfully',
      post: responsePost
    });
  } catch (error) {
    logger.error('Error creating post', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get a specific post by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Get post with author info - Use PostgreSQL syntax
    const query = `
      SELECT p.*,
             u.first_name, u.last_name, u.avatar_url
      FROM posts p
      LEFT JOIN users u ON p.author_id = u.id
      WHERE p.id = $1 AND p.status = 'published'
    `;

    const result = await db.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Post not found' });
    }

    const post = result.rows[0];

    // Increment view count
    await db.query('UPDATE posts SET view_count = view_count + 1 WHERE id = $1', [id]);

    const responsePost = {
      id: post.id,
      title: post.title,
      content: post.content,
      postType: post.post_type,
      category: post.category,
      tags: post.tags,
      likes: post.like_count || 0,
      upvotes: post.upvotes,
      downvotes: post.downvotes,
      viewCount: post.view_count + 1, // Include the increment
      commentCount: post.comment_count,
      shareCount: post.share_count,
      isPinned: post.is_pinned,
      isLocked: post.is_locked,
      isFeatured: post.is_featured,
      url: post.url,
      imageUrl: post.image_url,
      createdAt: post.created_at,
      updatedAt: post.updated_at,
      author: {
        id: post.author_id,
        firstName: post.first_name,
        lastName: post.last_name,
        avatarUrl: post.avatar_url
      }
    };

    res.json({ post: responsePost });
  } catch (error) {
    logger.error('Error fetching post', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update a post
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, authorId } = req.body;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if post exists and user is the author
    const postResult = await db.query(
      'SELECT * FROM posts WHERE id = $1 AND status = $2',
      [id, 'published']
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ error: 'Post not found' });
    }

    const post = postResult.rows[0];

    // Check if user is the author
    if (post.author_id !== authorId) {
      return res.status(403).json({ error: 'Not authorized to update this post' });
    }

    // Build update query
    const updates = [];
    const params = [];

    if (title) {
      updates.push('title = ?');
      params.push(title);
    }
    if (content) {
      updates.push('content = ?');
      params.push(content);
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    // Update post
    await db.query(
      `UPDATE posts SET ${updates.join(', ')} WHERE id = $${params.length}`,
      params
    );

    // Get updated post
    const updatedResult = await db.query(
      `SELECT p.*, u.first_name, u.last_name, u.avatar_url
       FROM posts p
       LEFT JOIN users u ON p.author_id = u.id
       WHERE p.id = $1`,
      [id]
    );

    const updatedPost = updatedResult.rows[0];

    logger.info('Post updated', { postId: id, authorId });

    res.json({
      message: 'Post updated successfully',
      post: {
        id: updatedPost.id,
        title: updatedPost.title,
        content: updatedPost.content,
        postType: updatedPost.post_type,
        category: updatedPost.category,
        tags: updatedPost.tags,
        likes: updatedPost.like_count || 0,
        upvotes: updatedPost.upvotes,
        downvotes: updatedPost.downvotes,
        viewCount: updatedPost.view_count,
        commentCount: updatedPost.comment_count,
        shareCount: updatedPost.share_count,
        url: updatedPost.url,
        imageUrl: updatedPost.image_url,
        createdAt: updatedPost.created_at,
        updatedAt: updatedPost.updated_at,
        author: {
          id: updatedPost.author_id,
          firstName: updatedPost.first_name,
          lastName: updatedPost.last_name,
          avatarUrl: updatedPost.avatar_url
        }
      }
    });
  } catch (error) {
    logger.error('Error updating post', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Delete a post (soft delete)
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { authorId } = req.body;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if post exists and user is the author
    const postResult = await db.query(
      'SELECT * FROM posts WHERE id = $1 AND status = $2',
      [id, 'published']
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ error: 'Post not found' });
    }

    const post = postResult.rows[0];

    // Check if user is the author
    if (post.author_id !== authorId) {
      return res.status(403).json({ error: 'Not authorized to delete this post' });
    }

    // Soft delete post
    await db.query(
      'UPDATE posts SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      ['deleted', id]
    );

    logger.info('Post deleted', { postId: id, authorId });

    res.json({ message: 'Post deleted successfully' });
  } catch (error) {
    logger.error('Error deleting post', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Share a post
 */
router.post('/:id/share', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, userName } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'Missing required field: userId' });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if post exists
    const postResult = await db.query(
      'SELECT * FROM posts WHERE id = $1 AND status = $2',
      [id, 'published']
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Check if user already shared this post
    const existingShare = await db.query(
      'SELECT id FROM shares WHERE user_id = $1 AND post_id = $2',
      [userId, id]
    );

    if (existingShare.rows.length > 0) {
      return res.status(400).json({ error: 'Post already shared by this user' });
    }

    // Create share record
    const shareId = uuidv4();
    await db.query(
      'INSERT INTO shares (id, user_id, post_id, user_name) VALUES ($1, $2, $3, $4)',
      [shareId, userId, id, userName || 'Anonymous']
    );

    // Update post share count
    await db.query(
      'UPDATE posts SET share_count = share_count + 1 WHERE id = $1',
      [id]
    );

    // Get updated share count
    const updatedPost = await db.query(
      'SELECT share_count FROM posts WHERE id = $1',
      [id]
    );

    const shareCount = updatedPost.rows[0].share_count;

    logger.info('Post shared', { postId: id, userId });

    res.json({
      message: 'Post shared successfully',
      share: {
        id: shareId,
        postId: id,
        userId,
        userName: userName || 'Anonymous',
        createdAt: new Date().toISOString()
      },
      shareCount
    });
  } catch (error) {
    logger.error('Error sharing post', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get shares for a post
 */
router.get('/:id/shares', async (req, res) => {
  try {
    const { id } = req.params;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if post exists
    const postResult = await db.query(
      'SELECT id FROM posts WHERE id = $1 AND status = $2',
      [id, 'published']
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Get shares for the post
    const sharesResult = await db.query(
      `SELECT s.*, u.first_name, u.last_name, u.avatar_url
       FROM shares s
       LEFT JOIN users u ON s.user_id = u.id
       WHERE s.post_id = $1
       ORDER BY s.created_at DESC`,
      [id]
    );

    const shares = sharesResult.rows.map(share => ({
      id: share.id,
      postId: share.post_id,
      userId: share.user_id,
      userName: share.user_name,
      createdAt: share.created_at,
      user: {
        id: share.user_id,
        firstName: share.first_name,
        lastName: share.last_name,
        avatarUrl: share.avatar_url
      }
    }));

    res.json({
      postId: id,
      shares,
      count: shares.length
    });
  } catch (error) {
    logger.error('Error fetching post shares', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get comments for a post
 */
router.get('/:id/comments', async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20, sortBy = 'created_at', order = 'desc' } = req.query;

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if post exists - Use PostgreSQL syntax
    const postResult = await db.query(
      'SELECT id FROM posts WHERE id = $1 AND status = $2',
      [id, 'published']
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Get comments with author info
    const query = `
      SELECT c.*,
             u.first_name, u.last_name, u.avatar_url, u.username,
             COUNT(*) OVER() as total_count
      FROM comments c
      LEFT JOIN users u ON c.author_id = u.id
      WHERE c.post_id = $1 AND c.is_deleted = FALSE
      ORDER BY ${sortBy} ${order.toUpperCase()}
      LIMIT $2 OFFSET $3
    `;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const result = await db.query(query, [id, parseInt(limit), offset]);

    const comments = result.rows.map(comment => ({
      id: comment.id,
      postId: comment.post_id,
      content: comment.content,
      likes: comment.like_count || comment.upvotes || 0,
      upvotes: comment.upvotes,
      downvotes: comment.downvotes,
      depth: comment.depth,
      parentId: comment.parent_id,
      isEdited: comment.is_edited,
      editReason: comment.edit_reason,
      createdAt: comment.created_at,
      updatedAt: comment.updated_at,
      author: {
        id: comment.author_id,
        firstName: comment.first_name,
        lastName: comment.last_name,
        username: comment.username,
        avatarUrl: comment.avatar_url
      }
    }));

    const totalCount = result.rows.length > 0 ? result.rows[0].total_count : 0;

    res.json({
      comments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error fetching comments', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Add comment to a post
 */
router.post('/:id/comments', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, authorId, parentId = null } = req.body;

    if (!content || !authorId) {
      return res.status(400).json({
        error: 'Missing required fields: content, authorId'
      });
    }

    if (!db) {
      return res.status(500).json({ error: 'Database not available' });
    }

    // Check if post exists
    const postResult = await db.query(
      'SELECT id FROM posts WHERE id = $1 AND status = $2',
      [id, 'published']
    );

    if (postResult.rows.length === 0) {
      return res.status(404).json({ error: 'Post not found' });
    }

    const commentId = uuidv4();
    let depth = 0;

    // Calculate depth if this is a reply
    if (parentId) {
      const parentResult = await db.query('SELECT depth FROM comments WHERE id = $1', [parentId]);
      if (parentResult.rows.length > 0) {
        depth = parentResult.rows[0].depth + 1;
      }
    }

    // Insert new comment
    const query = `
      INSERT INTO comments (id, post_id, author_id, parent_id, content, depth)
      VALUES ($1, $2, $3, $4, $5, $6)
    `;

    await db.query(query, [commentId, id, authorId, parentId, content, depth]);

    // Update comment count on post
    await db.query('UPDATE posts SET comment_count = comment_count + 1 WHERE id = $1', [id]);

    // Get the created comment with author info
    const commentResult = await db.query(`
      SELECT c.*,
             u.first_name, u.last_name, u.avatar_url, u.username
      FROM comments c
      LEFT JOIN users u ON c.author_id = u.id
      WHERE c.id = $1
    `, [commentId]);

    const comment = commentResult.rows[0];
    const responseComment = {
      id: comment.id,
      postId: comment.post_id,
      content: comment.content,
      likes: comment.like_count || comment.upvotes || 0,
      upvotes: comment.upvotes,
      downvotes: comment.downvotes,
      depth: comment.depth,
      parentId: comment.parent_id,
      createdAt: comment.created_at,
      updatedAt: comment.updated_at,
      author: {
        id: comment.author_id,
        firstName: comment.first_name,
        lastName: comment.last_name,
        username: comment.username,
        avatarUrl: comment.avatar_url
      }
    };

    logger.info('Comment created', { commentId, postId: id, authorId });

    res.status(201).json({
      message: 'Comment created successfully',
      comment: responseComment
    });
  } catch (error) {
    logger.error('Error creating comment', { error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
