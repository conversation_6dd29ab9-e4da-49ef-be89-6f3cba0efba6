/**
 * Login Modal Component
 * Shows login form when unauthenticated users try to interact with community posts
 */

import { useState, useEffect, Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { 
 XMarkIcon, 
 UserIcon, 
 LockClosedIcon,
 EyeIcon,
 EyeSlashIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { toast } from 'react-hot-toast';

const LoginModal = () => {
 const [isOpen, setIsOpen] = useState(false);
 const [loginData, setLoginData] = useState({ email: '', password: '' });
 const [showPassword, setShowPassword] = useState(false);
 const [isLoading, setIsLoading] = useState(false);
 const [modalContext, setModalContext] = useState(null);

 const { login, loginWithGoogle } = useAuth();
 const { t } = useLanguage();

 // Listen for custom events to show modal
 useEffect(() => {
  const handleShowLoginModal = (event) => {
   const { action, message, returnUrl } = event.detail || {};
   setModalContext({ action, message, returnUrl });
   setIsOpen(true);
  };

  window.addEventListener('show-login-modal', handleShowLoginModal);
  return () => window.removeEventListener('show-login-modal', handleShowLoginModal);
 }, []);

 const handleClose = () => {
  setIsOpen(false);
  setLoginData({ email: '', password: '' });
  setShowPassword(false);
  setModalContext(null);
 };

 const handleInputChange = (e) => {
  const { name, value } = e.target;
  setLoginData(prev => ({ ...prev, [name]: value }));
 };

 const handleLogin = async (e) => {
  e.preventDefault();
  
  if (!loginData.email || !loginData.password) {
   toast.error('Vui lòng nhập đầy đủ email và mật khẩu');
   return;
  }

  setIsLoading(true);
  try {
   const result = await login(loginData.email, loginData.password);
   
   if (result.success) {
    toast.success('Đăng nhập thành công!');
    handleClose();
    
    // Reload page to refresh authentication state
    setTimeout(() => {
     window.location.reload();
    }, 1000);
   } else {
    if (result.needsEmailVerification) {
     toast.error('Vui lòng xác thực email trước khi đăng nhập');
    } else {
     toast.error(result.error || 'Đăng nhập thất bại');
    }
   }
  } catch (error) {
   console.error('Login error:', error);
   toast.error('Có lỗi xảy ra khi đăng nhập');
  } finally {
   setIsLoading(false);
  }
 };

 const handleGoogleLogin = async () => {
  setIsLoading(true);
  try {
   const result = await loginWithGoogle();
   
   if (result.success) {
    toast.success('Đăng nhập Google thành công!');
    handleClose();
    
    // Reload page to refresh authentication state
    setTimeout(() => {
     window.location.reload();
    }, 1000);
   } else {
    toast.error(result.error || 'Đăng nhập Google thất bại');
   }
  } catch (error) {
   console.error('Google login error:', error);
   toast.error('Có lỗi xảy ra khi đăng nhập Google');
  } finally {
   setIsLoading(false);
  }
 };

 const getActionMessage = () => {
  if (!modalContext?.action) return 'Đăng nhập để tiếp tục';
  
  const actionMessages = {
   like: 'Đăng nhập để thích bài viết',
   dislike: 'Đăng nhập để không thích bài viết',
   comment: 'Đăng nhập để bình luận',
   share: 'Đăng nhập để chia sẻ',
   bookmark: 'Đăng nhập để lưu bài viết',
   react: 'Đăng nhập để thả cảm xúc',
   follow: 'Đăng nhập để theo dõi',
   vote: 'Đăng nhập để bình chọn',
   'create-post': 'Đăng nhập để tạo bài viết'
  };

  return actionMessages[modalContext.action] || modalContext.message || 'Đăng nhập để tiếp tục';
 };

 return (
  <Transition appear show={isOpen} as={Fragment}>
   <Dialog as="div" className="relative z-50" onClose={handleClose}>
    <Transition.Child
     as={Fragment}
     enter="ease-out duration-300"
     enterFrom="opacity-0"
     enterTo="opacity-100"
     leave="ease-in duration-200"
     leaveFrom="opacity-100"
     leaveTo="opacity-0"
    >
     <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
    </Transition.Child>

    <div className="fixed inset-0 overflow-y-auto">
     <div className="flex min-h-full items-center justify-center p-4 text-center">
      <Transition.Child
       as={Fragment}
       enter="ease-out duration-300"
       enterFrom="opacity-0 scale-95"
       enterTo="opacity-100 scale-100"
       leave="ease-in duration-200"
       leaveFrom="opacity-100 scale-100"
       leaveTo="opacity-0 scale-95"
      >
       <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
         <Dialog.Title
          as="h3"
          className="text-lg font-medium leading-6 text-gray-900"
         >
          Đăng Nhập
         </Dialog.Title>
         <button
          onClick={handleClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
         >
          <XMarkIcon className="h-6 w-6" />
         </button>
        </div>

        {/* Context Message */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
         <p className="text-sm text-blue-800 text-center">
          {getActionMessage()}
         </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleLogin} className="space-y-4">
         {/* Email Input */}
         <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
           Email
          </label>
          <div className="relative">
           <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <UserIcon className="h-5 w-5 text-gray-400" />
           </div>
           <input
            type="email"
            name="email"
            value={loginData.email}
            onChange={handleInputChange}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Nhập email của bạn"
            required
           />
          </div>
         </div>

         {/* Password Input */}
         <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
           Mật khẩu
          </label>
          <div className="relative">
           <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <LockClosedIcon className="h-5 w-5 text-gray-400" />
           </div>
           <input
            type={showPassword ? 'text' : 'password'}
            name="password"
            value={loginData.password}
            onChange={handleInputChange}
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Nhập mật khẩu"
            required
           />
           <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
           >
            {showPassword ? (
             <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            ) : (
             <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            )}
           </button>
          </div>
         </div>

         {/* Login Button */}
         <button
          type="submit"
          disabled={isLoading}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
         >
          {isLoading ? (
           <div className="flex items-center">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            Đang đăng nhập...
           </div>
          ) : (
           'Đăng Nhập'
          )}
         </button>
        </form>

        {/* Divider */}
        <div className="my-6">
         <div className="relative">
          <div className="absolute inset-0 flex items-center">
           <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
           <span className="px-2 bg-white text-gray-500">
            Hoặc
           </span>
          </div>
         </div>
        </div>

        {/* Google Login */}
        <button
         onClick={handleGoogleLogin}
         disabled={isLoading}
         className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
         <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
         </svg>
         Đăng nhập với Google
        </button>

        {/* Register Link */}
        <div className="mt-6 text-center">
         <p className="text-sm text-gray-600">
          Chưa có tài khoản?{' '}
          <button
           onClick={() => {
            handleClose();
            window.location.href = '/auth?mode=register';
           }}
           className="font-medium text-blue-600 hover:text-blue-500"
          >
           Đăng ký ngay
          </button>
         </p>
        </div>
       </Dialog.Panel>
      </Transition.Child>
     </div>
    </div>
   </Dialog>
  </Transition>
 );
};

export default LoginModal;
