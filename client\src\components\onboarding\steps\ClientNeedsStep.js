import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../contexts/AuthContext';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaProjectDiagram, FaClock, FaUserTie, FaArrowRight } from 'react-icons/fa';

const ClientNeedsStep = () => {
 const { user } = useAuth();
 const { 
  onboardingData, 
  updateOnboardingData, 
  goToNextStep, 
  goToPreviousStep,
  loading 
 } = useOnboarding();
 const { t } = useLanguage();

 const [selectedNeeds, setSelectedNeeds] = useState([]);
 const [errors, setErrors] = useState({});

 useEffect(() => {
  if (onboardingData.clientNeeds) {
   setSelectedNeeds(onboardingData.clientNeeds);
  }
 }, [onboardingData]);

 const needsOptions = [
  {
   id: 'project_based',
   icon: FaProjectDiagram,
   title: t('projectBased'),
   description: t('projectBasedDesc'),
   color: 'from-blue-400 to-blue-600',
   features: [t('projectBasedFeature1'), t('projectBasedFeature2'), t('projectBasedFeature3')]
  },
  {
   id: 'hourly',
   icon: FaClock,
   title: t('hourlyBased'),
   description: t('hourlyBasedDesc'),
   color: 'from-green-400 to-green-600',
   features: [t('hourlyBasedFeature1'), t('hourlyBasedFeature2'), t('hourlyBasedFeature3')]
  },
  {
   id: 'fixed',
   icon: FaUserTie,
   title: t('fixedTerm'),
   description: t('fixedTermDesc'),
   color: 'from-purple-400 to-purple-600',
   features: [t('fixedTermFeature1'), t('fixedTermFeature2'), t('fixedTermFeature3')]
  }
 ];

 const handleNeedToggle = (needId) => {
  setSelectedNeeds(prev => {
   if (prev.includes(needId)) {
    return prev.filter(id => id !== needId);
   } else {
    return [...prev, needId];
   }
  });
  
  if (errors.needs) {
   setErrors(prev => ({ ...prev, needs: null }));
  }
 };

 const validateForm = () => {
  if (selectedNeeds.length === 0) {
   setErrors({ needs: t('pleaseSelectAtLeastOneNeed') });
   return false;
  }
  return true;
 };

 const handleNext = () => {
  if (validateForm()) {
   updateOnboardingData({
    clientNeeds: selectedNeeds
   });
   goToNextStep();
  }
 };

 return (
  <div className="p-8 md:p-12">
   {/* Header */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
   >
    <div className="w-16 h-16 bg-gradient-to-br from-medieval-gold-400 to-medieval-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
     <FaProjectDiagram className="text-2xl text-white" />
    </div>
    
    <h2 className="text-2xl md:text-3xl font-medium font-bold text-gray-800 mb-2">
     {t('whatTypeOfFreelancer')}
    </h2>
    
    <p className="text-gray-600 font-medium">
     {t('needsDescription')}
    </p>
   </motion.div>

   {/* Needs Options */}
   <motion.div
    className="max-w-6xl mx-auto space-y-6"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
   >
    {needsOptions.map((option, index) => (
     <motion.button
      key={option.id}
      onClick={() => handleNeedToggle(option.id)}
      className={`w-full p-6 rounded-xl border-2 transition-all duration-300 ${
       selectedNeeds.includes(option.id)
        ? 'border-blue-500 bg-blue-50 shadow-lg'
        : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-md'
      }`}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.3 + index * 0.1 }}
     >
      <div className="flex items-start space-x-4">
       <div className={`w-12 h-12 bg-gradient-to-br ${option.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
        <option.icon className="text-xl text-white" />
       </div>
       <div className="flex-1 text-left">
        <div className="flex items-center justify-between mb-2">
         <h3 className="font-medium font-semibold text-gray-800">
          {option.title}
         </h3>
         {selectedNeeds.includes(option.id) && (
          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
           <FaArrowRight className="text-white text-xs" />
          </div>
         )}
        </div>
        <p className="text-gray-600 text-sm mb-3">
         {option.description}
        </p>
        <div className="space-y-1">
         {option.features.map((feature, featureIndex) => (
          <div key={featureIndex} className="flex items-center text-sm">
           <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
           <span className="text-gray-700">{feature}</span>
          </div>
         ))}
        </div>
       </div>
      </div>
     </motion.button>
    ))}
   </motion.div>

   {/* Selection Summary */}
   {selectedNeeds.length > 0 && (
    <motion.div
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     className="text-center mt-6"
    >
     <p className="text-gray-600 font-medium">
      {t('selectedNeeds')}: {selectedNeeds.length}
     </p>
    </motion.div>
   )}

   {/* Error Message */}
   {errors.needs && (
    <motion.div
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     className="text-center mt-4"
    >
     <p className="text-red-500 text-sm font-medium">{errors.needs}</p>
    </motion.div>
   )}

   {/* Navigation */}
   <motion.div
    className="flex justify-between mt-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.4 }}
   >
    <button
     onClick={goToPreviousStep}
     disabled={loading}
     className="btn-secondary px-6 py-2 font-medium"
    >
     {t('previous')}
    </button>

    <button
     onClick={handleNext}
     disabled={loading || selectedNeeds.length === 0}
     className={`px-6 py-2 font-medium ${
      selectedNeeds.length > 0
       ? 'btn-primary' 
       : 'bg-medieval-brown-300 text-gray-500 cursor-not-allowed'
     }`}
    >
     {t('next')}
    </button>
   </motion.div>
  </div>
 );
};

export default ClientNeedsStep; 