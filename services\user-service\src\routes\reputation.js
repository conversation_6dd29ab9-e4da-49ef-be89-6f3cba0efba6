/**
 * Reputation Routes for User Service
 * Handles user reputation and ratings
 */

const express = require('express');
const { verifyFirebaseToken, optionalAuth } = require('../middleware/auth');
const database = require('../config/database');

const router = express.Router();

/**
 * @route GET /api/v1/reputation/:userId
 * @desc Get user reputation
 * @access Public
 */
router.get('/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    const query = `
      SELECT *
      FROM user_reputation
      WHERE user_id = $1
    `;

    const result = await database.query(query, [userId]);

    if (result.rows.length === 0) {
      const createQuery = `
        INSERT INTO user_reputation (user_id)
        VALUES ($1)
        RETURNING *
      `;
      
      const createResult = await database.query(createQuery, [userId]);
      return res.success(createResult.rows[0], 'Reputation retrieved successfully');
    }

    res.success(result.rows[0], 'Reputation retrieved successfully');

  } catch (error) {
    console.error('❌ Get reputation error:', error);
    res.error('Failed to retrieve reputation', 500, error.message);
  }
});

module.exports = router; 