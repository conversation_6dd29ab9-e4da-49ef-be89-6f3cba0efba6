import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { ApplePageWrapper } from '../components/apple';
import {
 EnvelopeIcon,
 ArrowLeftIcon,
 CheckCircleIcon,
 ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const ForgotPasswordPage = () => {
 const { t } = useLanguage();
 const navigate = useNavigate();
 const { resetPassword, loading } = useAuth();
 
 const [email, setEmail] = useState('');
 const [error, setError] = useState('');
 const [success, setSuccess] = useState(false);
 const [isSubmitting, setIsSubmitting] = useState(false);

 const validateEmail = email => {
  return /\S+@\S+\.\S+/.test(email);
 };

 const handleSubmit = async e => {
  e.preventDefault();

  if (!email) {
   setError('<PERSON>ail là bắt buộc');
   return;
  }

  if (!validateEmail(email)) {
   setError('<PERSON>ui lòng nhập địa chỉ email hợp lệ');
   return;
  }

  setIsSubmitting(true);
  setError('');

  try {
   const result = await resetPassword(email);
   if (result.success) {
    setSuccess(true);
    setError('');
   } else {
    setError(result.error);
    setSuccess(false);
   }
  } catch (err) {
   setError('Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại.');
   setSuccess(false);
  } finally {
   setIsSubmitting(false);
  }
 };

 const handleInputChange = e => {
  setEmail(e.target.value);
  if (error) setError('');
 };

 const handleBackToLogin = () => {
  navigate('/login');
 };

 if (success) {
  return (
   <ApplePageWrapper>
    <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-red-50 flex items-center justify-center p-4">
     <div className="max-w-md w-full">
      <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
       {/* Logo */}
       <div className="text-center mb-8">
        <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
         <span className="text-white font-bold text-xl font-bold">V</span>
        </div>
        <h1 className="font-bold text-xl font-bold text-gray-800">
         VWork
        </h1>
       </div>

       {/* Success Content */}
       <div className="text-center">
        <div className="mx-auto flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
         <CheckCircleIcon className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="font-bold text-2xl font-bold text-gray-800 mb-4">
         Email Đã Được Gửi!
        </h2>
        <p className="font-medium text-gray-600 mb-6">
         Chúng tôi đã gửi hướng dẫn đặt lại mật khẩu đến <span className="font-semibold">{email}</span>. 
         Vui lòng kiểm tra hộp thư và làm theo hướng dẫn.
        </p>
        
        <div className="bg-medieval-brown-50 border border-gray-200 rounded-lg p-4 mb-6">
         <p className="font-medium text-sm text-gray-600">
          Không thấy email? Kiểm tra thư mục spam hoặc thử gửi lại sau vài phút.
         </p>
        </div>

        <div className="space-y-4">
         <button
          onClick={handleBackToLogin}
          className="w-full btn-primary"
         >
          Quay Lại Đăng Nhập
         </button>
         
         <button
          onClick={() => setSuccess(false)}
          className="w-full btn-secondary"
         >
          Gửi Lại Email
         </button>
        </div>
       </div>
      </div>
     </div>
    </div>
   </ApplePageWrapper>
  );
 }

 return (
  <ApplePageWrapper>
   <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-red-50 flex items-center justify-center p-4">
    <div className="max-w-md w-full">
     <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
      {/* Logo */}
      <div className="text-center mb-8">
       <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
        <span className="text-white font-bold text-xl font-bold">V</span>
       </div>
       <h1 className="font-bold text-xl font-bold text-gray-800">
        VWork
       </h1>
      </div>

      {/* Title */}
      <div className="text-center mb-8">
       <h2 className="font-bold text-2xl font-bold text-gray-800 mb-4">
        Quên Mật Khẩu?
       </h2>
       <p className="font-medium text-gray-600">
        Nhập email của bạn để nhận hướng dẫn đặt lại mật khẩu
       </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
       {/* Email Input */}
       <div>
        <label
         htmlFor="email"
         className="block font-medium text-sm font-medium text-gray-700 mb-2"
        >
         Địa chỉ email
        </label>
        <div className="relative">
         <input
          id="email"
          type="email"
          value={email}
          onChange={handleInputChange}
          className={`form-input w-full px-4 py-3 pl-12 font-medium ${
           error ? 'border-red-500' : ''
          }`}
          placeholder="Nhập địa chỉ email của bạn"
          disabled={isSubmitting}
         />
         <EnvelopeIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        </div>
        {error && (
         <p className="mt-2 text-sm text-red-600 font-medium">{error}</p>
        )}
       </div>

       {/* Submit Button */}
       <button
        type="submit"
        disabled={isSubmitting}
        className="w-full btn-primary"
       >
        {isSubmitting ? 'Đang gửi...' : 'Gửi Email Đặt Lại Mật Khẩu'}
       </button>
      </form>

      {/* Back to Login */}
      <div className="mt-8 text-center">
       <button
        onClick={handleBackToLogin}
        className="inline-flex items-center space-x-2 font-medium text-sm text-gray-600 hover:text-gray-800"
       >
        <ArrowLeftIcon className="w-4 h-4" />
        <span>Quay lại đăng nhập</span>
       </button>
      </div>

      {/* Help Text */}
      <div className="mt-6 text-center">
       <p className="font-medium text-xs text-gray-500">
        Bạn sẽ nhận được email với hướng dẫn đặt lại mật khẩu trong vài phút.
       </p>
      </div>
     </div>
    </div>
   </div>
  </ApplePageWrapper>
 );
};

export default ForgotPasswordPage;
