services:
  - type: web
    runtime: node
    name: vwork-api-gateway
    region: oregon
    branch: main
    rootDir: services/api-gateway
    buildCommand: npm ci --production
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      # Auth Service removed - using Firebase directly
      - key: USER_SERVICE_URL
        value: https://vwork-user-service.onrender.com
      - key: PROJECT_SERVICE_URL
        value: https://vwork-project-service.onrender.com
      - key: JOB_SERVICE_URL
        value: https://vwork-job-service.onrender.com
      - key: CHAT_SERVICE_URL
        value: https://vwork-chat-service.onrender.com
      - key: COMMUNITY_SERVICE_URL
        value: https://vwork-community-service.onrender.com
      - key: PAYMENT_SERVICE_URL
        value: https://vwork-payment-service.onrender.com
      - key: SEARCH_SERVICE_URL
        value: https://vwork-search-service.onrender.com
      - key: CORS_ORIGINS
        value: https://frontend-ce4z.onrender.com,https://vwork-client.onrender.com,https://nerafus.com,https://www.nerafus.com
    plan: free
