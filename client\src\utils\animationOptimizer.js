/**
 * Animation Performance Optimizer
 * Provides intelligent animation management with performance monitoring
 */

import { gsap } from 'gsap';
import responsiveAutoscaling from '../services/responsiveAutoscaling';

class AnimationOptimizer {
 constructor() {
  this.activeAnimations = new Map();
  this.animationQueue = [];
  this.isProcessingQueue = false;
  this.performanceThresholds = {
   high: { maxConcurrent: 8, complexityScore: 80 }, // Reduced from 10/100
   medium: { maxConcurrent: 4, complexityScore: 40 }, // Reduced from 6/60
   low: { maxConcurrent: 2, complexityScore: 20 }, // Reduced from 3/30
   minimal: { maxConcurrent: 1, complexityScore: 10 }
  };

  // Add frame rate monitoring
  this.frameRateMonitor = {
   frames: [],
   lastTime: performance.now(),
   currentFPS: 60
  };

  // Start FPS monitoring
  this.startFPSMonitoring();
 }

 /**
  * Calculate animation complexity score
  */
 calculateComplexity(config) {
  let score = 0;
  
  // Base animation properties
  if (config.rotation || config.rotationX || config.rotationY) score += 15;
  if (config.scale) score += 10;
  if (config.x || config.y) score += 5;
  if (config.opacity) score += 2;
  
  // Advanced properties
  if (config.morphSVG) score += 25;
  if (config.motionPath) score += 20;
  if (config.scrambleText) score += 15;
  if (config.physics2D) score += 30;
  
  // Stagger animations
  if (config.stagger) score += 10;
  
  // Duration impact
  if (config.duration > 2) score += 5;
  
  // Multiple targets
  if (Array.isArray(config.targets) && config.targets.length > 5) {
   score += config.targets.length * 2;
  }
  
  return score;
 }

 /**
  * Start FPS monitoring for dynamic performance adjustment
  */
 startFPSMonitoring() {
  const measureFPS = () => {
   const now = performance.now();
   const delta = now - this.frameRateMonitor.lastTime;
   this.frameRateMonitor.lastTime = now;

   // Calculate FPS
   const fps = 1000 / delta;
   this.frameRateMonitor.frames.push(fps);

   // Keep only last 60 frames for average
   if (this.frameRateMonitor.frames.length > 60) {
    this.frameRateMonitor.frames.shift();
   }

   // Calculate average FPS
   this.frameRateMonitor.currentFPS = this.frameRateMonitor.frames.reduce((a, b) => a + b, 0) / this.frameRateMonitor.frames.length;

   // Auto-adjust performance if FPS drops below 30
   if (this.frameRateMonitor.currentFPS < 30 && this.activeAnimations.size > 2) {
    this.emergencyPerformanceMode();
   }

   requestAnimationFrame(measureFPS);
  };

  requestAnimationFrame(measureFPS);
 }

 /**
  * Emergency performance mode - kill complex animations
  */
 emergencyPerformanceMode() {
  console.warn('🚨 Emergency performance mode activated - FPS:', this.frameRateMonitor.currentFPS);

  // Kill the most complex animations first
  const sortedAnimations = Array.from(this.activeAnimations.entries())
   .sort((a, b) => b[1].complexity - a[1].complexity);

  // Kill top 50% of complex animations
  const toKill = sortedAnimations.slice(0, Math.ceil(sortedAnimations.length / 2));

  toKill.forEach(([animation, config]) => {
   if (animation && animation.kill) {
    animation.kill();
    this.activeAnimations.delete(animation);
    console.log('🔥 Killed complex animation:', config.name || 'unnamed');
   }
  });
 }

 /**
  * Check if animation should be allowed based on performance
  */
 shouldAllowAnimation(config) {
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  const threshold = this.performanceThresholds[performanceLevel];
  const complexity = this.calculateComplexity(config);
  
  // Check complexity threshold
  if (complexity > threshold.complexityScore) {
   return false;
  }
  
  // Check concurrent animations limit
  if (this.activeAnimations.size >= threshold.maxConcurrent) {
   return false;
  }
  
  return true;
 }

 /**
  * Optimize animation configuration based on performance
  */
 optimizeConfig(config) {
  const performanceLevel = responsiveAutoscaling.currentPerformanceLevel;
  const settings = responsiveAutoscaling.getOptimizedGSAPSettings();
  
  const optimized = { ...config };
  
  // Apply performance-based optimizations
  optimized.duration = optimized.duration || settings.duration;
  optimized.ease = optimized.ease || settings.ease;
  optimized.force3D = settings.force3D;
  
  // Reduce complexity for lower performance levels
  if (performanceLevel === 'low' || performanceLevel === 'minimal') {
   // Remove complex transforms
   delete optimized.rotationX;
   delete optimized.rotationY;
   delete optimized.morphSVG;
   delete optimized.motionPath;
   delete optimized.physics2D;
   
   // Simplify stagger
   if (optimized.stagger) {
    optimized.stagger = Math.min(optimized.stagger, 0.05);
   }
  }
  
  if (performanceLevel === 'minimal') {
   // Keep only essential properties
   const essential = {};
   if (optimized.opacity !== undefined) essential.opacity = optimized.opacity;
   if (optimized.x !== undefined) essential.x = optimized.x;
   if (optimized.y !== undefined) essential.y = optimized.y;
   if (optimized.scale !== undefined) essential.scale = optimized.scale;
   
   return {
    ...essential,
    duration: Math.min(optimized.duration, 0.3),
    ease: 'none'
   };
  }
  
  return optimized;
 }

 /**
  * Create optimized animation
  */
 createAnimation(targets, config, id = null) {
  // Generate unique ID if not provided
  const animationId = id || `anim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Check if animation should be allowed
  if (!this.shouldAllowAnimation(config)) {
   console.warn(`Animation ${animationId} blocked due to performance constraints`);
   
   // Apply final state immediately for blocked animations
   if (config.opacity !== undefined) {
    gsap.set(targets, { opacity: config.opacity });
   }
   return null;
  }
  
  // Optimize configuration
  const optimizedConfig = this.optimizeConfig(config);
  
  // Create animation with cleanup tracking
  const animation = gsap.to(targets, {
   ...optimizedConfig,
   onStart: () => {
    this.activeAnimations.set(animationId, animation);
   },
   onComplete: () => {
    this.activeAnimations.delete(animationId);
    this.processQueue();
   },
   onKill: () => {
    this.activeAnimations.delete(animationId);
   }
  });
  
  return animation;
 }

 /**
  * Create optimized timeline
  */
 createTimeline(config = {}) {
  const timeline = gsap.timeline({
   ...config,
   onComplete: () => {
    if (config.onComplete) config.onComplete();
    this.processQueue();
   }
  });
  
  return timeline;
 }

 /**
  * Queue animation for later execution
  */
 queueAnimation(targets, config, priority = 0) {
  this.animationQueue.push({
   targets,
   config,
   priority,
   timestamp: Date.now()
  });
  
  // Sort by priority (higher first) and timestamp (older first)
  this.animationQueue.sort((a, b) => {
   if (a.priority !== b.priority) return b.priority - a.priority;
   return a.timestamp - b.timestamp;
  });
  
  this.processQueue();
 }

 /**
  * Process queued animations
  */
 processQueue() {
  if (this.isProcessingQueue || this.animationQueue.length === 0) return;
  
  this.isProcessingQueue = true;
  
  // Process animations that can fit within performance constraints
  while (this.animationQueue.length > 0) {
   const queuedAnimation = this.animationQueue[0];
   
   if (this.shouldAllowAnimation(queuedAnimation.config)) {
    const animation = this.animationQueue.shift();
    this.createAnimation(animation.targets, animation.config);
   } else {
    break; // Stop processing if we hit performance limits
   }
  }
  
  this.isProcessingQueue = false;
 }

 /**
  * Kill all active animations
  */
 killAllAnimations() {
  this.activeAnimations.forEach(animation => {
   if (animation && animation.kill) {
    animation.kill();
   }
  });
  this.activeAnimations.clear();
  this.animationQueue = [];
 }

 /**
  * Get performance statistics
  */
 getStats() {
  return {
   activeAnimations: this.activeAnimations.size,
   queuedAnimations: this.animationQueue.length,
   performanceLevel: responsiveAutoscaling.currentPerformanceLevel,
   maxConcurrent: this.performanceThresholds[responsiveAutoscaling.currentPerformanceLevel].maxConcurrent
  };
 }

 /**
  * Cleanup method for component unmount
  */
 cleanup() {
  this.killAllAnimations();
 }
}

// Export singleton instance
export const animationOptimizer = new AnimationOptimizer();

// Export class for custom instances
export default AnimationOptimizer;
