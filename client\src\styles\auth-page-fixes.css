/* Auth Page Button Click Fixes - Minimal and Clean */

/* Basic z-index hierarchy for auth page - LOWER than header */
.auth-page-container {
  position: relative !important;
  z-index: 1 !important;
}

/* Ensure buttons are clickable but still below header */
.auth-page-container button,
.auth-page-container .btn-primary,
.auth-page-container .btn-secondary {
  position: relative !important;
  z-index: 2 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Only disable pointer events when actually disabled */
.auth-page-container button:disabled,
.auth-page-container .btn-primary:disabled,
.auth-page-container .btn-secondary:disabled {
  pointer-events: none !important;
  cursor: not-allowed !important;
}

/* Fix for input field icons - prevent jumping and positioning issues */
.auth-page-container .relative {
  position: relative !important;
}

.auth-page-container .relative input {
  position: relative !important;
  z-index: 1 !important;
}

/* Fix for absolute positioned icons (lock, envelope) */
.auth-page-container .absolute {
  position: absolute !important;
  z-index: 2 !important;
}

/* Specific fix for left icons (lock, envelope) */
.auth-page-container .absolute.left-4 {
  left: 1rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  pointer-events: none !important;
  transition: none !important;
}

/* Specific fix for right icons (eye toggle) */
.auth-page-container .absolute.right-4 {
  right: 1rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  transition: none !important;
}

/* Fix for password toggle button specifically */
.auth-page-container .relative button[type="button"] {
  position: absolute !important;
  z-index: 3 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  background: none !important;
  border: none !important;
  padding: 0.25rem !important;
}

/* Prevent icons from jumping during animations */
.auth-page-container .absolute svg {
  display: block !important;
  transition: none !important;
  transform: none !important;
}

/* Ensure floating widgets don't interfere but stay below header */
.floating-widget-container {
  z-index: 40 !important;
}

/* Fix for password strength indicator */
.auth-page-container .password-strength-indicator {
  position: relative !important;
  z-index: 1 !important;
  margin-top: 0.5rem !important;
}

/* Ensure proper button focus */
.auth-page-container button:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* Preserve button styling */
.auth-page-container .btn-primary {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure full-width buttons work */
.auth-page-container .w-full {
  width: 100% !important;
}

/* Fix for user type selection buttons */
.auth-page-container .user-type-button {
  position: relative !important;
  z-index: 3 !important;
  pointer-events: auto !important;
}

/* Ensure submit button is always clickable */
.auth-page-container button[type="submit"] {
  position: relative !important;
  z-index: 4 !important;
  pointer-events: auto !important;
}

/* Fix for toggle auth mode button */
.auth-page-container .toggle-auth-button {
  position: relative !important;
  z-index: 3 !important;
  pointer-events: auto !important;
}

/* Prevent pseudo-elements from blocking clicks */
.auth-page-container *::before,
.auth-page-container *::after {
  pointer-events: none !important;
}

/* Mobile touch targets */
@media (max-width: 768px) {
  .auth-page-container button {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* Ensure proper stacking context without breaking layout */
.auth-page-container {
  isolation: isolate;
}

/* Fix for any GSAP animations that might interfere */
.auth-page-container [data-gsap] {
  pointer-events: auto !important;
}

/* Ensure input fields don't interfere with buttons */
.auth-page-container input,
.auth-page-container select,
.auth-page-container textarea {
  position: relative !important;
  z-index: 1 !important;
}
