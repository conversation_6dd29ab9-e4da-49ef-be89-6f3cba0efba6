services:
  - type: web
    name: vwork-user-service
    env: node
    plan: free
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3002
      - key: SERVICE_NAME
        value: user-service
      - key: FIREBASE_PROJECT_ID
        fromDatabase:
          name: vwork-user-service-db
          property: connectionString
      - key: ALLOWED_ORIGINS
        value: https://vwork-platform.netlify.app,https://vwork.com
      - key: CORS_CREDENTIALS
        value: true
      - key: LOG_LEVEL
        value: info
      - key: LOG_FORMAT
        value: combined

databases:
  - name: vwork-user-service-db
    databaseName: vwork_user_service
    user: vwork_user
