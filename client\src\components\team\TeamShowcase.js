import React, { useState } from 'react';
import { motion } from 'framer-motion';
// import FreelancerTeamMock from './FreelancerTeamMock';
import { 
 SparklesIcon, 
 CpuChipIcon, 
 DevicePhoneMobileIcon,
 EyeIcon,
 PaintBrushIcon,
 RocketLaunchIcon
} from '@heroicons/react/24/outline';

const TeamShowcase = () => {
 const [showDemo, setShowDemo] = useState(false);

 const features = [
  {
   icon: SparklesIcon,
   title: 'GSAP Animations',
   description: 'Smooth animations với ScrollTrigger, stagger effects và micro-interactions',
   highlights: ['Hero animations', 'Stats counter', 'Stagger effects', 'Scroll triggers']
  },
  {
   icon: CpuChipIcon,
   title: 'Framer Motion',
   description: 'Page transitions, component animations và interactive gestures',
   highlights: ['Page transitions', 'Hover effects', 'Loading states', 'Gesture support']
  },
  {
   icon: PaintBrushIcon,
   title: 'Glass Morphism',
   description: 'Modern glass effect với backdrop blur và transparency',
   highlights: ['Backdrop blur', 'Semi-transparent', 'Modern aesthetic', 'iOS-like design']
  },
  {
   icon: DevicePhoneMobileIcon,
   title: 'Mobile First',
   description: 'Responsive design từ mobile đến desktop với breakpoints tối ưu',
   highlights: ['Mobile first', 'Flexible grid', 'Touch friendly', 'Adaptive layout']
  },
  {
   icon: EyeIcon,
   title: 'Accessibility',
   description: 'Keyboard navigation, screen reader support và high contrast',
   highlights: ['Keyboard nav', 'ARIA labels', 'High contrast', 'Reduced motion']
  },
  {
   icon: RocketLaunchIcon,
   title: 'Performance',
   description: 'Skeleton loading, lazy loading và optimized animations',
   highlights: ['Skeleton loading', 'Lazy loading', 'Optimized GSAP', 'Smooth 60fps']
  }
 ];

 if (showDemo) {
  return (
   <div className="relative">
    <motion.button
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     className="fixed top-4 left-4 z-50 px-4 py-2 bg-black/20 backdrop-blur-sm text-white rounded-lg hover:bg-black/30 transition-colors"
     onClick={() => setShowDemo(false)}
    >
     ← Quay lại Showcase
    </motion.button>
          {/* <FreelancerTeamMock /> */}
   </div>
  );
 }

 return (
  <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
   {/* Hero Section */}
   <div className="container mx-auto px-6 py-20">
    <div className="text-center max-w-4xl mx-auto mb-16">
     <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.5 }}
      className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full mb-6"
     >
      <SparklesIcon className="w-5 h-5 text-blue-600 mr-2" />
      <span className="text-sm font-medium text-blue-800">
       UI/UX Showcase
      </span>
     </motion.div>
     
     <motion.h1
      initial={{ y: 50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.2, duration: 0.8 }}
      className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-600 bg-clip-text text-transparent mb-6"
     >
      Freelancer Team
      <br />
      <span className="text-4xl md:text-5xl">Modern UI/UX</span>
     </motion.h1>
     
     <motion.p
      initial={{ y: 30, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.4, duration: 0.8 }}
      className="text-xl md:text-2xl text-gray-600 mb-12 leading-relaxed"
     >
      Trải nghiệm giao diện hiện đại với GSAP animations, 
      <br />Framer Motion transitions và Glass Morphism design
     </motion.p>

     <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.6, duration: 0.8 }}
      className="flex flex-col sm:flex-row gap-4 justify-center"
     >
      <motion.button
       whileHover={{ scale: 1.05 }}
       whileTap={{ scale: 0.95 }}
       className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
       onClick={() => setShowDemo(true)}
      >
       <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
       <div className="relative flex items-center justify-center">
        <RocketLaunchIcon className="w-6 h-6 mr-2" />
        Xem Demo Live
       </div>
      </motion.button>
      
      <motion.button
       whileHover={{ scale: 1.05 }}
       whileTap={{ scale: 0.95 }}
       className="px-8 py-4 bg-white/80 backdrop-blur-sm text-gray-800 font-semibold rounded-2xl border-2 border-gray-200 hover:border-blue-300 hover:bg-white transition-all duration-300 shadow-lg hover:shadow-xl"
       onClick={() => {
        document.getElementById('features').scrollIntoView({ 
         behavior: 'smooth' 
        });
       }}
      >
       <div className="flex items-center justify-center">
        <EyeIcon className="w-6 h-6 mr-2" />
        Khám phá Features
       </div>
      </motion.button>
     </motion.div>
    </div>

    {/* Features Grid */}
    <div id="features" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
     {features.map((feature, index) => {
      const IconComponent = feature.icon;
      return (
       <motion.div
        key={feature.title}
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 + index * 0.1, duration: 0.6 }}
        whileHover={{ y: -8, scale: 1.02 }}
        className="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 hover:shadow-2xl transition-all duration-300"
       >
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
         <IconComponent className="w-8 h-8 text-white" />
        </div>
        
        <h3 className="text-xl font-bold text-gray-800 mb-3">
         {feature.title}
        </h3>
        
        <p className="text-gray-600 mb-4 leading-relaxed">
         {feature.description}
        </p>
        
        <div className="space-y-2">
         {feature.highlights.map((highlight, idx) => (
          <div key={idx} className="flex items-center text-sm text-gray-500">
           <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3"></div>
           {highlight}
          </div>
         ))}
        </div>
       </motion.div>
      );
     })}
    </div>

    {/* Tech Stack */}
    <motion.div
     initial={{ opacity: 0, y: 50 }}
     animate={{ opacity: 1, y: 0 }}
     transition={{ delay: 1.4, duration: 0.8 }}
     className="mt-20 text-center"
    >
     <h2 className="text-3xl font-bold text-gray-800 mb-8">
      Tech Stack
     </h2>
     
     <div className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
      {[
       'GSAP', 'Framer Motion', 'Tailwind CSS', 'React', 'Heroicons',
       'CSS Grid', 'Flexbox', 'Backdrop Filter', 'CSS Animations', 'Responsive Design'
      ].map((tech, index) => (
       <motion.div
        key={tech}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1.6 + index * 0.05, duration: 0.3 }}
        whileHover={{ scale: 1.1 }}
        className="px-4 py-2 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full text-sm font-medium text-blue-800"
       >
        {tech}
       </motion.div>
      ))}
     </div>
    </motion.div>

    {/* Performance Stats */}
    <motion.div
     initial={{ opacity: 0, y: 50 }}
     animate={{ opacity: 1, y: 0 }}
     transition={{ delay: 1.8, duration: 0.8 }}
     className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
    >
     {[
      { label: 'Animation FPS', value: '60', unit: 'fps' },
      { label: 'Load Time', value: '<2', unit: 's' },
      { label: 'Breakpoints', value: '4', unit: '+' },
      { label: 'Accessibility', value: 'AAA', unit: '' }
     ].map((stat, index) => (
      <motion.div
       key={stat.label}
       initial={{ scale: 0 }}
       animate={{ scale: 1 }}
       transition={{ delay: 2 + index * 0.1, duration: 0.5 }}
       className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg border border-white/20"
      >
       <div className="text-3xl font-bold text-gray-800 mb-2">
        {stat.value}<span className="text-blue-600">{stat.unit}</span>
       </div>
       <div className="text-sm text-gray-600">{stat.label}</div>
      </motion.div>
     ))}
    </motion.div>
   </div>
  </div>
 );
};

export default TeamShowcase; 
