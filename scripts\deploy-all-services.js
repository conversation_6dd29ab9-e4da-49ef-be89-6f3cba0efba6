#!/usr/bin/env node

/**
 * Deploy all VWork services to Render
 * This script handles deployment of all microservices
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const ROOT_DIR = path.join(__dirname, '..');
const SERVICES_DIR = path.join(ROOT_DIR, 'services');

// Service configurations
const SERVICES = [
  {
    name: 'auth-service',
    path: 'services/auth-service',
    renderName: 'vwork-auth-service'
  },
  {
    name: 'user-service', 
    path: 'services/user-service',
    renderName: 'vwork-user-service'
  },
  {
    name: 'project-service',
    path: 'services/project-service', 
    renderName: 'vwork-project-service'
  },
  {
    name: 'job-service',
    path: 'services/job-service',
    renderName: 'vwork-job-service'
  },
  {
    name: 'chat-service',
    path: 'services/chat-service',
    renderName: 'vwork-chat-service'
  },
  {
    name: 'community-service',
    path: 'services/community-service',
    renderName: 'nerafus-community-service'
  },
  {
    name: 'payment-service',
    path: 'services/payment-service',
    renderName: 'vwork-payment-service'
  },
  {
    name: 'api-gateway',
    path: 'services/api-gateway',
    renderName: 'vwork-api-gateway'
  }
];

/**
 * Logger utility
 */
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.error(`❌ ${msg}`),
  warn: (msg) => console.warn(`⚠️  ${msg}`)
};

/**
 * Deploy a single service
 */
async function deployService(service) {
  try {
    log.info(`Deploying ${service.name}...`);
    
    const servicePath = path.join(ROOT_DIR, service.path);
    
    // Check if service directory exists
    if (!fs.existsSync(servicePath)) {
      log.error(`Service directory not found: ${servicePath}`);
      return false;
    }

    // Check if render.yaml exists
    const renderYamlPath = path.join(servicePath, 'render.yaml');
    if (!fs.existsSync(renderYamlPath)) {
      log.warn(`No render.yaml found for ${service.name}, skipping...`);
      return false;
    }

    // Change to service directory
    process.chdir(servicePath);
    
    // Install dependencies
    log.info(`Installing dependencies for ${service.name}...`);
    execSync('npm ci --production', { stdio: 'inherit' });
    
    // Deploy to Render using render CLI
    log.info(`Deploying ${service.name} to Render...`);
    execSync(`render deploy ${service.renderName}`, { stdio: 'inherit' });
    
    log.success(`${service.name} deployed successfully!`);
    return true;
    
  } catch (error) {
    log.error(`Failed to deploy ${service.name}: ${error.message}`);
    return false;
  }
}

/**
 * Deploy all services
 */
async function deployAllServices() {
  log.info('🚀 Starting deployment of all VWork services...');
  
  const results = [];
  
  for (const service of SERVICES) {
    const success = await deployService(service);
    results.push({ service: service.name, success });
    
    // Add delay between deployments to avoid rate limiting
    if (success) {
      log.info('Waiting 30 seconds before next deployment...');
      await new Promise(resolve => setTimeout(resolve, 30000));
    }
  }
  
  // Summary
  log.info('\n📊 Deployment Summary:');
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  log.success(`${successful.length} services deployed successfully`);
  if (failed.length > 0) {
    log.error(`${failed.length} services failed to deploy`);
    failed.forEach(f => log.error(`  - ${f.service}`));
  }
  
  return results;
}

/**
 * Check service health after deployment
 */
async function checkServiceHealth() {
  log.info('\n🏥 Checking service health...');
  
  const healthChecks = [
    { name: 'API Gateway', url: 'https://vwork-api-gateway.onrender.com/api/v1/status' },
    { name: 'Auth Service', url: 'https://vwork-auth-service.onrender.com/health' },
    { name: 'User Service', url: 'https://vwork-user-service.onrender.com/health' },
    { name: 'Project Service', url: 'https://vwork-project-service.onrender.com/health' },
    { name: 'Job Service', url: 'https://vwork-job-service.onrender.com/health' },
    { name: 'Chat Service', url: 'https://vwork-chat-service.onrender.com/health' },
    { name: 'Community Service', url: 'https://vwork-community-service.onrender.com/health' }
  ];
  
  for (const check of healthChecks) {
    try {
      const response = await fetch(check.url, { timeout: 10000 });
      if (response.ok) {
        log.success(`${check.name}: Healthy (${response.status})`);
      } else {
        log.warn(`${check.name}: Unhealthy (${response.status})`);
      }
    } catch (error) {
      log.error(`${check.name}: Unreachable (${error.message})`);
    }
  }
}

// Main execution
async function main() {
  try {
    // Deploy all services
    const results = await deployAllServices();
    
    // Wait for services to start up
    log.info('Waiting 2 minutes for services to start up...');
    await new Promise(resolve => setTimeout(resolve, 120000));
    
    // Check health
    await checkServiceHealth();
    
    log.success('🎉 Deployment process completed!');
    
  } catch (error) {
    log.error(`Deployment failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { deployAllServices, checkServiceHealth }; 