#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 Committing CORS fixes to git repository...\n');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logWarning(message) {
    log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
    log(`ℹ️ ${message}`, 'blue');
}

// Check if we're in a git repository
function checkGitRepository() {
    try {
        execSync('git status', { stdio: 'pipe' });
        return true;
    } catch (error) {
        return false;
    }
}

// Get current branch
function getCurrentBranch() {
    try {
        return execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    } catch (error) {
        return 'main';
    }
}

// Check if there are changes to commit
function hasChanges() {
    try {
        const status = execSync('git status --porcelain', { encoding: 'utf8' });
        return status.trim().length > 0;
    } catch (error) {
        return false;
    }
}

// Main function
async function main() {
    log('🔧 VWork CORS Fix Git Commit Script', 'bright');
    log('=====================================\n');

    // Check if we're in a git repository
    if (!checkGitRepository()) {
        logError('Not in a git repository. Please run this script from the VWork project root.');
        process.exit(1);
    }

    // Check current branch
    const currentBranch = getCurrentBranch();
    logInfo(`Current branch: ${currentBranch}`);

    // Check if there are changes to commit
    if (!hasChanges()) {
        logWarning('No changes to commit. All files are already committed.');
        logInfo('If you want to force a deployment, you can create an empty commit:');
        log('   git commit --allow-empty -m "Trigger deployment"');
        log('   git push origin main');
        return;
    }

    try {
        // Show current status
        logInfo('Current git status:');
        execSync('git status', { stdio: 'inherit' });

        // Add all changes
        logInfo('\nAdding all changes...');
        execSync('git add .', { stdio: 'inherit' });

        // Create commit
        const commitMessage = 'Fix CORS configuration for nerafus.com domain\n\n- Add https://nerafus.com to allowed origins\n- Enhance CORS headers configuration\n- Add explicit preflight request handling\n- Update helmet configuration for cross-origin resources\n- Fix CORS issues in API Gateway and Community Service';
        
        logInfo('\nCreating commit...');
        execSync(`git commit -m "${commitMessage}"`, { stdio: 'inherit' });

        // Push to remote
        logInfo('\nPushing to remote repository...');
        execSync(`git push origin ${currentBranch}`, { stdio: 'inherit' });

        logSuccess('\n✅ CORS fixes committed and pushed successfully!');
        
        log('\n📋 Deployment Status:');
        log('• Changes have been pushed to the remote repository');
        log('• Render will automatically detect the changes and start deployment');
        log('• Deployment usually takes 2-5 minutes to complete');
        
        log('\n🔍 What was changed:');
        log('• services/api-gateway/src/index.js - Enhanced CORS configuration');
        log('• services/community-service/app.js - Added nerafus.com to allowed origins');
        log('• scripts/deploy-cors-fix.js - Deployment automation script');
        log('• test-cors-fix.js - CORS testing script');
        
        log('\n🌐 Next Steps:');
        log('1. Wait for Render deployment to complete');
        log('2. Run: node test-cors-fix.js (to test the fix)');
        log('3. Test manually at https://nerafus.com');
        log('4. Check browser console for any remaining CORS errors');

    } catch (error) {
        logError(`Failed to commit and push changes: ${error.message}`);
        log('\n🔧 Manual steps:');
        log('1. git add .');
        log('2. git commit -m "Fix CORS configuration for nerafus.com domain"');
        log('3. git push origin main');
        process.exit(1);
    }
}

// Run the script
main().catch(error => {
    logError(`Script failed: ${error.message}`);
    process.exit(1);
}); 