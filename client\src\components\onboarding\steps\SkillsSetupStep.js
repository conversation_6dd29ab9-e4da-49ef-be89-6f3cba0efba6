import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../../contexts/AuthContext';
import { useOnboarding } from '../../../contexts/OnboardingContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { FaCode, FaPalette, FaPen, FaBullhorn, FaPlus, FaTimes } from 'react-icons/fa';

const SkillsSetupStep = () => {
 const { user } = useAuth();
 const { 
  onboardingData, 
  updateOnboardingData, 
  goToNextStep, 
  goToPreviousStep,
  loading 
 } = useOnboarding();
 const { t } = useLanguage();

 const [selectedSkills, setSelectedSkills] = useState([]);
 const [customSkill, setCustomSkill] = useState('');
 const [skillLevel, setSkillLevel] = useState('');
 const [workExperience, setWorkExperience] = useState('');
 const [major, setMajor] = useState('');
 const [errors, setErrors] = useState({});

 useEffect(() => {
  if (onboardingData.profile?.skills) {
   setSelectedSkills(onboardingData.profile.skills);
  }
  if (onboardingData.profile?.skillLevel) {
   setSkillLevel(onboardingData.profile.skillLevel);
  }
  if (onboardingData.profile?.workExperience) {
   setWorkExperience(onboardingData.profile.workExperience);
  }
  if (onboardingData.profile?.major) {
   setMajor(onboardingData.profile.major);
  }
 }, [onboardingData]);

 const skillCategories = [
  {
   name: t('webDevelopment'),
   icon: FaCode,
   skills: ['JavaScript', 'React', 'Vue.js', 'Angular', 'Node.js', 'Python', 'PHP', 'Laravel', 'WordPress', 'HTML/CSS']
  },
  {
   name: t('design'),
   icon: FaPalette,
   skills: ['UI/UX Design', 'Graphic Design', 'Logo Design', 'Photoshop', 'Illustrator', 'Figma', 'Sketch', 'InDesign', 'After Effects', 'Blender']
  },
  {
   name: t('writing'),
   icon: FaPen,
   skills: ['Content Writing', 'Copywriting', 'Technical Writing', 'Blog Writing', 'SEO Writing', 'Translation', 'Proofreading', 'Creative Writing']
  },
  {
   name: t('marketing'),
   icon: FaBullhorn,
   skills: ['Digital Marketing', 'SEO', 'Social Media Marketing', 'Google Ads', 'Facebook Ads', 'Email Marketing', 'Content Marketing', 'Analytics']
  }
 ];

 const toggleSkill = (skill) => {
  setSelectedSkills(prev => {
   if (prev.includes(skill)) {
    return prev.filter(s => s !== skill);
   } else if (prev.length < 10) {
    return [...prev, skill];
   } else {
    setErrors({ skills: t('maxSkillsError') });
    return prev;
   }
  });

  if (errors.skills) {
   setErrors({});
  }
 };

 const addCustomSkill = () => {
  if (customSkill.trim() && !selectedSkills.includes(customSkill.trim()) && selectedSkills.length < 10) {
   setSelectedSkills(prev => [...prev, customSkill.trim()]);
   setCustomSkill('');
  }
 };

 const removeSkill = (skill) => {
  setSelectedSkills(prev => prev.filter(s => s !== skill));
 };

 const validateForm = () => {
  const newErrors = {};

  if (selectedSkills.length < 3) {
   newErrors.skills = t('minSkillsError');
  }

  if (!skillLevel) {
   newErrors.skillLevel = 'Please select your skill level';
  }

  if (!workExperience) {
   newErrors.workExperience = 'Please select your work experience';
  }

  if (!major) {
   newErrors.major = 'Please select your specialization';
  }

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
 };

 const handleNext = () => {
  if (validateForm()) {
   updateOnboardingData({
    profile: {
     ...onboardingData.profile,
     skills: selectedSkills,
     skillLevel,
     workExperience,
     major
    }
   });
   goToNextStep();
  }
 };

 return (
  <div className="p-8 md:p-12">
   {/* Header */}
   <motion.div
    className="text-center mb-8"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
   >
    <div className="w-16 h-16 bg-gradient-to-br from-medieval-gold-400 to-medieval-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
     <FaCode className="text-2xl text-white" />
    </div>
    
    <h2 className="text-2xl md:text-3xl font-medium font-bold text-gray-800 mb-2">
     {t('skillsSetup')}
    </h2>
    
    <p className="text-gray-600 font-medium">
     {t('skillsSetupDescription')}
    </p>
   </motion.div>

   {/* Experience Level & Specialization */}
   <motion.div
    className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
   >
    <div>
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('skillLevel')} *
     </label>
     <select
      value={skillLevel}
      onChange={(e) => setSkillLevel(e.target.value)}
      className="form-input w-full"
     >
      <option value="">{t('selectSkillLevel')}</option>
      <option value="beginner">{t('beginner')}</option>
      <option value="intermediate">{t('intermediate')}</option>
      <option value="advanced">{t('advanced')}</option>
      <option value="expert">{t('expert')}</option>
     </select>
     {errors.skillLevel && (
      <p className="text-red-500 mt-1 text-sm">{errors.skillLevel}</p>
     )}
    </div>

    <div>
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('workExperience')} *
     </label>
     <select
      value={workExperience}
      onChange={(e) => setWorkExperience(e.target.value)}
      className="form-input w-full"
     >
      <option value="">{t('selectWorkExperience')}</option>
      <option value="0-1">{t('lessThanOneYear')}</option>
      <option value="1-3">{t('oneToThreeYears')}</option>
      <option value="3-5">{t('threeToFiveYears')}</option>
      <option value="5-10">{t('fiveToTenYears')}</option>
      <option value="10+">{t('moreThanTenYears')}</option>
     </select>
     {errors.workExperience && (
      <p className="text-red-500 mt-1 text-sm">{errors.workExperience}</p>
     )}
    </div>

    <div className="md:col-span-2">
     <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
      {t('specialization')} *
     </label>
     <select
      value={major}
      onChange={(e) => setMajor(e.target.value)}
      className="form-input w-full"
     >
      <option value="">{t('selectSpecialization')}</option>
      <option value="web-development">{t('webDevelopment')}</option>
      <option value="mobile-development">{t('mobileDevelopment')}</option>
      <option value="design">{t('design')}</option>
      <option value="writing">{t('writing')}</option>
      <option value="marketing">{t('marketing')}</option>
      <option value="business">{t('businessFinance')}</option>
      <option value="other">{t('otherSpecialization')}</option>
     </select>
     {errors.major && (
      <p className="text-red-500 mt-1 text-sm">{errors.major}</p>
     )}
    </div>
   </motion.div>

   {/* Selected Skills */}
   {selectedSkills.length > 0 && (
    <motion.div
     className="mb-8"
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     transition={{ delay: 0.2 }}
    >
     <h3 className="font-medium font-semibold text-gray-800 mb-4">
      {t('selectedSkills')} ({selectedSkills.length}/10)
     </h3>
     <div className="flex flex-wrap gap-2">
      {selectedSkills.map((skill, index) => (
       <motion.span
        key={skill}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: index * 0.05 }}
        className="bg-blue-100 text-medieval-gold-800 px-3 py-2 rounded-full text-sm font-medium flex items-center"
       >
        {skill}
        <button
         onClick={() => removeSkill(skill)}
         className="ml-2 text-medieval-gold-600 hover:text-medieval-gold-800"
        >
         <FaTimes className="text-xs" />
        </button>
       </motion.span>
      ))}
     </div>
    </motion.div>
   )}

   {/* Skill Categories */}
   <motion.div
    className="space-y-6 mb-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.3 }}
   >
    {skillCategories.map((category, categoryIndex) => (
     <div key={category.name} className="bg-medieval-brown-50 rounded-xl p-6 border border-gray-200">
      <div className="flex items-center mb-4">
       <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
        <category.icon className="text-lg text-medieval-gold-600" />
       </div>
       <h3 className="font-medium font-semibold text-gray-800">
        {category.name}
       </h3>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
       {category.skills.map((skill, skillIndex) => (
        <motion.button
         key={skill}
         initial={{ opacity: 0, y: 10 }}
         animate={{ opacity: 1, y: 0 }}
         transition={{ delay: 0.4 + categoryIndex * 0.1 + skillIndex * 0.02 }}
         onClick={() => toggleSkill(skill)}
         className={`p-2 rounded-lg text-sm font-medium transition-all ${
          selectedSkills.includes(skill)
           ? 'bg-blue-500 text-white'
           : 'bg-white border border-gray-200 hover:border-blue-300 text-gray-700'
         }`}
        >
         {skill}
        </motion.button>
       ))}
      </div>
     </div>
    ))}
   </motion.div>

   {/* Custom Skill Input */}
   <motion.div
    className="mb-6"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.6 }}
   >
    <label className="block text-sm font-medium font-medium text-gray-700 mb-2">
     {t('addCustomSkill')}
    </label>
    <div className="flex gap-2">
     <input
      type="text"
      value={customSkill}
      onChange={(e) => setCustomSkill(e.target.value)}
      onKeyPress={(e) => e.key === 'Enter' && addCustomSkill()}
      className="form-input flex-1"
      placeholder={t('customSkillPlaceholder')}
      maxLength={30}
     />
     <button
      onClick={addCustomSkill}
      disabled={!customSkill.trim() || selectedSkills.length >= 10}
      className="btn-secondary px-4 py-2 font-medium flex items-center"
     >
      <FaPlus className="mr-1" />
      {t('add')}
     </button>
    </div>
   </motion.div>

   {/* Error Messages */}
   {(errors.skills || errors.skillLevel || errors.workExperience || errors.major) && (
    <motion.div
     initial={{ opacity: 0 }}
     animate={{ opacity: 1 }}
     className="mb-6"
    >
     {errors.skills && (
      <p className="text-red-500 text-sm font-medium">{errors.skills}</p>
     )}
     {errors.skillLevel && (
      <p className="text-red-500 text-sm font-medium">{errors.skillLevel}</p>
     )}
     {errors.workExperience && (
      <p className="text-red-500 text-sm font-medium">{errors.workExperience}</p>
     )}
     {errors.major && (
      <p className="text-red-500 text-sm font-medium">{errors.major}</p>
     )}
    </motion.div>
   )}

   {/* Navigation */}
   <motion.div
    className="flex justify-between"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.8 }}
   >
    <button
     onClick={goToPreviousStep}
     disabled={loading}
     className="btn-secondary px-6 py-2 font-medium"
    >
     {t('previous')}
    </button>

    <button
     onClick={handleNext}
     disabled={loading}
     className="btn-primary px-6 py-2 font-medium"
    >
     {t('next')}
    </button>
   </motion.div>
  </div>
 );
};

export default SkillsSetupStep;
