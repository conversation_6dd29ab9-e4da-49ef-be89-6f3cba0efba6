# Community Service Environment Variables
# Copy this file to .env and update with your values

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3006
SERVICE_HOST=localhost

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Option 1: Use DATABASE_URL (recommended for production)
# DATABASE_URL=postgresql://username:password@host:port/database

# Option 2: Use individual variables
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_community_service
DB_USER=vwork_admin
DB_PASSWORD=VWork2024!

# Database Pool Settings
DB_POOL_MAX=20
DB_POOL_MIN=2
DB_CONNECTION_TIMEOUT=5000
DB_IDLE_TIMEOUT=30000

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
CORS_CREDENTIALS=true

# =============================================================================
# SERVICE URLs (for inter-service communication)
# =============================================================================
# These will be auto-generated if not provided, using SERVICE_HOST and default ports
API_GATEWAY_URL=http://localhost:8080
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
JOB_SERVICE_URL=http://localhost:3004
CHAT_SERVICE_URL=http://localhost:3005
PAYMENT_SERVICE_URL=http://localhost:3007
EVENT_BUS_SERVICE_URL=http://localhost:3007

# =============================================================================
# EVENT STORE CONFIGURATION
# =============================================================================
EVENT_STORE_ENABLED=false
KURRENTDB_URL=esdb://localhost:2113?tls=false
EVENT_STORE_BATCH_SIZE=100
EVENT_STORE_RETRY_ATTEMPTS=3
EVENT_STORE_TIMEOUT=5000

# =============================================================================
# CONTENT LIMITS
# =============================================================================
MAX_TITLE_LENGTH=200
MAX_POST_LENGTH=10000
MAX_COMMENT_LENGTH=2000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=debug
LOG_FORMAT=simple

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ENABLE_HELMET=true
ENABLE_COMPRESSION=true
TRUST_PROXY=false

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=10000

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# Uncomment and set these for production deployment
# NODE_ENV=production
# LOG_LEVEL=info
# LOG_FORMAT=json
# TRUST_PROXY=true
# ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com 