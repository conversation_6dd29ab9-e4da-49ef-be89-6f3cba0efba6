import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import {
 // Sections
 AppleHeroSection,
 AppleHowItWorks,
 AppleCTASection,
 AppleFeaturesSection,
 // Interactive
 AppleCategoriesGrid,
} from '../components/apple';

const HomePage = () => {
 const { t } = useLanguage();

 // Apple-style homepage with comprehensive sections
 return (
     <div className='min-h-screen bg-white transition-colors duration-300'>
   {/* Hero Section - Main landing area */}
   <AppleHeroSection />

   {/* Categories Grid - Browse by category */}
   <AppleCategoriesGrid />

   {/* Why Choose VWork - Features section */}
   <AppleFeaturesSection />

   {/* How It Works - Explain the process */}
   <AppleHowItWorks />

   {/* Call to Action - Final conversion */}
   <AppleCTASection />
  </div>
 );
};

export default HomePage;
