# 🚀 Hướng dẫn Deploy Manual lên Azure

## 📋 Bước 1: Tạo App Service trên Azure Portal

1. **Mở Azure Portal:** https://portal.azure.com
2. **Tạo Resource Group:**
   - Name: `vwork-rg`
   - Region: `Southeast Asia`

3. **Tạo App Service:**
   - Click "Create a resource"
   - Tìm "Web App"
   - Click "Create"

4. **<PERSON><PERSON>u hình cơ bản:**
   - **Resource Group:** `vwork-rg`
   - **Name:** `vwork-community-service`
   - **Publish:** Code
   - **Runtime stack:** Node 20 LTS
   - **Operating System:** Linux
   - **Region:** Southeast Asia
   - **App Service Plan:** Create new
     - **Name:** `vwork-plan`
     - **SKU and size:** Free (F1)

5. **Review + Create**

## 📦 Bước 2: Deploy Code

### Cách 1: Deploy từ GitHub (Khuyến nghị)

1. **Push code lên GitHub:**
   ```bash
   git add .
   git commit -m "Prepare for Azure deployment"
   git push origin main
   ```

2. **Trong Azure Portal:**
   - Vào App Service → Deployment Center
   - Chọn "GitHub"
   - Connect GitHub account
   - Chọn repository và branch

### Cách 2: Deploy từ Local

1. **Cài đặt Azure CLI extension:**
   ```bash
   az extension add --name webapp
   ```

2. **Deploy từ thư mục project:**
   ```bash
   cd services/community-service
   az webapp deployment source config-local-git --name vwork-community-service --resource-group vwork-rg
   git remote add azure <git-url-from-above>
   git push azure main
   ```

### Cách 3: Deploy bằng ZIP

1. **Tạo deployment package:**
   ```bash
   cd services/community-service
   powershell Compress-Archive -Path src,scripts,migrations,app.js,package.json,package-lock.json,web.config -DestinationPath deploy.zip
   ```

2. **Deploy qua Azure CLI:**
   ```bash
   az webapp deployment source config-zip --resource-group vwork-rg --name vwork-community-service --src deploy.zip
   ```

## ⚙️ Bước 3: Cấu hình Environment Variables

Trong Azure Portal → App Service → Configuration → Application settings:

### Firebase Configuration (Bắt buộc)
```
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key\n-----END PRIVATE KEY-----\n"
```

### Database Configuration
```
DATABASE_URL=****************************************/database?sslmode=require
```

### CORS Configuration
```
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### Other Settings
```
NODE_ENV=production
PORT=8080
WEBSITES_PORT=8080
```

## 🗄️ Bước 4: Setup Database (Tùy chọn)

### Tạo PostgreSQL Database

1. **Tạo Database Server:**
   - Azure Portal → Create a resource
   - Tìm "Azure Database for PostgreSQL flexible servers"
   - Create với cấu hình:
     - **Server name:** `vwork-db-server`
     - **Resource Group:** `vwork-rg`
     - **Region:** Southeast Asia
     - **Admin username:** `vwork_admin`
     - **Password:** Tạo password mạnh

2. **Tạo Database:**
   - Vào Database Server
   - Databases → Create database
   - **Name:** `vwork_community_service`

3. **Cấu hình Firewall:**
   - Networking → Firewall rules
   - Add rule: `0.0.0.0 - ***************` (Allow all)

4. **Cập nhật DATABASE_URL:**
   ```
   DATABASE_URL=postgresql://vwork_admin:<EMAIL>:5432/vwork_community_service?sslmode=require
   ```

## 🔄 Bước 5: Run Database Migrations

1. **SSH vào App Service:**
   ```bash
   az webapp ssh --name vwork-community-service --resource-group vwork-rg
   ```

2. **Chạy migrations:**
   ```bash
   npm run db:setup
   ```

## 🧪 Bước 6: Test Deployment

1. **Test health endpoint:**
   ```bash
   curl https://vwork-community-service.azurewebsites.net/health
   ```

2. **Test API:**
   ```bash
   curl https://vwork-community-service.azurewebsites.net/api
   ```

## 📊 Bước 7: Monitoring

1. **View logs:**
   ```bash
   az webapp log tail --name vwork-community-service --resource-group vwork-rg
   ```

2. **Azure Portal:**
   - App Service → Log stream
   - App Service → Monitoring → Metrics

## 🔧 Troubleshooting

### App không start
- Kiểm tra logs trong Azure Portal
- Verify environment variables
- Check package.json scripts

### Database connection failed
- Verify DATABASE_URL format
- Check firewall rules
- Test connection via SSH

### Environment variables không hoạt động
- Restart app sau khi thay đổi
- Check format của FIREBASE_PRIVATE_KEY

## 🎯 Deploy các Service khác

Lặp lại các bước trên cho các service khác:

1. **User Service:** `vwork-user-service`
2. **Auth Service:** `vwork-auth-service`
3. **Job Service:** `vwork-job-service`
4. **Project Service:** `vwork-project-service`
5. **Chat Service:** `vwork-chat-service`
6. **API Gateway:** `vwork-api-gateway`

## 📋 Checklist

- [ ] Tạo Resource Group
- [ ] Tạo App Service Plan
- [ ] Tạo App Service
- [ ] Deploy code
- [ ] Cấu hình Environment Variables
- [ ] Setup Database (nếu cần)
- [ ] Run Migrations
- [ ] Test endpoints
- [ ] Setup Monitoring
- [ ] Deploy các service khác
- [ ] Update frontend API endpoints 