/**
 * Firebase Configuration for Team Service
 */

const admin = require('firebase-admin');
const config = require('./config');

// Initialize Firebase Admin SDK
function initializeFirebase() {
  try {
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert({
          projectId: config.FIREBASE_PROJECT_ID,
          clientEmail: config.FIREBASE_CLIENT_EMAIL,
          privateKey: config.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
        })
      });
    }
    
    console.log('✅ Firebase Admin SDK initialized for Team Service');
    return admin;
  } catch (error) {
    console.error('❌ Firebase initialization failed:', error.message);
    throw error;
  }
}

// Verify Firebase ID token
async function verifyToken(token) {
  try {
    if (!token) {
      throw new Error('No token provided');
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('❌ Token verification failed:', error.message);
    throw error;
  }
}

// Get user by UID
async function getUserByUid(uid) {
  try {
    const userRecord = await admin.auth().getUser(uid);
    return userRecord;
  } catch (error) {
    console.error('❌ Get user by UID failed:', error.message);
    throw error;
  }
}

module.exports = {
  admin,
  initializeFirebase,
  verifyToken,
  getUserByUid
}; 