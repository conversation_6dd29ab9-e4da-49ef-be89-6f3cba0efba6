import React, { useState, useEffect } from 'react';
import {
 Bars3Icon,
 XMarkIcon,
 AdjustmentsHorizontalIcon,
 MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

const CommunityResponsive = ({ 
 children, 
 sidebar, 
 searchComponent, 
 filtersComponent,
 showMobileMenu,
 setShowMobileMenu 
}) => {
 const [isMobile, setIsMobile] = useState(false);
 const [isTablet, setIsTablet] = useState(false);

 useEffect(() => {
  const checkScreenSize = () => {
   setIsMobile(window.innerWidth < 768);
   setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
  };

  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
  return () => window.removeEventListener('resize', checkScreenSize);
 }, []);

 return (
  <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50  transition-all duration-500">
   {/* Mobile Header */}
   {isMobile && (
    <div className="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200 px-4 py-3">
     <div className="flex items-center justify-between">
      <h1 className="text-lg font-bold text-gray-900">
       Community
      </h1>
      <div className="flex items-center space-x-2">
       <button
        onClick={() => setShowMobileMenu('search')}
        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
       >
        <MagnifyingGlassIcon className="h-5 w-5" />
       </button>
       <button
        onClick={() => setShowMobileMenu('filters')}
        className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200"
       >
        <AdjustmentsHorizontalIcon className="h-5 w-5" />
       </button>
       <button
        onClick={() => setShowMobileMenu('menu')}
        className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all duration-200"
       >
        <Bars3Icon className="h-5 w-5" />
       </button>
      </div>
     </div>
    </div>
   )}

   <div className="flex">
    {/* Desktop Sidebar */}
    {!isMobile && sidebar && (
     <div className="hidden lg:flex lg:w-80 lg:flex-col lg:fixed lg:inset-y-0 lg:z-50">
      <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4 border-r border-gray-200">
       {sidebar}
      </div>
     </div>
    )}

    {/* Main Content */}
    <div className={`flex-1 ${!isMobile && sidebar ? 'lg:pl-80' : ''}`}>
     <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
      {children}
     </div>
    </div>
   </div>

   {/* Mobile Overlays */}
   {isMobile && showMobileMenu && (
    <>
     {/* Backdrop */}
     <div 
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
      onClick={() => setShowMobileMenu(null)}
     />

     {/* Mobile Search Overlay */}
     {showMobileMenu === 'search' && (
      <div className="fixed inset-x-0 top-0 z-50 bg-white shadow-xl">
       <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">
         Search
        </h2>
        <button
         onClick={() => setShowMobileMenu(null)}
         className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
        >
         <XMarkIcon className="h-5 w-5" />
        </button>
       </div>
       <div className="p-4">
        {searchComponent}
       </div>
      </div>
     )}

     {/* Mobile Filters Overlay */}
     {showMobileMenu === 'filters' && (
      <div className="fixed inset-x-0 top-0 z-50 bg-white shadow-xl max-h-screen overflow-y-auto">
       <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">
         Filters
        </h2>
        <button
         onClick={() => setShowMobileMenu(null)}
         className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
        >
         <XMarkIcon className="h-5 w-5" />
        </button>
       </div>
       <div className="p-4">
        {filtersComponent}
       </div>
      </div>
     )}

     {/* Mobile Menu Overlay */}
     {showMobileMenu === 'menu' && sidebar && (
      <div className="fixed inset-y-0 right-0 z-50 w-full max-w-sm bg-white shadow-xl">
       <div className="flex items-center justify-between p-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
           Menu
          </h2>
        <button
         onClick={() => setShowMobileMenu(null)}
         className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
        >
         <XMarkIcon className="h-5 w-5" />
        </button>
       </div>
       <div className="p-4">
        {sidebar}
       </div>
      </div>
     )}
    </>
   )}
  </div>
 );
};

// Responsive Grid Component
export const ResponsiveGrid = ({ children, className = '' }) => {
 return (
  <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 ${className}`}>
   {children}
  </div>
 );
};

// Responsive Card Component
export const ResponsiveCard = ({ children, className = '', hover = true }) => {
 return (
  <div className={`
   bg-white 
   rounded-xl sm:rounded-2xl 
   shadow-sm sm:shadow-lg 
   border border-gray-100 
   p-4 sm:p-6 
   transition-all duration-300
   ${hover ? 'hover:shadow-xl hover:scale-[1.02] hover:border-blue-200' : ''}
   ${className}
  `}>
   {children}
  </div>
 );
};

// Responsive Text Component
export const ResponsiveText = ({ 
 children, 
 size = 'base', 
 weight = 'normal', 
 className = '' 
}) => {
 const sizeClasses = {
  xs: 'text-xs sm:text-sm',
  sm: 'text-sm sm:text-base',
  base: 'text-base sm:text-lg',
  lg: 'text-lg sm:text-xl',
  xl: 'text-xl sm:text-2xl',
  '2xl': 'text-2xl sm:text-3xl',
  '3xl': 'text-3xl sm:text-4xl',
  '4xl': 'text-4xl sm:text-5xl'
 };

 const weightClasses = {
  light: 'font-light',
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold'
 };

 return (
  <span className={`${sizeClasses[size]} ${weightClasses[weight]} ${className}`}>
   {children}
  </span>
 );
};

// Responsive Button Component
export const ResponsiveButton = ({ 
 children, 
 size = 'md', 
 variant = 'primary', 
 className = '',
 ...props 
}) => {
 const sizeClasses = {
  sm: 'px-3 py-2 text-sm sm:px-4 sm:py-2',
  md: 'px-4 py-2 text-sm sm:px-6 sm:py-3 sm:text-base',
  lg: 'px-6 py-3 text-base sm:px-8 sm:py-4 sm:text-lg'
 };

 const variantClasses = {
  primary: 'bg-blue-600 hover:bg-blue-700 text-white',
  secondary: 'bg-gray-100 hover:bg-gray-200 text-gray-900',
  outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'
 };

 return (
  <button 
   className={`
    ${sizeClasses[size]} 
    ${variantClasses[variant]}
    rounded-lg sm:rounded-xl 
    font-medium 
    transition-all duration-300 
    hover:scale-105 
    shadow-sm hover:shadow-lg
    ${className}
   `}
   {...props}
  >
   {children}
  </button>
 );
};



export default CommunityResponsive;
