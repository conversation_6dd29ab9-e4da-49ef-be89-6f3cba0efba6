import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  MegaphoneIcon, 
  StarIcon, 
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline';

// Mock data cho freelancers chuyên ngành Marketing
const mockMarketers = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    title: 'Digital Marketing Specialist',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 187,
    hourlyRate: 30,
    location: '<PERSON><PERSON> Ch<PERSON> Minh',
    skills: ['Google Ads', 'Facebook Ads', 'SEO', 'Analytics'],
    description: 'Chuyên gia marketing số với 7+ năm kinh nghiệm. Đã giúp 200+ doanh nghiệp tăng doanh thu.',
    completedJobs: 245,
    responseTime: '1 giờ',
    availability: 'Sẵn sàng',
    achievements: ['Google Ads Certified', 'Facebook Blueprint Certified'],
    successRate: 98,
    avgROI: '350%'
  },
  {
    id: 2,
    name: 'Tr<PERSON>n Văn <PERSON>',
    title: 'Content Marketing Manager',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 134,
    hourlyRate: 25,
    location: 'Hà Nội',
    skills: ['Content Strategy', 'Copywriting', 'Social Media', 'Email Marketing'],
    description: 'Chuyên gia content marketing. Tạo ra nội dung hấp dẫn và chiến lược marketing hiệu quả.',
    completedJobs: 189,
    responseTime: '30 phút',
    availability: 'Sẵn sàng',
    achievements: ['HubSpot Certified', 'Content Marketing Institute'],
    successRate: 95,
    avgROI: '280%'
  },
  {
    id: 3,
    name: 'Lê Thị Hương',
    title: 'SEO Specialist',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 4.7,
    reviewCount: 98,
    hourlyRate: 28,
    location: 'Đà Nẵng',
    skills: ['Technical SEO', 'Keyword Research', 'Link Building', 'Analytics'],
    description: 'Chuyên gia SEO với khả năng đưa website lên top Google. Kinh nghiệm 5+ năm.',
    completedJobs: 156,
    responseTime: '2 giờ',
    availability: 'Bận',
    achievements: ['Google Analytics Certified', 'SEMrush Certified'],
    successRate: 92,
    avgROI: '420%'
  },
  {
    id: 4,
    name: 'Phạm Văn Đức',
    title: 'Social Media Manager',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 4.6,
    reviewCount: 112,
    hourlyRate: 22,
    location: 'Hồ Chí Minh',
    skills: ['Facebook Marketing', 'Instagram', 'TikTok', 'Community Management'],
    description: 'Chuyên gia quản lý mạng xã hội. Xây dựng cộng đồng và tăng engagement hiệu quả.',
    completedJobs: 203,
    responseTime: '45 phút',
    availability: 'Sẵn sàng',
    achievements: ['Facebook Blueprint', 'Hootsuite Certified'],
    successRate: 94,
    avgROI: '250%'
  },
  {
    id: 5,
    name: 'Võ Thị Mai',
    title: 'Email Marketing Specialist',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 4.8,
    reviewCount: 89,
    hourlyRate: 24,
    location: 'Hà Nội',
    skills: ['Mailchimp', 'Automation', 'A/B Testing', 'CRM'],
    description: 'Chuyên gia email marketing với tỷ lệ mở email cao. Tạo ra các chiến dịch email hiệu quả.',
    completedJobs: 167,
    responseTime: '1.5 giờ',
    availability: 'Sẵn sàng',
    achievements: ['Mailchimp Certified', 'HubSpot Email Marketing'],
    successRate: 96,
    avgROI: '380%'
  },
  {
    id: 6,
    name: 'Hoàng Văn Tài',
    title: 'Performance Marketing Manager',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 4.9,
    reviewCount: 156,
    hourlyRate: 35,
    location: 'Hồ Chí Minh',
    skills: ['PPC', 'Conversion Optimization', 'Data Analysis', 'ROI Tracking'],
    description: 'Chuyên gia performance marketing. Tối ưu hóa chiến dịch để đạt ROI cao nhất.',
    completedJobs: 134,
    responseTime: '2 giờ',
    availability: 'Bận',
    achievements: ['Google Ads Expert', 'Facebook Marketing Partner'],
    successRate: 97,
    avgROI: '450%'
  }
];

const MarketingPage = () => {
  const { t } = useLanguage();
  const [filteredMarketers, setFilteredMarketers] = useState(mockMarketers);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSkill, setSelectedSkill] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [availability, setAvailability] = useState('');

  // Get all unique skills
  const allSkills = [...new Set(mockMarketers.flatMap(marketer => marketer.skills))];

  // Filter function
  useEffect(() => {
    let filtered = mockMarketers;

    if (searchQuery) {
      filtered = filtered.filter(marketer => 
        marketer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        marketer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        marketer.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedSkill) {
      filtered = filtered.filter(marketer => marketer.skills.includes(selectedSkill));
    }

    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filtered = filtered.filter(marketer => {
        if (max) {
          return marketer.hourlyRate >= min && marketer.hourlyRate <= max;
        } else {
          return marketer.hourlyRate >= min;
        }
      });
    }

    if (availability) {
      filtered = filtered.filter(marketer => marketer.availability === availability);
    }

    setFilteredMarketers(filtered);
  }, [searchQuery, selectedSkill, priceRange, availability]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-4 mb-6">
            <div className="p-3 bg-green-100 rounded-lg">
              <MegaphoneIcon className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Sales & Marketing</h1>
              <p className="text-lg text-gray-600">Tìm kiếm các chuyên gia marketing và bán hàng hàng đầu</p>
            </div>
          </div>
          
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input
              type="text"
              placeholder="Tìm kiếm marketer..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />

            <select
              value={selectedSkill}
              onChange={(e) => setSelectedSkill(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="">Tất cả kỹ năng</option>
              {allSkills.map(skill => (
                <option key={skill} value={skill}>{skill}</option>
              ))}
            </select>

            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="">Tất cả mức giá</option>
              <option value="0-20">$0 - $20/giờ</option>
              <option value="20-30">$20 - $30/giờ</option>
              <option value="30-40">$30 - $40/giờ</option>
              <option value="40">$40+/giờ</option>
            </select>

            <select
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Sẵn sàng">Sẵn sàng</option>
              <option value="Bận">Bận</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <p className="text-gray-600">
            Tìm thấy <span className="font-semibold">{filteredMarketers.length}</span> marketer phù hợp
          </p>
        </div>

        {/* Marketer Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMarketers.map(marketer => (
            <div key={marketer.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
              {/* Marketer Header */}
              <div className="flex items-start space-x-4 mb-4">
                <img
                  src={marketer.avatar}
                  alt={marketer.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{marketer.name}</h3>
                  <p className="text-green-600 font-medium">{marketer.title}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="flex items-center">
                      <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">{marketer.rating}</span>
                    </div>
                    <span className="text-gray-400">•</span>
                    <span className="text-sm text-gray-600">{marketer.reviewCount} đánh giá</span>
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-green-50 rounded-lg">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    <ChartBarIcon className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-semibold text-green-800">{marketer.successRate}%</span>
                  </div>
                  <p className="text-xs text-gray-600">Tỷ lệ thành công</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-semibold text-green-800">{marketer.avgROI}</span>
                  </div>
                  <p className="text-xs text-gray-600">ROI trung bình</p>
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{marketer.description}</p>

              {/* Skills */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {marketer.skills.slice(0, 4).map(skill => (
                    <span key={skill} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      {skill}
                    </span>
                  ))}
                  {marketer.skills.length > 4 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      +{marketer.skills.length - 4}
                    </span>
                  )}
                </div>
              </div>

              {/* Achievements */}
              <div className="mb-4">
                <p className="text-xs text-gray-500 mb-2">Chứng chỉ:</p>
                <div className="flex flex-wrap gap-1">
                  {marketer.achievements.slice(0, 2).map(achievement => (
                    <span key={achievement} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                      {achievement}
                    </span>
                  ))}
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center space-x-2">
                  <MapPinIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{marketer.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{marketer.responseTime}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">${marketer.hourlyRate}/giờ</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full ${marketer.availability === 'Sẵn sàng' ? 'bg-green-400' : 'bg-red-400'}`}></span>
                  <span className="text-gray-600">{marketer.availability}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm font-medium">
                  Liên hệ
                </button>
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm">
                  Xem hồ sơ
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {filteredMarketers.length > 0 && (
          <div className="text-center mt-8">
            <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              Xem thêm marketer
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MarketingPage;
