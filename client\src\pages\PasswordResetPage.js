import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { ApplePageWrapper } from '../components/apple';
import PasswordStrengthIndicator from '../components/common/PasswordStrengthIndicator';
import {
 LockClosedIcon,
 CheckCircleIcon,
 ExclamationTriangleIcon,
 EyeIcon,
 EyeSlashIcon,
 ArrowLeftIcon,
} from '@heroicons/react/24/outline';

const PasswordResetPage = () => {
 const { t } = useLanguage();
 const navigate = useNavigate();
 const [searchParams] = useSearchParams();
 const { verifyPasswordResetCode, confirmPasswordReset, loading } = useAuth();
 
 const [resetState, setResetState] = useState('verifying'); // verifying, form, success, error
 const [error, setError] = useState('');
 const [email, setEmail] = useState('');
 const [newPassword, setNewPassword] = useState('');
 const [confirmPassword, setConfirmPassword] = useState('');
 const [showPassword, setShowPassword] = useState(false);
 const [showConfirmPassword, setShowConfirmPassword] = useState(false);
 const [formErrors, setFormErrors] = useState({});

 // Get action code from URL
 const actionCode = searchParams.get('oobCode');

 // Verify the reset code when component mounts
 useEffect(() => {
  if (actionCode) {
   handleVerifyResetCode(actionCode);
  } else {
   setResetState('error');
   setError('Mã đặt lại mật khẩu không hợp lệ.');
  }
 }, [actionCode]);

 const handleVerifyResetCode = async (code) => {
  try {
   const result = await verifyPasswordResetCode(code);
   if (result.success) {
    setEmail(result.email);
    setResetState('form');
    setError('');
   } else {
    setResetState('error');
    setError(result.error);
   }
  } catch (err) {
   setResetState('error');
   setError('Mã đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.');
  }
 };

 const validateForm = () => {
  const errors = {};

  if (!newPassword) {
   errors.newPassword = 'Mật khẩu mới là bắt buộc';
  } else if (newPassword.length < 6) {
   errors.newPassword = 'Mật khẩu phải có ít nhất 6 ký tự';
  }

  if (!confirmPassword) {
   errors.confirmPassword = 'Xác nhận mật khẩu là bắt buộc';
  } else if (newPassword !== confirmPassword) {
   errors.confirmPassword = 'Mật khẩu xác nhận không khớp';
  }

  setFormErrors(errors);
  return Object.keys(errors).length === 0;
 };

 const handleSubmit = async (e) => {
  e.preventDefault();
  
  if (!validateForm()) return;

  try {
   const result = await confirmPasswordReset(actionCode, newPassword);
   if (result.success) {
    setResetState('success');
    setError('');
    
    // Redirect to login after success
    setTimeout(() => {
     navigate('/login');
    }, 3000);
   } else {
    setError(result.error);
   }
  } catch (err) {
   setError('Đã xảy ra lỗi khi đặt lại mật khẩu.');
  }
 };

 const handleInputChange = (field, value) => {
  if (field === 'newPassword') {
   setNewPassword(value);
  } else if (field === 'confirmPassword') {
   setConfirmPassword(value);
  }
  
  // Clear error when user starts typing
  if (formErrors[field]) {
   setFormErrors(prev => ({ ...prev, [field]: '' }));
  }
 };

 const renderContent = () => {
  switch (resetState) {
   case 'verifying':
    return (
     <div className="text-center">
      <div className="mx-auto flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
       <LockClosedIcon className="w-8 h-8 text-blue-600 animate-pulse" />
      </div>
      <h2 className="font-bold text-2xl font-bold text-gray-800 mb-4">
       Đang Xác Thực...
      </h2>
      <p className="font-medium text-gray-600">
       Vui lòng chờ trong khi chúng tôi xác thực yêu cầu đặt lại mật khẩu của bạn.
      </p>
     </div>
    );

   case 'form':
    return (
     <div>
      <div className="text-center mb-8">
       <div className="mx-auto flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
        <LockClosedIcon className="w-8 h-8 text-blue-600" />
       </div>
       <h2 className="font-bold text-2xl font-bold text-gray-800 mb-4">
        Đặt Lại Mật Khẩu
       </h2>
       <p className="font-medium text-gray-600">
        Nhập mật khẩu mới cho tài khoản: <span className="font-semibold">{email}</span>
       </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
       {/* New Password */}
       <div>
        <label
         htmlFor="newPassword"
         className="block font-medium text-sm font-medium text-gray-700 mb-2"
        >
         Mật khẩu mới
        </label>
        <div className="relative">
         <input
          id="newPassword"
          type={showPassword ? 'text' : 'password'}
          value={newPassword}
          onChange={(e) => handleInputChange('newPassword', e.target.value)}
          className={`form-input w-full px-4 py-3 pr-12 font-medium ${
           formErrors.newPassword ? 'border-red-500' : ''
          }`}
          placeholder="Nhập mật khẩu mới"
         />
         <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
         >
          {showPassword ? (
           <EyeSlashIcon className="w-5 h-5" />
          ) : (
           <EyeIcon className="w-5 h-5" />
          )}
         </button>
        </div>
        {formErrors.newPassword && (
         <p className="mt-1 text-sm text-red-600 font-medium">
          {formErrors.newPassword}
         </p>
        )}
        {newPassword && (
         <div className="mt-2">
          <PasswordStrengthIndicator password={newPassword} />
         </div>
        )}
       </div>

       {/* Confirm Password */}
       <div>
        <label
         htmlFor="confirmPassword"
         className="block font-medium text-sm font-medium text-gray-700 mb-2"
        >
         Xác nhận mật khẩu
        </label>
        <div className="relative">
         <input
          id="confirmPassword"
          type={showConfirmPassword ? 'text' : 'password'}
          value={confirmPassword}
          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
          className={`form-input w-full px-4 py-3 pr-12 font-medium ${
           formErrors.confirmPassword ? 'border-red-500' : ''
          }`}
          placeholder="Nhập lại mật khẩu mới"
         />
         <button
          type="button"
          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
         >
          {showConfirmPassword ? (
           <EyeSlashIcon className="w-5 h-5" />
          ) : (
           <EyeIcon className="w-5 h-5" />
          )}
         </button>
        </div>
        {formErrors.confirmPassword && (
         <p className="mt-1 text-sm text-red-600 font-medium">
          {formErrors.confirmPassword}
         </p>
        )}
       </div>

       {/* Error Message */}
       {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
         <p className="text-red-600 font-medium text-sm">{error}</p>
        </div>
       )}

       {/* Submit Button */}
       <button
        type="submit"
        disabled={loading}
        className="w-full btn-primary"
       >
        {loading ? 'Đang xử lý...' : 'Đặt Lại Mật Khẩu'}
       </button>
      </form>
     </div>
    );

   case 'success':
    return (
     <div className="text-center">
      <div className="mx-auto flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
       <CheckCircleIcon className="w-8 h-8 text-green-600" />
      </div>
      <h2 className="font-bold text-2xl font-bold text-gray-800 mb-4">
       Đặt Lại Mật Khẩu Thành Công!
      </h2>
      <p className="font-medium text-gray-600 mb-6">
       Mật khẩu của bạn đã được đặt lại thành công. 
       Bạn sẽ được chuyển hướng đến trang đăng nhập trong giây lát...
      </p>
      <div className="flex items-center justify-center space-x-2 text-gray-500">
       <ArrowLeftIcon className="w-4 h-4" />
       <span className="font-medium text-sm">Đang chuyển hướng...</span>
      </div>
     </div>
    );

   case 'error':
    return (
     <div className="text-center">
      <div className="mx-auto flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-6">
       <ExclamationTriangleIcon className="w-8 h-8 text-red-600" />
      </div>
      <h2 className="font-bold text-2xl font-bold text-gray-800 mb-4">
       Đặt Lại Mật Khẩu Thất Bại
      </h2>
      <p className="font-medium text-red-600 mb-6">
       {error || 'Không thể đặt lại mật khẩu. Link có thể đã hết hạn hoặc không hợp lệ.'}
      </p>
      <button
       onClick={() => navigate('/forgot-password')}
       className="w-full btn-primary mb-4"
      >
       Yêu Cầu Đặt Lại Mật Khẩu Mới
      </button>
      <button
       onClick={() => navigate('/login')}
       className="w-full btn-secondary"
      >
       Quay Lại Đăng Nhập
      </button>
     </div>
    );

   default:
    return null;
  }
 };

 return (
  <ApplePageWrapper>
   <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-red-50 flex items-center justify-center p-4">
    <div className="max-w-md w-full">
     <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
      {/* Logo */}
      <div className="text-center mb-8">
       <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
        <span className="text-white font-bold text-xl font-bold">V</span>
       </div>
       <h1 className="font-bold text-xl font-bold text-gray-800">
        VWork
       </h1>
      </div>

      {/* Content */}
      {renderContent()}
     </div>
    </div>
   </div>
  </ApplePageWrapper>
 );
};

export default PasswordResetPage; 
