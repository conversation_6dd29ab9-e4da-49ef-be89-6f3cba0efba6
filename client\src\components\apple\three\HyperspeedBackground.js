import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';

// Custom shader for chromatic aberration effect
const chromaticAberrationShader = {
 uniforms: {
  "tDiffuse": { value: null },
  "distortion": { value: 0.02 },
  "distortion2": { value: 0.5 },
  "speed": { value: 0.2 },
  "time": { value: 0 }
 },
 vertexShader: `
  varying vec2 vUv;
  void main() {
   vUv = uv;
   gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
 `,
 fragmentShader: `
  uniform sampler2D tDiffuse;
  uniform float distortion;
  uniform float distortion2;
  uniform float speed;
  uniform float time;
  varying vec2 vUv;
  
  void main() {
   vec2 uv = vUv;
   
   // Calculate distortion effect based on position
   float d = length(uv - 0.5);
   
   // Apply chromatic aberration (RGB shift)
   vec2 dir = normalize(uv - 0.5);
   float r = texture2D(tDiffuse, uv - dir * distortion * d).r;
   float g = texture2D(tDiffuse, uv).g;
   float b = texture2D(tDiffuse, uv + dir * distortion * d).b;
   
   gl_FragColor = vec4(r, g, b, 1.0);
  }
 `
};

const HyperspeedBackground = ({ className }) => {
 const containerRef = useRef(null);
 const requestRef = useRef(null);
 const sceneRef = useRef(null);
 const cameraRef = useRef(null);
 const rendererRef = useRef(null);
 const composerRef = useRef(null);
 const tubeRef = useRef(null);
 const textureRef = useRef(null);
 const chromaticPassRef = useRef(null);
 const timeRef = useRef(0);

 useEffect(() => {
  // Initialize Three.js scene
  const initialize = () => {
   if (!containerRef.current) return;
   
   // Scene setup
   const scene = new THREE.Scene();
   sceneRef.current = scene;
   
   // Camera setup
   const camera = new THREE.PerspectiveCamera(
    70, 
    window.innerWidth / window.innerHeight, 
    0.1, 
    1000
   );
   camera.position.z = 1;
   cameraRef.current = camera;
   
   // Renderer setup
   const renderer = new THREE.WebGLRenderer({ 
    antialias: true,
    alpha: true 
   });
   renderer.setSize(window.innerWidth, window.innerHeight);
   renderer.setPixelRatio(window.devicePixelRatio);
   renderer.setClearColor(0x000000, 0);
   containerRef.current.appendChild(renderer.domElement);
   rendererRef.current = renderer;
   
   // Create highway lines - DISABLED to remove colored lines
   // createHighwayLines();
   
   // Post-processing setup
   setupPostProcessing(scene, camera, renderer);
   
   // Handle window resize
   window.addEventListener('resize', handleResize);
   
   // Start animation loop
   animate();
  };
  


  // Create highway lines
  const createHighwayLines = () => {
   // Create multiple lines on each side to simulate highway perspective
   const lines = [];

   // Left side lines (pink/magenta) - converging inward
   for (let i = 0; i < 8; i++) {
    const geometry = new THREE.BufferGeometry();
    const material = new THREE.LineBasicMaterial({
     color: 0xec4899,
     transparent: true,
     opacity: 0.8 - (i * 0.08), // Fade out with distance
     linewidth: 3
    });

    // Create line points that converge toward center
    const points = [];
    const startX = -15 - (i * 2); // Start further left for each line
    const endX = -2; // Converge toward center
    const z = -10 - (i * 5); // Lines at different depths

    for (let j = 0; j < 20; j++) {
     const progress = j / 19;
     const x = startX + (endX - startX) * progress;
     const y = 0;
     const zPos = z + (j * 2);
     points.push(new THREE.Vector3(x, y, zPos));
    }

    geometry.setFromPoints(points);
    const line = new THREE.Line(geometry, material);
    sceneRef.current.add(line);
    lines.push(line);
   }

   // Right side lines (blue/cyan) - converging inward
   for (let i = 0; i < 8; i++) {
    const geometry = new THREE.BufferGeometry();
    const material = new THREE.LineBasicMaterial({
     color: 0x3b82f6,
     transparent: true,
     opacity: 0.8 - (i * 0.08), // Fade out with distance
     linewidth: 3
    });

    // Create line points that converge toward center
    const points = [];
    const startX = 15 + (i * 2); // Start further right for each line
    const endX = 2; // Converge toward center
    const z = -10 - (i * 5); // Lines at different depths

    for (let j = 0; j < 20; j++) {
     const progress = j / 19;
     const x = startX + (endX - startX) * progress;
     const y = 0;
     const zPos = z + (j * 2);
     points.push(new THREE.Vector3(x, y, zPos));
    }

    geometry.setFromPoints(points);
    const line = new THREE.Line(geometry, material);
    sceneRef.current.add(line);
    lines.push(line);
   }

   // Store references for animation
   tubeRef.current = { lines: lines };
  };
  
  // Optimized post-processing setup with performance considerations
  const setupPostProcessing = (scene, camera, renderer) => {
   // Check device capabilities for adaptive quality
   const pixelRatio = Math.min(window.devicePixelRatio, 2); // Cap pixel ratio for performance
   const isLowEndDevice = navigator.hardwareConcurrency <= 4 || window.innerWidth < 768;

   // Create composer with optimized settings
   const composer = new EffectComposer(renderer);
   composerRef.current = composer;

   // Add render pass
   const renderPass = new RenderPass(scene, camera);
   composer.addPass(renderPass);

   // Conditional bloom effect based on device capability
   if (!isLowEndDevice) {
    const bloomPass = new UnrealBloomPass(
     new THREE.Vector2(window.innerWidth / 2, window.innerHeight / 2), // Reduced resolution
     0.8,  // Reduced strength
     0.2,  // Reduced radius
     0.9   // Higher threshold
    );
    composer.addPass(bloomPass);
   }

   // Simplified chromatic aberration for better performance
   if (!isLowEndDevice) {
    const chromaticPass = new ShaderPass(chromaticAberrationShader);
    chromaticPass.uniforms.distortion.value = 0.01; // Reduced distortion
    composer.addPass(chromaticPass);
    chromaticPassRef.current = chromaticPass;
   }
  };
  
  // Handle window resize
  const handleResize = () => {
   if (!cameraRef.current || !rendererRef.current || !composerRef.current) return;
   
   const width = window.innerWidth;
   const height = window.innerHeight;
   
   cameraRef.current.aspect = width / height;
   cameraRef.current.updateProjectionMatrix();
   
   rendererRef.current.setSize(width, height);
   composerRef.current.setSize(width, height);
  };
  
  // Optimized animation loop with performance throttling
  const animate = () => {
   timeRef.current += 0.005; // Reduced time increment for smoother performance

   // Performance-optimized line animation (reduced frequency)
   if (tubeRef.current && tubeRef.current.lines && timeRef.current % 0.02 < 0.005) {
    // Only update every few frames to improve performance
    tubeRef.current.lines.forEach((line, index) => {
     const isLeftSide = index < 4; // Reduced number of lines for better performance
     const speed = 0.3 + (index % 4) * 0.03; // Reduced speed and complexity

     // Get the positions
     const positions = line.geometry.attributes.position;
     const array = positions.array;

     // Simplified movement calculation
     for (let i = 0; i < positions.count; i += 2) { // Skip every other point for performance
      array[i * 3 + 2] += speed;

      // Reset position when too far
      if (array[i * 3 + 2] > 20) { // Reduced distance
       array[i * 3 + 2] = -30;

       // Simplified position calculation
       const progress = i / (positions.count - 1);
       if (isLeftSide) {
        array[i * 3] = -10 + (5 * progress); // Simplified calculation
       } else {
        array[i * 3] = 10 - (5 * progress);
       }
      }
     }

     positions.needsUpdate = true;
    });
   }

   // Reduced chromatic aberration update frequency
   if (chromaticPassRef.current && timeRef.current % 0.1 < 0.005) {
    chromaticPassRef.current.uniforms.time.value = timeRef.current;
   }

   if (composerRef.current) {
    composerRef.current.render();
   }

   requestRef.current = requestAnimationFrame(animate);
  };
  
  initialize();
  
  // Cleanup
  return () => {
   window.removeEventListener('resize', handleResize);
   
   if (requestRef.current) {
    cancelAnimationFrame(requestRef.current);
   }
   
   if (rendererRef.current && containerRef.current) {
    containerRef.current.removeChild(rendererRef.current.domElement);
   }
   
   // Dispose of resources
   if (tubeRef.current && tubeRef.current.lines) {
    tubeRef.current.lines.forEach(line => {
     line.geometry.dispose();
     line.material.dispose();
    });
   }
   
   if (textureRef.current) {
    textureRef.current.dispose();
   }
  };
 }, []);

 return (
  <div 
   ref={containerRef} 
   className={`absolute inset-0 z-0 ${className || ''}`}
   style={{ pointerEvents: 'none' }}
  />
 );
};

export default HyperspeedBackground;
